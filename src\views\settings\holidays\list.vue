<script setup name="SettingsHolidaysList">
import { usePagination } from '@/utils/composables'
import FormMode from './components/FormMode/index.vue'
import { Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

import { getSettingHolidays, deleteSettingHolidays, updateSettingHolidayAvailability } from '@/api/modules/settings'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const { t } = useI18n()

const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        id: ''
    },
    batch: {
        enable: false,
        selectionDataList: []
    },
    dataList: []
})

onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams()
    getSettingHolidays(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.holiday_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    data.value.formModeProps.id = ''
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.visible = true
}

function alterAvailability(row) {
    let params = {
        id: row.id,
        isAvailable: !row.is_available
    }
    updateSettingHolidayAvailability(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        let params = {
            id: JSON.stringify([row.id])
        }
        deleteSettingHolidays(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}
function onBatchDel() {
    ElMessageBox.confirm(t('dialog.messages.batchDeletion', { module: t('delivery.services.sizeWeight') }), t('dialog.titles.confirmation')).then(() => {
        let ids = []
        data.value.batch.selectionDataList.forEach(i => ids.push(i.id))

        let params = {
            id: JSON.stringify(ids)
        }
        deleteSettingHolidays(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

</script>

<template>
    <div>
        <page-header title="默认模块管理" />
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button @click="onBatchDel" type="danger">
                        {{ $t('operations.batch', { op: t('operations.delete') }) }}
                    </el-button>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="description" :label="$t('fields.desc')" />
                <el-table-column prop="start_at" :label="$t('settings.holiday.fields.startAt')" />
                <el-table-column prop="end_at" :label="$t('settings.holiday.fields.endAt')" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="alterAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>

                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
