<script setup>
import { getMessengerSettings, updateMessengerSettings, getAllEmailSettings, getAllOperationHours } from '@/api/modules/messenger'

const data = ref({
    loading: false,
    data: { email_host: {}, time_table: {} },
    emailSettings: [],
    operationHours: []
})

onMounted(() => {
    getData()
    getEmail()
    getHours()
})



function getData() {
    data.value.loading = true
    getMessengerSettings().then(res => {
        data.value.data = res.data
        data.value.loading = false
    })


}

function getEmail() {
    getAllEmailSettings().then(res => {
        data.value.emailSettings = res.data
    })
}

function getHours() {
    getAllOperationHours().then(res => {
        data.value.operationHours = res.data
    })
}

function updateSettings() {
    data.value.loading = true
    let params = {
        email_host: data.value.data.email_host.id,
        time_table: data.value.data.time_table.id
    }
    updateMessengerSettings(params).then(res => {
        if (res.data.errCode == 365) {
            getData()
        }
    })

}
</script>
<template>
    <div>
        <page-header :title="$t('operations.update')">
        </page-header>
        <el-row :gutter="20">
            <el-col :span="12">
                <page-main title="Email Host">
                    <el-space class="m-2">
                        <el-select v-model="data.data.email_host" placeholder="Select" value-key="id">
                            <el-option v-for="item in data.emailSettings" :key="item.id" :label="item.name" :value="item" />
                        </el-select>
                        <el-button type="primary" @click="updateSettings">{{ $t('operations.save') }}</el-button>
                        <el-button type="primary" plain @click="getData">{{ $t('operations.reset') }}</el-button>
                    </el-space>
                    <el-descriptions :title="data.data.email_host.name" :column="1" border>
                        <el-descriptions-item label="Host">{{ data.data.email_host.host }}</el-descriptions-item>
                        <el-descriptions-item label="Port">{{ data.data.email_host.port }}</el-descriptions-item>
                        <el-descriptions-item label="Use TLS">{{ data.data.email_host.use_tls }}</el-descriptions-item>
                        <el-descriptions-item label="User">{{ data.data.email_host.user }}</el-descriptions-item>
                        <el-descriptions-item label="Password">{{ data.data.email_host.password }}</el-descriptions-item>
                        <el-descriptions-item label="From Email">{{ data.data.email_host.from_email
                        }}</el-descriptions-item>
                    </el-descriptions>

                </page-main>
            </el-col>
            <el-col :span="12">
                <page-main title="Operation Hours">
                    <el-space class="m-2">
                        <el-select v-model="data.data.time_table" placeholder="Select" value-key="id">
                            <el-option v-for="item in data.operationHours" :key="item.id" :label="item.name"
                                :value="item" />
                        </el-select>
                        <el-button type="primary" @click="updateSettings">{{ $t('operations.save') }}</el-button>
                        <el-button type="primary" plain @click="getData">{{ $t('operations.reset') }}</el-button>
                    </el-space>
                    <el-descriptions :title="data.data.time_table.name" :column="1" border>
                        <el-descriptions-item :label="$t('selection.weekday.0')">
                            {{ data.data.time_table.mon_start_at }} ~ {{ data.data.time_table.mon_end_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('selection.weekday.1')">
                            {{ data.data.time_table.tue_start_at }} ~ {{ data.data.time_table.tue_end_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('selection.weekday.2')">
                            {{ data.data.time_table.wed_start_at }} ~ {{ data.data.time_table.wed_end_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('selection.weekday.3')">
                            {{ data.data.time_table.thu_start_at }} ~ {{ data.data.time_table.thu_end_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('selection.weekday.4')">
                            {{ data.data.time_table.fri_start_at }} ~ {{ data.data.time_table.fri_end_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('selection.weekday.5')">
                            {{ data.data.time_table.sat_start_at }} ~ {{ data.data.time_table.sat_end_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('selection.weekday.6')">
                            {{ data.data.time_table.sun_start_at }} ~ {{ data.data.time_table.sun_end_at }}
                        </el-descriptions-item>
                    </el-descriptions>
                </page-main>
            </el-col>
        </el-row>

    </div>
</template>

<style>
.m-2 {
    margin-bottom: 20px;
}
</style>
