<template>
    <div class="driver-route-panel">
        <div class="header">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value">{{ scheduledCount }}</div>
                    <div class="stat-label">已分配</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ unscheduledCount }}</div>
                    <div class="stat-label">未分配</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ totalCount }}</div>
                    <div class="stat-label">总计</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ routeCount }}</div>
                    <div class="stat-label">路线</div>
                </div>
            </div>
            <div class="actions">
                <el-input
                    v-model="searchQuery"
                    placeholder="搜索司机..."
                    clearable
                    prefix-icon="Search"
                    size="small"
                />
                <el-switch
                    v-model="showAllDrivers"
                    active-text="全部"
                    inactive-text="在线"
                    inline-prompt
                    size="small"
                    :loading="isLoadingDrivers"
                />
            </div>
            <div v-if="filteredDrivers.length > 0" class="selection-actions">
                <el-button size="small" type="primary" :disabled="isAllSelected" @click="selectAllDrivers">全选</el-button>
                <el-button size="small" type="info" :disabled="!hasSelectedDrivers" @click="unselectAllDrivers">取消选择</el-button>
                <!-- 调试按钮 -->
                <el-button v-if="isDevMode" size="small" type="warning" @click="debugRouteStore">调试 RouteStore</el-button>
                <el-button v-if="isDevMode" size="small" type="success" @click="debugRouteGeometry">检查路线几何</el-button>
                <el-button v-if="isDevMode" size="small" type="info" @click="debugEndpoints">检查起终点</el-button>
                <el-button v-if="isDevMode" size="small" type="primary" @click="debugSegments">检查路段</el-button>
            </div>
        </div>

        <div class="driver-list">
            <el-empty v-if="!filteredDrivers.length" description="暂无司机数据" />

            <div v-else>
                <div
                    v-for="driver in filteredDrivers"
                    :key="driver.id"
                    class="driver-item"
                    :class="{ active: selectedDriver?.id === driver.id }"
                >
                    <div class="driver-header">
                        <el-checkbox
                            v-model="driver.isSelected"
                            @change="(val) => handleDriverSelect(driver, val)"
                            @click.stop
                        />
                        <div class="driver-color" :style="{ backgroundColor: driver.color }" />
                        <div class="driver-name" @click="selectDriver(driver)">{{ driver.name }}</div>
                        <el-tag :type="driver.status === '在线' ? 'success' : 'info'" size="small">
                            {{ driver.status }}
                        </el-tag>
                        <el-icon
                            class="expand-icon"
                            :class="{ 'is-active': driver.isExpanded }"
                            @click.stop="toggleDriverExpand(driver)"
                        >
                            <ArrowDown />
                        </el-icon>
                    </div>

                    <!-- 司机对应的路线 -->
                    <div
                        v-if="getDriverRoutes(driver.id).length > 0"
                        v-show="driver.isExpanded"
                        class="routes-container"
                    >
                        <div
                            v-for="route in getDriverRoutes(driver.id)"
                            :key="route.id"
                            class="route-item"
                            :class="{ selected: selectedRoute?.id === route.id }"
                        >
                            <div class="route-header" @click.stop="selectRoute(route)">
                                <div class="route-name">
                                    路线 {{ formatRouteNumber(route.name) }}
                                    <span class="order-count">
                                        ({{ getRouteOrderCount(route.name) }}单)
                                    </span>

                                    <!-- 添加图标按钮 -->
                                    <div class="route-action-icons">
                                        <el-tooltip content="保存路线顺序" placement="top">
                                            <el-button
                                                circle
                                                size="small"
                                                type="primary"
                                                :icon="Promotion"
                                                :loading="route.isSaving"
                                                @click.stop="saveRouteOrder(route)"
                                            ></el-button>
                                        </el-tooltip>

                                        <el-tooltip content="切换司机" placement="top">
                                            <el-button
                                                circle
                                                size="small"
                                                type="info"
                                                :icon="User"
                                                :loading="route.isSwitchingDriver"
                                                @click.stop="openSwitchDriverDialog(route)"
                                            ></el-button>
                                        </el-tooltip>

                                        <el-tooltip content="反转顺序" placement="top">
                                            <el-button
                                                circle
                                                size="small"
                                                type="danger"
                                                :icon="Switch"
                                                :loading="route.isReversing"
                                                @click.stop="reverseRouteOrder(route)"
                                            ></el-button>
                                        </el-tooltip>
                                    </div>
                                </div>
                                <el-icon
                                    class="route-expand-icon"
                                    :class="{ 'is-active': route.isExpanded }"
                                    @click.stop="toggleRouteExpand(route)"
                                >
                                    <ArrowDown />
                                </el-icon>
                            </div>
                            <div v-show="route.isExpanded" class="route-stops">
                                <!-- 修改判断条件，确保检查正确的数据源 -->
                                <div v-if="!routeOrdersMap[route.name] || routeOrdersMap[route.name].length === 0" class="empty-route-message">
                                    该路线暂无订单，请先分配订单
                                </div>
                                <draggable
                                    v-else
                                    v-model="routeOrdersMap[route.name]"
                                    item-key="id"
                                    ghost-class="ghost-item"
                                    animation="150"
                                    animation-duration="150"
                                    easing="ease-out"
                                    drag-class="dragging"
                                    :group="{ name: 'orders', pull: false, put: false }"
                                    :forceFallback="true"
                                    :delay="150"
                                    :delayOnTouchOnly="true"
                                    :touchStartThreshold="5"
                                    @start="(evt) => onDragStart(evt, route)"
                                    @end="(evt) => onDragEnd(evt, route)"
                                    @change="onDragChange(route, $event)"
                                >
                                    <template #item="{element, index}">
                                        <div
                                            class="stop-item"
                                            :class="{
                                                'selected': isOrderSelected(element.id),
                                                'editing-stop': editingStopOrder && editingStopOrder.id === element.id
                                            }"
                                            @click="handleOrderClick(element, $event)"
                                            @dblclick="handleOrderDoubleClick(element, index, route)"
                                        >
                                            <div class="stop-number" v-if="!(editingStopOrder && editingStopOrder.id === element.id)" title="长按拖拽可调整顺序">
                                                {{ index + 1 }}
                                                <span class="drag-indicator">≡</span>
                                            </div>
                                            <div class="stop-number-edit" v-else>
                                                <el-input
                                                    v-model="editingStopValue"
                                                    type="number"
                                                    size="small"
                                                    :min="1"
                                                    :max="routeOrdersMap[route.name]?.length || 1"
                                                    @blur="handleStopNumberBlur"
                                                    @keyup.enter="handleStopNumberConfirm"
                                                    @keyup.esc="cancelStopNumberEdit"
                                                    autofocus
                                                />
                                            </div>
                                            <div class="stop-info">
                                                <div class="stop-name">{{ element.name || '未知联系人' }}</div>
                                                <div class="stop-address">{{ element.address || '未知地址' }}</div>
                                                <div class="stop-no">{{ element.no || '未知订单号' }}</div>
                                                <!-- 添加路段距离和时间信息 -->
                                                <div class="stop-segment-info" v-if="getSegmentInfo(route, index)"
                                                     :class="{ 'same-address': getSegmentInfo(route, index).isSameAddress }">
                                                    <span class="segment-distance">
                                                        <el-icon><Location /></el-icon>
                                                        {{ getSegmentInfo(route, index).distance }}
                                                    </span>
                                                    <span class="segment-duration">
                                                        <el-icon><Clock /></el-icon>
                                                        {{ getSegmentInfo(route, index).duration }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </draggable>
                                <div class="route-actions">
                                    <el-button
                                        type="primary"
                                        size="small"
                                        :loading="route.isSaving"
                                        @click="saveRouteOrder(route)"
                                    >
                                        <el-icon><Promotion /></el-icon>
                                        保存路线(updateRoute)
                                    </el-button>
                                    <el-button
                                        type="success"
                                        size="small"
                                        :loading="route.isSavingLegacy"
                                        @click="saveRouteOrderLegacy(route)"
                                    >
                                        <el-icon><Edit /></el-icon>
                                        保存路线(逐单更新)
                                    </el-button>
                                    <el-button
                                        type="warning"
                                        size="small"
                                        :loading="route.isHereOptimizing"
                                        @click="optimizeRouteWithHERE(route)"
                                    >
                                        <el-icon><Guide /></el-icon>
                                        HERE分配
                                    </el-button>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 无路线时不显示任何内容 -->
                    <!-- <div v-else class="empty-routes">
            <span class="no-routes-text">暂无路线</span>
          </div> -->
                </div>
            </div>
        </div>

        <!-- 切换司机弹窗 -->
        <el-dialog
            v-model="switchDriverDialogVisible"
            title="切换司机"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="true"
        >
            <div class="switch-driver-dialog">
                <div class="dialog-actions">
                    <el-input
                        v-model="driverSearchQuery"
                        placeholder="搜索司机..."
                        clearable
                        prefix-icon="Search"
                        size="small"
                    />
                    <el-switch
                        v-model="showAllDriversInDialog"
                        active-text="全部"
                        inactive-text="在线"
                        inline-prompt
                        size="small"
                        @change="handleDriverFilterChange"
                    />
                </div>
                <div class="driver-list-container">
                    <el-empty v-if="allDrivers.length === 0" description="暂无司机数据" />
                    <el-radio-group v-else v-model="selectedDriverId" class="driver-radio-group">
                        <el-radio
                            v-for="driver in filteredAllDrivers"
                            :key="driver.id"
                            :label="driver.id"
                            class="driver-radio-item"
                        >
                            {{ driver.name }}
                            <el-tag :type="driver.status === '在线' ? 'success' : 'info'" size="small">
                                {{ driver.status }}
                            </el-tag>
                        </el-radio>
                    </el-radio-group>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="switchDriverDialogVisible = false">取消</el-button>
                    <el-button type="primary" :loading="isSavingDriver" @click="handleSwitchDriver">
                        确认
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, reactive, nextTick } from 'vue'
import { useDriverStore } from '../stores/driver'
import { useRouteStore } from '../stores/route'
import { useOrderStore } from '../stores/order'
import { useTimeStore } from '../stores/time'
import { useAddressStore } from '../stores/address'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import { ArrowDown, Switch, Guide, Promotion, User, Edit, Location, Clock } from '@element-plus/icons-vue'
import { eventBus, EVENT_TYPES } from '../utils/eventBus'
import draggable from 'vuedraggable'
import { routeAPI, driverAPI } from '../api'
import { inject } from 'vue'

const isDevMode = process.env.NODE_ENV === 'development'

const driverStore = useDriverStore()
const routeStore = useRouteStore()
const orderStore = useOrderStore()
const timeStore = useTimeStore()
const addressStore = useAddressStore()

// 注入地图组件的路线管理功能
const mapRouteManagement = inject('mapRouteManagement', null)

// 从store中获取响应式引用
const { selectedDriver } = storeToRefs(driverStore)
const { selectedRoute } = storeToRefs(routeStore)

// 本地状态
const searchQuery = ref('')
const showAllDrivers = ref(false) // 控制是否显示所有司机（包括离线司机）
const selectedDrivers = ref([]) // 存储多选的司机ID
const isLoadingDrivers = ref(false) // 控制司机加载状态

// 路线订单映射，用于拖拽排序
// 注意：现在使用外部容器的滚动，移除了内部滚动容器
// :forceFallback="true" - 强制使用回退模式以确保拖拽功能在所有浏览器中正常工作
// :delay="150" - 延迟150毫秒开始拖拽，这样短暂的触摸会被视为滚动而不是拖拽
// :delayOnTouchOnly="true" - 仅在触摸设备上应用延迟
// :touchStartThreshold="5" - 在延迟期间，手指移动超过5像素才会取消拖拽
// animation="150" - 使用较短的动画时间，使拖拽更加流畅
// easing="ease-out" - 使用简单的缓动函数，减少计算量
const routeOrdersMap = reactive({})

// 切换司机弹窗相关状态
const switchDriverDialogVisible = ref(false)
const driverSearchQuery = ref('')
const selectedDriverId = ref(null)
const currentRoute = ref(null)
const isSavingDriver = ref(false)
const allDrivers = ref([])
const showAllDriversInDialog = ref(false) // 控制弹窗中是否显示所有司机

// 根据搜索条件筛选所有司机
const filteredAllDrivers = computed(() => {
    if (!driverSearchQuery.value) {
        return allDrivers.value
    }

    const query = driverSearchQuery.value.toLowerCase()
    return allDrivers.value.filter(driver =>
        driver.name.toLowerCase().includes(query)
    )
})

// 根据筛选条件获取司机列表
const filteredDrivers = computed(() => {
    let drivers = driverStore.drivers || []

    console.log('过滤前司机数量:', drivers.length);

    // 如果只显示在线司机，但我们现在不需要在这里过滤
    // 因为我们已经在API请求中根据开关状态获取了不同的司机数据
    // 所以这里不再需要过滤

    // 根据搜索条件筛选
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        drivers = drivers.filter(driver => driver.name.toLowerCase().includes(query))
    }

    console.log('过滤后司机数量:', drivers.length);

    // 将司机分为有路线和无路线两组
    const driversWithRoutes = []
    const driversWithoutRoutes = []

    drivers.forEach(driver => {
        // 检查司机是否有路线
        if (getDriverRoutes(driver.id).length > 0) {
            driversWithRoutes.push(driver)
        } else {
            driversWithoutRoutes.push(driver)
        }
    })

    // 合并两组司机，有路线的在前面
    return [...driversWithRoutes, ...driversWithoutRoutes]
})

// 在组件挂载时初始化路线订单
onMounted(async() => {
    if (isDevMode) {
        console.time('DriverRoutePanel mounted')
    }

    try {
        console.log('开始初始化数据')

        // 先获取班次数据，fetchShifts 内部会自动设置当前班次
        console.log('获取班次数据...')
        await timeStore.fetchShifts()
        console.log('班次数据获取完成，当前班次:', timeStore.selectedShift ? timeStore.selectedShift.label : '未设置')

        // 获取司机数据，传递 shift 参数
        console.log('获取司机数据，使用当前班次...')
        console.log('参数:', {
            date: timeStore.selectedDate,
            shift_id: timeStore.selectedShift?.id
        });

        // 直接使用 API 调用，以便获取原始响应
        const driverResponse = await driverAPI.getDrivers({
            date: timeStore.selectedDate,
            shift_id: timeStore.selectedShift?.id
        });

        if (driverResponse && driverResponse.driver_list) {
            // 更新司机数据
            driverStore.updateDrivers(driverResponse.driver_list);
            console.log(`获取到 ${driverResponse.driver_list.length} 个司机`);

            // 调试：检查司机状态
            console.log('司机状态分布:');
            const statusCounts = {};
            driverResponse.driver_list.forEach(driver => {
                const status = driver.status || '未知';
                statusCounts[status] = (statusCounts[status] || 0) + 1;
            });
            console.log(statusCounts);
        } else {
            console.error('获取司机列表失败，返回格式不正确:', driverResponse);
        }

        console.log('司机数据获取完成，数量:', driverStore.drivers.length)

        // 获取路线数据
        if (timeStore.selectedDate && timeStore.selectedShift) {
            console.log('获取路线数据，日期:', timeStore.selectedDate, '班次:', timeStore.selectedShift.label)

            // 详细记录 selectedShift 对象
            console.log('selectedShift 对象详情:', JSON.stringify(timeStore.selectedShift, null, 2))

            // 根据班次名称直接设置 priority
            let priority;

            // 获取班次名称
            const shiftName = timeStore.selectedShift.name || '';
            console.log('shiftName:', shiftName)

            console.log('班次信息:', {
                name: shiftName,
                fullObject: JSON.stringify(timeStore.selectedShift)
            });

            // 根据班次名称设置 priority
            if (shiftName.includes('AM')) {
                priority = 2;
                console.log('根据班次名称设置 priority=2 (AM)');
            } else if (shiftName.includes('PM')) {
                priority = 4;
                console.log('根据班次名称设置 priority=4 (PM)');
            } else if (shiftName.includes('NT')) {
                priority = 6;
                console.log('根据班次名称设置 priority=6 (NT)');
            }
            // 如果无法确定班次，使用默认值
            else {
                // 默认使用 AM 班次
                priority = 2;
                console.log('无法确定班次，使用默认值 priority=2 (AM)');
            }

            // 调用 fetchRoutes 并直接传递 priority
            await routeStore.fetchRoutes({
                date: timeStore.selectedDate,
                shift: timeStore.selectedShift,
                priority: priority // 直接传递 priority 参数
            })

            // 初始化所有司机的展开状态为false
            driverStore.drivers.forEach(driver => {
                driver.isExpanded = false
                driver.isSelected = false
            })

            // 初始化所有路线的展开状态为false
            routeStore.routes.forEach(route => {
                route.isExpanded = false
                route.isSaving = false
                route.isSavingLegacy = false // <-- 逐单更新加载状态
                route.isHereOptimizing = false // <-- HERE 加载状态
                route.isSwitchingDriver = false // <-- 切换司机加载状态
                route.isReversing = false // <-- 反转顺序加载状态
            })

            // 初始化所有路线的订单数据
            updateAllRouteOrderMaps()
        } else {
            console.warn('缺少日期或班次信息，无法获取路线数据')
        }

        // 添加事件监听，监听订单更新事件
        eventBus.on(EVENT_TYPES.ORDERS_UPDATED, handleOrdersUpdated)

        // 添加对专门的订单取消分配事件的监听
        eventBus.on(EVENT_TYPES.ORDERS_UNASSIGNED, handleOrdersUnassigned)

        // 添加对订单选择事件的监听
        eventBus.on(EVENT_TYPES.ORDER_SELECTED, handleOrderSelected)

        // 添加对批量订单选择事件的监听
        eventBus.on(EVENT_TYPES.ORDERS_BATCH_SELECTED, handleOrdersBatchSelected)

        // 添加对订单选择清除事件的监听
        eventBus.on(EVENT_TYPES.ORDERS_SELECTION_CLEARED, handleOrdersSelectionCleared)

    } catch (error) {
        console.error('初始化数据失败:', error)
        ElMessage.error('加载数据失败')
    }

    if (isDevMode) {
        console.timeEnd('DriverRoutePanel mounted')
    }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
    // 清理事件监听
    eventBus.off(EVENT_TYPES.ORDERS_UPDATED, handleOrdersUpdated)
    eventBus.off(EVENT_TYPES.ORDERS_UNASSIGNED, handleOrdersUnassigned)
    eventBus.off(EVENT_TYPES.ORDER_SELECTED, handleOrderSelected)
    eventBus.off(EVENT_TYPES.ORDERS_BATCH_SELECTED, handleOrdersBatchSelected)
    eventBus.off(EVENT_TYPES.ORDERS_SELECTION_CLEARED, handleOrdersSelectionCleared)
})

// 监听选中司机变化，自动展开/折叠
watch(selectedDrivers, newSelected => {
    driverStore.drivers.forEach(driver => {
        driver.isExpanded = newSelected.includes(driver.id)
    })
})

// 监听selectedDrivers的变化
watch(selectedDrivers, newSelected => {
    // 这里可以添加当选中司机变化时的处理逻辑
    // 例如：更新地图显示、统计信息等
    console.log('选中的司机变化:', newSelected)
})

// 监听showAllDrivers的变化，切换时重新获取司机数据
watch(showAllDrivers, async (newValue) => {
    console.log(`司机显示模式切换为: ${newValue ? '全部' : '在线'}`);

    // 设置加载状态
    isLoadingDrivers.value = true;

    try {
        if (newValue) {
            // "全部"模式：获取所有司机（不添加参数）
            console.log('获取所有司机（不带参数）');
            const response = await driverAPI.getDrivers();

            if (response && response.driver_list) {
                // 更新司机数据
                driverStore.updateDrivers(response.driver_list);
                console.log(`获取到 ${response.driver_list.length} 个司机`);

                // 调试：检查司机状态
                console.log('司机状态分布:');
                const statusCounts = {};
                response.driver_list.forEach(driver => {
                    const status = driver.status || '未知';
                    statusCounts[status] = (statusCounts[status] || 0) + 1;
                });
                console.log(statusCounts);
            } else {
                console.error('获取司机列表失败，返回格式不正确:', response);
            }
        } else {
            // "在线"模式：获取在线司机（带日期和班次参数）
            console.log('获取在线司机（带参数）');
            console.log('参数:', {
                date: timeStore.selectedDate,
                shift_id: timeStore.selectedShift?.id
            });

            // 直接使用 API 调用，而不是 store 方法，以便获取原始响应
            const response = await driverAPI.getDrivers({
                date: timeStore.selectedDate,
                shift_id: timeStore.selectedShift?.id
            });

            if (response && response.driver_list) {
                // 更新司机数据
                driverStore.updateDrivers(response.driver_list);
                console.log(`获取到 ${response.driver_list.length} 个司机`);

                // 调试：检查司机状态
                console.log('司机状态分布:');
                const statusCounts = {};
                response.driver_list.forEach(driver => {
                    const status = driver.status || '未知';
                    statusCounts[status] = (statusCounts[status] || 0) + 1;
                });
                console.log(statusCounts);

                // 如果没有司机，显示提示
                if (response.driver_list.length === 0) {
                    ElMessage.info('当前班次没有在线司机');
                }
            } else {
                console.error('获取司机列表失败，返回格式不正确:', response);
            }
        }
    } catch (error) {
        console.error('获取司机数据失败:', error);
        ElMessage.error('获取司机数据失败');
    } finally {
        // 清除加载状态
        isLoadingDrivers.value = false;
    }
})

// 是否所有司机都已选中
const isAllSelected = computed(() => {
    if (filteredDrivers.value.length === 0) return false
    return filteredDrivers.value.every(driver => driver.isSelected)
})

// 是否有司机被选中
const hasSelectedDrivers = computed(() => {
    return selectedDrivers.value.length > 0
})

// 全选司机
const selectAllDrivers = () => {
    filteredDrivers.value.forEach(driver => {
        driver.isSelected = true
        // 确保添加到选中列表
        if (!selectedDrivers.value.includes(driver.id)) {
            selectedDrivers.value.push(driver.id)
        }
    })

    // 更新订单显示
    updateSelectedDriverOrders()
}

// 取消全选
const unselectAllDrivers = () => {
    filteredDrivers.value.forEach(driver => {
        driver.isSelected = false
    })

    // 清空选中列表
    selectedDrivers.value = []

    // 清除订单选择
    orderStore.clearSelection()
    driverStore.clearSelectedDriver()
}

// 更新所有选中司机的订单显示
const updateSelectedDriverOrders = () => {
    // 清除之前的订单选择
    orderStore.clearSelection()

    // 如果有选中的路线，取消选中
    if (routeStore.selectedRoute) {
        routeStore.clearSelectedRoute()
    }

    // 发布事件，通知其他组件司机选择已变更
    eventBus.emit(EVENT_TYPES.DRIVER_SELECTION_CHANGED, {
        selectedDriverIds: selectedDrivers.value
    })

    console.log('已更新选中司机IDs:', selectedDrivers.value)
}

// 切换司机展开状态
const toggleDriverExpand = driver => {
    driver.isExpanded = !driver.isExpanded
}

// 切换路线展开状态
const toggleRouteExpand = route => {
    route.isExpanded = !route.isExpanded

    // 如果是展开路线，重新获取最新订单数据
    if (route.isExpanded) {
        console.log(`展开路线 ${route.name}，主动获取最新订单数据`)

        // 强制清除缓存，重新获取订单
        delete routeOrdersMap[route.name]

        // 重新获取订单并确保正确存储
        const routeOrders = getSortedRouteOrders(route.name)
        console.log(`路线 ${route.name} 的订单数量:`, routeOrders.length)

        // 强制更新视图
        nextTick(() => {
            if (routeOrders.length > 0) {
                console.log(`路线 ${route.name} 的订单已更新，数量: ${routeOrders.length}`)
            } else {
                console.log(`路线 ${route.name} 没有订单，已更新状态`)
            }
        })
    }
}

// 更新拖拽后的停靠点序号
const updateStopNumbers = route => {
    console.time('updateStopNumbers')

    // 更新拖拽后的停靠点序号
    const orders = routeOrdersMap[route.name] || []
    if (orders.length === 0) return

    // 更新每个订单的停靠点序号
    orders.forEach((order, index) => {
        order.stop_no = index + 1
    })

    console.log('拖拽结束: 路线顺序已更新:', route.name, orders.map(o => ({ id: o.id, stop_no: o.stop_no })))

    // 直接发送MAP_REFRESH事件，更新地图标记
    // 这样可以避免闪烁问题，因为MAP_REFRESH事件处理程序会直接更新标记而不是清除后重建
    eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
        orders: orders,
        routeName: route.name,
        routeId: route.id,
        timestamp: Date.now() // 添加时间戳以便接收方判断更新顺序
    })

    // 触发重新绘制真实路线
    console.log('检查 mapRouteManagement 可用性:', {
        mapRouteManagement: !!mapRouteManagement,
        drawRealisticRoute: !!(mapRouteManagement?.drawRealisticRoute),
        routeId: route.id
    })

    if (mapRouteManagement && mapRouteManagement.drawRealisticRoute) {
        console.log('拖拽结束: 触发重新绘制真实路线, routeId:', route.id)
        try {
            await mapRouteManagement.drawRealisticRoute(route.id)
            console.log('真实路线重绘完成')
        } catch (error) {
            console.error('重新绘制真实路线失败:', error)
        }
    } else {
        console.warn('mapRouteManagement 或 drawRealisticRoute 不可用')
    }

    console.timeEnd('updateStopNumbers')
}

// 存储多选拖拽的状态
const multiDragState = reactive({
    isMultiDrag: false,
    selectedOrderIds: [],
    originalIndices: {},
    targetRoute: null
})

// 存储停靠点编辑状态
const editingStopOrder = ref(null)
const editingStopValue = ref('')
const editingStopRoute = ref(null)

// 移除了滚动控制逻辑，因为我们现在使用外部容器的滚动

// 处理拖拽开始事件
const onDragStart = (evt, route) => {
    console.log('拖拽开始', evt)

    // 获取当前拖拽的订单ID
    const draggedOrderId = evt.item.__draggable_context.element.id

    // 检查是否是多选拖拽（当前拖拽的订单是否在选中列表中）
    const isMultiDrag = orderStore.selectedOrderIds.has(draggedOrderId) &&
                        orderStore.selectedOrderIds.size > 1

    if (isMultiDrag) {
        console.log('检测到多选拖拽操作')

        // 设置多选拖拽状态
        multiDragState.isMultiDrag = true
        multiDragState.targetRoute = route

        // 获取所有选中的订单ID
        multiDragState.selectedOrderIds = Array.from(orderStore.selectedOrderIds)

        // 记录每个选中订单的原始索引
        const orders = routeOrdersMap[route.name] || []
        multiDragState.originalIndices = {}

        orders.forEach((order, index) => {
            if (orderStore.selectedOrderIds.has(order.id)) {
                multiDragState.originalIndices[order.id] = index
            }
        })

        console.log('多选拖拽状态:', multiDragState)
    } else {
        // 单个订单拖拽，重置多选拖拽状态
        multiDragState.isMultiDrag = false
        multiDragState.selectedOrderIds = []
        multiDragState.originalIndices = {}
        multiDragState.targetRoute = null
    }
}

// 处理拖拽结束事件
const onDragEnd = (_, route) => { // 使用下划线表示未使用的参数
    // 更新停靠点序号
    updateStopNumbers(route)

    // 如果是多选拖拽，重置状态
    if (multiDragState.isMultiDrag) {
        multiDragState.isMultiDrag = false
        multiDragState.selectedOrderIds = []
        multiDragState.originalIndices = {}
        multiDragState.targetRoute = null
    }
}

// 检查订单是否被选中
const isOrderSelected = (orderId) => {
    return orderStore.selectedOrderIds.has(orderId)
}

// 处理订单点击事件
const handleOrderClick = (order, event) => {
    // 如果是拖拽操作，不处理点击事件
    if (event.target.closest('.sortable-chosen') || event.target.closest('.sortable-drag')) {
        return
    }

    // 如果正在编辑停靠点编号，不处理点击事件
    if (editingStopOrder.value) {
        return
    }

    // 检查是否按下了Ctrl键（多选模式）
    const isMultiSelect = event.ctrlKey || event.metaKey

    console.log('订单点击:', order.id, '多选模式:', isMultiSelect)

    // 阻止事件冒泡，防止触发其他点击事件
    event.stopPropagation()

    // 处理订单选择
    if (isMultiSelect) {
        // Ctrl键多选模式：切换选中状态
        orderStore.toggleOrderSelection(order.id)
    } else {
        // 单选模式
        const isCurrentlySelected = orderStore.selectedOrderIds.has(order.id)

        if (isCurrentlySelected && orderStore.selectedOrderIds.size === 1) {
            // 如果当前点击的订单已选中且只有它一个被选中，则取消选择
            orderStore.clearSelection()
        } else {
            // 否则清空所有选择，然后选中这一个
            orderStore.clearSelection()
            orderStore.toggleOrderSelection(order.id)
        }
    }

    // 通过事件总线通知其他组件（如地图、订单列表）
    eventBus.emit(EVENT_TYPES.ORDER_SELECTED, {
        orderId: order.id,
        isMultiSelect: isMultiSelect,
        source: 'driver-route-panel' // 标记事件来源
    })
}

// 处理订单双击事件，进入停靠点编号编辑模式
const handleOrderDoubleClick = (order, index, route) => {
    console.log('订单双击:', order.id, '当前索引:', index)

    // 如果已经在编辑其他订单，先取消编辑
    if (editingStopOrder.value) {
        cancelStopNumberEdit()
    }

    // 设置当前编辑的订单和路线
    editingStopOrder.value = order
    editingStopRoute.value = route
    editingStopValue.value = String(index + 1) // 当前索引+1作为初始值

    // 下一个事件循环中聚焦输入框
    nextTick(() => {
        const inputEl = document.querySelector('.stop-number-edit .el-input__inner')
        if (inputEl) {
            inputEl.focus()
            inputEl.select()
        }
    })
}

// 处理停靠点编号输入框失焦事件
const handleStopNumberBlur = () => {
    // 确认编辑
    handleStopNumberConfirm()
}

// 确认停靠点编号编辑
const handleStopNumberConfirm = () => {
    if (!editingStopOrder.value || !editingStopRoute.value) return

    const newPosition = parseInt(editingStopValue.value)
    const route = editingStopRoute.value
    const orders = routeOrdersMap[route.name] || []

    // 验证输入的位置是否有效
    if (isNaN(newPosition) || newPosition < 1 || newPosition > orders.length) {
        ElMessage.warning('请输入有效的停靠点编号')
        return
    }

    // 获取当前订单的索引
    const currentIndex = orders.findIndex(o => o.id === editingStopOrder.value.id)
    if (currentIndex === -1) {
        console.error('找不到当前编辑的订单')
        cancelStopNumberEdit()
        return
    }

    // 计算目标索引（输入的位置减1，因为索引从0开始）
    const targetIndex = newPosition - 1

    // 如果位置没有变化，直接取消编辑
    if (currentIndex === targetIndex) {
        cancelStopNumberEdit()
        return
    }

    console.log(`移动订单 ${editingStopOrder.value.id} 从位置 ${currentIndex + 1} 到位置 ${newPosition}`)

    // 移动订单到新位置
    const orderToMove = orders[currentIndex]

    // 创建新的订单数组
    const newOrders = [...orders]

    // 从原位置移除
    newOrders.splice(currentIndex, 1)

    // 插入到新位置
    newOrders.splice(targetIndex, 0, orderToMove)

    // 更新路线订单映射
    routeOrdersMap[route.name] = newOrders

    // 重新分配停靠点序号
    newOrders.forEach((order, index) => {
        order.stop_no = index + 1
    })

    // 使用OrderStore的updateLocalOrderSequence方法更新订单顺序
    orderStore.updateLocalOrderSequence(route.id, newOrders)

    // 发送地图刷新事件
    eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
        routeId: route.id,
        timestamp: Date.now()
    })

    // 触发重新绘制真实路线
    if (mapRouteManagement && mapRouteManagement.drawRealisticRoute) {
        console.log('停靠点编号变更: 触发重新绘制真实路线')
        try {
            mapRouteManagement.drawRealisticRoute(route.id)
        } catch (error) {
            console.error('重新绘制真实路线失败:', error)
        }
    }

    // 取消编辑状态
    cancelStopNumberEdit()

    // 显示成功提示
    ElMessage.success(`已将订单移动到位置 ${newPosition}`)
}

// 取消停靠点编号编辑
const cancelStopNumberEdit = () => {
    editingStopOrder.value = null
    editingStopRoute.value = null
    editingStopValue.value = ''
}

// 处理拖拽变化事件，当有订单位置变化时立即更新
const onDragChange = (route, event) => {
    // 检查是否是拖拽取消事件（例如按下Esc键）
    if (event.canceled) {
        return;
    }

    // 只有当发生了移动事件时才处理
    if (event.moved || event.added || event.removed) {
        console.log('拖拽元素变化:', event)

        // 获取最新顺序的订单列表
        const orders = routeOrdersMap[route.name] || []
        if (orders.length === 0) return

        // 如果是多选拖拽，需要特殊处理
        if (multiDragState.isMultiDrag && event.moved) {
            console.log('处理多选拖拽变化')

            // 获取当前拖拽的订单
            const draggedOrder = event.moved.element
            const draggedOrderId = draggedOrder.id

            // 获取拖拽前和拖拽后的索引
            const oldIndex = event.moved.oldIndex
            const newIndex = event.moved.newIndex

            // 计算移动的偏移量
            const offset = newIndex - oldIndex

            if (offset !== 0) {
                // 获取所有选中的订单，按照它们在原始列表中的顺序排序
                const selectedOrders = multiDragState.selectedOrderIds
                    .map(id => {
                        const order = orders.find(o => o.id === id)
                        const originalIndex = multiDragState.originalIndices[id] || 0
                        return { order, originalIndex }
                    })
                    .filter(item => item.order) // 过滤掉未找到的订单
                    .sort((a, b) => a.originalIndex - b.originalIndex) // 按原始索引排序

                console.log('选中的订单（按原始顺序）:', selectedOrders.map(item => ({
                    id: item.order.id,
                    originalIndex: item.originalIndex
                })))

                // 从列表中移除所有选中的订单（除了当前拖拽的订单，它已经被移动了）
                const orderIdsToRemove = multiDragState.selectedOrderIds.filter(id => id !== draggedOrderId)

                // 创建一个新的订单列表，移除其他选中的订单
                let newOrders = [...orders]
                newOrders = newOrders.filter(order => !orderIdsToRemove.includes(order.id))

                // 计算其他选中订单应该插入的位置
                // 如果是向下拖拽，则按照原始相对顺序插入到拖拽订单之后
                // 如果是向上拖拽，则按照原始相对顺序插入到拖拽订单之前
                const draggedOrderIndex = newOrders.findIndex(o => o.id === draggedOrderId)

                if (draggedOrderIndex === -1) {
                    console.error('找不到拖拽的订单，无法完成多选拖拽')
                    return
                }

                // 按照原始相对顺序，将其他选中的订单插入到适当的位置
                let insertIndex = draggedOrderIndex
                if (offset > 0) {
                    // 向下拖拽，从拖拽订单后面开始插入
                    insertIndex += 1
                    selectedOrders.forEach(item => {
                        if (item.order.id !== draggedOrderId) {
                            newOrders.splice(insertIndex, 0, item.order)
                            insertIndex++
                        }
                    })
                } else {
                    // 向上拖拽，从拖拽订单前面开始插入（倒序）
                    for (let i = selectedOrders.length - 1; i >= 0; i--) {
                        const item = selectedOrders[i]
                        if (item.order.id !== draggedOrderId) {
                            newOrders.splice(insertIndex, 0, item.order)
                        }
                    }
                }

                // 更新路线订单映射
                routeOrdersMap[route.name] = newOrders

                // 重新分配停靠点序号
                newOrders.forEach((order, index) => {
                    order.stop_no = index + 1
                })

                // 使用OrderStore的updateLocalOrderSequence方法更新订单顺序
                orderStore.updateLocalOrderSequence(route.id, newOrders)

                console.log('多选拖拽更新完成，新的订单顺序:', newOrders.map(o => ({
                    id: o.id,
                    stop_no: o.stop_no,
                    isSelected: orderStore.selectedOrderIds.has(o.id)
                })))
            }
        } else {
            // 单个订单拖拽，简单更新停靠点序号
            orders.forEach((order, index) => {
                order.stop_no = index + 1
            })

            // 使用OrderStore的updateLocalOrderSequence方法更新订单顺序
            orderStore.updateLocalOrderSequence(route.id, orders)
        }

        // 直接发送MAP_REFRESH事件，更新地图标记
        // 这样可以避免闪烁问题，因为MAP_REFRESH事件处理程序会直接更新标记而不是清除后重建
        eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
            orders: orders,
            routeName: route.name,
            routeId: route.id,
            isDragging: true, // 标记这是拖拽过程中的更新
            isMultiDrag: multiDragState.isMultiDrag, // 标记是否是多选拖拽
            changeType: event.moved ? 'moved' : event.added ? 'added' : 'removed',
            // 如果是移动，添加移动信息以便其他组件优化更新
            moveInfo: event.moved
                ? {
                    oldIndex: event.moved.oldIndex,
                    newIndex: event.moved.newIndex,
                    element: event.moved.element
                }
                : null,
            timestamp: Date.now() // 添加时间戳以便接收方判断更新顺序
        })

        // 触发重新绘制真实路线（延迟执行，避免拖拽过程中频繁重绘）
        console.log('拖拽变化: 检查 mapRouteManagement 可用性:', {
            mapRouteManagement: !!mapRouteManagement,
            drawRealisticRoute: !!(mapRouteManagement?.drawRealisticRoute),
            routeId: route.id
        })

        if (mapRouteManagement && mapRouteManagement.drawRealisticRoute) {
            console.log('拖拽变化: 触发重新绘制真实路线 (延迟500ms)')
            // 使用防抖，避免拖拽过程中频繁重绘
            clearTimeout(route._redrawTimeout)
            route._redrawTimeout = setTimeout(async () => {
                try {
                    console.log('执行延迟的真实路线重绘, routeId:', route.id)
                    await mapRouteManagement.drawRealisticRoute(route.id)
                    console.log('延迟的真实路线重绘完成')
                } catch (error) {
                    console.error('重新绘制真实路线失败:', error)
                }
            }, 500) // 500ms 延迟
        } else {
            console.warn('拖拽变化: mapRouteManagement 或 drawRealisticRoute 不可用')
        }

        console.log('拖拽操作更新完成, 顺序已重新计算')
    }

    console.timeEnd('onDragChange')
}

// 保存路线顺序
const saveRouteOrder = async (route) => {
    if (route.isSaving) return

    try {
        // 设置保存中状态
        route.isSaving = true

        // 获取当前顺序的订单ID列表 - 从routeOrdersMap中获取，而不是route.orders
        const routeOrders = routeOrdersMap[route.name] || []
        if (routeOrders.length === 0) {
            ElMessage.warning('该路线没有订单，无法保存顺序')
            return
        }

        console.log(`保存路线 ${route.name} 的顺序，共 ${routeOrders.length} 个订单`)

        // 获取原始订单顺序（用于动态路线更新）
        const originalOrders = orderStore.getOrdersByRouteNumber(route.id) || []
        const originalOrderIds = originalOrders.map(o => o.id)
        const newOrderIds = routeOrders.map(o => o.id)

        console.log('订单顺序变更检测:', {
            原始顺序: originalOrderIds,
            新顺序: newOrderIds,
            是否有变更: JSON.stringify(originalOrderIds) !== JSON.stringify(newOrderIds)
        })

        // 构建与 createRoute 相同格式的 routeData
        const routeData = await buildRouteDataForUpdate(route, routeOrders)

        console.log('准备调用 updateRoute API，数据:', routeData)

        // 调用 updateRoute API
        await routeAPI.updateRoute(route.id, routeData)

        // 如果地图组件可用且路线顺序发生变更，触发动态路线更新
        if (mapRouteManagement &&
            mapRouteManagement.handleRouteOrderChange &&
            JSON.stringify(originalOrderIds) !== JSON.stringify(newOrderIds)) {

            console.log('触发动态路线更新...')
            try {
                await mapRouteManagement.handleRouteOrderChange(route.id, originalOrderIds, newOrderIds)
                console.log('动态路线更新完成')
            } catch (updateError) {
                console.warn('动态路线更新失败，但路线保存成功:', updateError)
                // 不影响主要的保存流程
            }
        } else {
            console.log('跳过动态路线更新:', {
                mapRouteManagement可用: !!mapRouteManagement,
                handleRouteOrderChange可用: !!(mapRouteManagement?.handleRouteOrderChange),
                顺序是否变更: JSON.stringify(originalOrderIds) !== JSON.stringify(newOrderIds)
            })
        }

        // 提示成功
        ElMessage.success('路线顺序已保存')
    } catch (error) {
        console.error('保存路线顺序失败:', error)
        ElMessage.error('保存路线顺序失败')
    } finally {
        // 清除保存中状态
        route.isSaving = false
    }
}

// 构建与 createRoute 相同格式的 routeData
const buildRouteDataForUpdate = async (route, routeOrders) => {
    try {
        // 根据班次名称确定 priority 值
        let priority = 2 // 默认 AM
        const shiftName = timeStore.selectedShift?.name || ''

        if (shiftName.includes('AM')) {
            priority = 2
        } else if (shiftName.includes('PM')) {
            priority = 4
        } else if (shiftName.includes('NT')) {
            priority = 6
        }

        // 获取仓库地址ID和司机地址ID（与 createRoute 逻辑保持一致）
        const warehouseAddressId = addressStore.warehouse.id;
        const driver = driverStore.getDriverById(route.driver || route.driverId);
        const driverAddressId = driver?.address_id || '';

        console.log('[Update] 仓库地址ID:', warehouseAddressId);
        console.log('[Update] 司机信息:', {
            id: driver?.id,
            name: driver?.name,
            address_id: driver?.address_id
        });

        // 根据班次类型设置起点和终点（与 createRoute 逻辑完全一致）
        let startPoint, endPoint;

        if (shiftName.includes('AM')) {
            // 早班: 司机家 -> 仓库
            startPoint = driverAddressId || warehouseAddressId;
            endPoint = warehouseAddressId;
            console.log('[Update] AM班次: 起点=司机家, 终点=仓库');
            if (!driverAddressId) {
                console.warn('[Update] 司机没有地址信息，使用仓库地址作为起点');
            }
        } else if (shiftName.includes('PM')) {
            // 下午班: 仓库 -> 仓库
            startPoint = warehouseAddressId;
            endPoint = warehouseAddressId;
            console.log('[Update] PM班次: 起点=仓库, 终点=仓库');
        } else if (shiftName.includes('NT')) {
            // 晚班: 仓库 -> 司机家
            startPoint = warehouseAddressId;
            endPoint = driverAddressId || warehouseAddressId;
            console.log('[Update] NT班次: 起点=仓库, 终点=司机家');
            if (!driverAddressId) {
                console.warn('[Update] 司机没有地址信息，使用仓库地址作为终点');
            }
        }

        console.log('[Update] 最终设置的起点和终点:', {
            startPoint,
            endPoint,
            shiftName
        });

        // 分离 pickup 和 delivery 订单，保持它们在路线中的实际顺序
        const pickups = []
        const deliveries = []

        routeOrders.forEach((order, index) => {
            const actualStopNo = index + 1  // 在整个路线中的实际位置

            if (order.type === 'PICKUP') {
                pickups.push({
                    id: order.id,
                    stop_no: actualStopNo  // 使用在路线中的实际位置
                })
            } else {
                deliveries.push({
                    id: order.id,
                    stop_no: actualStopNo  // 使用在路线中的实际位置
                })
            }
        })

        console.log(`[Update] 构建路线数据: ${pickups.length} 个 pickups, ${deliveries.length} 个 deliveries`)
        console.log(`[Update] Pickups stop_no 范围:`, pickups.map(p => p.stop_no))
        console.log(`[Update] Deliveries stop_no 范围:`, deliveries.map(d => d.stop_no))

        // 构建 routeData，格式与 createRoute 保持一致
        const routeData = {
            priority: priority,
            shift_id: timeStore.selectedShift?.id,
            date: timeStore.selectedDate,
            name: route.name,
            driver: route.driver || route.driverId,
            start_point: startPoint,
            end_point: endPoint,
            pickups: pickups,
            deliveries: deliveries,
            services: []
        }

        console.log('[Update] 构建的 routeData:', routeData)
        return routeData

    } catch (error) {
        console.error('[Update] 构建 routeData 失败:', error)
        throw error
    }
}

// 保存路线顺序（原来的逐单更新方式）
const saveRouteOrderLegacy = async (route) => {
    if (route.isSavingLegacy) return

    try {
        // 设置保存中状态
        route.isSavingLegacy = true

        // 获取当前顺序的订单ID列表 - 从routeOrdersMap中获取，而不是route.orders
        const routeOrders = routeOrdersMap[route.name] || []
        if (routeOrders.length === 0) {
            ElMessage.warning('该路线没有订单，无法保存顺序')
            return
        }

        console.log(`[Legacy] 保存路线 ${route.name} 的顺序，共 ${routeOrders.length} 个订单`)

        // 准备要提交的数据
        const orderUpdates = routeOrders.map((order, index) => ({
            id: order.id,
            stop_no: index + 1,
            type: order.type || 'PICKUP'
        }))

        console.log('[Legacy] 准备逐单更新订单顺序:', orderUpdates)

        // 调用API逐单保存路线顺序
        const apiPromises = orderUpdates.map(update => {
            console.log(`[Legacy] 更新订单 ${update.id} 的停靠点编号为 ${update.stop_no}`)
            return orderStore.orderService.orderRepository.updateOrderStopNumber(
                update.id,
                update.stop_no,
                update.type
            )
        })

        // 等待所有API调用完成
        await Promise.all(apiPromises)

        // 成功提示
        ElMessage.success(`路线 ${formatRouteNumber(route.name)} 顺序已保存（逐单更新）`)
        console.log(`[Legacy] 路线 ${route.name} 的 ${routeOrders.length} 个订单顺序更新完成`)
    } catch (error) {
        console.error('[Legacy] 保存路线顺序失败:', error)
        ElMessage.error('保存路线顺序失败，请重试')
    } finally {
        // 清除保存中状态
        route.isSavingLegacy = false
    }
}

// TomTom优化路线顺序函数已删除

// 调试 RouteStore 数据
const debugRouteStore = () => {
    console.log('=== RouteStore 调试信息 ===')

    // 基本信息
    console.log('1. 基本信息:')
    console.log('  - routeStore 对象:', routeStore)
    console.log('  - routeStore.routes 数量:', routeStore.routes?.length || 0)
    console.log('  - routeStore.selectedRoute:', routeStore.selectedRoute)
    console.log('  - routeStore.currentRouteNumber:', routeStore.currentRouteNumber)

    // 路线详细信息
    console.log('\n2. 路线详细信息:')
    if (routeStore.routes && routeStore.routes.length > 0) {
        routeStore.routes.forEach((route, index) => {
            console.log(`  路线 ${index + 1}:`)
            console.log('    - id:', route.id)
            console.log('    - name:', route.name)
            console.log('    - routeNumber:', route.routeNumber)
            console.log('    - driver/driverId:', route.driver || route.driverId)
            console.log('    - status:', route.status)
            console.log('    - start_point:', route.start_point)
            console.log('    - end_point:', route.end_point)
            console.log('    - color:', route.color)
            console.log('    - orders 数量:', route.orders?.length || 0)
            if (route.orders && route.orders.length > 0) {
                console.log('    - orders 详细结构:')
                route.orders.forEach((order, orderIndex) => {
                    console.log(`      订单 ${orderIndex + 1}:`, {
                        orderId: order.orderId || order.id,
                        stopNumber: order.stopNumber || order.stop_no,
                        type: order.type,
                        name: order.name,
                        address: order.address,
                        完整对象: order
                    })
                })
            }
            console.log('    - 完整对象:', route)
            console.log('    ---')
        })
    } else {
        console.log('  没有路线数据')
    }

    // RouteService 信息
    console.log('\n3. RouteService 信息:')
    console.log('  - routeService:', routeStore.routeService)
    console.log('  - routeService.routes 数量:', routeStore.routeService?.routes?.length || 0)
    console.log('  - routeService.currentRouteNumber:', routeStore.routeService?.currentRouteNumber)

    // 本地路线订单映射
    console.log('\n4. 本地路线订单映射 (routeOrdersMap):')
    console.log('  - routeOrdersMap 对象:', routeOrdersMap)
    Object.keys(routeOrdersMap).forEach(routeName => {
        console.log(`  - ${routeName}: ${routeOrdersMap[routeName]?.length || 0} 个订单`)
        if (routeOrdersMap[routeName]?.length > 0) {
            console.log(`    订单列表:`, routeOrdersMap[routeName].map(o => ({
                id: o.id,
                name: o.name,
                stop_no: o.stop_no
            })))
        }
    })

    // 时间和班次信息
    console.log('\n5. 时间和班次信息:')
    console.log('  - selectedDate:', timeStore.selectedDate)
    console.log('  - selectedShift:', timeStore.selectedShift)

    // 司机信息
    console.log('\n6. 司机信息:')
    console.log('  - drivers 数量:', driverStore.drivers?.length || 0)
    console.log('  - selectedDriver:', driverStore.selectedDriver)

    console.log('=== RouteStore 调试信息结束 ===')

    // 在浏览器控制台中也显示一个简化版本
    ElMessage.info('RouteStore 调试信息已输出到控制台，请按 F12 查看详细信息')
}

// 检查路线几何数据存储状态
const debugRouteGeometry = () => {
    console.log('=== 路线几何数据检查 ===')

    if (!routeStore.routes || routeStore.routes.length === 0) {
        console.log('没有路线数据')
        ElMessage.info('没有路线数据可检查')
        return
    }

    let totalRoutes = 0
    let routesWithDirectGeometry = 0
    let routesWithRealisticGeometry = 0
    let routesWithBothGeometry = 0

    routeStore.routes.forEach((route, index) => {
        totalRoutes++

        console.log(`\n路线 ${index + 1}: ${route.name || route.id}`)
        console.log('  - routeGeometry 对象:', route.routeGeometry)

        const hasDirectGeometry = route.routeGeometry?.direct &&
                                 Array.isArray(route.routeGeometry.direct) &&
                                 route.routeGeometry.direct.length > 0

        const hasRealisticGeometry = route.routeGeometry?.realistic &&
                                    Array.isArray(route.routeGeometry.realistic) &&
                                    route.routeGeometry.realistic.length > 0

        console.log('  - 直线路线 (direct):', hasDirectGeometry ?
                   `有 (${route.routeGeometry.direct.length} 个点)` : '无')

        console.log('  - 真实路线 (realistic):', hasRealisticGeometry ?
                   `有 (${route.routeGeometry.realistic.length} 个点)` : '无')

        if (hasDirectGeometry) {
            routesWithDirectGeometry++
            console.log('    直线路线示例点:', route.routeGeometry.direct.slice(0, 2))
        }

        if (hasRealisticGeometry) {
            routesWithRealisticGeometry++
            console.log('    真实路线示例点:', route.routeGeometry.realistic.slice(0, 2))
        }

        if (hasDirectGeometry && hasRealisticGeometry) {
            routesWithBothGeometry++
        }

        // 检查路线可见性
        console.log('  - 路线可见性:', route.visible !== false ? '可见' : '隐藏')
        console.log('  - 路线颜色:', route.color || '默认')

        // 检查路线详细信息（只显示真实路线）
        if (route.routeDetails && route.routeDetails.realistic) {
            const realistic = route.routeDetails.realistic
            if (realistic.totalDistance > 0 || realistic.segments.length > 0) {
                console.log('  - 真实路线详细信息:')
                console.log(`    总距离: ${(realistic.totalDistance / 1000).toFixed(2)}km`)
                console.log(`    总时间: ${(realistic.totalDuration / 60).toFixed(2)}min`)
                console.log(`    权重: ${realistic.weight?.toFixed(2) || 'N/A'}`)
                console.log(`    路段数: ${realistic.segments?.length || 0}`)
                console.log(`    UUID: ${realistic.uuid || 'N/A'}`)

                if (realistic.segments && realistic.segments.length > 0) {
                    console.log('    路段详情:', realistic.segments.map((seg, i) =>
                        `段${i+1}: ${(seg.distance/1000).toFixed(2)}km, ${(seg.duration/60).toFixed(2)}min`
                    ).join('; '))
                }
            } else {
                console.log('  - 真实路线详细信息: 无数据')
            }
        } else {
            console.log('  - 真实路线详细信息: 无')
        }
    })

    console.log('\n=== 统计信息 ===')
    console.log(`总路线数: ${totalRoutes}`)
    console.log(`有直线几何数据的路线: ${routesWithDirectGeometry}`)
    console.log(`有真实几何数据的路线: ${routesWithRealisticGeometry}`)
    console.log(`同时有两种几何数据的路线: ${routesWithBothGeometry}`)

    // 检查本地路线订单映射中的几何数据
    console.log('\n=== 本地路线订单映射检查 ===')
    Object.keys(routeOrdersMap).forEach(routeName => {
        const orders = routeOrdersMap[routeName] || []
        console.log(`路线 ${routeName}: ${orders.length} 个订单`)
        if (orders.length > 0) {
            const ordersWithCoords = orders.filter(order =>
                order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2
            )
            console.log(`  - 有坐标的订单: ${ordersWithCoords.length}/${orders.length}`)
        }
    })

    console.log('=== 路线几何数据检查结束 ===')

    // 显示简化的统计信息
    ElMessage.info(`路线几何检查完成：${totalRoutes} 条路线，${routesWithRealisticGeometry} 条有真实路线数据`)
}

// 检查起点和终点设置
const debugEndpoints = () => {
    console.log('=== 起点和终点检查 ===')

    if (!routeStore.routes || routeStore.routes.length === 0) {
        console.log('没有路线数据')
        ElMessage.info('没有路线数据可检查')
        return
    }

    const startPointCounts = {}
    const endPointCounts = {}
    let routesWithStartPoint = 0
    let routesWithEndPoint = 0

    routeStore.routes.forEach((route, index) => {
        console.log(`\n路线 ${index + 1}: ${route.name || route.id}`)
        console.log('  - 司机ID:', route.driver || route.driverId)
        console.log('  - 起点 (start_point):', route.start_point)
        console.log('  - 终点 (end_point):', route.end_point)

        if (route.start_point) {
            routesWithStartPoint++
            startPointCounts[route.start_point] = (startPointCounts[route.start_point] || 0) + 1

            // 获取起点坐标
            const startCoords = addressStore.getCoordinatesByAddressId(route.start_point)
            console.log('    起点坐标:', startCoords)
        }

        if (route.end_point) {
            routesWithEndPoint++
            endPointCounts[route.end_point] = (endPointCounts[route.end_point] || 0) + 1

            // 获取终点坐标
            const endCoords = addressStore.getCoordinatesByAddressId(route.end_point)
            console.log('    终点坐标:', endCoords)
        }

        // 检查是否起点和终点相同
        if (route.start_point && route.end_point && route.start_point === route.end_point) {
            console.warn('    ⚠️ 警告：起点和终点相同！')
        }
    })

    console.log('\n=== 起点统计 ===')
    Object.keys(startPointCounts).forEach(addressId => {
        const count = startPointCounts[addressId]
        const coords = addressStore.getCoordinatesByAddressId(addressId)
        console.log(`起点 ${addressId}: ${count} 条路线使用，坐标: ${coords}`)
        if (count > 1) {
            console.warn(`  ⚠️ 多条路线使用相同起点`)
        }
    })

    console.log('\n=== 终点统计 ===')
    Object.keys(endPointCounts).forEach(addressId => {
        const count = endPointCounts[addressId]
        const coords = addressStore.getCoordinatesByAddressId(addressId)
        console.log(`终点 ${addressId}: ${count} 条路线使用，坐标: ${coords}`)
        if (count > 1) {
            console.warn(`  ⚠️ 多条路线使用相同终点`)
        }
    })

    console.log('\n=== 总结 ===')
    console.log(`总路线数: ${routeStore.routes.length}`)
    console.log(`有起点的路线: ${routesWithStartPoint}`)
    console.log(`有终点的路线: ${routesWithEndPoint}`)
    console.log(`不同起点数量: ${Object.keys(startPointCounts).length}`)
    console.log(`不同终点数量: ${Object.keys(endPointCounts).length}`)

    // 检查仓库地址
    console.log('\n=== 仓库地址检查 ===')
    const warehouseId = 'CA|CP|B|10440098'
    const warehouseCoords = addressStore.getCoordinatesByAddressId(warehouseId)
    console.log(`仓库地址 ${warehouseId}:`, warehouseCoords)

    const routesUsingWarehouseAsStart = Object.keys(startPointCounts).filter(id => id === warehouseId).length
    const routesUsingWarehouseAsEnd = Object.keys(endPointCounts).filter(id => id === warehouseId).length

    console.log(`使用仓库作为起点的路线: ${startPointCounts[warehouseId] || 0}`)
    console.log(`使用仓库作为终点的路线: ${endPointCounts[warehouseId] || 0}`)

    console.log('=== 起点和终点检查结束 ===')

    // 显示简化的统计信息
    ElMessage.info(`起终点检查完成：${routeStore.routes.length} 条路线，${Object.keys(endPointCounts).length} 个不同终点`)
}

// 检查路段和订单对应关系
const debugSegments = () => {
    console.log('=== 路段和订单对应关系检查 ===')

    if (!routeStore.routes || routeStore.routes.length === 0) {
        console.log('没有路线数据')
        ElMessage.info('没有路线数据可检查')
        return
    }

    routeStore.routes.forEach((route, routeIndex) => {
        console.log(`\n路线 ${routeIndex + 1}: ${route.name || route.id}`)

        // 获取路线详细信息
        const routeDetails = route.getRouteDetails ? route.getRouteDetails() : null
        if (!routeDetails || !routeDetails.segments) {
            console.log('  ❌ 没有路段数据')
            return
        }

        const segments = routeDetails.segments
        console.log(`  📊 路段数量: ${segments.length}`)

        // 获取订单
        const orders = routeOrdersMap[route.name] || []
        console.log(`  📦 订单数量: ${orders.length}`)

        if (orders.length === 0) {
            console.log('  ❌ 没有订单数据')
            return
        }

        // 分析唯一地址
        const uniqueAddresses = []
        const orderToAddressIndex = new Map()

        orders.forEach((order, index) => {
            const addressKey = `${order.lng_lat[0]},${order.lng_lat[1]}`

            let addressIndex = uniqueAddresses.findIndex(addr =>
                `${addr.lng_lat[0]},${addr.lng_lat[1]}` === addressKey
            )

            if (addressIndex === -1) {
                addressIndex = uniqueAddresses.length
                uniqueAddresses.push({
                    lng_lat: order.lng_lat,
                    address_id: order.address_id,
                    firstOrderIndex: index
                })
            }

            orderToAddressIndex.set(index, addressIndex)
        })

        console.log(`  🏠 唯一地址数量: ${uniqueAddresses.length}`)

        // 显示路段详情
        console.log('  📍 路段详情:')
        segments.forEach((segment, segIndex) => {
            const distance = (segment.distance / 1000).toFixed(2)
            const duration = (segment.duration / 60).toFixed(1)
            console.log(`    路段 ${segIndex}: ${distance}km, ${duration}min`)
        })

        // 分析路段的实际含义
        console.log('  🔍 路段含义分析:')
        console.log('    假设1: 路段0=起点→地址0, 路段1=地址0→地址1, ...')
        for (let i = 0; i < Math.min(segments.length, uniqueAddresses.length); i++) {
            const segment = segments[i]
            const distance = (segment.distance / 1000).toFixed(2)
            const duration = (segment.duration / 60).toFixed(1)
            if (i === 0) {
                console.log(`      路段 ${i}: 起点 → 地址${i} (${distance}km, ${duration}min)`)
            } else {
                console.log(`      路段 ${i}: 地址${i-1} → 地址${i} (${distance}km, ${duration}min)`)
            }
        }

        console.log('    假设2: 路段0=地址0→地址1, 路段1=地址1→地址2, ...')
        for (let i = 0; i < Math.min(segments.length, uniqueAddresses.length - 1); i++) {
            const segment = segments[i]
            const distance = (segment.distance / 1000).toFixed(2)
            const duration = (segment.duration / 60).toFixed(1)
            console.log(`      路段 ${i}: 地址${i} → 地址${i+1} (${distance}km, ${duration}min)`)
        }
        if (segments.length === uniqueAddresses.length) {
            const lastSegment = segments[segments.length - 1]
            const distance = (lastSegment.distance / 1000).toFixed(2)
            const duration = (lastSegment.duration / 60).toFixed(1)
            console.log(`      路段 ${segments.length - 1}: 地址${uniqueAddresses.length - 1} → 终点 (${distance}km, ${duration}min)`)
        }

        // 显示当前的订单-路段对应关系
        console.log('  🔗 当前订单-路段对应关系:')
        orders.forEach((order, orderIndex) => {
            const addressIndex = orderToAddressIndex.get(orderIndex)
            const isFirstAtAddress = uniqueAddresses[addressIndex].firstOrderIndex === orderIndex

            if (isFirstAtAddress) {
                const segmentIndex = addressIndex
                if (segmentIndex < segments.length) {
                    const segment = segments[segmentIndex]
                    const distance = (segment.distance / 1000).toFixed(2)
                    const duration = (segment.duration / 60).toFixed(1)
                    console.log(`    订单 ${orderIndex + 1} (${order.name}): 路段 ${segmentIndex} → ${distance}km, ${duration}min`)
                } else {
                    console.log(`    订单 ${orderIndex + 1} (${order.name}): ❌ 路段索引 ${segmentIndex} 超出范围`)
                }
            } else {
                console.log(`    订单 ${orderIndex + 1} (${order.name}): 同地址 → 0km, 0min`)
            }
        })

        // 检查是否有未使用的路段
        if (segments.length > uniqueAddresses.length) {
            console.log(`  ⚠️ 有 ${segments.length - uniqueAddresses.length} 个路段未被使用（可能是到终点的路段）`)
        }
    })

    console.log('=== 路段检查结束 ===')
    ElMessage.info('路段检查完成，请查看控制台详情')
}

// 打开切换司机弹窗
const openSwitchDriverDialog = async (route) => {
    // 保存当前路线
    currentRoute.value = route;

    // 重置选中的司机ID
    selectedDriverId.value = route.driver || null;

    // 清空搜索框
    driverSearchQuery.value = '';

    // 重置司机显示模式为"在线"
    showAllDriversInDialog.value = false;

    // 获取司机数据
    await fetchDriversForDialog();

    // 显示弹窗
    switchDriverDialogVisible.value = true;
};

// 根据开关状态获取司机数据
const fetchDriversForDialog = async () => {
    try {
        let response;

        if (showAllDriversInDialog.value) {
            // 获取所有司机（不添加参数）
            console.log('获取所有司机（不带参数）');
            response = await driverAPI.getDrivers();
        } else {
            // 获取在线司机（带日期和班次参数）
            console.log('获取在线司机（带参数）');
            response = await driverAPI.getDrivers({
                date: timeStore.selectedDate,
                shift_id: timeStore.selectedShift?.id
            });
        }

        if (response && response.driver_list) {
            allDrivers.value = response.driver_list;
            console.log(`获取到 ${allDrivers.value.length} 个司机`);
        } else {
            console.error('获取司机列表失败，返回格式不正确:', response);
            allDrivers.value = [];
        }
    } catch (error) {
        console.error('获取司机列表失败:', error);
        allDrivers.value = [];
        ElMessage.error('获取司机列表失败');
    }
};

// 处理司机过滤模式变化
const handleDriverFilterChange = async () => {
    console.log(`司机显示模式切换为: ${showAllDriversInDialog.value ? '全部' : '在线'}`);
    await fetchDriversForDialog();
};

// 处理切换司机
const handleSwitchDriver = async () => {
    if (!currentRoute.value) {
        ElMessage.warning('未选择路线');
        return;
    }

    if (!selectedDriverId.value) {
        ElMessage.warning('请选择一个司机');
        return;
    }

    // 设置保存中状态
    isSavingDriver.value = true;
    currentRoute.value.isSwitchingDriver = true;

    try {
        // 获取当前路线信息
        const routeId = currentRoute.value.id;
        const routeName = currentRoute.value.name;
        const oldDriverId = currentRoute.value.driver;

        console.log(`切换路线 ${routeName} (ID: ${routeId}) 的司机，从 ${oldDriverId} 到 ${selectedDriverId.value}`);

        // 调用 API 更新路线
        await routeAPI.updateRoute(routeId, {
            driver_id: selectedDriverId.value,
            name: routeName // 保持路线名称不变
        });

        // 更新本地路线对象
        currentRoute.value.driver = selectedDriverId.value;

        // 提示成功
        ElMessage.success('司机切换成功');

        // 关闭弹窗
        switchDriverDialogVisible.value = false;

        // 刷新路线数据
        await routeStore.fetchRoutes({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift
        });

        // 更新所有路线订单映射
        updateAllRouteOrderMaps();
    } catch (error) {
        console.error('切换司机失败:', error);
        ElMessage.error('切换司机失败');
    } finally {
        // 清除保存中状态
        isSavingDriver.value = false;
        if (currentRoute.value) {
            currentRoute.value.isSwitchingDriver = false;
        }
    }
};

// +++ 添加 HERE 优化函数 +++
const optimizeRouteWithHERE = async (route) => {
    if (route.isHereOptimizing) return; // 防止重复点击

    route.isHereOptimizing = true; // 设置加载状态
    ElMessage.info('正在使用 HERE API 计算最佳路线顺序...');

    try {
        const apiKey = 'e6h7jZODoCtiZWKNeei-vVeoSt4C436p8HvaOD8nSuU'; // <-- 在这里替换你的 HERE API Key
        if (apiKey === 'YOUR_HERE_API_KEY') {
            console.warn("请在 DriverRoutePanel.vue 中设置您的 HERE API Key");
            ElMessage.warning("未配置 HERE API Key，无法进行优化。");
            route.isHereOptimizing = false;
            return;
        }

        const routeOrders = getSortedRouteOrders(route.name);
        if (routeOrders.length === 0) {
            ElMessage.warning('路线没有订单，无法优化');
            route.isHereOptimizing = false;
            return;
        }

        console.log(`[HERE Optimize] 路线 ${route.name} 包含 ${routeOrders.length} 个订单`);

        // 筛选有坐标的订单
        const ordersWithCoords = routeOrders.filter(order =>
            (order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2) ||
            (order.location && order.location.latitude && order.location.longitude)
        );

        if (ordersWithCoords.length === 0) {
            ElMessage.warning('订单没有有效坐标信息，无法优化路线');
            route.isHereOptimizing = false;
            return;
        }
        console.log(`[HERE Optimize] 有效坐标订单数量: ${ordersWithCoords.length}`);

        // 获取路线的起点和终点
        let startPoint = "43.818770,-79.345225"; // 默认仓库坐标，格式: lat,lon
        let endPoint = "43.818770,-79.345225";   // 默认仓库坐标，格式: lat,lon

        // 获取完整的路线对象，以便获取起点和终点信息
        const fullRoute = await routeStore.routeService.routeRepository.getRouteById(route.id);
        console.log(`[HERE Optimize] 获取到完整路线信息:`, fullRoute);

        // 根据路线的起点和终点获取坐标
        if (fullRoute) {
            // 导入地址存储
            const addressStore = useAddressStore();

            // 确保地址存储已初始化
            addressStore.initialize();

            // 获取司机ID，以便检查地址
            const driverId = fullRoute.driver || fullRoute.driverId;

            // 如果是晚班（NT），确保司机地址已加载
            const shiftName = timeStore.selectedShift?.name || '';
            if (shiftName.includes('NT') && driverId) {
                // 获取司机对象
                const driver = driverStore.getDriverById(driverId);
                if (driver) {
                    console.log(`[HERE Optimize] 晚班路线，司机信息:`, {
                        id: driver.id,
                        name: driver.name,
                        address_id: driver.address_id,
                        address_lng_lat: driver.address_lng_lat
                    });

                    // 检查司机地址是否已加载到地址存储
                    if (driver.address_id && !addressStore.driverAddresses.has(driver.address_id)) {
                        console.log(`[HERE Optimize] 司机地址未加载到地址存储，手动添加`);

                        // 手动添加司机地址到地址存储
                        if (driver.address_lng_lat) {
                            const driverAddress = {
                                id: driver.address_id,
                                driver_id: driver.id,
                                driver_name: driver.name,
                                driver_color: driver.color,
                                coordinates: driver.address_lng_lat, // 已经是 [lat, lng] 格式
                                original_coordinates: driver.original_address_lng_lat, // 原始 [lng, lat] 格式
                                address_label: driver.address_label || null,
                                type: 'driver_home'
                            };

                            // 添加到司机地址集合
                            addressStore.driverAddresses.set(driver.address_id, driverAddress);

                            // 添加到地址缓存
                            addressStore.addressCache.set(driver.address_id, driverAddress);

                            console.log(`[HERE Optimize] 已手动添加司机地址到地址存储:`, driverAddress);
                        }
                    }
                }
            }

            // 打印路线的起点和终点信息
            console.log(`[HERE Optimize] 路线起点和终点:`, {
                start_point: fullRoute.start_point,
                end_point: fullRoute.end_point,
                shift: shiftName
            });

            // 根据班次类型设置默认起点和终点
            let defaultStartPoint = "43.818770,-79.345225"; // 默认仓库坐标
            let defaultEndPoint = "43.818770,-79.345225";   // 默认仓库坐标

            // 如果有司机ID，尝试获取司机家庭地址坐标
            if (driverId) {
                const driver = driverStore.getDriverById(driverId);
                if (driver && driver.address_lng_lat) {
                    const driverHomeCoords = driver.address_lng_lat;
                    const driverHomePoint = `${driverHomeCoords[0]},${driverHomeCoords[1]}`;

                    // 根据班次类型设置默认起点和终点
                    if (shiftName.includes('AM')) {
                        // 早班: 司机家 -> 仓库
                        defaultStartPoint = driverHomePoint;
                        console.log(`[HERE Optimize] AM班次默认起点(司机家):`, driverHomePoint);
                    } else if (shiftName.includes('NT')) {
                        // 晚班: 仓库 -> 司机家
                        defaultEndPoint = driverHomePoint;
                        console.log(`[HERE Optimize] NT班次默认终点(司机家):`, driverHomePoint);
                    }
                }
            }

            // 处理起点
            if (fullRoute.start_point) {
                try {
                    // 尝试获取起点坐标
                    const startCoords = addressStore.getCoordinatesByAddressId(fullRoute.start_point);
                    if (startCoords) {
                        console.log(`[HERE Optimize] 路线起点坐标:`, startCoords);
                        // 转换为 "lat,lon" 格式
                        startPoint = `${startCoords[0]},${startCoords[1]}`;
                    } else {
                        console.warn(`[HERE Optimize] 无法获取路线起点坐标，使用默认值`);

                        // 使用班次类型对应的默认起点
                        startPoint = defaultStartPoint;
                        console.log(`[HERE Optimize] 使用班次默认起点:`, startPoint);

                        // 如果起点是对象并且有 Position 属性，直接使用其中的坐标
                        if (typeof fullRoute.start_point === 'object' && fullRoute.start_point.Position) {
                            const pos = fullRoute.start_point.Position;
                            if (pos.Latitude && pos.Longitude) {
                                startPoint = `${pos.Latitude},${pos.Longitude}`;
                                console.log(`[HERE Optimize] 从对象中提取起点坐标:`, startPoint);
                            }
                        }
                    }
                } catch (error) {
                    console.error(`[HERE Optimize] 处理起点时出错:`, error);
                    startPoint = defaultStartPoint;
                }
            } else {
                console.log(`[HERE Optimize] 路线没有起点信息，使用默认起点`);
                startPoint = defaultStartPoint;
            }

            // 处理终点
            if (fullRoute.end_point) {
                try {
                    // 尝试获取终点坐标
                    const endCoords = addressStore.getCoordinatesByAddressId(fullRoute.end_point);
                    if (endCoords) {
                        console.log(`[HERE Optimize] 路线终点坐标:`, endCoords);
                        // 转换为 "lat,lon" 格式
                        endPoint = `${endCoords[0]},${endCoords[1]}`;
                    } else {
                        console.warn(`[HERE Optimize] 无法获取路线终点坐标，使用默认值`);

                        // 使用班次类型对应的默认终点
                        endPoint = defaultEndPoint;
                        console.log(`[HERE Optimize] 使用班次默认终点:`, endPoint);

                        // 如果终点是对象并且有 Position 属性，直接使用其中的坐标
                        if (typeof fullRoute.end_point === 'object' && fullRoute.end_point.Position) {
                            const pos = fullRoute.end_point.Position;
                            if (pos.Latitude && pos.Longitude) {
                                endPoint = `${pos.Latitude},${pos.Longitude}`;
                                console.log(`[HERE Optimize] 从对象中提取终点坐标:`, endPoint);
                            }
                        }
                    }
                } catch (error) {
                    console.error(`[HERE Optimize] 处理终点时出错:`, error);
                    endPoint = defaultEndPoint;
                }
            } else {
                console.log(`[HERE Optimize] 路线没有终点信息，使用默认终点`);
                endPoint = defaultEndPoint;
            }

            // 打印最终使用的起点和终点坐标
            console.log(`[HERE Optimize] 最终使用的坐标:`, {
                startPoint,
                endPoint,
                shift: shiftName
            });
        } else {
            console.warn(`[HERE Optimize] 无法获取完整路线信息，使用默认起点和终点坐标`);
        }

        // 构建 HERE API 请求 URL
        let apiUrl = `https://wse.ls.hereapi.com/2/findsequence.json?apiKey=${apiKey}`;
        apiUrl += `&start=start;${startPoint}`;
        apiUrl += `&end=end;${endPoint}`;


        // 添加目的地 (订单)
        const orderIdToWaypointId = new Map(); // 用于将 HERE 返回的 ID 映射回订单
        ordersWithCoords.forEach((order, index) => {
            let lat, lon;
            if (order.lng_lat) { [lat, lon] = order.lng_lat; }
            else { lat = order.location.latitude; lon = order.location.longitude; }
            const waypointId = `destination${index + 1}`; // e.g., destination1, destination2
            apiUrl += `&${waypointId}=${order.id};${lat},${lon}`; // 使用 order.id 作为 HERE waypoint ID
            orderIdToWaypointId.set(order.id.toString(), order); // 存储映射关系 (确保 ID 是字符串)
        });

        apiUrl += `&mode=fastest;car;tollroad:-2`; // <-- 使用 tollroad:-2 强烈避免收费公路
        apiUrl += '&clustering=drivingDistance' ;
        console.log(`[HERE Optimize] 请求 URL (已隐藏 API Key): ${apiUrl.replace(apiKey, 'HIDDEN_API_KEY')}`);

        // 发送请求
        const response = await fetch(apiUrl);
        console.log(`[HERE Optimize] 收到响应，状态码: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[HERE Optimize] API 请求失败: ${response.status} ${response.statusText}`, errorText);
             try {
                const errorJson = JSON.parse(errorText);
                ElMessage.error(`HERE API 错误: ${errorJson.title || response.statusText}`);
             } catch {
                 ElMessage.error(`HERE API 请求失败: ${response.status}`);
             }
            route.isHereOptimizing = false;
            return;
        }

        const data = await response.json();
        console.log('[HERE Optimize] API 响应数据:', JSON.stringify(data, null, 2));

        // 检查响应数据结构
        if (!data || !data.results || data.results.length === 0 || !data.results[0].waypoints || !data.results[0].interconnections) {
            console.error('[HERE Optimize] HERE API 响应格式无效:', data);
            ElMessage.error('HERE API 返回无效数据');
            route.isHereOptimizing = false;
            return;
        }

        // 提取优化后的路径点顺序
        const optimizedWaypoints = data.results[0].waypoints;
        console.log('[HERE Optimize] 优化后的路径点:', optimizedWaypoints);

        const sortedOrders = [];
        // 遍历优化后的路径点 (排除起点和终点)
        optimizedWaypoints.forEach(waypoint => {
            // 查找原始订单
            const originalOrder = orderIdToWaypointId.get(waypoint.id);
            if (originalOrder) {
                sortedOrders.push(originalOrder);
                console.log(`[HERE Optimize] 添加优化订单: ID=${originalOrder.id}, 原 Waypoint ID=${waypoint.id}`);
            } else if (waypoint.id !== 'start' && waypoint.id !== 'end') {
                console.warn(`[HERE Optimize] 未能通过 Waypoint ID "${waypoint.id}" 找到对应的原始订单`);
            }
        });

        console.log(`[HERE Optimize] 按优化顺序排序后的订单: ${sortedOrders.length} 个`);
        console.log('优化后顺序:', sortedOrders.map((o, idx) => `[${idx+1}] ${o.id} (${o.name || '未知'})`));

        // 确保所有原始订单都包含在结果中 (包括无坐标的)
        const includedOrderIds = new Set(sortedOrders.map(order => order.id));
        const missingOrders = routeOrders.filter(order => !includedOrderIds.has(order.id));
        if (missingOrders.length > 0) {
            console.log(`[HERE Optimize] 添加 ${missingOrders.length} 个因无坐标或其他原因未参与优化的订单到末尾:`, missingOrders.map(o => o.id));
            sortedOrders.push(...missingOrders);
        }

        // 更新UI和状态
        const updatedOrders = sortedOrders.map((order, index) => ({
            ...order,
            stop_no: index + 1
        }));

        // 更新本地路线订单映射
        routeOrdersMap[route.name] = updatedOrders;

        // 使用OrderStore的updateLocalOrderSequence方法更新订单顺序
        orderStore.updateLocalOrderSequence(route.id, updatedOrders);

        // 发送地图刷新事件，并明确指示需要重新绘制路线
        eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
            routeId: route.id,
            orders: updatedOrders,
            needRedrawRoute: true, // 添加标志，指示需要重新绘制路线
            timestamp: Date.now()
        });
        console.log(`[HERE Optimize] 路线 ${route.name} 订单顺序优化完成，已请求重新绘制路线`);
        ElMessage.success('HERE路线优化成功，路线已重新绘制，请点击"保存路线顺序"以保存');

    } catch (error) {
        console.error('[HERE Optimize] 优化路线失败:', error);
        ElMessage.error(`HERE路线优化失败: ${error.message || '未知错误'}`);
    } finally {
        route.isHereOptimizing = false; // 结束加载状态
    }
};

// 计算所有司机的路线
const driverRoutes = computed(() => {
    return routeStore.routes || []
})

// 处理订单更新事件
const handleOrdersUpdated = data => {
    console.log('司机路线面板收到订单更新事件:', data)
    console.log('事件数据详情:', JSON.stringify({
        routeId: data.routeId,
        routeName: data.routeName,
        isDragging: data.isDragging,
        isMapSave: data.isMapSave,
        ordersCount: data.orders ? data.orders.length : 0,
        timestamp: data.timestamp
    }))

    // 处理来自地图的拖拽更新、保存操作或HERE优化
    if (data.isDragging || data.isMapSave || data.isMapDrag || data.isHereOptimize) {
        console.log('处理来自地图的更新:',
            data.isDragging ? '拖拽中' :
            data.isMapSave ? '最终保存' :
            data.isMapDrag ? '地图拖拽' :
            data.isHereOptimize ? 'HERE优化' : '未知操作')

        // 获取受影响的路线
        const routeId = data.routeId
        if (routeId && data.orders) {
            const route = routeStore.routes.find(r => r.id === routeId)
            if (route) {
                console.log(`更新路线 ${route.name} 的订单数据，来自地图${data.isDragging ? '拖拽' : '保存'}`)
                console.log('订单数量:', data.orders.length)
                console.log('订单ID列表:', data.orders.map(o => o.id))

                // 直接更新路线订单映射
                routeOrdersMap[route.name] = [...data.orders]

                // 强制更新视图
                nextTick(() => {
                    console.log(`路线 ${route.name} 数据已更新，强制刷新视图`)

                    // 如果当前选中的路线就是变更的路线，需更新UI
                    if (selectedRoute.value && selectedRoute.value.id === route.id) {
                        console.log('当前选中的路线内容已变更，需要更新UI')

                        // 通知路由组件（如果有）更新
                        eventBus.emit(EVENT_TYPES.ROUTE_UPDATED, {
                            route: route,
                            orders: data.orders
                        })
                    }

                    // 如果路线已展开，确保显示最新数据
                    if (route.isExpanded) {
                        console.log(`路线 ${route.name} 已展开，刷新显示`)
                        // 强制重新渲染
                        const temp = routeOrdersMap[route.name]
                        routeOrdersMap[route.name] = []
                        setTimeout(() => {
                            routeOrdersMap[route.name] = temp
                        }, 0)
                    }
                })
            } else {
                console.warn(`找不到路线ID为 ${routeId} 的路线`)
            }
        } else {
            console.warn('缺少路线ID或订单数据')
        }

        return // 处理完拖拽更新后直接返回
    }

    // 如果是分配或取消分配操作，需要刷新所有路线数据
    if (data.isAssign || data.isUnassign) {
        // 刷新订单路线映射
        updateAllRouteOrderMaps()

        // 刷新统计信息
        calculateStats()

        // 处理分配订单的情况
        if (data.isAssign) {
            console.log('处理订单分配事件')

            // 获取受影响的路线
            const routeId = data.routeId
            if (routeId) {
                const route = routeStore.routes.find(r => r.id === routeId)
                if (route) {
                    console.log(`刷新路线 ${route.name} 的数据，订单已被分配`)

                    // 强制清除缓存
                    delete routeOrdersMap[route.name]

                    // 重新获取路线订单
                    const updatedRouteOrders = orderStore.getOrdersByRouteNumber(route.id)
                    console.log(`路线 ${route.name} 更新后的订单数量:`, updatedRouteOrders?.length || 0)

                    // 更新路线订单映射
                    routeOrdersMap[route.name] = updatedRouteOrders

                    // 如果当前选中的路线就是变更的路线，需更新UI
                    if (selectedRoute.value && selectedRoute.value.id === route.id) {
                        console.log('当前选中的路线内容已变更，需要更新UI')

                        // 通知路由组件（如果有）更新
                        if (typeof window !== 'undefined' && window.eventBus) {
                            window.eventBus.emit(EVENT_TYPES.ROUTE_UPDATED, {
                                route: route,
                                orders: updatedRouteOrders
                            })
                        }

                        // 更新选中的订单
                        if (updatedRouteOrders.length > 0) {
                            orderStore.clearSelection()
                            orderStore.selectOrders(updatedRouteOrders.map(o => o.id))
                        }
                    }

                    // 如果路线已展开，确保显示最新数据
                    if (route.isExpanded) {
                        nextTick(() => {
                            console.log(`路线 ${route.name} 已展开，刷新显示`)
                        })
                    }
                }
            }

            // 如果当前没有选中路线，但有选中司机，检查是否需要更新司机视图
            if (!selectedRoute.value && selectedDriver.value && data.orders) {
                // 检查分配的订单是否属于当前选中的司机
                const driverId = selectedDriver.value.id
                const assignedToCurrentDriver = data.orders.some(order => order.driver_id === driverId)

                if (assignedToCurrentDriver) {
                    console.log(`发现订单分配给当前选中司机 ${driverId}，更新司机视图`)

                    // 如果司机有路线，确保路线数据是最新的
                    const driverRoutes = getDriverRoutes(driverId)
                    driverRoutes.forEach(route => {
                        // 强制刷新路线订单数据
                        delete routeOrdersMap[route.name]
                        routeOrdersMap[route.name] = getSortedRouteOrders(route.name)

                        // 如果路线已展开，确保显示最新数据
                        if (route.isExpanded) {
                            nextTick(() => {
                                console.log(`司机路线 ${route.name} 已展开，刷新显示`)
                            })
                        }
                    })
                }
            }
        }

        // 处理取消分配的情况
        if (data.isUnassign) {
            // 获取受影响的路线ID列表
            const affectedRouteIds = data.previousRouteIds || []
            if (data.previousRouteId && !affectedRouteIds.includes(data.previousRouteId)) {
                affectedRouteIds.push(data.previousRouteId)
            }

            console.log('取消分配影响的路线:', affectedRouteIds)

            // 为每个受影响的路线更新数据
            affectedRouteIds.forEach(routeId => {
                if (routeId) {
                    const route = routeStore.routes.find(r => r.id === routeId)
                    if (route) {
                        console.log(`刷新路线 ${route.name} 的数据，订单已被取消分配`)
                        // 重新获取该路线的订单
                        const updatedRouteOrders = orderStore.getOrdersByRouteNumber(route.id)
                        routeOrdersMap[route.name] = updatedRouteOrders

                        // 如果当前选中的是这个路线，需要更新UI显示
                        if (selectedRoute.value && selectedRoute.value.id === route.id) {
                            console.log('当前选中的路线内容已变更，需要更新UI')
                            // 通知路由组件（如果有）更新
                            if (typeof window !== 'undefined' && window.eventBus) {
                                window.eventBus.emit(EVENT_TYPES.ROUTE_UPDATED, {
                                    route: route,
                                    orders: updatedRouteOrders
                                })
                            }
                        }
                    }
                }
            })
        } else if (data.routeId) {
            // 如果是更新特定路线的订单，更新该路线数据
            const route = routeStore.routes.find(r => r.id === data.routeId)
            if (route) {
                console.log(`刷新路线 ${route.name} 的订单数据`)
                routeOrdersMap[route.name] = data.orders
            }
        }

        // 延迟执行，确保数据已更新
        setTimeout(() => {
            // 更新展开的路线中的订单显示
            routeStore.routes.forEach(route => {
                if (route.isExpanded) {
                    // 刷新已展开路线的订单数据
                    getSortedRouteOrders(route.name)
                }
            })

            // 触发视图更新
            nextTick(() => {
                console.log('司机路线面板已刷新')
            })
        }, 100)
    } else if (data.routeId) {
        // 如果是更新特定路线的订单（如停靠点序号变更），只需刷新该路线
        const route = routeStore.routes.find(r => r.id === data.routeId)
        if (route) {
            console.log(`刷新路线 ${route.name} 的订单数据`)
            routeOrdersMap[route.name] = data.orders
        }
    }
}

// 处理订单取消分配事件 - 这是专门处理取消分配的事件处理器
const handleOrdersUnassigned = data => {
    console.log('司机路线面板收到订单取消分配事件:', data)

    if (!data || !data.orders || !Array.isArray(data.orders)) return

    // 立即刷新统计信息
    calculateStats()

    // 刷新所有受影响的路线数据
    if (data.affectedRouteIds && Array.isArray(data.affectedRouteIds)) {
        // 为每个受影响的路线更新数据
        data.affectedRouteIds.forEach(routeId => {
            if (routeId) {
                const route = routeStore.routes.find(r => r.id === routeId)
                if (route) {
                    console.log(`[取消分配事件] 刷新路线 ${route.name} 的数据`)
                    // 重新获取该路线的订单
                    const updatedRouteOrders = orderStore.getOrdersByRouteNumber(route.id)
                    routeOrdersMap[route.name] = updatedRouteOrders
                }
            }
        })

        // 如果当前选中的路线受到影响，进行特殊处理
        if (selectedRoute.value && data.affectedRouteIds.includes(selectedRoute.value.id)) {
            const route = selectedRoute.value
            console.log(`[取消分配事件] 当前选中的路线 ${route.name} 受到影响，更新UI`)

            // 更新路线订单数据
            const updatedRouteOrders = orderStore.getOrdersByRouteNumber(route.id)
            routeOrdersMap[route.name] = updatedRouteOrders

            // 通知路由组件更新
            if (typeof window !== 'undefined' && window.eventBus) {
                window.eventBus.emit(EVENT_TYPES.ROUTE_UPDATED, {
                    route: route,
                    orders: updatedRouteOrders
                })
            }

            // 更新订单选择状态
            if (updatedRouteOrders.length > 0) {
                orderStore.clearSelection()
                orderStore.selectOrders(updatedRouteOrders.map(o => o.id))
            }
        }
    }

    // 强制UI更新
    nextTick(() => {
        console.log('司机路线面板已刷新 (取消分配事件)')
    })
}

// 统计信息计算
// 已分配订单统计保持不变
const scheduledCount = computed(() => orderStore.assignedOrders.length)
// 修改未分配订单统计，排除status为closed和exceptional的订单
const unscheduledCount = computed(() => orderStore.orders.filter(order =>
    order.status !== 'closed' && order.status !== 'exceptional'
).length)
// 总计 = 已分配订单 + 未分配非closed和非exceptional订单
const totalCount = computed(() => scheduledCount.value + unscheduledCount.value)
const routeCount = computed(() => routeStore.routes.length)

// 获取司机的路线
const getDriverRoutes = driverId => {
    return routeStore.routes.filter(route => route.driver === driverId)
}

// 获取路线对应的订单数量
const getRouteOrderCount = routeNumber => {
    // 如果路线订单映射中已有数据，直接使用
    if (routeOrdersMap[routeNumber] !== undefined) {
        return routeOrdersMap[routeNumber].length
    }

    // 获取路线对象
    const route = routeStore.routes.find(r => r.name === routeNumber)
    if (!route) {
        console.warn(`找不到路线: ${routeNumber}`)
        return 0
    }

    console.log('路线数据:', route)

    // 使用路线ID (route.id) 而不是路线名称来获取订单
    let routeOrders = orderStore.allOrders.filter(order => order.route_id === route.id)
    console.log(`通过route.id (${route.id}) 获取订单:`, routeOrders?.length || 0)

    // 如果通过route_id找不到订单，尝试通过路线名称
    if (!routeOrders || routeOrders.length === 0) {
        routeOrders = orderStore.getOrdersByRouteNumber(routeNumber)
        console.log(`通过路线名称 (${routeNumber}) 获取订单:`, routeOrders?.length || 0)
    }

    // 如果仍然找不到，尝试通过司机ID
    if (!routeOrders || routeOrders.length === 0) {
        const driverId = route.driver
        if (driverId) {
            routeOrders = orderStore.getDriverOrders(driverId)
            console.log(`通过司机ID (${driverId}) 获取订单:`, routeOrders?.length || 0)
        }
    }

    // 更新到映射中以保持一致性
    if (!routeOrdersMap[routeNumber]) {
        routeOrdersMap[routeNumber] = routeOrders || []
    }

    return routeOrders?.length || 0
}

// 获取排序后的路线订单（按停靠点序号排序）
const getSortedRouteOrders = routeNumber => {
    // 获取路线对象
    const route = routeStore.routes.find(r => r.name === routeNumber)
    if (!route) {
        console.warn(`找不到路线: ${routeNumber}`)
        return []
    }

    // 首先尝试通过route_id获取订单
    let routeOrders = orderStore.allOrders.filter(order => order.route_id === route.id)
    console.log(`通过route_id获取订单 (路线ID: ${route.id}):`, routeOrders?.length || 0)
    // console.log('routeOrders:', routeOrders) // 减少冗余日志

    // 如果通过route_id找不到订单，尝试通过路线名称
    if (!routeOrders || routeOrders.length === 0) {
        routeOrders = orderStore.getOrdersByRouteNumber(routeNumber)
        console.log(`通过路线编号获取订单 (${routeNumber}):`, routeOrders?.length || 0)
        // console.log('routeOrders:', routeOrders) // 减少冗余日志
    }

    // 如果仍然找不到，尝试通过司机ID
    if (!routeOrders || routeOrders.length === 0) {
        const driverId = route.driver
        if (driverId) {
            routeOrders = orderStore.getDriverOrders(driverId)
            console.log(`通过司机ID获取订单 (${driverId}):`, routeOrders?.length || 0)
            // console.log('routeOrders:', routeOrders) // 减少冗余日志
        }
    }

    if (!routeOrders || routeOrders.length === 0) {
        console.log(`路线 ${routeNumber} 没有找到任何订单`)
        // 确保路线订单映射中此路线对应为空数组
        routeOrdersMap[routeNumber] = []
        return []
    }

    // 按停靠点序号排序
    const sortedOrders = [...routeOrders].sort((a, b) => {
        // 如果两个订单都没有停靠点序号，保持原有顺序
        if (!a.stop_no && !b.stop_no) return 0
        // 如果只有一个订单有停靠点序号，将其排在前面
        if (!a.stop_no) return 1
        if (!b.stop_no) return -1
        // 按停靠点序号排序
        return a.stop_no - b.stop_no
    })

    // 将排序后的订单存储到路线订单映射中
    console.log(`路线 ${routeNumber} 排序后的订单:`, sortedOrders.length)

    // 重要：始终更新映射，即使之前已经存在
    routeOrdersMap[routeNumber] = sortedOrders

    return routeOrdersMap[routeNumber]
}

// 截断文本
const truncateText = (text, length) => {
    if (!text) return ''
    return text.length > length ? text.substring(0, length) + '...' : text
}

// 格式化路线编号
const formatRouteNumber = num => {
    return String(num || '').padStart(5, '0')
}

// 选择司机
const selectDriver = driver => {
    // 切换多选框状态
    driver.isSelected = !driver.isSelected

    // 调用多选框处理函数
    handleDriverSelect(driver, driver.isSelected)
}

// 选择路线
const selectRoute = route => {
    if (selectedRoute.value?.id === route.id) {
        routeStore.clearSelectedRoute()

        // 显示所有选中司机的订单
        updateSelectedDriverOrders()
    } else {
        routeStore.setSelectedRoute(route)

        // 选择路线时，获取并显示路线订单
        let routeOrders = []

        // 首先尝试通过route_id获取订单
        routeOrders = orderStore.allOrders.filter(order => order.route_id === route.id)
        console.log(`选择路线: 通过route_id获取订单 (路线ID: ${route.id}):`, routeOrders?.length || 0)

        // 如果通过route_id找不到订单，尝试通过路线名称
        if (!routeOrders || routeOrders.length === 0) {
            routeOrders = orderStore.getOrdersByRouteNumber(route.name)
            console.log(`选择路线: 通过路线名称获取订单 (${route.name}):`, routeOrders?.length || 0)
        }

        // 如果仍然找不到，尝试通过司机ID
        if (!routeOrders || routeOrders.length === 0 && route.driver) {
            routeOrders = orderStore.getDriverOrders(route.driver)
            console.log(`选择路线: 通过司机ID获取订单 (${route.driver}):`, routeOrders?.length || 0)
        }

        // 清除当前选择，然后选中此路线的订单
        orderStore.clearSelection()
        if (routeOrders.length > 0) {
            orderStore.selectOrders(routeOrders.map(o => o.id))
        }
    }
}

// 创建新路线
const createRoute = async driverId => {
    try {
        const newRoute = await routeStore.createRoute(driverId, [])
        ElMessage.success('已成功创建新路线')
        return newRoute
    } catch (error) {
        console.error('创建路线失败:', error)
        ElMessage.error('创建路线失败')
    }
}

// 处理司机选择/取消选择
const handleDriverSelect = (driver, isSelected) => {
    if (isSelected) {
        // 添加到选中司机列表
        if (!selectedDrivers.value.includes(driver.id)) {
            selectedDrivers.value.push(driver.id)
        }
    } else {
        // 从选中司机列表移除
        selectedDrivers.value = selectedDrivers.value.filter(id => id !== driver.id)
    }

    // 更新所有选中司机的订单显示
    updateSelectedDriverOrders()

    // 处理单个司机选择逻辑
    if (selectedDrivers.value.length === 1) {
        // 只有一个司机被选中时，设置为当前选中司机
        const selectedDriverId = selectedDrivers.value[0]
        const selectedDriverObj = driverStore.drivers.find(d => d.id === selectedDriverId)
        driverStore.setSelectedDriver(selectedDriverObj)
    } else if (selectedDrivers.value.length === 0) {
        // 没有司机被选中时，清除当前选中司机
        driverStore.clearSelectedDriver()
    } else {
        // 多个司机被选中时，也清除当前单个选中司机
        driverStore.clearSelectedDriver()
    }

    console.log('当前选中的司机IDs:', selectedDrivers.value)
}

// 更新所有路线订单映射
const updateAllRouteOrderMaps = () => {
    console.log('更新所有路线订单映射')

    // 清空当前映射，避免使用过时数据
    Object.keys(routeOrdersMap).forEach(key => {
        delete routeOrdersMap[key]
    })

    // 遍历所有路线，重新获取订单数据
    routeStore.routes.forEach(route => {
        console.log(`处理路线 ${route.name} 的订单数据`)
        const orders = getSortedRouteOrders(route.name)
        console.log(`路线 ${route.name} 订单数量: ${orders.length}`)

        // 确保即使没有订单也设置一个空数组
        if (!routeOrdersMap[route.name]) {
            routeOrdersMap[route.name] = []
        }
    })

    console.log('所有路线订单映射已更新')
}

// 计算统计信息
const calculateStats = () => {
    // 使用 computed 属性，不需要手动调用
}

// 处理订单选择事件
const handleOrderSelected = (data) => {
    // 如果事件来源是自己，则不处理，避免循环
    if (data && data.source === 'driver-route-panel') {
        return
    }

    console.log('DriverRoutePanel 收到订单选择事件:', data)

    // 不需要做任何处理，因为我们直接使用 orderStore.selectedOrderIds 来渲染选中状态
    // 组件会自动响应 orderStore 中的变化
}

// 处理批量订单选择事件
const handleOrdersBatchSelected = (data) => {
    console.log('DriverRoutePanel 收到批量订单选择事件:', data)

    // 不需要做任何处理，因为我们直接使用 orderStore.selectedOrderIds 来渲染选中状态
    // 组件会自动响应 orderStore 中的变化
}

// 处理订单选择清除事件
const handleOrdersSelectionCleared = (data) => {
    console.log('DriverRoutePanel 收到订单选择清除事件:', data)

    // 不需要做任何处理，因为我们直接使用 orderStore.selectedOrderIds 来渲染选中状态
    // 组件会自动响应 orderStore 中的变化
}

// 反转路线顺序
const reverseRouteOrder = async (route) => {
    console.log(`开始反转路线 ${route.name} 的顺序`)

    // 设置加载状态
    route.isReversing = true

    try {
        // 获取当前路线的订单
        const orders = routeOrdersMap[route.name] || []
        if (orders.length === 0) {
            ElMessage.warning('该路线没有订单，无法反转顺序')
            return
        }

        // 反转订单数组
        const reversedOrders = [...orders].reverse()

        // 更新路线订单映射
        routeOrdersMap[route.name] = reversedOrders

        // 重新分配停靠点序号
        reversedOrders.forEach((order, index) => {
            order.stop_no = index + 1
        })

        // 使用OrderStore的updateLocalOrderSequence方法更新订单顺序
        orderStore.updateLocalOrderSequence(route.id, reversedOrders)

        // 发送地图刷新事件
        eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
            routeId: route.id,
            timestamp: Date.now()
        })

        // 显示成功提示
        ElMessage.success(`已成功反转路线 ${route.name} 的顺序`)
    } catch (error) {
        console.error(`反转路线 ${route.name} 顺序失败:`, error)
        ElMessage.error('反转路线顺序失败')
    } finally {
        // 清除加载状态
        route.isReversing = false
    }
}

// 获取路段信息（距离和时间）
const getSegmentInfo = (route, orderIndex) => {
    try {
        // 获取路线详细信息
        const routeDetails = route.getRouteDetails ? route.getRouteDetails() : null
        if (!routeDetails || !routeDetails.segments) {
            console.log(`路线 ${route.name} 没有路段数据`)
            return null
        }

        const segments = routeDetails.segments
        console.log(`路线 ${route.name} 有 ${segments.length} 个路段`)

        // 获取当前路线的订单
        const orders = routeOrdersMap[route.name] || []
        if (orders.length === 0) {
            console.log(`路线 ${route.name} 没有订单`)
            return null
        }

        console.log(`路线 ${route.name} 订单数量: ${orders.length}`)

        // 调试：显示路段结构
        console.log(`路线 ${route.name} 路段结构:`)
        segments.forEach((seg, idx) => {
            console.log(`  路段 ${idx}: 距离=${(seg.distance/1000).toFixed(2)}km, 时间=${(seg.duration/60).toFixed(1)}min`)
        })

        // 处理同地址订单的情况
        // 我们需要找到当前订单对应的实际路段

        // 创建唯一地址列表，保持订单顺序
        const uniqueAddresses = []
        const orderToAddressIndex = new Map()

        orders.forEach((order, index) => {
            const addressKey = `${order.lng_lat[0]},${order.lng_lat[1]}`

            // 查找是否已有相同地址
            let addressIndex = uniqueAddresses.findIndex(addr =>
                `${addr.lng_lat[0]},${addr.lng_lat[1]}` === addressKey
            )

            if (addressIndex === -1) {
                // 新地址，添加到唯一地址列表
                addressIndex = uniqueAddresses.length
                uniqueAddresses.push({
                    lng_lat: order.lng_lat,
                    address_id: order.address_id,
                    firstOrderIndex: index
                })
            }

            // 记录订单到地址索引的映射
            orderToAddressIndex.set(index, addressIndex)
        })

        console.log(`路线 ${route.name} 唯一地址数量: ${uniqueAddresses.length}`)
        console.log(`订单 ${orderIndex} 对应地址索引:`, orderToAddressIndex.get(orderIndex))

        // 获取当前订单对应的地址索引
        const addressIndex = orderToAddressIndex.get(orderIndex)
        if (addressIndex === undefined) {
            console.log(`路线 ${route.name} 订单 ${orderIndex} 找不到对应的地址索引`)
            return null
        }

        // 对于同一地址的后续订单，显示 "同地址" 信息
        const isFirstOrderAtAddress = uniqueAddresses[addressIndex].firstOrderIndex === orderIndex
        if (!isFirstOrderAtAddress) {
            return {
                distance: "同地址",
                duration: "0min",
                rawDistance: 0,
                rawDuration: 0,
                isSameAddress: true
            }
        }

        // 计算路段索引
        // 正确的显示逻辑：
        // - 第1个地址：显示从起点到第1个地址的路段（索引0）
        // - 第2个地址：显示从第1个地址到第2个地址的路段（索引1）
        // - 第3个地址：显示从第2个地址到第3个地址的路段（索引2）
        // - 最后一个地址：显示从倒数第二个地址到最后一个地址的路段
        // - 不显示：最后一个地址到终点的路段

        const segmentIndex = addressIndex

        // 检查路段索引是否有效
        // 注意：segments 数组包含所有路段，包括到终点的路段
        // 我们需要确保不超出有效范围
        if (segmentIndex < 0) {
            console.log(`路线 ${route.name} 地址索引 ${addressIndex} 的路段索引 ${segmentIndex} 小于0`)
            return null
        }

        // 处理路段索引超出范围的情况
        if (segmentIndex >= segments.length) {
            console.log(`路线 ${route.name} 地址索引 ${addressIndex} 的路段索引 ${segmentIndex} 超出范围 [0, ${segments.length - 1}]`)
            console.log(`segments 数组长度: ${segments.length}, uniqueAddresses 数量: ${uniqueAddresses.length}`)

            // 特殊处理：如果是最后一个唯一地址，且路段数组比地址数量少1
            // 这种情况下，最后一个地址应该使用最后一个可用的路段
            if (addressIndex === uniqueAddresses.length - 1 && segments.length === uniqueAddresses.length - 1) {
                console.log(`路线 ${route.name} 最后一个地址使用最后一个路段 ${segments.length - 1}`)
                const lastSegment = segments[segments.length - 1]
                if (lastSegment) {
                    // 格式化距离（转换为公里）
                    const distanceKm = (lastSegment.distance || 0) / 1000
                    const formattedDistance = distanceKm < 1
                        ? `${Math.round(lastSegment.distance || 0)}m`
                        : `${distanceKm.toFixed(1)}km`

                    // 格式化时间（转换为分钟）
                    const durationMin = (lastSegment.duration || 0) / 60
                    const formattedDuration = durationMin < 1
                        ? `${Math.round(lastSegment.duration || 0)}s`
                        : `${Math.round(durationMin)}min`

                    return {
                        distance: formattedDistance,
                        duration: formattedDuration,
                        rawDistance: lastSegment.distance || 0,
                        rawDuration: lastSegment.duration || 0,
                        isSameAddress: false
                    }
                }
            }

            return null
        }

        const segment = segments[segmentIndex]
        if (!segment) {
            console.log(`路线 ${route.name} 路段 ${segmentIndex} 不存在`)
            return null
        }

        console.log(`路线 ${route.name} 订单 ${orderIndex} (地址索引 ${addressIndex}) 路段信息:`, segment)

        // 格式化距离（转换为公里）
        const distanceKm = (segment.distance || 0) / 1000
        const formattedDistance = distanceKm < 1
            ? `${Math.round(segment.distance || 0)}m`
            : `${distanceKm.toFixed(1)}km`

        // 格式化时间（转换为分钟）
        const durationMin = (segment.duration || 0) / 60
        const formattedDuration = durationMin < 1
            ? `${Math.round(segment.duration || 0)}s`
            : `${Math.round(durationMin)}min`

        return {
            distance: formattedDistance,
            duration: formattedDuration,
            rawDistance: segment.distance || 0,
            rawDuration: segment.duration || 0,
            isSameAddress: false
        }
    } catch (error) {
        console.error('获取路段信息失败:', error)
        return null
    }
}

// Ensure reactive variables are defined before use in computed properties
// const scheduledCount = ref(0);
// const unscheduledCount = ref(0);
// const totalCount = ref(0);
// const routeCount = ref(0);

// const calculateStats = () => {
//     scheduledCount.value = orderStore.assignedOrders.length;
//     unscheduledCount.value = orderStore.orders.length;
//     totalCount.value = scheduledCount.value + unscheduledCount.value;
//     routeCount.value = routeStore.routes.length;
// };

// Call initially and maybe on updates if not reactive enough
// onMounted(calculateStats);
// watch([() => orderStore.assignedOrders, () => orderStore.orders, () => routeStore.routes], calculateStats);


</script>

<style scoped>
.driver-route-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    max-height: calc(100vh - var(--g-header-height));
}

.header {
    padding: 8px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eaeaea;
}

.stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.stat-card {
    flex: 1;
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    padding: 8px 0;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
}

.stat-label {
    font-size: 12px;
    color: #909399;
}

.actions {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;
}

.selection-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    justify-content: space-between;
}

.driver-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    position: relative; /* Needed for proper scrolling with absolute positioned elements */
    touch-action: pan-y; /* 允许垂直滚动，但阻止水平滚动 */
    -webkit-overflow-scrolling: touch; /* 在iOS上提供更流畅的滚动 */
}

.driver-item {
    margin-bottom: 12px;
    padding: 8px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border: 1px solid #eaeaea;
    transition: all 0.3s ease;
}

.driver-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
}

.driver-item.active {
    background-color: #ecf5ff;
    border-color: #409eff;
}

/* 选中状态样式 */

.driver-item .el-checkbox.is-checked+.driver-col {
    color: #409eff;
    font-weight: 600;
}

/* 切换司机弹窗样式 */
.switch-driver-dialog {
    padding: 10px;
}

.dialog-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    gap: 10px;
}

.dialog-actions .el-input {
    flex: 1;
}

.driver-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #eaeaea;
    border-radius: 4px;
    padding: 10px;
}

.driver-radio-group {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.driver-radio-item {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s;
}

.driver-radio-item:hover {
    background-color: #f5f7fa;
}

.driver-radio-item .el-tag {
    margin-left: 8px;
}

.driver-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.driver-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.driver-name {
    flex-grow: 1;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
}

.driver-name:hover {
    color: #409eff;
}

.expand-icon {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.expand-icon.is-active {
    transform: rotate(180deg);
}

.routes-container {
    margin-top: 8px;
    margin-left: 24px;
    transition: all 0.3s ease;
    overflow: visible; /* Changed from hidden to visible to allow proper scrolling */
}

.route-item {
    border-left: 2px solid #dcdfe6;
    padding: 6px 0 6px 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.route-item:hover {
    border-left-color: #409eff;
    background-color: #f0f7ff;
}

.route-item.selected {
    border-left-color: #409eff;
    background-color: #ecf5ff;
}

.route-name {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.order-count {
    font-size: 12px;
    color: #909399;
    font-weight: normal;
    margin-right: 8px;
}

.route-action-icons {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

.route-action-icons .el-button {
    padding: 4px;
    height: 24px;
    width: 24px;
}

.route-action-icons .el-button:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.route-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 8px 0;
}

.route-expand-icon {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.route-expand-icon.is-active {
    transform: rotate(180deg);
}

.route-stops {
    margin-top: 8px;
    padding-left: 8px;
    border-left: 1px dashed #dcdfe6;
    position: relative;
}

/* 移除了滚动条样式，因为现在使用外部容器的滚动 */

.stop-item {
    cursor: pointer; /* 修改为指针光标，表示可点击 */
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    border-bottom: 1px dashed #ebeef5;
    position: relative;
    border-radius: 4px;
    margin-bottom: 4px;
    user-select: none; /* 防止文本被选中 */
    touch-action: none; /* 阻止浏览器的默认触摸行为，让拖拽更可靠 */
    -webkit-tap-highlight-color: transparent; /* 移除移动设备上的点击高亮 */
}

.stop-item:hover {
    background-color: #f0f7ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

/* 选中状态样式 */
.stop-item.selected {
    background-color: #ecf5ff;
    border: 1px solid #409eff;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

/* 拖拽时的光标 */
.stop-item.sortable-chosen {
    cursor: move;
    z-index: 10;
}

/* 多选拖拽时的样式 */
.stop-item.selected.sortable-chosen {
    background-color: #409eff;
    color: white;
    border: 1px solid #409eff;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
    transform: scale(1.05);
    z-index: 20;
}

.stop-item.selected.sortable-chosen .stop-number {
    background-color: white;
    color: #409eff;
}

.stop-item.selected.sortable-chosen .stop-name,
.stop-item.selected.sortable-chosen .stop-address,
.stop-item.selected.sortable-chosen .stop-no {
    color: white;
}

.ghost-item {
    opacity: 0.7;
    background: #e6f1fc;
    border: 1px dashed #409eff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 10px;
}

.dragging {
    opacity: 0.95;
    background: #f0f7ff;
    border: 1px solid #409eff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: scale(1.03);
    z-index: 10;
    border-radius: 4px;
    padding: 10px;
}

.stop-number {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #909399;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
    position: relative;
}

.drag-indicator {
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #909399;
    opacity: 0.5;
    transition: opacity 0.2s;
}

.stop-item:hover .drag-indicator {
    opacity: 1;
}

.stop-number-edit {
    width: 50px;
    height: 28px;
    flex-shrink: 0;
    margin-right: 4px;
}

.stop-number-edit .el-input__inner {
    height: 28px;
    padding: 0 8px;
    text-align: center;
    font-size: 12px;
}

/* 编辑状态的订单样式 */
.stop-item.editing-stop {
    background-color: #f0f7ff;
    border: 1px dashed #409eff;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

.stop-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.stop-name {
    font-size: 13px;
    color: #303133;
    font-weight: 500;
}

.stop-address {
    font-size: 12px;
    color: #606266;
}

.stop-no {
    font-size: 12px;
    color: #909399;
}

.stop-segment-info {
    display: flex;
    gap: 12px;
    margin-top: 4px;
    font-size: 11px;
    color: #67c23a;
}

.stop-segment-info.same-address {
    color: #909399;
    font-style: italic;
}

.segment-distance,
.segment-duration {
    display: flex;
    align-items: center;
    gap: 2px;
}

.segment-distance .el-icon,
.segment-duration .el-icon {
    font-size: 12px;
}

.stop-item:last-child {
    border-bottom: none;
}

.empty-routes {
    padding: 12px 0;
    display: flex;
    justify-content: center;
}

.no-routes-text {
    font-size: 12px;
    color: #909399;
}

.empty-route-message {
    padding: 10px 0;
    color: #909399;
    font-size: 12px;
    text-align: center;
    font-style: italic;
}

.route-actions {
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
}
</style>