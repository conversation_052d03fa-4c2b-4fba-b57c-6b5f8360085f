<script setup name="ModuleList">
import { deleteOperation, getOperations, updateOperationAvailability } from '@/api/modules/permissions'
import { usePagination } from '@/utils/composables'
import { Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import FormMode from './components/FormMode/index.vue'
const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination()
// const router = useRouter()

const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        id: '',
        page: ''
    },
    search: {
        title: ''
    },
    batch: {
        enable: false,
        selectionDataList: []
    },
    dataList: []
})

onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams()
    data.value.search.title && (params.title = data.value.search.title)
    getOperations(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.operation_list
        pagination.value.total = res.data.total
    })
}

function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

function onCreate() {
    data.value.formModeProps.id = ''
    data.value.formModeProps.page = 'o'
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.page = 'o'
    data.value.formModeProps.visible = true
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deleteOperation({ id: row.id }).then(res => {
            if (res.data.errCode === 365) { getDataList() }
        })
    }).catch(() => { })
}

function onAlterAvailability(row) {
    let params = {
        id: row.id,
        isAvailable: !row.is_available
    }
    updateOperationAvailability(params).then(res => {
        if (res.data.errCode === 365) {
            getDataList()
        }
    })

}
</script>

<template>
    <div>
        <page-header :title="$t('permission.operations')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">

                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-suffix="：">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="标题">
                                        <el-input v-model="data.search.title" placeholder="请输入标题，支持模糊查询" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item></el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList"
                    @check-all="$refs.table.toggleAllSelection()" @check-null="$refs.table.clearSelection()">
                    <el-button size="default" type="danger" plain @click="onBatchDel">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>

            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }"
                @selection-change="data.batch.selectionDataList = $event" @row-dblclick="onEdit">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')">
                    <template #default="scope">
                        {{ $t(`operations.${scope.row.name}`) }}
                    </template>
                </el-table-column>
                <el-table-column prop="desc" :label="$t('fields.desc')" />
                <el-table-column prop="is_available" :label="$t('fields.availability')">
                    <template #default="scope">
                        <el-icon :color="scope.row.is_available ? '#67C23A' : '#F56C6C'" :size="20">
                            <svg-icon :name="scope.row.is_available ? 'i-ep:success-filled' : 'i-ep:circle-close'" />
                        </el-icon>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'primary'" circle size="small"
                                @click="onAlterAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" :page="data.formModeProps.page"
            @success="getDataList" />
    </div>
</template>

<style lang="scss" scoped>
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
