<script setup>
import { getActiveDriversCurrentPosition, getAppleMapJwt } from '@/api/modules/dispatcher';

const driverPostions = ref([])
const mapJwt = ref(null)

onMounted(() => {
    getJwt();
    getDriversPostions();
})


function getJwt() {
    getAppleMapJwt().then(res => {
        mapJwt.value = res.data.jwt
    })
}

function getDriversPostions() {
    getActiveDriversCurrentPosition().then(res => {
        driverPostions.value = res.data
    })
}
</script>

<template>
    <div>
        <page-main>
            <el-button type="primary" @click="getDriversPostions">Refresh</el-button>
            <map-kit v-if="mapJwt" :jwt=mapJwt :driver-positions=driverPostions />
        </page-main>
    </div>
</template>
