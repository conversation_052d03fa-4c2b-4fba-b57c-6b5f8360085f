<script setup>
import { getDeliveryOrderAddresses, getPickupTimeByPc, getDeliveryTimeByPc, updateDeliveryOrder } from '@/api/modules/delivery';
import { phoneNumberFormatter } from '@/utils/formatter'
import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'

const props = defineProps({
    id: {
        type: String,
        default: null
    },
    cardShadow: {
        type: String,
        default: 'always'
    }
})



onMounted(() => {
    if (props.id) {
        getAddresses();
    }
})

defineExpose({ reset, swapAddresses, updateAddresses, clearAddressToModify })
const emit = defineEmits(['success', 'validated'])

const addressesFormRef = ref()
const senderAddressSelectRef = ref()
const receiverAddressSelectRef = ref()
const addresses = ref({
    sender_name: null,
    sender_company: null,
    sender_phone: null,
    sender_secondary_phone: null,
    sender_email: null,
    sender_unit_no: null,
    sender_buzzer_code: null,
    sender_address: null,
    sender_postal_code: null,
    sender_city: null,
    sender_province: null,
    sender_country: null,
    receiver_name: null,
    receiver_company: null,
    receiver_phone: null,
    receiver_secondary_phone: null,
    receiver_email: null,
    receiver_unit_no: null,
    receiver_buzzer_code: null,
    receiver_address: null,
    receiver_postal_code: null,
    receiver_city: null,
    receiver_province: null,
    receiver_country: null,
    sender_addr_db: null,
    receiver_addr_db: null,
    pickup_date: null,
    pickup_time: null,
    expected_pickup_date: null,
    expected_pickup_start_at: null,
    expected_pickup_end_at: null,
    delivery_date: null,
    delivery_time: null,
    expected_delivery_start_at: null,
    expected_delivery_end_at: null,
    mod_level: null,
})

const addressModifyEnabled = ref(false)

const addressesFormRules = {
    sender_name: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    sender_phone: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    sender_email: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    sender_address: [
        { required: true, message: '请输入地址', trigger: 'blur' }
    ],
    sender_postal_code: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    pickup_date: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    pickup_time: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    receiver_name: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    receiver_phone: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    receiver_email: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    receiver_address: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    receiver_postal_code: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    delivery_date: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    delivery_time: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
}

const pickupTimes = ref([])
const firstPickupDate = ref()
const deliveryTimes = ref([])
const firstDeliveryDate = ref()

function disabledPickupDates(date) {
    return date.getTime() < (firstPickupDate.value ?? new Date()).getTime()
}

function disabledDeliveryDates(date) {
    return date.getTime() < (firstDeliveryDate.value ?? new Date()).getTime()
}


watch(() => addresses.value.sender_postal_code, (val) => {
    if (val && val.length >= 6) {
        getPickupTimes()
    }
    else {
        pickupTimes.value = []
    }
},
    { immediate: true }
)

watch(() => addresses.value.receiver_postal_code, (val) => {
    if (val && val.length >= 6) {
        getDeliveryTimes()
    }
    else {
        pickupTimes.value = []
    }
},
    { immediate: true }
)

// API
function getAddresses() {
    getDeliveryOrderAddresses(props.id).then(res => {
        addresses.value = res.data;
    })
}
function getPickupTimes() {
    if (addresses.value.mod_level != 'all') return;

    if (senderPcIsAvailable.value != false) {
        let params = {
            userId: addresses.value.user,
            postalCode: addresses.value.sender_postal_code.replace(' ', ''),
            date: addresses.value.pickup_date

        }
        getPickupTimeByPc(params).then(res => {
            if (!res.data.errCode) {
                addresses.value.pickup_date = res.data.pickup_date
                firstPickupDate.value = new Date(res.data.first_pickup_date)
                pickupTimes.value = res.data.pickup_time_list
                if (pickupTimes.value.length > 0 && pickupTimes.value.find(e => e.id == addresses.value.pickup_time) == undefined) {
                    addresses.value.pickup_time = pickupTimes.value[0].id

                }
                if (addresses.value.pickup_time) {
                    getDeliveryTimes();
                }
            }
        })
    } else {
        pickupTimes.value = []
    }
}

function getDeliveryTimes() {
    if (!['all', 'sender'].includes(addresses.value.mod_level)) return;
    if (addresses.value.receiver_postal_code && (addresses.value.pickup_date ?? addresses.value.expected_pickup_date)) {
        let params = {
            userId: addresses.value.user,
            pickupDate: addresses.value.pickup_date,
            pickupTime: addresses.value.pickup_time,
            pickupPostalCode: addresses.value.sender_postal_code,
            postalCode: addresses.value.receiver_postal_code.replace(' ', ''),
            date: addresses.value.delivery_date
        }
        getDeliveryTimeByPc(params).then(res => {
            if (!res.data.errCode) {
                firstDeliveryDate.value = new Date(res.data.first_delivery_date)
                addresses.value.delivery_date = res.data.delivery_date
                deliveryTimes.value = res.data.delivery_time_list
                if (deliveryTimes.value.length > 0 && deliveryTimes.value.find(e => e.id == addresses.value.delivery_time) == undefined) {
                    addresses.value.delivery_time = deliveryTimes.value[0].id
                }

            }
        })
    }
}

function updateAddresses() {
    addressesFormRef.value.validate(valid => {
        if (valid) {
            let params = {
                id: props.id,
                receiver_name: addresses.value.receiver_name,
                receiver_company: addresses.value.receiver_company,
                receiver_phone: addresses.value.receiver_phone,
                receiver_secondary_phone: addresses.value.receiver_secondary_phone,
                receiver_email: addresses.value.receiver_email,
                receiver_buzzer_code: addresses.value.receiver_buzzer_code,
            }
            if (['receiver', 'sender', 'all'].includes(addresses.value.mod_level)) {
                if (addressModifyEnabled) {
                    params.receiver_addr_db = addresses.value.receiver_addr_db
                    params.receiver_unit_no = addresses.value.receiver_unit_no
                    params.delivery_date = addresses.value.delivery_date
                    params.delivery_time = addresses.value.delivery_time
                }
                if (['sender', 'all'].includes(addresses.value.mod_level)) {
                    params.sender_name = addresses.value.sender_name
                    params.sender_company = addresses.value.sender_company
                    params.sender_phone = addresses.value.sender_phone
                    params.sender_secondary_phone = addresses.value.sender_secondary_phone
                    params.sender_email = addresses.value.sender_email
                    params.sender_buzzer_code = addresses.value.sender_buzzer_code
                    if (addresses.value.mod_level == 'all') {
                        if (addressModifyEnabled) {
                            params.sender_addr_db = addresses.value.sender_addr_db
                            params.sender_unit_no = addresses.value.sender_unit_no
                            params.pickup_date = addresses.value.pickup_date
                            params.pickup_time = addresses.value.pickup_time
                        }
                    }
                }

            }
            updateDeliveryOrder(params).then(res => {
                if (res.data.errCode === 365) {
                    // if (res.data.total_difference) {
                    //     ElMessageBox.alert(
                    //         `Please be acknowledged: Order total been updated, delta is ${currencyFormatter(null, null, res.data.total_difference)}`,
                    //         'Warning',
                    //         {
                    //             confirmButtonText: 'OK',
                    //             type: 'warning',
                    //             center: true,
                    //         }
                    //     )

                    // }
                    emit('success')
                }
            })
        }
    })
}


function reset() {
    getAddresses()
    addressesFormRef.value.clearValidate()
}

function clearAddressToModify() {
    for (let k in addresses.value) {
        addresses.value[k] = null
    }
    senderAddressSelectRef.value.reset(null)
    receiverAddressSelectRef.value.reset(null)
    pickupTimes.value.length = 0
    deliveryTimes.value.length = 0

}

function swapAddresses() {
    if (addresses.value.mod_level != 'all') return;
    const temp = {
        name: addresses.value.sender_name,
        phone: addresses.value.sender_phone,
        secondary_phone: addresses.value.sender_secondary_phone,
        email: addresses.value.sender_email,
        unit_no: addresses.value.sender_unit_no,
        buzzer_code: addresses.value.sender_buzzer_code,
        address: addresses.value.sender_address,
        postal_code: addresses.value.sender_postal_code,
        city: addresses.value.sender_city,
        province: addresses.value.sender_province,
        addr_db: addresses.value.sender_addr_db,
    }
    addresses.value.sender_name = addresses.value.receiver_name
    addresses.value.sender_phone = addresses.value.receiver_phone
    addresses.value.sender_secondary_phone = addresses.value.receiver_secondary_phone
    addresses.value.sender_email = addresses.value.receiver_email
    addresses.value.sender_buzzer_code = addresses.value.receiver_buzzer_code
    addresses.value.sender_unit_no = addresses.value.receiver_unit_no

    addresses.value.sender_address = addresses.value.receiver_address
    addresses.value.sender_postal_code = addresses.value.receiver_postal_code
    addresses.value.sender_city = addresses.value.receiver_city
    addresses.value.sender_province = addresses.value.receiver_province
    addresses.value.sender_addr_db = addresses.value.receiver_addr_db

    addresses.value.receiver_name = temp.name
    addresses.value.receiver_phone = temp.phone
    addresses.value.receiver_secondary_phone = temp.secondary_phone
    addresses.value.receiver_email = temp.email
    addresses.value.receiver_buzzer_code = temp.buzzer_code
    addresses.value.receiver_unit_no = temp.unit_no
    addresses.value.receiver_address = temp.address
    addresses.value.receiver_postal_code = temp.postal_code
    addresses.value.receiver_city = temp.city
    addresses.value.receiver_province = temp.province
    addresses.value.receiver_addr_db = temp.addr_db

    senderAddressSelectRef.value.reset(addresses.value.receiver_address)
    receiverAddressSelectRef.value.reset(addresses.value.sender_address)

    getPickupTimes()
}
const senderPcIsAvailable = ref(null)
const receiverPcIsAvailable = ref(null)


function onClearSenderAddress() {
    addresses.value.sender_address = null
    addresses.value.sender_city = null
    addresses.value.sender_province = null
    addresses.value.sender_postal_code = null
    pickupTimes.value = []
}

function onClearReceiverAddress() {
    addresses.value.receiver_address = null
    addresses.value.receiver_city = null
    addresses.value.receiver_province = null
    addresses.value.receiver_postal_code = null
    deliveryTimes.value = []
}

function retrieveSenderAddress(val) {
    addresses.value.sender_address = val.Line1
    addresses.value.sender_city = val.City
    addresses.value.sender_province = val.ProvinceName
    addresses.value.sender_postal_code = val.PostalCode
    addresses.value.sender_addr_db = val.Id
    senderPcIsAvailable.value = val.is_available
    getPickupTimes()
}

function retrieveReceiverAddress(val) {
    addresses.value.receiver_address = val.Line1
    addresses.value.receiver_city = val.City
    addresses.value.receiver_province = val.ProvinceName
    addresses.value.receiver_postal_code = val.PostalCode
    addresses.value.receiver_addr_db = val.Id
    receiverPcIsAvailable.value = val.is_available
    getDeliveryTimes()
}


function onModifyAddresses() {
    if (!addressModifyEnabled.value) {
        ElMessageBox.confirm(
            'Pickup time / Delivery time will be reset. Continue?',
            'Warning',
            {
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel',
                type: 'warning',
            }
        )
            .then(() => {
                addressModifyEnabled.value = true
                getPickupTimes();
            })
    }
}

</script>
<template>
    <el-form :rules="addressesFormRules" :model="addresses" ref="addressesFormRef">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-card :header="$t('delivery.orders.fields.sender')" class="sender-card" :shadow="cardShadow">
                    <el-form-item :label="$t('fields.name')" prop="sender_name">
                        <el-input v-model="addresses.sender_name"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level)" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.companyName')" prop="sender_company">
                        <el-input v-model="addresses.sender_company"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level)" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.phoneNumber')" prop="sender_phone">
                        <el-input v-model="addresses.sender_phone" :formatter="phoneNumberFormatter"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level)" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.secondaryPhone')">
                        <el-input v-model="addresses.sender_secondary_phone"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level)" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.email')" prop="sender_email">
                        <el-input v-model="addresses.sender_email"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level)" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.buzzerCode')">
                        <el-input v-model="addresses.sender_buzzer_code"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level)" />
                    </el-form-item>
                    <el-divider />
                    <el-form-item :label="$t('user.fields.address')" prop="sender_address">
                        <AddressSelect ref="senderAddressSelectRef" :address="addresses.sender_address"
                            :disabled="addresses.mod_level != 'all' || !addressModifyEnabled"
                            :id="addresses.sender_addr_db" :city="addresses.sender_city"
                            :province="addresses.sender_province" :postal-code="addresses.sender_postal_code"
                            :on-clear="onClearSenderAddress" @success="retrieveSenderAddress" />
                    </el-form-item>
                    <el-form-item prop="sender_postal_code">
                        <div class="address-auto-span">
                            <span v-if="addresses.sender_postal_code">
                                {{ addresses.sender_postal_code }}&ThickSpace;{{
                                    addresses.sender_city
                                }},&ThickSpace;{{ addresses.sender_province }}
                            </span>
                            <span v-else>&ThickSpace;</span>
                            <span class="address-not-available" v-if="senderPcIsAvailable === false">
                                (Not Available)
                            </span>
                            <span v-else>&ThickSpace;</span>
                            <el-button type="danger" size="small" @click="onModifyAddresses" text
                                v-if="addresses.mod_level == 'all' && !addressModifyEnabled">{{ $t('operations.modify')
                                }}</el-button>
                            <el-input v-model="addresses.sender_postal_code" type="hidden"
                                @change="senderPcIsAvailable ? getPickupTimes : null" />
                        </div>
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.unitNo')">
                        <el-input v-model="addresses.sender_unit_no" text
                            :disabled="addresses.mod_level != 'all' || !addressModifyEnabled" />
                    </el-form-item>

                    <template v-if="addresses.mod_level == 'all' && addressModifyEnabled">
                        <el-form-item :label="$t('delivery.orders.fields.expectedPickupDate')" prop="pickup_date">
                            <el-date-picker v-model="addresses.pickup_date" type="date" placeholder="Pick a day"
                                @change="getPickupTimes" :disabled-date="disabledPickupDates" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" :size="size" />
                        </el-form-item>
                        <el-form-item :label="$t('delivery.orders.fields.expectedPickupTime')" prop="pickup_time">
                            <el-select v-model="addresses.pickup_time" placeholder="Select" :disabled="!pickupTimes">
                                <el-option v-for="item in pickupTimes" :key="item.id" :value="item.id"
                                    :label="`${item.start_at} ~ ${item.end_at}`" />
                            </el-select>
                        </el-form-item>
                    </template>
                    <el-form-item v-else-if="addresses.expected_pickup_start_at">
                        <el-alert type="info" :closable="false" show-icon>
                            <template #title>
                                {{ $t('delivery.orders.fields.expectedPickupTime') }}
                            </template>
                            <template #default>
                                {{ addresses.expected_pickup_start_at }} ~
                                {{ addresses.expected_pickup_end_at }}
                            </template>
                        </el-alert>
                    </el-form-item>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card :header="$t('delivery.orders.fields.receiver')" class="receiver-card" :shadow="cardShadow">
                    <el-form-item :label="$t('fields.name')" prop="receiver_name">
                        <el-input v-model="addresses.receiver_name" :disabled="!addresses.mod_level" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.companyName')" prop="receiver_company">
                        <el-input v-model="addresses.receiver_company_name" :disabled="!addresses.mod_level" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.phoneNumber')" prop="receiver_phone">
                        <el-input v-model="addresses.receiver_phone" :disabled="!addresses.mod_level" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.secondaryPhone')">
                        <el-input v-model="addresses.receiver_secondary_phone" :disabled="!addresses.mod_level" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.email')" prop="receiver_email">
                        <el-input v-model="addresses.receiver_email" :disabled="!addresses.mod_level" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.buzzerCode')">
                        <el-input v-model="addresses.receiver_buzzer_code" :disabled="!addresses.mod_level" />
                    </el-form-item>
                    <el-divider />
                    <el-form-item :label="$t('user.fields.address')" prop="receiver_address">
                        <AddressSelect ref="receiverAddressSelectRef" :address="addresses.receiver_address"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level) || !addressModifyEnabled"
                            :id="addresses.receiver_addr_db" :city="addresses.receiver_city"
                            :province="addresses.receiver_province" :postal-code="addresses.receiver_postal_code"
                            :on-clear="onClearReceiverAddress" @success="retrieveReceiverAddress" />
                    </el-form-item>
                    <el-form-item prop="receiver_postal_code">
                        <div class="address-auto-span">
                            <span v-if="addresses.receiver_postal_code">
                                {{ addresses.receiver_postal_code }}&ThickSpace;
                                {{ addresses.receiver_city }},&ThickSpace;
                                {{ addresses.receiver_province }}
                            </span>
                            <span v-else>&ThickSpace;</span>
                            <span class="address-not-available" v-if="senderPcIsAvailable === false">
                                (Not Available)
                            </span>
                            <span v-else>&ThickSpace;</span>
                            <el-button type="danger" size="small" @click="onModifyAddresses" text
                                v-if="['all', 'sender'].includes(addresses.mod_level) && !addressModifyEnabled">{{
                                    $t('operations.modify') }}</el-button>
                            <el-input v-model="addresses.receiver_postal_code" type="hidden"
                                @change="getDeliveryTimes" />
                        </div>
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.unitNo')">
                        <el-input v-model="addresses.receiver_unit_no"
                            :disabled="!['all', 'sender'].includes(addresses.mod_level) || !addressModifyEnabled"
                            :city="addresses.receiver_city" />
                    </el-form-item>
                    <template v-if="addresses.delivery_date && addressModifyEnabled">
                        <el-form-item :label="$t('delivery.orders.fields.expectedDeliveryDate')" prop="delivery_date">
                            <el-date-picker v-model="addresses.delivery_date" type="date" placeholder="Pick a day"
                                @change="getDeliveryTimes" :disabled-date="disabledDeliveryDates" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" :size="size" />
                        </el-form-item>
                        <el-form-item :label="$t('delivery.orders.fields.expectedDeliveryTime')" prop="delivery_time">
                            <el-select v-model="addresses.delivery_time" placeholder="Select"
                                :disabled="!deliveryTimes.length" value-key="id">
                                <el-option v-for="item in deliveryTimes" :key="item.id"
                                    :label="`${item.start_at} ~ ${item.end_at}`" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </template>
                    <el-form-item v-else-if="addresses.expected_delivery_start_at">
                        <el-alert type="info" :closable="false" show-icon>
                            <template #title>
                                {{ $t('delivery.orders.fields.expectedDeliveryTime') }}
                            </template>
                            <template #default>
                                {{ addresses.expected_delivery_start_at }} ~
                                {{ addresses.expected_delivery_end_at }}
                            </template>
                        </el-alert>
                    </el-form-item>
                </el-card>
            </el-col>
        </el-row>
    </el-form>
</template>
<style scoped lang="scss">
:deep(.el-card__header) {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-weight: bolder;
    color: white;
}

.el-alert {
    margin: 20px 0 0;

    :deep(.el-alert__description) {
        margin: 0;
        padding: 0;
        line-height: normal;
    }
}

.el-alert:first-child {
    margin: 0;
}

.sender-card {

    :deep(.el-card__header) {
        background-color: #eebe77 !important;
    }
}

.receiver-card {

    :deep(.el-card__header) {
        background-color: #b3e19d !important;
    }
}

.address-not-available {
    display: block;
    text-align: right;
    font-size: 0.8em;
    color: red;
    line-height: 0.8em;
}

.address-auto-span {
    font-weight: bolder;
    text-align: center;
    width: 100%;
    margin-top: -20px;
    margin-bottom: -20px;
    padding-right: 10px;
}
</style>