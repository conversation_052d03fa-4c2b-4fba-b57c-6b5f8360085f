<script setup>
import DetailForm from '../DetailForm/index.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const { proxy } = getCurrentInstance()

const props = defineProps({
    // eslint-disable-next-line vue/valid-define-props
    ...DetailForm.props,
    modelValue: {
        type: Boolean,
        default: false
    },
    canUpdate: {
        type: Boolean,
        default: null
    },
    canReview: {
        type: <PERSON>olean,
        default: null
    },
})

const emit = defineEmits(['update:modelValue', 'success'])

let myVisible = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})

const title = computed(() => props.id == '' ? t('operations.add') : t('operations.edit'))

function onSubmit() {
    // submit() 为组件内部方法
    proxy.$refs['form'].submit(() => {
        emit('success')
        onCancel()
    })
}

function onReset() {
    proxy.$refs['form'].reset()
}

function onApprove() {
    proxy.$refs['form'].approve(
        () => {
            emit('success')
            onCancel()
        }
    )

}

function onReject() {
    proxy.$refs['form'].reject(
        () => {
            emit('success')
            onCancel()
        }
    )

}

function onCancel() {
    myVisible.value = false
}
</script>

<template>
    <div>
        <el-dialog v-model="myVisible" :title="title" width="600px" :close-on-click-modal="false" append-to-body
            destroy-on-close show-close>
            <DetailForm ref="form" v-bind="$props" />
            <template #footer>
                <el-row justify="space-between">
                    <el-col :span="8">
                        <el-button type="success" @click="onApprove" v-if="props.canReview">
                            {{ $t('operations.approve') }}
                        </el-button>
                        <el-button type="warning" @click="onReject" v-if="props.canReview">
                            {{ $t('operations.reject') }}
                        </el-button>
                    </el-col>
                    <el-col :span="8">
                        <el-button type="warning" plain @click="onReset" v-if="props.canUpdate">
                            {{ $t('operations.reset') }}
                        </el-button>
                        <el-button type="primary" @click="onSubmit" v-if="props.canUpdate">
                            {{ $t('operations.save') }}
                        </el-button>
                    </el-col>
                </el-row>

            </template>
        </el-dialog>
    </div>
</template>