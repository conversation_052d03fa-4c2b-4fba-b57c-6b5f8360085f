<script setup>
import { addCostAttribution, updateCostAttribution } from '@/api/modules/statistics'

const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    defaultAmount: {
        type: Number,
        default: 0
    },
    defaultHst: {
        type: Number,
        default: 0
    },
    isAvailable: {
        type: Boolean,
        default: true
    },
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: props.name,
        is_available: props.isAvailable,
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    },
    defaultAmount: 0,
    defaultHst: 0,
})

watch(() => props.defaultAmount, (val) => {
    console.log(val);

    data.value.defaultAmount = Number((val / 100).toFixed(2))
},
    { immediate: true }
)

watch(() => props.defaultHst, (val) => {
    console.log(val);

    data.value.defaultHst = Number((val / 100).toFixed(2))
},
    { immediate: true }
)



defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.default_amount = Math.round(data.value.defaultAmount * 100)
        params.default_hst = Math.round(data.value.defaultHst * 100)
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addCostAttribution(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateCostAttribution(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.fee')">
                <el-input-number v-model="data.defaultAmount" placeholder="请输入标题" :min="0.00" :precision="2"
                    :step="1" />
            </el-form-item>
            <el-form-item :label="$t('fields.tax')">
                <el-input-number v-model="data.defaultHst" placeholder="请输入标题" :min="0.00" :precision="2" :step="1" />
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')">
                <el-switch v-model="data.form.is_available" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>
