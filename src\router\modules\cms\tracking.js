/*
* Author: <EMAIL>'
* Date: '2024-06-06 21:36:23'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/cms/tracking.js'
* File: 'tracking.js'
* Version: '1.0.0'
*/



const Layout = () => import('@/layout/index.vue')

export default {
    path: '/cms/tracking',
    component: Layout,
    redirect: '/cms/tracking/status',
    name: 'cmsTracking',
    meta: {
        title: '包裹跟踪',
        icon: 'carbon:image-service',
        auth: ['super'],
        i18n: 'route.cms.tracking.title'
    },
    children: [
        {
            path: 'status',
            name: 'cmsTrackingStatus',
            component: () => import('@/views/cms/tracking/status/list.vue'),
            meta: {
                title: 'Status',
                icon: 'tabler:message-2-up',
                activeMenu: '/cms/tracking/status',
                i18n: 'route.cms.tracking.status',
                auth: ['super']
            }
        },
    ]
}
