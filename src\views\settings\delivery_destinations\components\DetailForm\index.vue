<script setup>
import api from '@/api'
import { addDeliveryDestination, updateDeliveryDestination } from '@/api/modules/settings'
const props = defineProps({
    row: {
        type: Object,
        default: {
            id: ''
        }
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: props.row,
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        en: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        desc_en: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addDeliveryDestination(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateDeliveryDestination(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="160px">
            <el-form-item :label="$t('fields.priority')" prop="priority">
                <el-input-number v-model="data.form.priority" placeholder="请输入标题" :min="10" :step="1" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.en')" prop="en">
                <el-input v-model="data.form.en" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.descEn')" prop="desc_en">
                <el-input v-model="data.form.desc_en" placeholder="请输入标题" type="textarea" :rows="5" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.zh')" prop="zh">
                <el-input v-model="data.form.zh" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.descZh')" prop="desc_zh">
                <el-input v-model="data.form.desc_zh" placeholder="请输入标题" type="textarea" :rows="5" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.zhTw')" prop="zh_tw">
                <el-input v-model="data.form.zh_tw" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.descZhTw')" prop="desc_zh_tw">
                <el-input v-model="data.form.desc_zh_tw" placeholder="请输入标题" type="textarea" :rows="5" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.fr')" prop="fr">
                <el-input v-model="data.form.fr" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.descFr')" prop="desc_fr">
                <el-input v-model="data.form.desc_fr" placeholder="请输入标题" type="textarea" :rows="5" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.es')" prop="es">
                <el-input v-model="data.form.es" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.deliveryDestinations.fields.descEs')" prop="desc_es">
                <el-input v-model="data.form.desc_es" placeholder="请输入标题" type="textarea" :rows="5" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
