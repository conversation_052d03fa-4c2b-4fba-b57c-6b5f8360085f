<script setup name="DeliveryAdminOrdersList">
import { CreditCard, DocumentAdd } from '@element-plus/icons-vue'
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { getAdminOrders, generatePaymentOrder } from '@/api/modules/delivery'
import { currencyFormatter } from '@/utils/formatter'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        sn: null,
        user__name__icontains: null,
        admin__name__icontains: null,
        created_at__gte: null,
        created_at__lte: null,
    },
    // 列表数据
    dataList: []
})

const searchBarCollapsed = ref(0)

// Filters
function resetFilters() {
    data.value.search =
    {
        sn: null,
        user__name__icontains: null,
        admin__name__icontains: null,
        created_at__gte: null,
        created_at__lte: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
    if (data.value.formMode === 'router') {
        eventBus.on('get-data-list', () => {
            getDataList()
        })
    }
})

onBeforeUnmount(() => {
    if (data.value.formMode === 'router') {
        eventBus.off('get-data-list')
    }
})

// Api
function getDataList() {
    data.value.loading = true
    let params = getParams({ filters: JSON.stringify(data.value.search) })
    getAdminOrders(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.order_list
        pagination.value.total = res.data.total
    })
}

function generatePo(id) {
    let params = {
        id: id
    }
    generatePaymentOrder(params).then(res => {
        if (res.data.errCode === 365) {
            getDataList()
        }
    })
}

function pay(id) {
    let params = {
        id: id
    }
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'newDeliveryOrderForm'
    })
}

function onDetail(row) {
    router.push({
        name: 'deliveryAdminOrderDetail',
        query: {
            id: row.id
        }
    })
}
</script>
<template>
    <div>
        <page-header :title="($t('delivery.adminOrders.title'))" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.orders.fields.no')">
                                        <el-input v-model="data.search.sn"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.no') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.orders.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.adminOrders.fields.admin')">
                                        <el-input v-model="data.search.admin__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.adminOrders.fields.admin') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>

                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('fields.createdAtMin')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('fields.createdAtMax')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onDetail" @sort-change="sortChange">
                <el-table-column prop="sn" :label="$t('fields.no')" sortable="custom" />
                <el-table-column prop="user.name" :label="$t('user.fields.name')" />
                <el-table-column prop="quantity" :label="$t('fields.quantity')" align="center" sortable="custom" />
                <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter"
                    sortable="custom" />
                <el-table-column prop="total_paid" :label="$t('delivery.orders.fields.paid')" :formatter="currencyFormatter"
                    sortable="custom" />
                <el-table-column prop="admin.name" :label="$t('fields.admin')" width="160" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" v-if="!scope.row.paymentOrderGenerated"
                            :content="$t('delivery.adminOrders.operations.generatePaymentOrder')" placement="top-start">
                            <el-button type="primary" :icon="DocumentAdd" circle size="small"
                                @click="generatePo(scope.row.id)" />
                        </el-tooltip>
                        <el-tooltip v-if="scope.row.paymentOrderGenerated && !scope.row.isPaid" class="box-item"
                            :content="$t('delivery.adminOrders.operations.pay')" placement="top-start">
                            <el-button type="success" :icon="CreditCard" circle size="small" @click="pay(scope.row.id)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
