const Layout = () => import('@/layout/index.vue')

export default {
    path: '/delivery/timetables',
    component: Layout,
    redirect: '/delivery/timetables',
    name: 'deliveryTimetables',
    meta: {
        title: '时间设置',
        icon: 'mdi:timetable',
        auth: ['super'],
        i18n: 'route.delivery.timetables.title'
    },
    children: [
        {
            path: 'pickup-times',
            name: 'deliveryPickupTimes',
            component: () => import('@/views/delivery/timetables/pickup_times/list.vue'),
            meta: {
                title: '取货时间',
                icon: 'icon-park-outline:download',
                i18n: 'route.delivery.timetables.pickupTimes',
                activeMenu: '/delivery/timetables/pickup-times',
                auth: ['super'],
                cache: ['deliveryPickupTimeDetail']
            }
        },
        {
            path: 'pickup-times/detail',
            name: 'deliveryPickupTimeDetail',
            component: () => import('@/views/delivery/timetables/pickup_times/detail.vue'),
            meta: {
                title: '取货时间',
                i18n: 'route.delivery.timetables.pickupTimes',
                activeMenu: '/delivery/timetables/pickup-times',
                auth: ['super'],
                sidebar: false
            }
        },
        {
            path: 'delivery-times',
            name: 'deliveryDeliveryTimes',
            component: () => import('@/views/delivery/timetables/delivery_times/list.vue'),
            meta: {
                title: '送货时间',
                icon: 'icon-park-outline:upload',
                i18n: 'route.delivery.timetables.deliveryTimes',
                activeMenu: '/delivery/timetables/delivery-times',
                auth: ['super'],
                cache: ['deliveryDeliveryTimeDetail']
            }
        },
        {
            path: 'delivery-times/detail',
            name: 'deliveryDeliveryTimeDetail',
            component: () => import('@/views/delivery/timetables/delivery_times/detail.vue'),
            meta: {
                title: '送货时间',
                i18n: 'route.delivery.timetables.deliveryTimes',
                activeMenu: '/delivery/timetables/delivery-times',
                auth: ['super'],
                sidebar: false
            }
        },

    ]
}
