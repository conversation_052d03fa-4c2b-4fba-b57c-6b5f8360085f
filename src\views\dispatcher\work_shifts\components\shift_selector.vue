<script setup>
    import { getAllWorkShifts } from '@/api/modules/dispatcher'

    const props = defineProps({
        modelValue: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        validOnly: {
            type: Boolean,
            default: true,
        },
    })

    const allShifts = ref([])

    onMounted(() => {
        getShiftList()
    })

    function getShiftList() {
        getAllWorkShifts().then(res => {
            allShifts.value = res.data
        })
    }

    const emit = defineEmits(['update:modelValue'])

    let shift = computed({
        get: function () {
            return props.modelValue
        },
        set: function (val) {
            emit('update:modelValue', val)
        }
    })

</script>

<template>
    <el-select v-model="shift" placeholder="Select" :disabled="props.disabled" clearable filterable
        style="width: 100%; min-width: 250px;">
        <el-option v-for="item in allShifts" :key="item" :label="item.name" :value="item.id" />
    </el-select>
    {{ shift?.name }}

</template>
