<script setup name="DeliverySupplierStoreDetail">
import api from '@/api'
import {
    getDeliverySupplierStores,
    addDeliverySupplierStore,
    updateDeliverySupplierStore,
    getPackageTypeList,
    getSizeWeightList
} from '@/api/modules/delivery'
import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    },
    zones: {
        type: Array,
        default: []
    },
    supplierId: {
        type: String,
        default: ''
    }
})

const packageTypes = ref([])
const sizeWeights = ref([])

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        priority: 10,
        address: {
            address: '',
            postal_code: '',
            city: '',
            province: ''
        },
        zones: [],
    },
    zones: props.zones,
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != null && data.value.form.id != '') {
        getInfo()
    }
    getPackageTypeList().then(res => packageTypes.value = res.data)
    getSizeWeightList().then(res => sizeWeights.value = res.data)

})

function getInfo() {
    data.value.loading = true
    getDeliverySupplierStores({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function _onClearAddress() {
    data.value.form.address = {}
}

function _onRetrieveAddress(val) {
    data.value.form.address.address = val.Line1
    data.value.form.address.city = val.City
    data.value.form.address.province = val.ProvinceName
    data.value.form.address.postal_code = val.PostalCode
    data.value.form.address.id = val.Id
}


defineExpose({
    submit(callback) {
        var params = JSON.parse(JSON.stringify(data.value.form))
        params.address = data.value.form.address?.id
        params.supplier_id = props.supplierId

        if (data.value.form.id == null || data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addDeliverySupplierStore(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateDeliverySupplierStore(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.priority')" prop="priority">
                <el-input-number v-model="data.form.priority" placeholder="请输入标题" :step="10" />
            </el-form-item>
            <el-form-item :label="$t('fields.sku')" prop="sku">
                <el-input v-model="data.form.sku" placeholder="请输入标题" maxlength="10" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" rows="5" maxlength="200"
                    show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('user.fields.address')" prop="address">
                <AddressSelect :address="data.form.address.address" :city="data.form.address.city"
                    :province="data.form.address.province" :postal-code="data.form.address.postal_code"
                    :on-clear="_onClearAddress" @success="_onRetrieveAddress" />
                <el-space>
                    <span>{{ data.form.address.postal_code }}</span>
                    <span>{{ data.form.address.city }}</span>
                    <span>{{ data.form.address.province }}</span>
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('fields.contact')" prop="contact">
                <el-input v-model="data.form.contact" placeholder="请输入标题" maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('user.fields.phoneNumber')" prop="phone_number" maxlength="11" show-word-limit>
                <el-input v-model="data.form.phone_number" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.email')" prop="email">
                <el-input v-model="data.form.email" placeholder="请输入标题" maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.zones')">
                <el-select v-model="data.form.zones" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.services.fields.zones') })" clearable>
                    <el-option v-for="item in props.zones" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.supplierStores.fields.packageTypes')">
                <el-select v-model="data.form.package_types" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.supplierStores.fields.packageTypes') })"
                    clearable>
                    <el-option v-for="item in packageTypes" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.supplierStores.fields.sizeWeights')">
                <el-select v-model="data.form.size_weights" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.supplierStores.fields.sizeWeights') })"
                    clearable>
                    <el-option v-for="item in sizeWeights" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')" prop="priority">
                <el-switch v-model="data.form.is_available" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
