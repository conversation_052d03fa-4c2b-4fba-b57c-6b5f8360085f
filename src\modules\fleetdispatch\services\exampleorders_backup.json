{"orders": [{"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L3R 1G6", "address_1": "1-110 Torbay Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503201020288807", "city": "Markham", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "安速空运", "email": "<EMAIL>", "telephone": "4379254588", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503201020288807", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "1-110 Torbay Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L3R 1G6", "sender_city": "Markham", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "安速空运", "sender_email": "<EMAIL>", "sender_telephone": "4379254588", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8187644, "longitude": -79.3453229, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-7836-9451", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 598, "distance": 6495, "receiver_name": "<PERSON><PERSON><PERSON>", "receiver_company_name": "16757", "receiver_telephone": "6478928351", "receiver_address_1": "106 Byng Ave", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M2N 4K4", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "M2J 0E8", "address_1": "128 Fairview Mall Dr", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503200950334787", "city": "North York", "province": "ON", "country": "Canada", "name": "duoduo", "company_name": "duoduo", "email": "<EMAIL>", "telephone": "16479784023", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503200950334787", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "duoduo", "sender_address_1": "128 Fairview Mall Dr", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "M2J 0E8", "sender_city": "North York", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "duoduo", "sender_email": "<EMAIL>", "sender_telephone": "16479784023", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.77998, "longitude": -79.3449211, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-4192-8375", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 33, "distance": 40, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "6475448135", "receiver_address_1": "81 Jopling Ave N", "receiver_address_2": "", "receiver_city": "Etobicoke", "receiver_province": "ON", "receiver_postcode": "M9B 4G5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5B 0J8", "address_1": "510 Curran Pl", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192127243909", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "King", "company_name": "", "email": "<EMAIL>", "telephone": "4165628088", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192127243909", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "King", "sender_address_1": "510 Curran Pl", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5B 0J8", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "", "sender_telephone": "4165628088", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5856931, "longitude": -79.6464523, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6723-1945", "name": "Ada", "email": "", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 568, "distance": 7234, "receiver_name": "Ada", "receiver_company_name": "王府井鼎鲜超市服务台", "receiver_telephone": "4168822806", "receiver_address_1": "9390 Woodbine Ave", "receiver_address_2": "", "receiver_city": "Markham", "receiver_province": "ON", "receiver_postcode": "L6C 0M5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5L 5S2", "address_1": "2828 Council Ring Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503182152032344", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "追味酒酿米糕", "email": "<EMAIL>", "telephone": "16479962688", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503182152032344", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2828 Council Ring Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5L 5S2", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "追味酒酿米糕", "sender_email": "<EMAIL>", "sender_telephone": "16479962688", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5316842, "longitude": -79.6912268, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-9284-5610", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "4373491362", "receiver_address_1": "90 Queens Wharf Rd", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M5V 0J4", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5L 5S2", "address_1": "2828 Council Ring Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503182152016411", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "追味酒酿米糕", "email": "<EMAIL>", "telephone": "16479962688", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503182152016411", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2828 Council Ring Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5L 5S2", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "追味酒酿米糕", "sender_email": "<EMAIL>", "sender_telephone": "16479962688", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5316842, "longitude": -79.6912268, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-3156-7492", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON><PERSON><PERSON>", "receiver_company_name": "", "receiver_telephone": "12498751991", "receiver_address_1": "175 Wynford Dr", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M3C 1J3", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5L 5S2", "address_1": "2828 Council Ring Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503182152022090", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "追味酒酿米糕", "email": "<EMAIL>", "telephone": "16479962688", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503182152022090", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2828 Council Ring Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5L 5S2", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "追味酒酿米糕", "sender_email": "<EMAIL>", "sender_telephone": "16479962688", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5316842, "longitude": -79.6912268, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6841-3972", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON><PERSON>", "receiver_company_name": "", "receiver_telephone": "4375186904", "receiver_address_1": "100 Harbour St", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M5J 0B5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5L 5S2", "address_1": "2828 Council Ring Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503182152010574", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "追味酒酿米糕", "email": "<EMAIL>", "telephone": "16479962688", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503182152010574", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2828 Council Ring Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5L 5S2", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "追味酒酿米糕", "sender_email": "<EMAIL>", "sender_telephone": "16479962688", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5316842, "longitude": -79.6912268, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-5826-9384", "name": "<PERSON><PERSON> Ge", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON><PERSON> Ge", "receiver_company_name": "", "receiver_telephone": "6479362033", "receiver_address_1": "10 Delisle Ave", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M4V 3C6", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5L 5S2", "address_1": "2828 Council Ring Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503182152027214", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "追味酒酿米糕", "email": "<EMAIL>", "telephone": "16479962688", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503182152027214", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2828 Council Ring Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5L 5S2", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "追味酒酿米糕", "sender_email": "<EMAIL>", "sender_telephone": "16479962688", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5316842, "longitude": -79.6912268, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-4758-1623", "name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 778, "distance": 8680, "receiver_name": "<PERSON><PERSON><PERSON><PERSON>", "receiver_company_name": "", "receiver_telephone": "4166661625", "receiver_address_1": "1 <PERSON>", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M3C 4B9", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6H 0V6", "address_1": "56 Ballmer Trail", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192238209339", "city": "Oakville", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "", "email": "<EMAIL>", "telephone": "14168039029", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192238209339", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "56 Ballmer Trail", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6H 0V6", "sender_city": "Oakville", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "14168039029", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.4864761, "longitude": -79.7356871, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6689-7412", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 795, "distance": 6977, "receiver_name": "<PERSON><PERSON>", "receiver_company_name": "", "receiver_telephone": "**********", "receiver_address_1": "109 White's Hill Ave", "receiver_address_2": "", "receiver_city": "Markham", "receiver_province": "ON", "receiver_postcode": "L6B 1C2", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6M 1V7", "address_1": "1225 Bonnybank Crt", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503181905176676", "city": "Oakville", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "", "email": "<EMAIL>", "telephone": "**********", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503181905176676", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "1225 Bonnybank Crt", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6M 1V7", "sender_city": "Oakville", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "**********", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.4403987, "longitude": -79.7259439, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-9782-3451", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 1818, "distance": 29245, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "2269772891", "receiver_address_1": "15 Holmes Ave", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M2N 0L4", "receiver_country": "Canada"}, {"type": "D", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4W 3Y4", "address_1": "1044 Ronsa Crt", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503181539095329", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "sunny saini", "company_name": "mediline enterprises inc", "email": "<EMAIL>", "telephone": "6472845446", "need_pick_up": 0, "packages": 1, "packagesDetail": [{"ref": "DO202503181539095329", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "351 Manhattan Dr", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L3P 7L7", "sender_city": "Unionville", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "EACU Medical Instruments Inc", "sender_email": "<EMAIL>", "sender_telephone": "16479385058", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T17:00:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T17:00:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.6399589, "longitude": -79.6491111, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-3841-7592", "name": "sunny saini", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 479, "distance": 4020, "receiver_name": "sunny saini", "receiver_company_name": "mediline enterprises inc", "receiver_telephone": "6472845446", "receiver_address_1": "1044 Ronsa Crt", "receiver_address_2": "", "receiver_city": "Mississauga", "receiver_province": "ON", "receiver_postcode": "L4W 3Y4", "receiver_country": "Canada"}, {"type": "D", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4Z 2Z1", "address_1": "Gr05-25 Watline Ave", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503181758128784", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "工商银行", "company_name": "中国工行密西沙加分行", "email": "<EMAIL>", "telephone": "4166072001", "need_pick_up": 0, "packages": 1, "packagesDetail": [{"ref": "DO202503181758128784", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2-1050 McNicoll Ave", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "M1W 2L8", "sender_city": "Scarborough", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "Max Printing Center", "sender_email": "<EMAIL>", "sender_telephone": "14165028386", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T17:00:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T17:00:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.6216869, "longitude": -79.6719782, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6792-3451", "name": "工商银行", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 629, "distance": 6411, "receiver_name": "工商银行", "receiver_company_name": "中国工行密西沙加分行", "receiver_telephone": "4166072001", "receiver_address_1": "Gr05-25 Watline Ave", "receiver_address_2": "", "receiver_city": "Mississauga", "receiver_province": "ON", "receiver_postcode": "L4Z 2Z1", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L3X 1V7", "address_1": "625 Brooker Ridge", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191812360680", "city": "Newmarket", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "枫叶国际", "email": "<EMAIL>", "telephone": "4165430429", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191812360680", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "625 Brooker Ridge", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L3X 1V7", "sender_city": "Newmarket", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "枫叶国际", "sender_email": "<EMAIL>", "sender_telephone": "4165430429", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 44.0309104, "longitude": -79.4445671, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6742-9183", "name": "默默", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 328, "distance": 2324, "receiver_name": "默默", "receiver_company_name": "", "receiver_telephone": "6476734766", "receiver_address_1": "5 Denewood Cres", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M3B 1M6", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L3T 5W5", "address_1": "385 John <PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192322393690", "city": "Thornhill", "province": "ON", "country": "Canada", "name": "Deer Cake thornhill", "company_name": "deer cake thornhill", "email": "<EMAIL>", "telephone": "9056042212", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192322393690", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "Deer Cake thornhill", "sender_address_1": "385 John <PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L3T 5W5", "sender_city": "Thornhill", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "deer cake thornhill", "sender_email": "<EMAIL>", "sender_telephone": "9056042212", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8198335, "longitude": -79.389054, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-8937-2156", "name": "FreshPro Foodmart", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 924, "distance": 9966, "receiver_name": "FreshPro Foodmart", "receiver_company_name": "", "receiver_telephone": "19055088887", "receiver_address_1": "10488 Yonge St", "receiver_address_2": "", "receiver_city": "Richmond Hill", "receiver_province": "ON", "receiver_postcode": "L4C 3C7", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4J 0K1", "address_1": "9128 Bathurst St", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191809349882", "city": "Thornhill", "province": "ON", "country": "Canada", "name": "Lea <PERSON>", "company_name": "Print Paper Production", "email": "<EMAIL>", "telephone": "6475243363", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191809349882", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "Lea <PERSON>", "sender_address_1": "9128 Bathurst St", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L4J 0K1", "sender_city": "Thornhill", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "Print Paper Production", "sender_email": "<EMAIL>", "sender_telephone": "6475243363", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8442744, "longitude": -79.4583278, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-2647-8915", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 393, "distance": 2921, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "6476227895", "receiver_address_1": "323-366 Adelaide St E", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M5A 3X9", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4A 4P7", "address_1": "137 Moraine Hill Dr", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192314256039", "city": "Maple", "province": "ON", "country": "Canada", "name": "IICE", "company_name": "", "email": "<EMAIL>", "telephone": "6478926488", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192314256039", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "IICE", "sender_address_1": "137 Moraine Hill Dr", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L4A 4P7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6478926488", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8565412, "longitude": -79.474511, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-3614-7925", "name": "TONG XU", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 459, "distance": 3547, "receiver_name": "TONG XU", "receiver_company_name": "", "receiver_telephone": "4379832690", "receiver_address_1": "1606-5793 Yonge St", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M2M 0A9", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 0X9", "address_1": "24 Maple Valley Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503200957164949", "city": "Maple", "province": "ON", "country": "Canada", "name": "Silk Factory Store", "company_name": "", "email": "<EMAIL>", "telephone": "6475464279", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503200957164949", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "Silk Factory Store", "sender_address_1": "24 Maple Valley Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 0X9", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475464279", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8440077, "longitude": -79.488558, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-1765-9824", "name": "<PERSON><PERSON>（代收850）", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 1047, "distance": 9875, "receiver_name": "<PERSON><PERSON>（代收850）", "receiver_company_name": "", "receiver_telephone": "6472310929", "receiver_address_1": "20 Swan Park Rd", "receiver_address_2": "", "receiver_city": "Markham", "receiver_province": "ON", "receiver_postcode": "L6E 1Z5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4H 4C7", "address_1": "102 Headwind Blvd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191226037645", "city": "Woodbridge", "province": "ON", "country": "Canada", "name": "<PERSON><PERSON>", "company_name": "<PERSON><PERSON>", "email": "<EMAIL>", "telephone": "6476407366", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191226037645", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON><PERSON>", "sender_address_1": "102 Headwind Blvd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L4H 4C7", "sender_city": "Woodbridge", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "<PERSON><PERSON>", "sender_email": "<EMAIL>", "sender_telephone": "6476407366", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8504661, "longitude": -79.572921, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6319-4782", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "2368827588", "receiver_address_1": "2507-203 College St", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M5T 0C8", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4H 4C7", "address_1": "102 Headwind Blvd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191226022975", "city": "Woodbridge", "province": "ON", "country": "Canada", "name": "<PERSON><PERSON>", "company_name": "<PERSON><PERSON>", "email": "<EMAIL>", "telephone": "6476407366", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191226022975", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON><PERSON>", "sender_address_1": "102 Headwind Blvd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L4H 4C7", "sender_city": "Woodbridge", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "<PERSON><PERSON>", "sender_email": "<EMAIL>", "sender_telephone": "6476407366", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8504661, "longitude": -79.572921, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-7354-9216", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "16478800760", "receiver_address_1": "4-199 St Clair Ave E", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M4T 1N9", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4H 4C7", "address_1": "102 Headwind Blvd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191226044936", "city": "Woodbridge", "province": "ON", "country": "Canada", "name": "<PERSON><PERSON>", "company_name": "<PERSON><PERSON>", "email": "<EMAIL>", "telephone": "6476407366", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191226044936", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON><PERSON>", "sender_address_1": "102 Headwind Blvd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L4H 4C7", "sender_city": "Woodbridge", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "<PERSON><PERSON>", "sender_email": "<EMAIL>", "sender_telephone": "6476407366", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8504661, "longitude": -79.572921, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-1932-8475", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON><PERSON>", "receiver_company_name": "", "receiver_telephone": "6476869478", "receiver_address_1": "1604-159 Wellesley St E", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M4Y 0H5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L4H 4C7", "address_1": "102 Headwind Blvd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191226051488", "city": "Woodbridge", "province": "ON", "country": "Canada", "name": "<PERSON><PERSON>", "company_name": "<PERSON><PERSON>", "email": "<EMAIL>", "telephone": "6476407366", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191226051488", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON><PERSON>", "sender_address_1": "102 Headwind Blvd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L4H 4C7", "sender_city": "Woodbridge", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "<PERSON><PERSON>", "sender_email": "<EMAIL>", "sender_telephone": "6476407366", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8504661, "longitude": -79.572921, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-4837-9521", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 1123, "distance": 11273, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "6477652916", "receiver_address_1": "3910-1080 Bay St", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M5S 0A5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 0G1", "address_1": "28 <PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192046081703", "city": "Maple", "province": "ON", "country": "Canada", "name": "<PERSON><PERSON><PERSON><PERSON>", "company_name": "", "email": "<EMAIL>", "telephone": "6477097999", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192046081703", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON><PERSON><PERSON><PERSON>", "sender_address_1": "28 <PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 0G1", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6477097999", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8778074, "longitude": -79.4788053, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-8329-4617", "name": "glory", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 274, "distance": 1742, "receiver_name": "glory", "receiver_company_name": "", "receiver_telephone": "6476770386", "receiver_address_1": "907 Memorial Cir", "receiver_address_2": "", "receiver_city": "Newmarket", "receiver_province": "ON", "receiver_postcode": "L3X 0H9", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646310248", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646310248", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-5478-1923", "name": "YATONG", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "YATONG", "receiver_company_name": "", "receiver_telephone": "6479690819", "receiver_address_1": "27 Breda Crt", "receiver_address_2": "", "receiver_city": "Richmond Hill", "receiver_province": "ON", "receiver_postcode": "L4C 6E1", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646299246", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646299246", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-3965-8714", "name": "YUNA", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "YUNA", "receiver_company_name": "", "receiver_telephone": "6478235889", "receiver_address_1": "9 Lancelot Lane", "receiver_address_2": "", "receiver_city": "Markham", "receiver_province": "ON", "receiver_postcode": "L6C 2G4", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646305429", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646305429", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-9275-3146", "name": "LIL", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "LIL", "receiver_company_name": "", "receiver_telephone": "4379851526", "receiver_address_1": "22 Olive Ave", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M2N 7G6", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646320110", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646320110", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6731-9284", "name": "CLORIS-尽量早送", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "CLORIS-尽量早送", "receiver_company_name": "", "receiver_telephone": "5197011718", "receiver_address_1": "2902-7890 Jane St", "receiver_address_2": "", "receiver_city": "Concord", "receiver_province": "ON", "receiver_postcode": "L4K 0K9", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646329893", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646329893", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-1498-7326", "name": "龙龙-3点前送", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "龙龙-3点前送", "receiver_company_name": "", "receiver_telephone": "6478218888", "receiver_address_1": "25 Adra Grado Way", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M2J 0H6", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646315137", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646315137", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-8216-4593", "name": "K-车库门#98", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "K-车库门#98", "receiver_company_name": "", "receiver_telephone": "4373502098", "receiver_address_1": "203-269 Georgian Dr", "receiver_address_2": "", "receiver_city": "Oakville", "receiver_province": "ON", "receiver_postcode": "L6H 0L1", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646324830", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646324830", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-3867-5912", "name": "WSYU", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "WSYU", "receiver_company_name": "", "receiver_telephone": "6478874770", "receiver_address_1": "15 Credit Lane", "receiver_address_2": "", "receiver_city": "Richmond Hill", "receiver_province": "ON", "receiver_postcode": "L4E 1G9", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6A 4X7", "address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191646335196", "city": "Maple", "province": "ON", "country": "Canada", "name": "新街口小吃", "company_name": "", "email": "<EMAIL>", "telephone": "6475284354", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191646335196", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "新街口小吃", "sender_address_1": "46 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6A 4X7", "sender_city": "Maple", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "<EMAIL>", "sender_telephone": "6475284354", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8699263, "longitude": -79.4659158, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-7195-2843", "name": "CRYSTAL", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "CRYSTAL", "receiver_company_name": "", "receiver_telephone": "4168802238", "receiver_address_1": "16 <PERSON>", "receiver_address_2": "", "receiver_city": "Unionville", "receiver_province": "ON", "receiver_postcode": "L3R 7V5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6C 0M4", "address_1": "9980 Kennedy Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191448500963", "city": "Markham", "province": "ON", "country": "Canada", "name": "MILAN TAM", "company_name": "BLUE SKIES PHARMACY IDA", "email": "<EMAIL>", "telephone": "19058888833", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191448500963", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "MILAN TAM", "sender_address_1": "9980 Kennedy Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6C 0M4", "sender_city": "Markham", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "BLUE SKIES PHARMACY IDA", "sender_email": "<EMAIL>", "sender_telephone": "19058888833", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8963909, "longitude": -79.319814, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-9514-2378", "name": "yu, chui ngai", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "yu, chui ngai", "receiver_company_name": "", "receiver_telephone": "6472911288", "receiver_address_1": "56 Lark Cres", "receiver_address_2": "", "receiver_city": "Richmond Hill", "receiver_province": "ON", "receiver_postcode": "L4S 2N2", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L6C 0M4", "address_1": "9980 Kennedy Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503191447541464", "city": "Markham", "province": "ON", "country": "Canada", "name": "MILAN TAM", "company_name": "BLUE SKIES PHARMACY IDA", "email": "<EMAIL>", "telephone": "19058888833", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503191447541464", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "MILAN TAM", "sender_address_1": "9980 Kennedy Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L6C 0M4", "sender_city": "Markham", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "BLUE SKIES PHARMACY IDA", "sender_email": "<EMAIL>", "sender_telephone": "19058888833", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8963909, "longitude": -79.319814, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-8471-3625", "name": "Huynh, Hoa Thai", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 179, "distance": 944, "receiver_name": "Huynh, Hoa Thai", "receiver_company_name": "", "receiver_telephone": "9057711787", "receiver_address_1": "110 Westmount Blvd", "receiver_address_2": "", "receiver_city": "Thornhill", "receiver_province": "ON", "receiver_postcode": "L4J 7W5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "M5V 1B7", "address_1": "600 Fleet St", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192147260535", "city": "Toronto", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "Soft Dough Co.", "email": "<EMAIL>", "telephone": "14373138011", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192147260535", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "600 Fleet St", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "M5V 1B7", "sender_city": "Toronto", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "Soft Dough Co.", "sender_email": "<EMAIL>", "sender_telephone": "14373138011", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.636663, "longitude": -79.4004661, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-6341-9782", "name": "<PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 1048, "distance": 10044, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "19059223873", "receiver_address_1": "171 Toynbee Trail", "receiver_address_2": "", "receiver_city": "Scarborough", "receiver_province": "ON", "receiver_postcode": "M1E 1G5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "M1B 1N8", "address_1": "37 Burkwood Cres", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192208320282", "city": "Scarborough", "province": "ON", "country": "Canada", "name": "Tindahan To / Tin", "company_name": "Tindahanto", "email": "<EMAIL>", "telephone": "19059222898", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192208320282", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "Tindahan To / Tin", "sender_address_1": "37 Burkwood Cres", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "M1B 1N8", "sender_city": "Scarborough", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "Tindahanto", "sender_email": "<EMAIL>", "sender_telephone": "19059222898", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8020314, "longitude": -79.2132077, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-5428-3176", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON><PERSON><PERSON>", "receiver_company_name": "", "receiver_telephone": "6478396496", "receiver_address_1": "403-5 Dufresne Crt", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M3C 1B7", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "M1B 1N8", "address_1": "37 Burkwood Cres", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192351092389", "city": "Scarborough", "province": "ON", "country": "Canada", "name": "Tindahan To / Tin", "company_name": "Tindahanto", "email": "<EMAIL>", "telephone": "19059222898", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192351092389", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "Tindahan To / Tin", "sender_address_1": "37 Burkwood Cres", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "M1B 1N8", "sender_city": "Scarborough", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "Tindahanto", "sender_email": "<EMAIL>", "sender_telephone": "19059222898", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8020314, "longitude": -79.2132077, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "EC-2025-8953-7142", "name": "<PERSON>lyn lee", "email": "<EMAIL>", "system_code": "es1"}, "a_scan_at": **********, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON>lyn lee", "receiver_company_name": "", "receiver_telephone": "4375996454", "receiver_address_1": "506 Dawes Rd", "receiver_address_2": "", "receiver_city": "East York", "receiver_province": "ON", "receiver_postcode": "M4B 2G2", "receiver_country": "Canada"}]}