<script setup name="{{ properCase componentName }}">
    import api from '@/api'
    import { Delete } from '@element-plus/icons-vue'
    import eventBus from '@/utils/eventBus'
    import { usePagination } from '@/utils/composables'
    import FormMode from './components/FormMode/index.vue'

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        /**
         * 详情展示模式
         * router 路由跳转
         * dialog 对话框
         * drawer 抽屉
         */
        formMode: 'router',
        // 详情
        formModeProps: {
            visible: false,
            id: ''
        },
        // 搜索
        search: {
            name__icontains: null,
        },
        // 批量操作
        batch: {
            enable: false,
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            name__icontains: null,
        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
        if (data.value.formMode === 'router') {
            eventBus.on('get-data-list', () => {
                getDataList()
            })
        }
    })

    onBeforeUnmount(() => {
        if (data.value.formMode === 'router') {
            eventBus.off('get-data-list')
        }
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        api.get(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        if (data.value.formMode === 'router') {
            router.push({
                name: 'routerName'
            })
        } else {
            data.value.formModeProps.id = ''
            data.value.formModeProps.visible = true
        }
    }

    function onEdit(row) {
        if (data.value.formMode === 'router') {
            router.push({
                name: 'routerName',
                query: {
                    id: row.id
                }
            })
        } else {
            data.value.formModeProps.id = row.id
            data.value.formModeProps.visible = true
        }
    }

    function onDel(row) {
        ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
            api.post({ id: row.id }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.is_available) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-header title="{{ cname }}" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    \{{$t('operations.reset')}}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    \{{$t('operations.filter')}}
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    \{{$t('operations.add')}} {{ cname }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column prop="title" label="标题" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode v-if="['dialog', 'drawer'].includes(data.formMode)" :id="data.formModeProps.id"
            v-model="data.formModeProps.visible" :mode="data.formMode" @success="getDataList" />
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>
