<script setup>
import {
    addDistributionCenter,
    getDistributionCenters,
    updateDistributionCenter,
    getHandlers,
    getZones,
    getPickupTimeList,
    getDeliveryTimeList,
    getHolidays
} from '@/api/modules/delivery';
import { getDrivers } from '@/api/modules/staffs'
import { weekdays } from '@/utils/constants'
import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        priority: 10,
        name: '',
        weekdays: [0, 1, 2, 3, 4, 5, 6],
        address: {},
        zones: [],
        handlers: [],
        drivers: [],
        pickup_times: [],
        delivery_times: [],
        holidays: []
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        priority: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        sku: [
            { message: '请输入标题', trigger: 'blur' }
        ],
        description: [
            { message: '请输入标题', trigger: 'blur' }
        ],
        address: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

const holidays = ref([])
const zones = ref([])
const drivers = ref([])
const handlers = ref([])
const pickupTimes = ref([])
const deliveryTimes = ref([])


onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getDistributionCenters({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })

    getHandlers().then(res => handlers.value = res.data)
    getZones().then(res => zones.value = res.data)
    getDrivers().then(res => drivers.value = res.data)
    getPickupTimeList().then(res => pickupTimes.value = res.data)
    getDeliveryTimeList().then(res => deliveryTimes.value = res.data)
    getHolidays().then(res => holidays.value = res.data)
}

defineExpose({
    submit(callback) {
        var params = JSON.parse(JSON.stringify(data.value.form))
        params.address = data.value.form.address?.id

        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addDistributionCenter(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateDistributionCenter(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})

function _onClearAddress() {
    data.value.form.address = {}
}

function _onRetrieveAddress(val) {
    data.value.form.address.address = val.Line1
    data.value.form.address.city = val.City
    data.value.form.address.province = val.ProvinceName
    data.value.form.address.postal_code = val.PostalCode
    data.value.form.address.id = val.Id
}
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.priority')" prop="priority">
                <el-input-number v-model="data.form.priority" placeholder="请输入标题" :step="10" />
            </el-form-item>
            <el-form-item :label="$t('fields.sku')" prop="sku">
                <el-input v-model="data.form.sku" placeholder="请输入标题" maxlength="10" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" rows="5" maxlength="200"
                    show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('user.fields.address')" prop="address">
                <AddressSelect :address="data.form.address.address" :city="data.form.address.city"
                    :province="data.form.address.province" :postal-code="data.form.address.postal_code"
                    :on-clear="_onClearAddress" @success="_onRetrieveAddress" />
                <el-space>
                    <span>{{ data.form.address.postal_code }}</span>
                    <span>{{ data.form.address.city }}</span>
                    <span>{{ data.form.address.province }}</span>
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('delivery.distributionCenters.fields.operationHours')" prop="operation_hours_from">
                <el-space>
                    <el-time-select v-model="data.form.operation_hours_from" :max-time="data.form.operation_hours_to"
                        :placeholder="$t('fields.startTime')" start="08:30" step="00:30" end="22:30" /> ~
                    <el-time-select v-model="data.form.operation_hours_to" :min-time="data.form.operation_hours_from"
                        :placeholder="$t('fields.endTime')" start="08:30" step="00:30" end="22:30" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('delivery.distributionCenters.fields.volume')" prop="volume">
                <el-input-number v-model="data.form.volume" placeholder="请输入标题" :step="100" :min="0" />
            </el-form-item>
            <el-form-item :label="$t('fields.weekdays')" prop="weekdays">
                <el-checkbox-group v-model="data.form.weekdays">
                    <el-checkbox v-for="item in weekdays" :key="item" :label="item">
                        {{ $t(`selection.weekdayShort.${item}`) }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="$t('fields.contact')">
                <el-select v-model="data.form.contact"
                    :placeholder="$t('selectPlaceHolder', { field: $t('fields.contact') })" clearable>
                    <el-option v-for="item in handlers" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.distributionCenters.fields.handlers')">
                <el-select v-model="data.form.handlers" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.distributionCenters.fields.handlers') })"
                    clearable>
                    <el-option v-for="item in handlers" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.zones')">
                <el-select v-model="data.form.zones" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.services.fields.zones') })" clearable>
                    <el-option v-for="item in zones" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.distributionCenters.fields.drivers')">
                <el-select v-model="data.form.drivers" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.distributionCenters.fields.drivers') })"
                    clearable>
                    <el-option v-for="item in drivers" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.distributionCenters.fields.pickupTimes')">
                <el-select v-model="data.form.pickup_times" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.distributionCenters.fields.pickupTimes') })"
                    clearable>
                    <el-option v-for="item in pickupTimes" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.distributionCenters.fields.deliveryTimes')">
                <el-select v-model="data.form.delivery_times" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('delivery.distributionCenters.fields.deliveryTimes') })"
                    clearable>
                    <el-option v-for="item in deliveryTimes" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>

            <el-form-item :label="$t('fields.holidays')" prop="holidays">
                <el-select v-model="data.form.holidays" :multiple="true"
                    :placeholder="$t('selectPlaceHolder', { field: $t('fields.holidays') })" clearable>
                    <el-option v-for="item in holidays" :value="item.id" :key="item.id" :label="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')" prop="priority">
                <el-switch v-model="data.form.is_available" />
            </el-form-item>

        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>