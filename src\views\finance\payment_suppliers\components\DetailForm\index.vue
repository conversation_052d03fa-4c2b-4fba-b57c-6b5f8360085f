<script setup>
import { getPaymentSuppliers, addPaymentSupplier, updatePaymentSupplier } from '@/api/modules/payment'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: null,
        code: null,
        id_1: null,
        id_2: null,
        pin: null
    },
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getPaymentSuppliers({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addPaymentSupplier(data.value.form).then((res) => {
                        if (res.data.errCode === 365) {
                            callback && callback()
                        }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updatePaymentSupplier(data.value.form).then((res) => {
                        if (res.data.errCode === 365) {
                            callback && callback()
                        }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" label-width="150px" label-suffix="：">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" :placeholder="$t('placeholder', { field: $t('fields.name') })"
                    clearable />
            </el-form-item>
            <el-form-item :label="$t('finance.paymentSupplier.fields.code')" prop="code" v-if="data.form.code">
                <el-input v-model="data.form.code" disabled />
            </el-form-item>
            <el-form-item :label="$t('finance.paymentSupplier.fields.id_1')" prop="id_1">
                <el-input v-model="data.form.id_1"
                    :placeholder="$t('placeholder', { field: $t('finance.paymentSupplier.fields.id_1') })" clearable />
            </el-form-item>
            <el-form-item :label="$t('finance.paymentSupplier.fields.id_2')" prop="id_2">
                <el-input v-model="data.form.id_2"
                    :placeholder="$t('placeholder', { field: $t('finance.paymentSupplier.fields.id_2') })" clearable />
            </el-form-item>
            <el-form-item :label="$t('finance.paymentSupplier.fields.pin')" prop="pin">
                <el-input v-model="data.form.pin"
                    :placeholder="$t('placeholder', { field: $t('finance.paymentSupplier.fields.pin') })" clearable />
            </el-form-item>
            <el-form-item :label="$t('finance.paymentSupplier.fields.baseUrl')" prop="base_url">
                <el-input v-model="data.form.base_url"
                    :placeholder="$t('placeholder', { field: $t('finance.paymentSupplier.fields.baseUrl') })" clearable />
            </el-form-item>
            <el-form-item :label="$t('finance.paymentSupplier.fields.notifyPath')" prop="notify_path">
                <el-input v-model="data.form.notify_path"
                    :placeholder="$t('placeholder', { field: $t('finance.paymentSupplier.fields.notifyPath') })"
                    clearable />
            </el-form-item>
        </el-form>
    </div>
</template>
