<script setup>
import { getVisitors, getVisitorMessages } from '@/api/modules/chat_now'
import { usePagination } from '@/utils/composables'
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: ''
    },
    messages: []

})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
        getMessages()
    }
})

function getInfo() {
    data.value.loading = true
    getVisitors({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function getMessages() {
    data.value.loading = true
    let params = getParams(
        {
            visitor_id: data.value.form.id,
            filters: JSON.stringify(data.value.search),
        }
    )
    getVisitorMessages(params).then(res => {
        data.value.messages = res.data.message_list;
        data.value.loading = false
        pagination.value.total = res.data.total

    })
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getMessages())
}

</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <el-descriptions title="Visitor Info" direction="vertical" :column="5" :size="size" border>
                <el-descriptions-item :label="$t('user.fields.name')">{{ data.form.name }}</el-descriptions-item>
                <el-descriptions-item :label="$t('user.fields.email')">{{ data.form.email }}</el-descriptions-item>
                <el-descriptions-item :label="$t('user.fields.phoneNumber')">{{ data.form.phone }}</el-descriptions-item>
                <el-descriptions-item :label="$t('user.fields.companyName')">
                    {{ data.form.company_name }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('user.fields.city')">
                    {{ data.form.city?.city }}
                </el-descriptions-item>
            </el-descriptions>
        </page-main>
        <page-main>
            <div style="height:calc(100vh - 480px);">
                <div class="messages-box">
                    <template v-for="msg in data.messages">
                        <div class="message-item-title">
                            <el-space>
                                <el-text tag="b" :type="msg.admin ? 'warning' : 'success'">
                                    {{ msg.admin ? msg.admin.name : msg.user.name }}
                                </el-text>
                                <!-- <el-tooltip content="标记为未处理" v-if="msg.type == 'csOfflineMessage' && msg.is_read">
                                    <el-button size="small" text type="primary" :icon="SuccessFilled"
                                        @click="onUnread([msg])" />
                                </el-tooltip>
                                <el-tooltip content="发送" v-if="msg.admin_name != null && msg.status == 'created'">
                                    <el-button size="small" text type="success" :icon="Promotion" @click="onSend(msg)" />
                                </el-tooltip>
                                <el-tooltip content="再次发送" v-else-if="msg.admin_name != null && msg.status == 'failed'">
                                    <el-button size="small" text type="warning" :icon="WarnTriangleFilled"
                                        @click="onSend(msg)" />
                                </el-tooltip> -->
                            </el-space>
                            <el-text tag="sup" size="small">{{ msg.created_at }}</el-text>
                        </div>
                        <el-text tag="p">{{ msg.content }}</el-text>
                    </template>
                </div>
                <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="20"
                    :page-sizes="pagination.sizes" :hide-on-single-page="true" class="pagination" layout="prev, pager, next"
                    @current-change="currentChange" />
            </div>
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
.messages-box {
    height: calc(100vh - 480px);
    position: relative;
    overflow-y: auto;
}

.el-pagination {
    position: absolute;
    margin-top: 20px;
    bottom: 0;
    right: 0;
}

.message-item-title {
    display: flex;
    flex-wrap: row nowrap;
    justify-content: space-between;
    margin-top: 20px;
}
</style>
