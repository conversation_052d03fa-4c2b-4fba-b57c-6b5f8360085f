<script setup name="StaffsStubsList">
    import { Delete } from '@element-plus/icons-vue'
    import { usePagination } from '@/utils/composables'
    import { getDeliveryStubs, deleteDeliveryStub, getDrivers } from '@/api/modules/staffs'
    import { currencyFormatter } from '@/utils/formatter';
    import EmployeeSelector from '../components/employee_selector.vue';
    const router = useRouter()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            worksheet__employee__pk: null,
            date__lte: null,
            date__gte: null,
        },
        // 列表数据
        dataList: []
    })
    function resetFilters() {
        data.value.search = {
            worksheet__employee__pk: null,
            date__lte: null,
            date__gte: null,
        }
        getDataList();
    }

    const drivers = ref([])

    onMounted(() => {
        getDataList()
        getDriverList()

    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getDeliveryStubs(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.stub_list
            pagination.value.total = res.data.total
        })
    }

    function getDriverList() {
        getDrivers().then(res => drivers.value = res.data)
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }


    function onEdit(row) {
        router.push({
            name: 'deliveryStubDetail',
            query: {
                id: row.id
            }
        })
    }

    function onDel(row) {
        ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
            deleteDeliveryStub({ id: row.id }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }


</script>

<template>
    <div>
        <page-header :title="$t('staffs.fields.stubs')" />
        <page-main>
            <el-form :model="data.search" size="default" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item :label="$t('staffs.fields.employee')">
                            <EmployeeSelector v-model="data.search.worksheet__employee__pk" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('fields.createdAt')">
                            <el-space>
                                <el-date-picker v-model="data.search.date__gte" type="date"
                                    :placeholder="$t('fields.createdAtMin')" clearable format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD" @clear="onSearch" @change="onSearch" />
                                <span>~</span>
                                <el-date-picker v-model="data.search.date__lte" type="date"
                                    :placeholder="$t('fields.createdAtMax')" clearable format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD" @clear="onSearch" @change="onSearch" />
                            </el-space>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4" align="center">
                        <el-button type="warning" @click="resetFilters()" plain>
                            <template #icon>
                                <el-icon>
                                    <svg-icon name="ep:refresh-left" />
                                </el-icon>
                            </template>
                            {{ $t('operations.reset') }}
                        </el-button>
                        <el-button type="primary" @click="currentChange()">

                            <template #icon>
                                <el-icon>
                                    <svg-icon name="ep:search" />
                                </el-icon>
                            </template>
                            {{ $t('operations.filter') }}
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit"
                @sort-change="sortChange">
                <el-table-column type="index" align="center" fixed width="50" />
                <el-table-column prop="employee" :label="$t('staffs.fields.employee')" width="150"
                    show-overflow-tooltip />
                <el-table-column prop="date" :label="$t('fields.date')" />
                <el-table-column prop="base_salary" :label="$t('staffs.stub.fields.baseSalary')"
                    :formatter="currencyFormatter" />
                <el-table-column prop="fuel_allowance" :label="$t('staffs.stub.fields.fuelAllowance')"
                    :formatter="currencyFormatter" />
                <el-table-column prop="bonus" :label="$t('staffs.stub.fields.bonus')" :formatter="currencyFormatter" />
                <el-table-column prop="extra_bonus" :label="$t('staffs.stub.fields.extraBonus')"
                    :formatter="currencyFormatter" />
                <el-table-column prop="compensation" :label="$t('staffs.stub.fields.compensation')"
                    :formatter="currencyFormatter" />
                <el-table-column prop="deduction" :label="$t('staffs.stub.fields.deduction')"
                    :formatter="currencyFormatter" />
                <el-table-column prop="hst" :label="$t('staffs.stub.fields.tax')" :formatter="currencyFormatter" />
                <el-table-column prop="tip" :label="$t('staffs.stub.fields.tip')" :formatter="currencyFormatter" />
                <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="50" align="center" fixed="right">

                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start"
                            v-if="scope.row.can_update">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>