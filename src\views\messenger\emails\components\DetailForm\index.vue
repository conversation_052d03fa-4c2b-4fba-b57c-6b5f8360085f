<script setup>
import { addEmail, getEmails, updateEmail } from '@/api/modules/messenger'
import { languages } from '@/utils/constants'
import { getUserSimpleList } from '@/api/modules/users'

import UserSelector from '@/views/components/UserSelector/index.vue'

const props = defineProps({
    id: {
        type: String,
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
    },
    rules: {
        subject: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        language: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        body: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],

    }
})

const users = ref([])

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getEmails({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        users.value = res.data.recipients
    })
}


defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        if (params.recipients && params.recipients.length > 0) { params.recipients = data.value.form.recipients.map(e => e.id) }

        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addEmail(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    if (data.value.form.status != 'sent') {
                        updateEmail(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                    else {
                        callback && callback()
                    }
                }
            })
        }
    }
})

const sendable = computed(() => !['sent', 'sending'].includes(data.value.form.status))
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('messenger.email.fields.subject')" prop="subject">
                <el-input v-model="data.form.subject" placeholder="请输入标题" :disabled="data.form.status == 'sent'" />
            </el-form-item>
            <el-form-item :label="$t('messenger.fields.language')" prop="language">
                <el-select v-model="data.form.language" class="m-2" placeholder="Select"
                    :disabled="data.form.status == 'sent'">
                    <el-option v-for="item in languages" :key="item"
                        :label="$t(`messenger.selections.languageSelections.${item}`)" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('messenger.fields.category')" v-if="data.form.category">
                {{ data.form.category?.name }}
            </el-form-item>
            <el-form-item :label="$t('messenger.fields.operator')" v-if="data.form.admin">
                {{ data.form.admin }}
            </el-form-item>
            <el-form-item :label="$t('messenger.email.fields.recipients')">
                <UserSelector v-model="data.form.recipients" place-holder="Leave blank for selecting all users" multiple
                    :disabled="!sendable" />
            </el-form-item>
            <el-form-item :label="$t('messenger.email.fields.body')" prop="body">
                <el-input v-model="data.form.body" placeholder="请输入标题" type="textarea" :rows="5" :disabled="!sendable" />
            </el-form-item>
            <el-form-item :label="$t('messenger.email.fields.richText')" prop="rich_text">
                <div v-if="!sendable" v-html="data.form.alternatives" />
                <editor v-else v-model="data.form.alternatives" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
