<script setup>
import { usePagination } from '@/utils/composables'
import { getReimbursements } from '@/api/modules/staffs'
import { currencyFormatter } from '@/utils/formatter'
import { reimbursementStatusStyles } from '@/utils/constants'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const props = defineProps({
    employee: {
        type: String,
        default: null
    },
    dateMin: {
        type: String,
        default: null
    },
    dateMax: {
        type: String,
        default: null
    },
    modelValue: {
        type: Array,
        default: []
    },
    all: {
        type: Boolean,
        default: false,
    },
    defaultSelected: {
        type: Array,
        default: []
    }
})
const table = ref()

defineExpose({
    fetch(callback) { if (props.employee) getDataList(); },
    reset(callBack) { onReset() },
})


const emit = defineEmits(['update:modelValue'])
watch(() => props.employee, (val) => data.value.search.employee__pk = val)
watch(() => props.dateMin, (val) => data.value.search.date__gte = val)
watch(() => props.dateMax, (val) => data.value.search.date__lte = val)

const data = ref({
    loading: false,
    dataList: [],
    search: {
        employee__pk: props.employee,
        date__gte: props.dateMin,
        date__lte: props.dateMax,
        status: 'approved',
        statements__isnull: props.all ? null : true,
    },
})

function onReset() {
    data.value.search = {
        employee__pk: null,
        date__lte: null,
        date__gte: null,
        status: 'approved',
        statements__isnull: props.all ? null : true,
    }
    data.value.dataList.length = 0;
    selectionDataList.length = 0;
}


let selectionDataList = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})



function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getReimbursements(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.reimbursement_list
        pagination.value.total = res.data.total
        nextTick().then(() => {
            if (props.defaultSelected.length > 0) {
                for (let s of props.defaultSelected) {
                    let found = table.value.data.find(e => e.id == s.id)
                    if (found != undefined) { table.value.toggleRowSelection(found, true) }
                }
            }
        })

    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

</script>

<template>
    <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe highlight-current-row
        @sort-change="sortChange" @selection-change="selectionDataList = $event">
        <el-table-column type="selection" align="center" fixed />
        <el-table-column type="index" align="center" fixed width="50" />
        <el-table-column prop="date" :label="$t('fields.date')" width="120" />
        <el-table-column prop="employee" :label="$t('staffs.fields.employee')" width="150" />
        <el-table-column prop="description" :label="$t('fields.desc')" show-overflow-tooltip width="150" />
        <el-table-column prop="purpose" :label="$t('staffs.reimbursement.fields.purpose')" width="150"
            show-overflow-tooltip />
        <el-table-column prop="vendor" :label="$t('staffs.reimbursement.fields.vendor')" width="150"
            show-overflow-tooltip />
        <el-table-column prop="amount" :label="$t('fields.preTax')" :formatter="currencyFormatter" />
        <el-table-column prop="hst" :label="$t('fields.tax')" :formatter="currencyFormatter" />
        <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter" />

        <el-table-column prop="status" :label="$t('fields.status')">

            <template #default="scope">
                <el-tag :type="reimbursementStatusStyles[scope.row.status]" round size="small">
                    {{ $t(`staffs.reimbursement.selections.status.${scope.row.status}`) }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="reviewed_by" :label="$t('staffs.reimbursement.fields.reviewedBy')" width="160" />
        <el-table-column prop="reviewed_at" :label="$t('staffs.reimbursement.fields.reviewedAt')" width="160"
            sortable="custom" />
        <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
    </el-table>
    <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
        :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
        background @size-change="sizeChange" @current-change="currentChange" />
</template>