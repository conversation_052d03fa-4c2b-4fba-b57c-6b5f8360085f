const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/holidays',
    component: Layout,
    redirect: '/settings/holidays/list',
    name: 'settingHoliday',
    meta: {
        title: '节日管理',
        icon: 'icon-park-outline:calendar-three',
        auth: ['super'],
        i18n: 'route.system.holidays.title'
    },
    children: [
        {
            path: 'list',
            name: 'settingHolidayList',
            component: () => import('@/views/settings/holidays/list.vue'),
            meta: {
                title: '列表',
                icon: 'bx:key',
                activeMenu: '/settings/holidays/list',
                i18n: 'route.system.holidays.title',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
