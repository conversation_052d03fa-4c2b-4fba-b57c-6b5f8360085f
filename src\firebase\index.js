import { initializeApp } from 'firebase/app'

const firebaseConfig = {
    apiKey: "AIzaSyAi2AVP-S-5paRYMlServgkg7sLuKgoCKc",
    authDomain: "fleetnow-v3.firebaseapp.com",
    projectId: "fleetnow-v3",
    storageBucket: "fleetnow-v3.appspot.com",
    messagingSenderId: "378868034319",
    appId: "1:378868034319:web:a824d77978ad8735d26c7a",
    measurementId: "G-D28SD103S0"
};


export const firebaseApp = initializeApp(firebaseConfig)

export const vapidKey = 'BAcq3V_ovgqXxcfSGTJTbPHq_h8x2eTMFirWpc1hiZA67gQ-X3A695OJlH2YnNqzcJKhZCJrp7WiduwGPCPySTk';

/**
 * 注册Firebase消息Service Worker
 * 确保Service Worker正确注册并激活
 * @returns {Promise<ServiceWorkerRegistration|null>} 返回ServiceWorker注册对象
 */
export const registerFirebaseServiceWorker = async () => {
    if (!('serviceWorker' in navigator)) {
        console.error('此浏览器不支持Service Worker');
        return null;
    }

    try {
        // 首先检查Service Worker是否已注册
        const registrations = await navigator.serviceWorker.getRegistrations();
        let existingRegistration = null;
        
        // 查找已存在的Firebase消息Service Worker
        for (const reg of registrations) {
            if (reg.scope.includes(window.location.origin)) {
                console.log('已找到现有Service Worker:', reg.scope);
                existingRegistration = reg;
                
                // 检查Service Worker状态
                if (reg.active) {
                    console.log('Firebase消息Service Worker已激活');
                    return reg;
                }
            }
        }
        
        // 如果存在注册但未激活，尝试激活
        if (existingRegistration) {
            if (existingRegistration.installing || existingRegistration.waiting) {
                console.log('等待Service Worker安装完成...');
                
                // 等待Service Worker激活
                return new Promise((resolve) => {
                    existingRegistration.addEventListener('updatefound', () => {
                        const newWorker = existingRegistration.installing;
                        
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'activated') {
                                console.log('Service Worker激活成功');
                                resolve(existingRegistration);
                            }
                        });
                    });
                    
                    // 如果超过5秒仍未激活，尝试使用现有注册
                    setTimeout(() => {
                        console.log('Service Worker激活超时，使用现有注册');
                        resolve(existingRegistration);
                    }, 5000);
                });
            }
        }
        
        // 不存在注册或需要重新注册
        console.log('注册新的Firebase消息Service Worker...');
        // 确保使用正确的Service Worker文件路径
        const swUrl = `${window.location.origin}/firebase-messaging-sw.js`;
        console.log('注册Service Worker URL:', swUrl);
        
        // 注册Service Worker
        const newRegistration = await navigator.serviceWorker.register(swUrl, {
            scope: '/'
        });
        
        console.log('Firebase消息Service Worker注册成功');
        
        // 等待Service Worker激活
        if (newRegistration.installing) {
            return new Promise((resolve) => {
                const newWorker = newRegistration.installing;
                
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'activated') {
                        console.log('新Service Worker激活成功');
                        resolve(newRegistration);
                    }
                });
                
                // 如果超过5秒仍未激活，直接返回注册对象
                setTimeout(() => {
                    console.log('新Service Worker激活超时，继续使用');
                    resolve(newRegistration);
                }, 5000);
            });
        }
        
        return newRegistration;
    } catch (error) {
        console.error('注册Firebase消息Service Worker失败:', error);
        return null;
    }
};

/**
 * 清理Firebase消息服务相关存储
 * 用于强制刷新Firebase令牌时清理浏览器中的缓存
 */
export const clearFirebaseMessagingStorage = async () => {
    try {
        // 1. 尝试清理相关的Service Worker
        if ('serviceWorker' in navigator) {
            const registrations = await navigator.serviceWorker.getRegistrations();
            for (const registration of registrations) {
                if (registration.scope.includes('firebase-messaging') || 
                    registration.scope.includes('firebase-cloud-messaging')) {
                    await registration.unregister();
                    console.log('已注销Firebase消息相关Service Worker');
                }
            }
        }
        
        // 2. 清理FCM令牌存储的IndexedDB
        const fcmDatabases = ['fcm_token_details_db', 'firebase-messaging-database'];
        for (const dbName of fcmDatabases) {
            // 打开数据库
            const request = indexedDB.open(dbName);
            
            request.onsuccess = (event) => {
                try {
                    const db = event.target.result;
                    const objectStoreNames = Array.from(db.objectStoreNames);
                    
                    if (objectStoreNames.length > 0) {
                        // 创建一个事务清除所有存储区
                        const transaction = db.transaction(objectStoreNames, 'readwrite');
                        objectStoreNames.forEach(storeName => {
                            transaction.objectStore(storeName).clear();
                        });
                        console.log(`已清理 ${dbName} 数据库数据`);
                    }
                    
                    db.close();
                } catch (err) {
                    console.warn(`清理 ${dbName} 数据库失败:`, err);
                }
            };
            
            request.onerror = (err) => {
                console.warn(`打开 ${dbName} 数据库失败:`, err);
            };
        }
        
        // 3. 清理本地存储相关数据
        const fcmKeys = Object.keys(localStorage).filter(key => 
            key.includes('firebase') || key.includes('fcm') || key.includes('FcmToken'));
        
        fcmKeys.forEach(key => {
            localStorage.removeItem(key);
        });
        
        if (fcmKeys.length > 0) {
            console.log(`已清理 ${fcmKeys.length} 个Firebase相关本地存储项`);
        }
        
        // 等待一小段时间确保清理完成
        await new Promise(resolve => setTimeout(resolve, 500));
        
        return true;
    } catch (error) {
        console.error('清理Firebase消息服务存储失败:', error);
        return false;
    }
};
