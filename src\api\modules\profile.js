/*
 * Author: <EMAIL>'
 * Date: '2022-10-30 21:29:14'
 * Project: 'Admin-UI'
 * Path: 'src/api/apis/profile.js'
 * File: 'profile.js'
 * Version: '1.0.0'
 */

import { GET, POST, UPL } from '../methods'

const path = 'administrators/'

export const adminLogin = params => POST(path + 'login/', params)

export const adminLogout = params => GET(path + 'logout/', params)

export const getAdminLoginState = params =>
    GET(path + 'login-state/', params)

export const getAdminProfile = params => GET(path + 'profile/', params)

export const getAdminPermissions = params =>
    GET(path + 'permissions/', params)

export const uploadAdminAvatar = params =>
    UPL(path + 'upload-avatar/', params)

export const updateAdminProfile = params =>
    POST(path + 'update-profile/', params)

export const updateAdminPassword = params =>
    POST(path + 'update-password/', params)
