<script setup name="FinancePromotionsLogsList">
    import { usePagination } from '@/utils/composables'
    import { getDiscountUsageLogs } from '@/api/modules/promotions'
    import { currencyFormatter } from '@/utils/formatter'

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            user__name__icontains: null,
            discount__name__icontains: null,
            created_at__lte: null,
            created_at__gte: null,

        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            user__name__icontains: null,
            discount__name__icontains: null,
            created_at__lte: null,
            created_at__gte: null,

        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getDiscountUsageLogs(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.log_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

</script>

<template>
    <div>
        <page-header :title="$t('promotions.logs.discountUsage')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20" align="bottom">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('promotions.discounts.fields.name')">
                                        <el-input v-model="data.search.discount__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('promotions.discounts.fields.name') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('fields.createdAtMin')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange"
                                                @clear="currentChange" @change="currentChange" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('fields.createdAtMax')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange"
                                                @clear="currentChange" @change="currentChange" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item>
                                        <el-button type="warning" @click="resetFilters()" plain>
                                            <template #icon>
                                                <el-icon>
                                                    <svg-icon name="ep:refresh-left" />
                                                </el-icon>
                                            </template>
                                            {{ $t('operations.reset') }}
                                        </el-button>
                                        <el-button type="primary" @click="currentChange()">
                                            <template #icon>
                                                <el-icon>
                                                    <svg-icon name="ep:search" />
                                                </el-icon>
                                            </template>
                                            {{ $t('operations.filter') }}
                                        </el-button>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @sort-change="sortChange">
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="discount.name" :label="$t('promotions.discounts.fields.name')" />
                <el-table-column prop="amount" :label="$t('fields.amount')" :formatter="currencyFormatter" />
                <el-table-column prop="user" :label="$t('fields.user')" />
                <el-table-column prop="record.order" :label="$t('fields.order')">
                    <template #default="scope">
                        {{ scope.row.record.orders?.join(', ') }}
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>