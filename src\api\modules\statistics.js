/*
* Author: <EMAIL>'
* Date: '2023-07-13 19:51:32'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/statistics.js'
* File: 'statistics.js'
* Version: '1.0.0'
*/
import { GET, POST, DEL, PUT, PAT } from '../methods'

const path = 'statistics/'

// Available Dates
export const getNoReportDates = () => GET(path + 'available-dates/')

//  Report
export const getReports = (params) => GET(path, params);
export const addReport = (params) => POST(path, params);
export const deleteReport = (params) => DEL(path, params);

// Costs Attributions
const costAttrPath = path + 'costs/attributions/'
export const getCostAttributions = () => GET(costAttrPath)
export const getAvailableCostAttributions = () => GET(costAttrPath + 'available/')
export const addCostAttribution = (params) => POST(costAttrPath, params)
export const updateCostAttribution = (params) => PUT(costAttrPath, params)
export const updateCostAttributionAvailability = (params) => PAT(costAttrPath, params)
export const deleteCostAttribution = (params) => DEL(costAttrPath, params)

// Delivery Report
export const getDeliveryReports = (params) => GET(path + 'delivery/', params);
export const addDeliveryReport = (params) => POST(path + 'delivery/', params);
export const deleteDeliveryReport = (params) => DEL(path + 'delivery/', params);

// Payment Report
export const getPaymentReports = (params) => GET(path + 'payment/', params);
export const addPaymentReport = (params) => POST(path + 'payment/', params);
export const deletePaymentReport = (params) => DEL(path + 'payment/', params);

// Costs Report
export const getCostReports = (params) => GET(path + 'costs/', params)
export const addCostReport = (params) => POST(path + 'costs/', params)
export const deleteCostReport = (params) => DEL(path + 'costs/', params)

// Salary
export const getSalaryReports = (params) => GET(path + 'salary/', params);
export const addSalaryReport = (params) => POST(path + 'salary/', params);
export const deleteSalaryReport = (params) => DEL(path + 'salary/', params);
