<script setup>
import ListVue from './components/list.vue'
import { getPackageTypes, deletePackageType, updatePackageTypeAvailabilityPriority, batchAlterServiceAvailability, addPackageType, updatePackageType } from '@/api/modules/delivery'

</script>
<template>
    <ListVue v-bind="$attrs" :get-data="getPackageTypes" :on-delete="deletePackageType"
        :on-update-availability-priority="updatePackageTypeAvailabilityPriority"
        :on-batch-alter-availability="batchAlterServiceAvailability" :on-add="addPackageType" :on-update="updatePackageType"
        title="packageType" module="pt" />
</template>
