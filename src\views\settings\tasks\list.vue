<script setup name="SettingsTasksList">
import { Delete, Calendar } from '@element-plus/icons-vue'
import { usePagination } from '@/utils/composables'
import FormMode from './components/FormMode/index.vue'
import { getTasks, pauseTask, resumeTask, deleteTask } from '@/api/modules/tasks'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        id: '',
        toReschedule: false,
    },
    // 搜索
    search: {
        name__icontains: null,
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
})


function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getTasks(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.task_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    data.value.formModeProps.id = ''
    data.value.formModeProps.visible = true
    data.value.formModeProps.toReschedule = false
}

function onEdit(row, column, event, toReschedule = false) {
    if (!['scheduled', 'pause'].includes(row.status)) return
    data.value.formModeProps.id = row.id
    data.value.formModeProps.visible = true
    data.value.formModeProps.toReschedule = toReschedule
}

function onPause(row) {
    ElMessageBox.confirm(t('dialog.messages.pause', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        pauseTask({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onResume(row) {
    ElMessageBox.confirm(t('dialog.messages.resume', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        resumeTask({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deleteTask({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

const rowStyles = ({ row, rowIndex }) => {
    if (['scheduled', 'pause'].includes(row.status)) {
        return {
            cursor: 'pointer'
        }
    } else {
        return {
            cursor: 'not-allowed'
        }
    }
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (row.status == 'expired') {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('settings.tasks.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    Reset
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    Filter
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="rowStyles" :row-class-name="tableRowClassName" @row-dblclick="onEdit"
                @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="description" :label="$t('fields.desc')" />
                <el-table-column prop="scheduled_on_date" :label="$t('settings.tasks.fields.scheduledAt')">
                    <template #default="scope">
                        {{ scope.row.scheduled_on_date ?? $t('settings.tasks.fields.everyDay') }} &ThickSpace; {{
                            scope.row.scheduled_at_time }}
                    </template>
                </el-table-column>
                <el-table-column prop="next_run_time" :label="$t('settings.tasks.fields.nextRunAt')" />
                <el-table-column prop="status" :label="$t('fields.status')">
                    <template #default="scope">
                        {{ $t(`settings.tasks.selection.status.${scope.row.status}`) }}
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="right" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('settings.tasks.operations.reschedule')"
                            placement="top-start" v-if="scope.row.status != 'expired'">
                            <el-button type="primary" :icon="Calendar" circle size="small"
                                @click="onEdit(scope.row, _, _, toReschedule = true)" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('settings.tasks.operations.pause')" placement="top-start"
                            v-if="scope.row.status == 'scheduled'">
                            <el-button type="warning" circle size="small" @click="onPause(scope.row)">
                                <svg-icon name="codicon:debug-stop" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('settings.tasks.operations.resume')" placement="top-start"
                            v-if="scope.row.status == 'pause'">
                            <el-button type="success" circle size="small" @click="onResume(scope.row)">
                                <svg-icon name="codicon:debug-start" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible"
            :to-reschedule="data.formModeProps.toReschedule" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
