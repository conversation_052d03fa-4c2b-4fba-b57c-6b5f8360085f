<script setup>
import api from '@/api'
import { getTodos, alterTodoStatus, deleteTodos, updateTodo, addTodo, executeTodos } from '@/api/modules/todos'
import { todoStatusStyles as statusStyle } from '../../../constants'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        module: {},
        detail: {}
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getTodos({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        canVoid.value = res.data.can_void
        canPend.value = res.data.can_pend
        canSchedule.value = res.data.can_schedule
        canUpdate.value = res.data.can_update
        canExecute.value = res.data.can_execute
        amount.value = (res.data.detail.amount || 0.0) / 100
    })
}

const amount = ref(0.0)

const canVoid = ref(false)
const canPend = ref(false)
const canSchedule = ref(false)
const canUpdate = ref(true)
const canExecute = ref(true)

defineExpose({
    submit(callback) {
        callback && callback()
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.detail.amount = Math.round(amount.value * 100)
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addTodo(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateTodo(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    },
    execute(callback) {
        let params = { ids: [data.value.form.id] }
        data.value.loading = true
        executeTodos(params).then((res) => {
            data.value.loading = false
            if (res.data.errCode == 365) {
                callback && callback()
            }
        })
    },
    alterStatus(status, callback) {
        let params = { id: data.value.form.id, status: status }
        data.value.loading = true
        alterTodoStatus(params).then((res) => {
            data.value.loading = false
            if (res.data.errCode == 365) {
                getInfo()
                callback && callback()
            }
        })
    },
    delete(callback) {
        let params = { id: JSON.stringify([data.value.form.id]) }
        data.value.loading = true
        deleteTodos(params).then((res) => {
            data.value.loading = false
            if (res.data.errCode == 365) {
                callback && callback()
            }
        })
    },
    canVoid, canPend, canSchedule, canUpdate, canExecute
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('todo.fields.opName')">
                {{ data.form.module.name }}
            </el-form-item>
            <el-form-item :label="$t('todo.fields.notes')">
                {{ data.form.notes }}
            </el-form-item>
            <el-form-item :label="$t('fields.status')">
                <el-tag :type="statusStyle[data.form.status]" round>
                    •&ThickSpace;{{ $t('todo.selections.status.' + data.form.status) }}
                </el-tag>
            </el-form-item>
            <el-form-item :label="$t('todo.fields.amount')">
                <el-input-number v-model="amount" :disabled="!canUpdate" :min="0" :max="data.form.amount / 100" :step="0.1"
                    :precision="2" />
            </el-form-item>
            <el-form-item :label="$t('todo.fields.toCredits')">
                <el-switch v-model="data.form.detail.toCredits" :disabled="!canUpdate" />
            </el-form-item>

            <el-form-item :label="$t('todo.fields.scheduledAt')">
                <el-date-picker v-model="data.form.scheduled_at" type="datetime" placeholder="Select date and time"
                    value-format="YYYY-MM-DD HH:mm:ss" :disabled="!canUpdate" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
