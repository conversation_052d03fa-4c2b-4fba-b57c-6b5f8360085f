<script setup>
import { updateDeliveryStub, deleteDeliveryStub } from '@/api/modules/staffs'
import { currencyFormatter } from '@/utils/formatter';

const props = defineProps({
    stub: {
        type: Object,
        default: {}
    },
    onUpdate: {
        type: Function,
        default: null
    },
    onDelete: {
        type: Function,
        default: null,
    }

})

watch(() => props.stub, () => { data.value.stub = props.stub }, { deep: true })

const data = ref({
    stub: props.stub,
    services: Number((props.stub.services / 100).toFixed(2)),
    bonus: Number((props.stub.bonus / 100).toFixed(2)),
    extraBonus: Number((props.stub.extra_bonus / 100).toFixed(2)),
    compensation: Number((props.stub.compensation / 100).toFixed(2)),
    deduction: Number((props.stub.deduction / 100).toFixed(2)),
    tip: Number((props.stub.tip / 100).toFixed(2)),
})
function onUpdateStub() {
    let params = {
        id: data.value.stub.id,
        services: Math.round(data.value.services * 100),
        bonus: Math.round(data.value.bonus * 100),
        extra_bonus: Math.round(data.value.extraBonus * 100),
        compensation: Math.round(data.value.compensation * 100),
        deduction: Math.round(data.value.deduction * 100),
        tip: Math.round(data.value.tip * 100),
        note: data.value.stub.note,
    }
    updateDeliveryStub(params).then(res => {
        if (res.data.errCode == 365) props.onUpdate && props.onUpdate()
    })
}
function onDeleteStub() {
    let params = { id: data.value.stub.id }
    deleteDeliveryStub(params).then(res => {
        if (res.data.errCode == 365) { props.onDelete && props.onDelete() }
    })
}
</script>

<template>
    <page-main title="工资条">

        <template #extra>
            <el-space size="large" v-if="data.stub.can_update">
                <el-button type="danger" text @click="onDeleteStub">
                    {{ $t('operations.delete') }}
                </el-button>
                <el-button type="warning" text @click="getInfo">{{ $t('operations.reset') }}</el-button>
                <el-button type="primary" @click="onUpdateStub">{{ $t('operations.save') }}</el-button>
            </el-space>
        </template>
        <el-form :model="data.stub" label-width="170px">
            <el-form-item :label="$t('statistics.salary.fields.baseSalary')">
                {{ currencyFormatter(_, _, data.stub.base_salary) }}
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.fuelAllowance')">
                {{ currencyFormatter(_, _, data.stub.fuel_allowance) }}
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.services')">
                <el-input-number v-model="data.services" :min="0" :precision="2" :step="1"
                    :disabled="!data.stub.can_update || !data.services" />
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.bonus')">
                <el-input-number v-model="data.bonus" :min="0" :precision="2" :step="1"
                    :disabled="!data.stub.can_update" />
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.extraBonus')">
                <el-input-number v-model="data.extraBonus" :min="0" :precision="2" :step="1"
                    :disabled="!data.stub.can_update" />
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.compensation')">
                <el-input-number v-model="data.compensation" :min="0" :precision="2" :step="1"
                    :disabled="!data.stub.can_update" />
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.deduction')">
                <el-input-number v-model="data.deduction" :min="0" :precision="2" :step="1"
                    :disabled="!data.stub.can_update" />
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.tip')">
                <el-input-number v-model="data.tip" :min="0" :precision="2" :step="1"
                    :disabled="!data.stub.can_update" />
            </el-form-item>
            <el-form-item :label="$t('statistics.salary.fields.note')" :span="3">
                <el-input v-model="data.stub.note" type="textarea" :rows="3" :disabled="!data.stub.can_update" />
            </el-form-item>
            <el-form-item :label="$t('fields.tax')">
                {{ currencyFormatter(_, _, data.stub.hst) }}
            </el-form-item>
            <el-form-item :label="$t('fields.total')">
                {{ currencyFormatter(_, _, data.stub.total) }}
            </el-form-item>
        </el-form>
    </page-main>
</template>