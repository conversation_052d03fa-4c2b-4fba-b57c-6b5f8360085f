<script setup name="ElavonOrderDetail">
import { getElavonOrders } from '@/api/modules/payment';
import { userNameFormatter } from '@/utils/formatter'

const props = defineProps({
    id: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
    form: {
        id: props.id,
        ssl_trans_status: null,
        operator: null
    },
})

const logStyles = {
    PEN: {
        type: 'warning',
        hollow: true
    },
    OPN: {
        type: 'success',
        hollow: true
    },
    REV: {
        type: 'danger',
        hollow: true
    },
    STL: {
        type: 'primary',
    },
    PST: {
        type: 'danger',
        hollow: true
    },
    FPR: {
        type: 'danger',
    },
    PRE: {
        type: 'danger',
        hollow: true
    },
}


onMounted(() => {
    console.log(props.id)
    getInfo()
})

function getInfo() {
    data.value.loading = true
    getElavonOrders({ id: data.value.form.id }).then(res => {
        data.value.form = res.data
        data.value.loading = false
    })
}

</script>
<template>
    <el-space direction="vertical" alignment="start">
        <el-space size="large" v-if="data.form.ssl_trans_status">
            <el-tag :type="logStyles[data.form.ssl_trans_status]?.type">
                {{ data.form.ssl_trans_status }}
            </el-tag>
            <span class="operator">{{ data.form.operator }}</span>
        </el-space>
        <el-descriptions :column="1">
            <template v-if="data.form.payment_order">
                <el-descriptions-item :label="$t('finance.paymentOrder.fields.no')" label-class-name="log-label">
                    {{ data.form.payment_order.no }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('fields.user')" label-class-name="log-label">
                    <el-space>
                        <el-tag v-if="data.form.user.user_code" size="small" type="info" effect="plain">{{
                            data.form.user.user_code
                        }}</el-tag>
                        <span>{{ userNameFormatter(data.form) }}</span>
                    </el-space>
                </el-descriptions-item>
            </template>
            <el-descriptions-item :label="$t('finance.ccLog.fields.status')" label-class-name="log-label">
                {{ data.form.ssl_trans_status ? $t(`finance.ccLog.selections.status.${data.form.ssl_trans_status}`) : '-' }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.amount')" label-class-name="log-label">
                {{ data.form.ssl_amount }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.currency')" label-class-name="log-label">
                {{ data.form.ssl_cardholder_currency }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.txnType')" label-class-name="log-label">
                {{ data.form.ssl_transaction_type }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.result')" label-class-name="log-label">
                {{ data.form.ssl_result_message }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.txnId')" label-class-name="log-label"
                class-name="wrap-text" with="100">
                {{ data.form.ssl_txn_id }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.txnTime')" label-class-name="log-label">
                <span>{{ data.form.ssl_txn_time }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.approvalCode')" label-class-name="log-label">
                <span>{{ data.form.ssl_approval_code }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.oarData')" label-class-name="log-label">
                <span>{{ data.form.ssl_oar_data }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.ps2000Data')" label-class-name="log-label">
                <span>{{ data.form.ssl_ps2000_data }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.settlementBatch')" label-class-name="log-label">
                <span>{{ data.form.ssl_settlement_batch_response }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.settleTime')" label-class-name="log-label">
                <span>{{ data.form.ssl_settle_time }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.cardType')" label-class-name="log-label">
                <span>{{ data.form.ssl_card_type }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.token')" label-class-name="log-label">
                <span>{{ data.form.ssl_token }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.customerId')" label-class-name="log-label">
                <span>{{ data.form.ssl_customer_code }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.cardNumber')" label-class-name="log-label">
                <span>{{ data.form.ssl_card_number }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.expDate')" label-class-name="log-label">
                <span>{{ data.form.ssl_exp_date }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.shortDesc')" label-class-name="log-label">
                <span>{{ data.form.ssl_card_short_description }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.avsResponse')" label-class-name="log-label">
                <span>{{ data.form.ssl_avs_response }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.firstName')" label-class-name="log-label">
                <span>{{ data.form.ssl_first_name }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.lastName')" label-class-name="log-label">
                <span>{{ data.form.ssl_last_name }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.address')" label-class-name="log-label">
                <span>{{ data.form.ssl_avs_address }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.zipCode')" label-class-name="log-label">
                <span>{{ data.form.ssl_avs_zip }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.city')" label-class-name="log-label">
                <span>{{ data.form.ssl_city }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.state')" label-class-name="log-label">
                <span>{{ data.form.ssl_state }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.ccLog.fields.country')" label-class-name="log-label">
                <span>{{ data.form.ssl_country }}</span>
            </el-descriptions-item>
        </el-descriptions>
    </el-space>
</template>

<style>
.log-label {
    font-weight: bold;
}
</style>

