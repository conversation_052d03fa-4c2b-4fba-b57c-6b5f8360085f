import { createApp } from 'vue'
import { VueFire, VueFireAuth } from 'vuefire'
import App from './App.vue'
const app = createApp(App)
import { firebaseApp } from './firebase'

app.use(VueFire, {
    // imported above but could also just be created here
    firebaseApp,
    modules: [
        // we will see other modules later on
        VueFireAuth(),
    ],
})

import pinia from './store'
app.use(pinia)

import router from './router'
app.use(router)

import { useI18n } from './locales'
useI18n(app)

/* importElementPlusPlaceholder */

import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import ContextMenu from '@imengyu/vue3-context-menu'
app.use(ContextMenu)

// 自定义指令
import directive from '@/utils/directive'
directive(app)

// 错误日志上报
import errorLog from '@/utils/error.log'
errorLog(app)

// 加载 svg 图标
import 'virtual:svg-icons-register'

// 加载 iconify 图标
import { downloadAndInstall } from '@/iconify'
import icons from '@/iconify/index.json'
if (icons.useType === 'offline') {
    for (const info of icons.collections) {
        downloadAndInstall(info)
    }
}

// 全局样式
import 'uno.css'
import '@/assets/styles/globals.scss'

// PWA
// import './pwa'

// 初始化Firebase消息推送服务（应用启动时）
import { getMessaging, getToken } from 'firebase/messaging'
import { vapidKey, registerFirebaseServiceWorker } from './firebase'
import useUserStore from './store/modules/user'

// 先注册Service Worker
async function initializeApp() {
    try {
        // 先注册Service Worker，这是FCM正常工作的前提
        console.log('应用启动，注册Service Worker...');
        const swRegistration = await registerFirebaseServiceWorker();
        
        if (!swRegistration) {
            console.error('Service Worker注册失败，FCM功能可能无法正常工作');
        } else {
            console.log('Service Worker注册成功，可以使用FCM功能');
        }
        
        // 挂载Vue应用
        app.mount('#app');
        
        // 在应用启动后检查Firebase消息服务状态
        setTimeout(() => {
            try {
                const userStore = useUserStore();
                // 如果用户已登录，尝试初始化Firebase消息服务
                if (userStore.isLogin) {
                    console.log('应用启动，正在初始化Firebase消息服务...');
                    userStore.requestFCMPermission()
                        .then(token => {
                            if (token) {
                                console.log('应用启动时成功获取Firebase令牌');
                            }
                        })
                        .catch(error => {
                            console.error('应用启动时初始化Firebase消息服务失败:', error);
                        });
                }
            } catch (error) {
                console.error('应用启动时检查Firebase消息服务状态失败:', error);
            }
        }, 2000); // 延迟2秒执行，确保应用已完全加载
    } catch (error) {
        console.error('初始化应用失败:', error);
        // 如果Service Worker注册失败，仍然挂载应用，但FCM功能可能不可用
        app.mount('#app');
    }
}

// 启动应用
initializeApp();
