<script setup name="DeliveryServicesSizeWeightsList">
    import { usePagination } from '@/utils/composables'
    import FormMode from './formMode.vue'
    import { getUserGroups } from '@/api/modules/users';
    import { Delete } from '@element-plus/icons-vue'
    import { useI18n } from 'vue-i18n'
    import { currencyFormatter } from '@/utils/formatter'
    import { userTypes, weekdays } from '@/utils/constants'

    const { t } = useI18n()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()


    const props = defineProps({
        title: {
            type: String,
        },
        module: {
            type: String
        },
        getData: {
            type: Function
        },
        onDelete: {
            type: Function
        },
        onUpdateAvailabilityPriority: {
            type: Function
        },
        onBatchAlterAvailability: {
            type: Function
        },
    })
    const data = ref({
        loading: false,
        formModeProps: {
            visible: false,
            id: ''
        },
        search: {
            title: ''
        },
        batch: {
            selectionDataList: []
        },
        dataList: [],
        search: {
            name__icontains: null,
            description: null,
            priority__gte: 0,
            priority__lte: null,
            fee__gte: null,
            fee__lte: null,
            weekdays__contains: [],
            holidays__contains: [],
            user_type: null,
            user_group: null,
            created_at__lte: null,
            created_at__gte: null,
            updated_at__lte: null,
            updated_at__gte: null,
        },
    })

    const userGroups = ref([])
    const holidays = ref([])


    onMounted(() => {
        getDataList()
        setUserGroups()
    })

    // Filters
    function resetFilters() {
        data.value.search =
        {
            name__icontains: null,
            description: null,
            priority__gte: 0,
            priority__lte: null,
            fee__gte: null,
            fee__lte: null,
            weekdays__contains: [],
            holidays__contains: [],
            user_type: null,
            user_group: null,
            created_at__lte: null,
            created_at__gte: null,
            updated_at__lte: null,
            updated_at__gte: null,
        }
        currentChange()
    }

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        props.getData(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data[`${props.title}_list`]
            pagination.value.total = res.data.total
        })
    }

    function setUserGroups() {
        getUserGroups().then(res => {
            userGroups.value = res.data.group_list
        })
    }


    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        data.value.formModeProps.id = ''
        data.value.formModeProps.visible = true
    }

    function onEdit(row) {
        data.value.formModeProps.id = row.id
        data.value.formModeProps.visible = true
    }

    function onDel(row) {
        ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
            let params = {
                id: JSON.stringify([row.id])
            }
            props.onDelete(params).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }

    function onBatchDel() {
        ElMessageBox.confirm(t('dialog.messages.batchDeletion', { module: t('delivery.services.sizeWeight') }), t('dialog.titles.confirmation')).then(() => {
            let ids = data.value.batch.selectionDataList.map(i => i.id)
            let params = {
                id: JSON.stringify(ids)
            }
            props.onDelete(params).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }

    function onUpdatePriority(row) {
        let params = {
            id: row.id,
            priority: row.priority
        }
        props.onUpdateAvailabilityPriority(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }

    function onUpdateAvailability(row) {
        let params = {
            id: row.id,
            is_available: !row.is_available
        }
        props.onUpdateAvailabilityPriority(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }

    function onBatchAlterAvailability(isAvailable) {
        let ids = data.value.batch.selectionDataList.map(i => i.id)

        let params = {
            id: ids,
            isAvailable: isAvailable,
            module: props.module
        }
        props.onBatchAlterAvailability(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }


    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.is_available) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-header :title="$t(`delivery.services.${props.title}`)" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.name')">
                                        <el-input v-model="data.search.names__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.name') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.desc')">
                                        <el-input v-model="data.search.descriptions__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.desc') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('fields.createdAtMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('fields.createdAtMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.updatedAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.updated_at__gte" type="date"
                                                :placeholder="$t('fields.updatedAtMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.updated_at__lte" type="date"
                                                :placeholder="$t('fields.updatedAtMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.priority')">
                                        <el-input-number v-model="data.search.priority__gte" :min="0"
                                            :placeholder="$t('fields.priorityMin')" controls-position="right" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.priority__lte" :min="0"
                                            :placeholder="$t('fields.priorityMax')" controls-position="right" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('delivery.services.fields.fee')">
                                        <el-input-number v-model="data.search.fee__gte" :min="0"
                                            :placeholder="$t('delivery.services.fields.feeMin')"
                                            controls-position="right" clearable @keydown.enter="currentChange()"
                                            @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.fee__lte" :min="0"
                                            :placeholder="$t('delivery.services.fields.feeMax')"
                                            controls-position="right" clearable @keydown.enter="currentChange()"
                                            @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.type')">
                                        <el-select v-model="data.search.user_type"
                                            :placeholder="$t('statSelectHolder', { field: $t('user.fields.type') })"
                                            clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in userTypes" :value="item" :key="item"
                                                :label="$t(`user.selection.userType.${item}`)">
                                                {{ $t(`user.selection.userType.${item}`) }}
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.group')">
                                        <el-select v-model="data.search.user_group"
                                            :placeholder="$t('statSelectHolder', { field: $t('user.fields.group') })"
                                            clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in userGroups" :value="item.id" :key="item.id"
                                                :label="item.name" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.weekdays')">
                                        <el-select v-model="data.search.weekdays__contains"
                                            :placeholder="$t('statSelectHolder', { field: $t('fields.weekdays') })"
                                            clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in weekdays" :key="item"
                                                :label="$t(`selection.weekday.${item} `)" :value="item" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.holidays')">
                                        <el-select v-model="data.search.holidays__contains"
                                            :placeholder="$t('statSelectHolder', { field: $t('fields.holidays') })"
                                            clearable @change="currentChange()" @clear="currentChange()"
                                            :disabled="holidays.length == 0">
                                            <el-option v-for="item in holidays" :key="item.id" :label="items.name"
                                                :value="item.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button @click="onBatchDel" type="danger">
                        {{ $t('operations.batch', { op: t('operations.delete') }) }}
                    </el-button>
                    <el-button-group>
                        <el-button type="success" @click="onBatchAlterAvailability(true)">
                            {{ $t('operations.batch', { op: t('operations.enable') }) }}
                        </el-button>
                        <el-button type="warning" @click="onBatchAlterAvailability(false)">
                            {{ $t('operations.batch', { op: t('operations.disable') }) }}
                        </el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="priority" :label="$t('fields.priority')" align="center" width="80">
                    <template #default="scope">
                        <el-input v-model="scope.row.priority" :input-style="{ 'text-align': 'center' }"
                            @blur="onUpdatePriority(scope.row)" @keyup.enter="onUpdatePriority(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="code" :label="$t('fields.code')" min-width="80" align="center" />
                <el-table-column prop="names.en-us" :label="$t('fields.name')" min-width="150" />
                <el-table-column prop="banner_img" min-width="150">
                    <template #default="scope">
                        <img :src="scope.row.banner_img" style="height: 24px;" />
                    </template>
                </el-table-column>
                <template v-if="props.module == 'as'">
                    <el-table-column prop="packs.length" :label="$t('delivery.services.fields.packs')"
                        show-overflow-tooltip min-width="150" />
                    <el-table-column prop="items.length" :label="$t('delivery.services.fields.items')"
                        show-overflow-tooltip min-width="150" />
                </template>
                <el-table-column prop="fee" :label="$t('delivery.services.fields.fee')" :formatter="currencyFormatter"
                    sortable="custom" align="center" width="100" />
                <el-table-column prop="weekdays.length" :label="$t('fields.weekdays')" align="center" width="100" />
                <el-table-column prop="holidays.length" :label="$t('fields.holidays')" align="center" width="100">
                    <template #default="scope">
                        {{ scope.row.holidays.length > 0 ? scope.row.holidays.length : '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="user_type" :label="$t('user.fields.type')" align="center" width="100">
                    <template #default="scope">
                        <el-tag type="primary" plain v-if="scope.row.user_type === 0">B</el-tag>
                        <el-tag type="success" plain v-else-if="scope.row.user_type === 1">P</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="user_group.name" :label="$t('user.fields.group')" align="center" width="110" />
                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon
                                    :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" @success="getDataList"
            :module="props.module" :get-data="props.getData" :title="$t(`delivery.services.${props.title}`)"
            v-bind="$attrs" />
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: var(--g-unavailable-color);
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>
