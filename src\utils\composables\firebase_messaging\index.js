import { getMessaging } from 'firebase/messaging'
import { useFirebaseApp } from 'vuefire'

/**
 * 获取Firebase消息服务
 * 确保在前台和后台都能正确接收和处理消息
 * @returns {firebase.messaging.Messaging} Firebase消息服务实例
 */
export function useFirebaseMessaging() {
    // 获取Firebase应用实例
    const app = useFirebaseApp();
    
    // 确保应用实例存在
    if (!app) {
        console.error('Firebase应用实例不可用，无法初始化消息服务');
        throw new Error('Firebase应用实例不可用');
    }
    
    try {
        // 初始化Firebase消息服务
        const messaging = getMessaging(app);
        
        // 记录消息服务初始化成功
        console.log('Firebase消息服务初始化成功');
        
        return messaging;
    } catch (error) {
        console.error('初始化Firebase消息服务失败:', error);
        throw error;
    }
}
