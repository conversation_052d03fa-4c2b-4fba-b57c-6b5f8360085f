<script setup name="DispatcherOrdersList">
    import { <PERSON><PERSON>illed, Question<PERSON>ille<PERSON>, <PERSON>Bold, DocumentCopy } from '@element-plus/icons-vue'
    import { usePagination } from '@/utils/composables'
    import FormMode from './components/FormMode/index.vue'
    import { getOrders, } from '@/api/modules/dispatcher'
    import { getAvailableDrivers, clearDriverBuckets } from '@/api/modules/staffs'
    import { useClipboard } from '@vueuse/core'
    const { text, copy, copied, isSupported } = useClipboard()
    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            order__no__icontains: null,
            order__sender__icontains: null,
            order__receiver__icontains: null,
        },
        // 列表数据
        dataList: [],
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            order__no__icontains: null,
            order__sender__icontains: null,
            order__receiver__icontains: null,
        }
        currentChange()
    }

    onMounted(() => {
        getDataList();
    })



    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search),
            }
        )
        getOrders(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.order_list
            pagination.value.total = res.data.total
        })
    }

    function getDrivers() {
        data.value.loading = true
        getAvailableDrivers().then(res => {
            data.value.loading = false
            data.value.drivers = res.data
            if (!currentDriver && data.value.driver.length > 0) {
                currentDriver.value = data.value.drivers[0].id
            }
        })
    }

    function onClearBuckets() {
        data.value.loading = true
        clearDriverBuckets().then((_) => {
            data.value.loading = false

        })
    }



    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function packageIndexClass(row, index) {
        if (row.sorted_packages.length === 0) {
            return 'uncompleted';
        }

        const sortedPackage = row.sorted_packages.find(e => e.index === index);

        if (!sortedPackage) {
            return 'uncompleted';
        }

        if (row.type === 'pickup' || sortedPackage.is_departed) {
            return 'completed';
        }

        if (row.type === 'delivery') {
            return 'unsorted';
        }

        return '';
    }
    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (row.completed_at != null) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>
<template>
    <div>
        <page-header :title="$t('dispatcher.orders.title')" />
        <page-main>
            <el-space alignment="start">
                <el-form-item :label="$t('fields.no')">
                    <el-input v-model="data.search.order__no__icontains"
                        :placeholder="$t('placeholder', { field: $t('fields.no') }) + ', ' + $t('fuzzySupported')"
                        clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                </el-form-item>
                <el-form-item :label="$t('delivery.orders.fields.senderName')">
                    <el-input v-model="data.search.order__sender__icontains"
                        :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.senderName') }) + ', ' + $t('fuzzySupported')"
                        clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                </el-form-item>
                <el-form-item :label="$t('delivery.orders.fields.receiverName')">
                    <el-input v-model="data.search.order__receiver__icontains"
                        :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.receiverName') }) + ', ' + $t('fuzzySupported')"
                        clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                </el-form-item>
                <el-button type="warning" @click="resetFilters()" plain circle size="small">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:refresh-left" />
                        </el-icon>
                    </template>
                </el-button>
                <el-button type="primary" @click="currentChange()" circle>
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:search" />
                        </el-icon>
                    </template>
                </el-button>
            </el-space>

            <el-divider />
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @sort-change="sortChange">
                <el-table-column prop="no" label="#" width="200" />
                <el-table-column prop="pickup_dispatched_at" :label="$t('dispatcher.orders.fields.dispatchedAt')"
                    width="160" sortable="custom" />
                <el-table-column prop="picked_up_by" :label="$t('dispatcher.orders.fields.pickedUpBy')"
                    sortable="custom" />
                <el-table-column prop="pickup_stop_no" :label="$t('dispatcher.orders.fields.pickupStopNo')"
                    sortable="custom">
                    <template #default="scope">
                        <el-tag type="success" effect="plain" style="width: 20;" v-if="scope.row.pickup_stop_no">
                            {{ scope.row.pickup_stop_no }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="picked_up_at" :label="$t('dispatcher.orders.fields.pickedUpAt')" width="160"
                    sortable="custom" />
                <el-table-column prop="quantity" :label="$t('fields.quantity')">
                    <template #default="scope">
                        {{ scope.row.sorted_package_count }} /
                        <el-text tag="b">{{ scope.row.quantity }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="is_sorted" :label="$t('dispatcher.orders.fields.isSorted')" align="center"
                    sortable="custom">
                    <template #default="scope">
                        <SuccessFilled v-if="scope.row.is_sorted" style="width: 1.5em; height: 1.5em; color:#67C23A" />
                        <QuestionFilled v-else-if="scope.row.sorted_package_count > 0"
                            style="width: 1.5em; height: 1.5em; color:#E6A23C" />
                        <span v-else>-</span>
                    </template>
                </el-table-column>

                <el-table-column prop="delivered_by" :label="$t('dispatcher.orders.fields.deliveredBy')"
                    sortable="custom" />
                <el-table-column prop="delivery_stop_no" :label="$t('dispatcher.orders.fields.deliveryStopNo')"
                    sortable="custom">
                    <template #default="scope">
                        <el-tag type="success" effect="dark" style="width: 20;" v-if="scope.row.delivery_stop_no">
                            {{ scope.row.delivery_stop_no }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="delivered_at" :label="$t('dispatcher.orders.fields.deliveredAt')" width="160"
                    sortable="custom" />
                <el-table-column :label="$t('fields.operations')" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('dispatcher.orders.operations.remove')"
                            placement="top-start"
                            v-if="!(scope.row.picked_up_at && scope.row.delivered_at) && scope.row.pickup_dispatched_at">
                            <el-button type="danger" :icon="CloseBold" circle size="small"
                                @click="onRemoveAssignment(scope.row, true)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
            <!-- <el-tab-pane label="Driver Orders" name="drivers">
                    <el-tabs tab-position="left" v-model="currentDriver" @tab-change="getDriverOrders"
                        type="border-card" style="height: calc(100vh - 280px);">
                        <el-tab-pane v-for="d of data.drivers" :name="d.id">
                            <template #label>
                                <div style="display: flex; justify-content: space-between; width: 100%;">
                                    <el-text tag="b" size="large" type="primary">
                                        {{ d.bucket_sn ?? d.bucket_name }}
                                    </el-text>
                                    <el-space size="small">
                                        <span style="color: #F56C6C;" v-if="d.is_active">◉</span>
                                        <el-text v-if="d.firstname" size="small" tag="p">{{ d.firstname }}</el-text>
                                        <el-text tag="b">{{ d.lastname }}</el-text>
                                    </el-space>

                                </div>
                            </template>
                            <el-table ref="table" v-loading="data.loading" :data="data.driverOrders" border stripe
                                style="height: calc(100vh - 300px);" highlight-current-row
                                :summary-method="getSummaries" show-summary>
                                <el-table-column :label="$t('dispatcher.orders.fields.stop')" prop="stop_no" width="55"
                                    align="center" fixed :resizable="false">
                                    <template #default="scope">
                                        <el-tag type="success" style="width: 30px;"
                                            :effect="scope.row.type == 'pickup' ? 'plain' : 'dark'">{{
                                                scope.row.stop_no
                                            }}</el-tag>
                                    </template>

                                </el-table-column>
                                <el-table-column prop="no" :label="$t('fields.no')" width="110" :resizable="false">
                                    <template #default="scope">
                                        {{ orderNoFormatter(_, _, scope.row.no) }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="quantity" :label="$t('fields.quantity')" width="150"
                                    align="center">
                                    <template #default="scope">
                                        <div style="display: flex; justify-content: center;">
                                            <div class="package-index" :class="packageIndexClass(scope.row, i)"
                                                v-for="i in Array.from(Array(scope.row.quantity)).map((e, i) => i + 1)">
                                                {{ i }}
                                            </div>
                                        </div>

                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" :label="$t('delivery.orders.fields.status')"
                                    :resizable="false" align="center" width="110">

                                    <template #default="scope">
                                        <el-tag :type="orderStatusStyles[scope.row.status]" round size="small">
                                            {{ $t(`delivery.orders.selections.status.${scope.row.status}`) }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="name" :label="$t('fields.name')" width="100"
                                    show-overflow-tooltip />
                                <el-table-column prop="address" :label="$t('dispatcher.orders.fields.address')"
                                    width="150" show-overflow-tooltip />
                                <el-table-column prop="eta" :label="$t('dispatcher.orders.fields.eta')" width="150"
                                    :resizable="false" />
                                <el-table-column prop="travel_time" :label="$t('dispatcher.orders.fields.travelTime')"
                                    width="100" :resizable="false" align="center">
                                    <template #default="scope">
                                        {{ scope.row.travel_time ? Math.round(scope.row.travel_time / 60) : '-' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="distance" :label="$t('dispatcher.orders.fields.distance')"
                                    width="100" :resizable="false" align="center"><template #default="scope">
                                        {{ scope.row.distance > 0 ? (scope.row.distance / 1000).toFixed(2) : '-' }}
                                    </template></el-table-column>
                                <el-table-column prop="dispatched_at"
                                    :label="$t('dispatcher.orders.fields.dispatchedAt')" width="160"
                                    :resizable="false" />
                                <el-table-column prop="completed_at" :label="$t('dispatcher.orders.fields.completedAt')"
                                    width="160" :resizable="false" />
                                <el-table-column :label="$t('fields.operations')" align="center" fixed="right">
                                    <template #default="scope">
                                        <el-tooltip class="box-item"
                                            :content="$t('dispatcher.orders.operations.remove')" placement="top-start"
                                            v-if="!scope.row.completed_at">
                                            <el-button type="danger" :icon="CloseBold" circle size="small"
                                                @click="onRemoveAssignment(scope.row)" />
                                        </el-tooltip>
                                    </template>
                                </el-table-column>
                            </el-table>

                        </el-tab-pane>
 -->

        </page-main>
        <FormMode v-if="['dialog', 'drawer'].includes(data.formMode)" :id="data.formModeProps.id"
            v-model="data.formModeProps.visible" :mode="data.formMode" @success="getDataList" />
        <el-dialog v-model="tokenDialogVisible" title="Sync Token" width="500" :before-close="handleClose">
            <el-space>
                <el-text size="large" tag="b">{{ token }}</el-text>
                <el-button @click.stop="copy(token)" size="small" type="primary" plain circle :icon="DocumentCopy" />
            </el-space>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="closeTokenDialog">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }

    .package-index {
        width: 20px;
        text-align: center;
        font-weight: bolder;
    }

    .completed {
        background-color: #79bbff;
        color: white;
    }

    .unsorted {
        background-color: #E6A23C;
        color: white;
    }

    .uncompleted {
        background-color: #F56C6C;
        color: white;
    }
</style>