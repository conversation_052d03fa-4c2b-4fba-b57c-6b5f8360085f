<script setup>
import { usePagination } from '@/utils/composables'
import { getElavonOrders, queryOrderPayments } from '@/api/modules/payment';
import { Refresh } from '@element-plus/icons-vue'

const { pagination, getParams, onCurrentChange } = usePagination()

const props = defineProps({
    orderId: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
    dataList: [],
})

const logStyles = {
    PEN: {
        type: 'warning',
        hollow: true
    },
    OPN: {
        type: 'success',
        hollow: true
    },
    REV: {
        type: 'danger',
        hollow: true
    },
    STL: {
        type: 'primary',
    },
    PST: {
        type: 'danger',
        hollow: true
    },
    FPR: {
        type: 'danger',
    },
    PRE: {
        type: 'danger',
        hollow: true
    },
}



defineExpose({
    reload() {
        getDataList()
    }
})


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams({ paymentOrderId: props.orderId })
    getElavonOrders(params).then(res => {
        pagination.value.total = res.data.total
        data.value.dataList = res.data.order_list
        data.value.loading = false
    })
}


function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

</script>
<template>
    <page-main>
        <template #title>
            <div class="title">
                Elavon&ThickSpace;{{ $t('finance.paymentOrder.sections.log') }}
            </div>
        </template>
        <el-timeline v-if="data.dataList?.length">
            <el-timeline-item v-for="(item, index) in data.dataList" :key="index" :timestamp="item.ssl_txn_time"
                :type="logStyles[item.ssl_trans_status]?.type" :hollow="logStyles[item.ssl_trans_status]?.hollow"
                placement="top">
                <el-space direction="vertical" alignment="start">
                    <el-space size="large">
                        <el-tag :type="logStyles[item.ssl_trans_status]?.type" v-if="item.ssl_trans_status">
                            {{ item.ssl_trans_status }}
                        </el-tag>
                        <span class="operator">{{ item.operator }}</span>
                    </el-space>
                    <el-descriptions :column="1">
                        <el-descriptions-item :label="$t('finance.ccLog.fields.status')" label-class-name="log-label"
                            class-name="log-content">
                            {{ item.ssl_trans_status ? $t(`finance.ccLog.selections.status.${item.ssl_trans_status}`) : '-'
                            }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.amount')" label-class-name="log-label"
                            class-name="log-content">
                            {{ item.ssl_amount }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.refundedAmount')"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.ssl_refunded_amount }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.currency')" label-class-name="log-label"
                            class-name="log-content">
                            {{ item.ssl_cardholder_currency }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.txnType')" label-class-name="log-label"
                            class-name="log-content">
                            <b>{{ item.ssl_transaction_type }}</b>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.result')" label-class-name="log-label"
                            class-name="log-content">
                            {{ item.ssl_result_message }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.txnId')" label-class-name="log-label"
                            class-name="log-content" with="100">
                            {{ item.ssl_txn_id }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.txnTime')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_txn_time }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.approvalCode')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_approval_code }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.oarData')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_oar_data }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.ps2000Data')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_ps2000_data }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.settlementBatch')"
                            label-class-name="log-label" class-name="log-content">
                            <span>{{ item.ssl_settlement_batch_response }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.settleTime')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_settle_time }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.cardType')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_card_type }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.token')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_token }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.customerId')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_customer_code }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.cardNumber')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_card_number }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.expDate')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_exp_date }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.shortDesc')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_card_short_description }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.avsResponse')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_avs_response }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.firstName')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_first_name }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.lastName')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_last_name }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.address')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_avs_address }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.zipCode')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_avs_zip }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.city')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_city }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.state')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_state }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.ccLog.fields.country')" label-class-name="log-label"
                            class-name="log-content">
                            <span>{{ item.ssl_country }}</span>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-space>
            </el-timeline-item>
        </el-timeline>
        <el-empty v-else :description="$t('noData')" />
        <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
            :layout="pagination.layoutM" :hide-on-single-page="true" class="pagination" @current-change="currentChange" />
    </page-main>
</template>

