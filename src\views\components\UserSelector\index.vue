<script setup>
    import { getUserSimpleList } from '@/api/modules/users'
    import { phoneNumberFormatter } from '@/utils/formatter'

    const props = defineProps({
        modelValue: {
            type: Array,
            default: null
        },

        disabled: {
            type: Boolean,
            default: false
        },

        placeHolder: {
            type: String,
            default: 'Input user name or company name to search'
        },

        multiple: {
            type: Boolean,
            default: false,
        },

    })


    const emit = defineEmits(['update:modelValue'])

    const userList = ref([])


    function getUsers(query) {
        if (query && query.length > 3) {
            getUserSimpleList({ terms: query }).then(res => {
                userList.value = res.data
            })
        } else {
            userList.value = []
        }
    }

    let selectedUser = computed({
        get: function () {
            return props.modelValue
        },
        set: function (val) {
            emit('update:modelValue', val)
        }
    })



</script>
<template>
    <el-select v-model="selectedUser" value-key="id" filterable remote reserve-keyword clearable
        style="min-width: 240px; width: 100%;" :disabled="props.disabled" :remote-method="getUsers"
        :placeholder="props.placeHolder" :multiple="props.multiple">
        <template #label="{ label, value }">
            <el-text tag='b' type="primary">{{ label.name }}</el-text>
            <el-text tag='b' type="info" size="small">
                {{ label.code ? (`(${label.code})`) : '' }}
            </el-text>
        </template>
        <el-option v-for="item of userList" :key="item.id" :value="item" :label="item">
            <el-space>
                <el-text tag='b' type="primary">{{ item.name }}</el-text>
                <el-text tag='b' type="info" size="small">
                    {{ item.code ? (`(${item.code})`) : '' }}
                </el-text>
                {{ item.email }}
                {{ phoneNumberFormatter(item.phone) }}
            </el-space>
        </el-option>
    </el-select>
</template>