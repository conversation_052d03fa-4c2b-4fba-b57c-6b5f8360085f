<script setup name="DispatcherExceptionsDetail">
import useSettingsStore from '@/store/modules/settings'
import DetailForm from './components/DetailForm/index.vue'
import { useTabbar } from '@/utils/composables'

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()

const formRef = ref()

// 返回列表页
function goBack() {
    if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
        useTabbar().close({ name: 'dispatcherExceptionLogs' })
    } else {
        router.push({ name: 'dispatcherExceptionLogs' })
    }
}
</script>

<template>
    <div>
        <page-header :title="'Detail'">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>
        <DetailForm :id="route.query.id" ref="formRef" />
    </div>
</template>
<style lang="scss" scoped>
// scss</style>