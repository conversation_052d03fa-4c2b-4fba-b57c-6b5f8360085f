const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/addresses',
    component: Layout,
    redirect: '/dispatcher/addresses/list',
    name: 'dispatcherAddresses',
    meta: {
        title: '地址库管理',
        icon: 'mdi:address-marker-outline',
        auth: ['super'],
        i18n: 'route.dispatcher.addresses.title'
    },
    children: [
        {
            path: 'list',
            name: 'dispatcherAddressList',
            component: () => import('@/views/dispatcher/addresses/list.vue'),
            meta: {
                title: '地址列表',
                icon: 'bx:key',
                activeMenu: '/dispatcher/addresses/list',
                i18n: 'route.dispatcher.addresses.title',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },
    ]
}
