const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/deliveryDestinations',
    component: Layout,
    redirect: '/settings/deliveryDestinations/list',
    name: 'deliveryDestinations',
    meta: {
        title: '目的地管理',
        icon: 'streamline:travel-map-triangle-flag-navigation-map-maps-flag-gps-location-destination-goal',
        auth: ['super'],
        i18n: 'route.system.deliveryDestinations'
    },
    children: [
        {
            path: 'list',
            name: 'deliveryDestinations',
            component: () => import('@/views/settings/delivery_destinations/list.vue'),
            meta: {
                title: '列表',
                icon: 'bx:key',
                activeMenu: '/settings/deliveryDestinations/list',
                i18n: 'route.system.deliveryDestinations',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },
    ]
}
