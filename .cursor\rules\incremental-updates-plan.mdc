# Refactoring Plan: Incremental Updates for Map Markers

**Goal:** Modify the Firebase message handling and map display logic to use incremental updates instead of full refreshes, resolving map marker flickering and improving performance.

**Key Files Involved:**

*   Message Processing: [src/modules/fleetdispatch/services/FirebaseMessagingService.js](mdc:src/modules/fleetdispatch/services/FirebaseMessagingService.js)
*   Order State Management: [src/modules/fleetdispatch/stores/order.js](mdc:src/modules/fleetdispatch/stores/order.js) (or related OrderService)
*   Event Bus: [src/modules/fleetdispatch/utils/eventBus.js](mdc:src/modules/fleetdispatch/utils/eventBus.js)
*   Map View: [src/modules/fleetdispatch/views/MapViewNew.vue](mdc:src/modules/fleetdispatch/views/MapViewNew.vue)
*   Marker Management: Likely [src/modules/fleetdispatch/composables/useMarkerManagement.js](mdc:src/modules/fleetdispatch/composables/useMarkerManagement.js) (Confirm actual file)
*   Order API: [src/modules/fleetdispatch/api/orderAPI.js](mdc:src/modules/fleetdispatch/api/orderAPI.js)

**Steps:**

1.  **Modify `FirebaseMessagingService.js`:**
    *   In `handleOrderStatusChange`, `handleOrderUpdate`: Replace `fetchOrders()` with `orderAPI.getOrder(orderId, orderType)`, call new store method `updateSingleOrder(orderData)`, emit new event `EVENT_TYPES.ORDER_UPDATED` with single order data.
    *   In `handleOrderCreate`: Replace full fetch logic with `orderAPI.getOrder(orderId, orderType)`, filter by time/shift, call new store method `addSingleOrder(orderData)`, emit new event `EVENT_TYPES.ORDER_ADDED` with single order data.
    *   In `flushPositionUpdates` (for `handleDriverPositionsUpdate`): Ensure `EVENT_TYPES.DRIVER_POSITIONS_UPDATED` payload contains only *changed* driver positions.
2.  **Modify Order Store (`stores/order.js` or Service):**
    *   Add method `updateSingleOrder(orderData)` to find and update a single order in state arrays (handle moves between assigned/unassigned).
    *   Add method `addSingleOrder(orderData)` to add a single order to the correct state array.
    *   Ensure methods do *not* call `fetchOrders()`.
3.  **Modify `eventBus.js`:**
    *   Add new constants: `EVENT_TYPES.ORDER_UPDATED`, `EVENT_TYPES.ORDER_ADDED`.
4.  **Modify `MapViewNew.vue`:**
    *   Remove/adjust listeners for full refresh events (e.g., `orders:updated`) when triggered by single updates.
    *   Add listeners for `EVENT_TYPES.ORDER_UPDATED`, `EVENT_TYPES.ORDER_ADDED`.
    *   Implement handlers (`handleOrderUpdated`, `handleOrderAdded`) that call corresponding *incremental* marker management functions (`updateMarker`, `addMarker`).
    *   Ensure handler for `EVENT_TYPES.DRIVER_POSITIONS_UPDATED` calls an *incremental* driver marker update function.
    *   **Crucially: Remove "clear all markers" logic from these incremental handlers.** Ensure full refresh logic (`isTimeRangeChange`) is only triggered by explicit filter changes.
5.  **Modify Marker Management Composable:**
    *   Implement `addMarker(orderData)`.
    *   Implement `updateMarker(orderData)`.
    *   Implement `removeMarker(orderId)` (optional, if needed).
    *   Implement `updateDriverMarkers(driversData)` for incremental driver updates.
    *   Use map library's API for single marker manipulation.
