<template>
    <div class="driver-list">
        <div class="header">
            <!-- 隐藏标题，因为已经在FlexibleLayout中有了 -->
            <!-- <h3>司机列表</h3> -->
            <DriverStatusManager />
        </div>

        <div v-if="driverStore.drivers?.length">
            <el-empty v-if="!availableDrivers.length" description="暂无在线司机" />

            <div v-else class="drivers">
                <div
                    v-for="driver in availableDrivers"
                    :key="driver.id"
                    class="driver-item"
                    :class="{ active: selectedDriver?.id === driver.id }"
                    @click="selectDriver(driver)"
                >
                    <div class="driver-main">
                        <div class="driver-color" :style="{ backgroundColor: driver.color }" />
                        <div class="driver-details">
                            <div class="driver-header">
                                <span class="driver-name">{{ driver.name }}</span>
                                <el-tag :type="driver.status === '在线' ? 'success' : 'info'" size="small">
                                    {{ driver.status }}
                                </el-tag>
                            </div>
                            <div class="driver-info">
                                <span class="order-count">
                                    <el-badge :value="getDriverOrderCount(driver.id)" :max="99" type="primary">
                                        订单数
                                    </el-badge>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-empty v-else description="暂无司机数据" />
    </div>
</template>

<script setup>
import { onMounted, computed } from 'vue'
import { useDriverStore } from '@/stores/driver'
import { storeToRefs } from 'pinia'
import { useOrderStore } from '@/stores/order'
import { useTimeStore } from '@/stores/time'
import DriverStatusManager from '@/components/DriverStatusManager.vue'
import { eventBus, EVENT_TYPES } from '@/utils/eventBus'

const driverStore = useDriverStore()
const orderStore = useOrderStore()
const timeStore = useTimeStore()
const { selectedDriver } = storeToRefs(driverStore)
const availableDrivers = computed(() =>
    driverStore.drivers.filter(driver => driver.status === '在线')
)

onMounted(async() => {
    try {
        // 确保已经有班次数据
        if (!timeStore.selectedShift) {
            await timeStore.fetchShifts()
            console.log('班次数据获取完成，当前班次:', timeStore.selectedShift ? timeStore.selectedShift.label : '未设置')
        }

        // 获取司机数据，传递 shift 参数
        console.log('获取司机数据，使用当前班次...')
        await driverStore.fetchDrivers({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift
        })
        console.log('司机数据获取完成，数量:', driverStore.drivers.length)
    } catch (error) {
        console.error('加载司机数据失败:', error)
    }
})

// 添加获取司机订单数量的方法
const getDriverOrderCount = driverId => {
    return orderStore.getDriverOrders(driverId).length
}

// 修改选中司机的方法
const selectDriver = driver => {
    if (selectedDriver.value?.id === driver.id) {
        driverStore.clearSelectedDriver()
    } else {
        driverStore.setSelectedDriver(driver)
    }

    // 使用事件总线发送司机选择变更事件
    eventBus.emit(EVENT_TYPES.DRIVER_SELECTED, {
        driverId: selectedDriver.value?.id || null,
        isSelected: !!selectedDriver.value
    })
}
</script>

<style scoped>
.driver-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.header {
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eaeaea;
    background-color: #f5f7fa;
}

.header h3 {
    margin: 0;
    color: #303133;
}

.drivers {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.driver-item {
    padding: 12px;
    border-radius: 6px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.driver-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-1px);
}

.driver-item.active {
    background-color: #ecf5ff;
    border-color: #409eff;
}

.driver-main {
    display: flex;
    align-items: center;
    gap: 12px;
}

.driver-color {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgb(0 0 0 / 10%);
}

.driver-details {
    flex-grow: 1;
    min-width: 0;
}

.driver-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.driver-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
}

.driver-info {
    margin-top: 4px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.order-count {
    display: flex;
    align-items: center;
}

:deep(.el-badge__content) {
    background-color: #409eff;
}

:deep(.el-tag--small) {
    height: 20px;
    padding: 0 6px;
    font-size: 12px;
}
</style>