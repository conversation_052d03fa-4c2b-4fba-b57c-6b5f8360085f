<script setup name="FinancePromotionsLogsList">
    import { usePagination } from '@/utils/composables'
    import { getCouponUsageLogs, exportExcelCouponUsageLogs, getAllTags, } from '@/api/modules/promotions'
    import { currencyFormatter } from '@/utils/formatter'
    import { fa } from 'element-plus/es/locales.mjs';

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const operations = ['LOCK', 'UNLOCK', 'USE']

    const data = ref({
        loading: false,
        // 搜索
        search: {
            coupon__tag__icontains: null,
            user__name__icontains: null,
            operation__in: ['LOCK', 'UNLOCK', 'USE'],
            created_at__lte: null,
            created_at__gte: null,

        },
        // 批量操作
        batch: {
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            coupon__tag__icontains: null,
            user__name__icontains: null,
            operation__in: ['LOCK', 'UNLOCK', 'USE'],
            created_at__lte: null,
            created_at__gte: null,


        }
        currentChange()
    }

    onMounted(() => {
        getDataList()
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getCouponUsageLogs(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.log_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }


    function onBatchExport() {
        let params = {
            ids: data.value.batch.selectionDataList.map(e => e.id),
        }
        data.value.loading = true;
        exportExcelCouponUsageLogs(params).then(res => {
            if (res.data.errCode == 365) {
                window.open(res.data.url);
            }
        })
        data.value.loading = false;
    }

    // Export
    const tags = ref()

    const exportDialogVisible = ref(false)
    const exportParams = ref({
        tag: null,
        operations: ['LOCK', 'UNLOCK', 'USE'],
        createdMin: null,
        createdMax: null,
    })

    function onExport() {
        getAllTags().then(res => {
            tags.value = res.data;
        })
        exportDialogVisible.value = true
    }

    function onConfirmExport() {
        let params = exportParams.value;
        data.value.loading = true;
        exportExcelCouponUsageLogs(params).then(res => {
            if (res.data.errCode == 365) {
                window.open(res.data.url);
            }
        })
        data.value.loading = false;
        onCancelExport();
    }

    function onCancelExport() {
        exportParams.value = {
            tag: null,
            operations: ['LOCK', 'UNLOCK', 'USE'],
            createdMin: null,
            createdMax: null,
        }
        exportDialogVisible.value = false;


    }
</script>

<template>
    <div>
        <page-header :title="$t('promotions.logs.couponUsage')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20" align="bottom">
                                <el-col :span="3">
                                    <el-form-item :label="$t('promotions.coupon.fields.tag')">
                                        <el-input v-model="data.search.coupon__tag__icontains"
                                            :placeholder="$t('placeholder', { field: $t('promotions.coupon.fields.tag') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="3">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5">
                                    <el-form-item :label="$t('fields.operations')">
                                        <el-checkbox-group v-model="data.search.operation__in">
                                            <el-checkbox v-for="o of operations" :label="o" :value="o" />
                                        </el-checkbox-group>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('fields.createdAtMin')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange"
                                                @clear="currentChange" @change="currentChange" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('fields.createdAtMax')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange"
                                                @clear="currentChange" @change="currentChange" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="7">
                                    <el-form-item>
                                        <el-button type="warning" @click="resetFilters()" plain>
                                            <template #icon>
                                                <el-icon>
                                                    <svg-icon name="ep:refresh-left" />
                                                </el-icon>
                                            </template>
                                            {{ $t('operations.reset') }}
                                        </el-button>
                                        <el-button type="primary" @click="currentChange()">
                                            <template #icon>
                                                <el-icon>
                                                    <svg-icon name="ep:search" />
                                                </el-icon>
                                            </template>
                                            {{ $t('operations.filter') }}
                                        </el-button>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button size="default" type="primary" plain @click=onBatchExport>
                        {{ $t('operations.batch', { op: $t('operations.export') }) }}
                    </el-button>
                </batch-action-bar>
                <el-button type="primary" @click="onExport">
                    {{ $t('operations.export') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @sort-change="sortChange" border
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="coupon.tag" :label="$t('promotions.coupon.fields.tag')" width="200"
                    show-overflow-tooltip />
                <el-table-column prop="coupon.code" :label="$t('promotions.coupon.fields.code')" />
                <el-table-column prop="coupon.discount" :label="$t('promotions.discounts.fields.name')" />
                <el-table-column prop="amount" :label="$t('fields.amount')" :formatter="currencyFormatter" />
                <el-table-column prop="user" :label="$t('fields.user')" />
                <el-table-column prop="operation" :label="$t('fields.operations')" align="center" width="100" />
                <el-table-column prop="record.order" :label="$t('fields.order')" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <el-dialog v-model="exportDialogVisible" :title="$t('operations.export')" align-center>
            <el-form :model="selectedTag" label-width="auto" style="max-width: 600px">
                <el-form-item :label="$t('promotions.coupon.fields.tag')">
                    <el-select v-model="exportParams.tag" placeholder="please select tag">
                        <el-option v-for="tag in tags" :label="tag.tag" :value="tag.tag" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('fields.operations')">
                    <el-checkbox-group v-model="exportParams.operations">
                        <el-checkbox v-for="o of operations" :label="o" :value="o" />
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item :label="$t('fields.createdAt')">
                    <el-space>
                        <el-date-picker v-model="exportParams.createdMin" type="date"
                            :placeholder="$t('fields.createdAtMin')" clearable format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" />
                        <span>~</span>
                        <el-date-picker v-model="exportParams.createdMax" type="date"
                            :placeholder="$t('fields.createdAtMax')" clearable format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" />
                    </el-space>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelExport">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmExport">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>