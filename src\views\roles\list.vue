<script setup name="RolesList">
import { deleteRole, getRoles, updateRoleAvailability } from '@/api/modules/permissions'
import { usePagination } from '@/utils/composables'
import eventBus from '@/utils/eventBus'
import { Delete } from '@element-plus/icons-vue'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        title: ''
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})

onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams()
    getRoles({ params }).then(res => {
        data.value.loading = false
        data.value.dataList = res.data
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'roleCreate'
    })
}

function onEdit(row) {
    router.push({
        name: 'roleEdit',
        query: {
            id: row.id
        }
    })
}

function onAlterAvailability(row) {
    let params = {
        id: row.id,
        isAvailable: !row.is_available
    }
    updateRoleAvailability(params).then(res => {
        if (res.data.errCode === 365) { getDataList() }
    })
}

function onDel(row) {
    ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
        deleteRole({ id: row.id }).then(res => {
            if (res.data.errCode === 365) { getDataList() }
        })
    })
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'banned-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('role.title')" />
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                :row-class-name="tableRowClassName" highlight-current-row :row-style="{ cursor: 'pointer' }"
                @row-dblclick="onEdit" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="desc" :label="$t('fields.desc')" />
                <el-table-column prop="permissions" :label="$t('administrator.fields.permissions')">
                    <template #default="scope">
                        <div v-for="(item, index) in scope.row.permissions" :key="index" class="permissions-container">
                            {{ $t(`${item.module}.title`) }} - {{ $t(`operations.${item.operation}`) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'primary'" circle size="small"
                                @click="onAlterAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .banned-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
