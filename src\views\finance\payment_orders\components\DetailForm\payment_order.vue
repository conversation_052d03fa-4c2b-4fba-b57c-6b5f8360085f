<script setup name="PaymentOrderDetail">
import { getPaymentOrders, AlterPaymentOrderStatus, queryOrderPayments, refundPaymentOrder, verifyPaymentOrder } from '@/api/modules/payment';
import { userNameFormatter, currencyFormatter } from '@/utils/formatter'
import statusStyle from '../../status'
import { Refresh, Loading } from '@element-plus/icons-vue'

import Log from './logs.vue'
import CCLog from './cc_logs.vue'
import APLog from './ap_logs.vue'
import CRLog from './credit_logs.vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const logRef = ref()
const crLogRef = ref()
const ccLogRef = ref()
const apLogRef = ref()

const data = ref({
    loading: false,
    form: {
        id: props.id,
        user: {
            name: null,
            user_code: null,
            company_name: null,
            user_type: null
        },
        created_at: null,
        updated_at: null,
        total: null,
        subtotal: null,
        tax: null,
        tip: null,
        refunded: null,
        status: null,
        no: null
    },
})

onMounted(() => {
    getInfo()
})

function getInfo() {
    data.value.loading = true
    getPaymentOrders({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function onQueryPayments() {
    data.value.loading = true
    let params = { id: data.value.form.id }
    verifyPaymentOrder(params).then((res) => {
        if (res.data.errCode == null) {
            data.value.loading = false
            data.value.form = res.data
            reloadLogs()
        }
    })
}

function reloadLogs() {
    logRef.value.reload()
    crLogRef.value.reload()
    if (data.value.form.cc_paid) {
        ccLogRef.value.reload()
    } else if (data.value.form.ap_paid) {
        apLogRef.value.reload()
    }
}


function onRefund() {
    data.value.loading = true
    let params = {
        id: data.value.form.id,
        amount: Math.round(refundAmount.value * 100),
        toCredits: refundToCredits.value
    }
    refundPaymentOrder(params).then(res => {
        if (res.data.errCode == 365) {
            onQueryPayments()
        }
        showRefundDialog.value = false
        data.value.loading = false

    })
}
function onAlterStatus(status) {
    let params = {
        id: data.value.form.id,
        status: status
    }
    AlterPaymentOrderStatus(params).then(res => {
        if (res.data.errCode === 365) {
            getInfo()
            logRef.value.reload()
        }
    })
}

// Refund
const showRefundDialog = ref(false)
const refundAmount = ref(0.0)
const refundToCredits = ref(true)


function onShowRefundDialog() {
    refundAmount.value = Number((data.value.form.still_to_refund / 100).toFixed(2))
    showRefundDialog.value = true
}
function onCancelRefund() {
    showRefundDialog.value = false
}
</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <template #title>
                <el-space size="large">
                    <el-tooltip placement="top" :content="$t('finance.paymentOrder.operations.query')">
                        <el-button type="primary" plain circle :icon="Refresh" size="small" :loading="data.loading"
                            @click="onQueryPayments" />
                    </el-tooltip>

                    <span>{{ data.form.no }}</span>
                    <el-tag :type="statusStyle[data.form.status]" round size="large">
                        ●&ThickSpace;{{ $t(`finance.paymentOrder.selections.status.${data.form.status}`) }}
                    </el-tag>
                </el-space>
            </template>
            <template #extra>
                <el-space>
                    <el-button type="danger" v-if="data.form.can_refund || data.form.can_refund_credits"
                        @click="onShowRefundDialog" :loading="data.loading">
                        {{ $t('finance.paymentOrder.operations.refund') }}
                    </el-button>
                    <el-button type="primary" v-if="data.form.can_close" plain @click="onAlterStatus('closed')"
                        :loading="data.loading">
                        {{ $t('finance.paymentOrder.operations.close') }}
                    </el-button>
                    <el-button type="primary" v-if="data.form.can_complete" @click="onAlterStatus('completed')"
                        :loading="data.loading">
                        {{ $t('finance.paymentOrder.operations.complete') }}
                    </el-button>
                </el-space>
            </template>
            <el-row>
                <el-col :span="12">
                    <el-descriptions :title="data.form.no" :column="1">
                        <template #title>
                            <el-space>
                                <el-tag v-if="data.form.user.user_code" size="small" type="info" effect="plain">
                                    {{ data.form.user.user_code }}</el-tag>
                                <span v-if="data.form.user.name != null">{{ userNameFormatter(data.form)
                                }}</span>
                            </el-space>
                        </template>
                        <el-descriptions-item :label="$t('fields.createdAt')">
                            {{ data.form.created_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('fields.updatedAt')">
                            {{ data.form.updated_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.paymentOrder.fields.amount')">
                            {{ currencyFormatter(_, _, data.form.total) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.paymentOrder.fields.tip')">
                            {{ currencyFormatter(_, _, data.form.tip) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.paymentOrder.fields.refunded')">
                            <span v-if="data.form.refunded_amount" class="refunded-column">
                                ({{ currencyFormatter(_, _, data.form.refunded_amount) }})
                            </span>
                            <span v-else>-</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.paymentOrder.fields.paymentMethod')">
                            <el-image :src="data.form.method_img" style="height: 20px;" />
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.paymentOrder.fields.paidAt')">
                            <span v-if="data.form.paid_at"> {{ data.form.paid_at }}</span>
                            <span v-else> {{ $t('finance.paymentOrder.selections.notPaid') }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.paymentOrder.fields.invoice')">
                            <span v-if="data.form.invoice?.no">
                                {{ data.form.invoice.no }}
                            </span>
                            <span v-else>
                                {{ $t('finance.paymentOrder.selections.noInvoice') }}
                            </span>
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-divider border-style="dashed" />
                    <el-descriptions :title="$t('finance.paymentOrder.fields.relatedOrders')" direction="vertical"
                        :column="1">
                        <el-descriptions-item v-for="(item, index) in data.form.related_orders">
                            <el-space>
                                <div class="circle">{{ index }}</div>
                                <el-text tag="b">{{ item.no }}:</el-text>
                                <el-text>{{ item.from }} </el-text>
                                <el-icon>
                                    <svg-icon name="ep:right" />
                                </el-icon>
                                <el-text>{{ item.to }}</el-text>
                            </el-space>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-col>
                <el-col :span="12">
                    <Log ref="logRef" :order-id="data.form.id" />
                </el-col>
            </el-row>
        </page-main>
        <el-row v-if="data.form.cc_paid || data.form.ap_paid || data.form.credits_paid">
            <el-col :span="12">
                <CRLog ref="crLogRef" :order-id="data.form.id" />
            </el-col>
            <el-col :span="12" v-if="data.form.cc_paid">
                <CCLog ref="ccLogRef" :order-id="data.form.id" />
            </el-col>
            <el-col :span="12" v-if="data.form.ap_paid">
                <APLog ref="apLogRef" :order-id="data.form.id" />
            </el-col>
        </el-row>
        <el-dialog v-model="showRefundDialog" title="Refund Payment" width="33%">
            <el-form :model="form">
                <el-form-item :label="$t('fields.amount')" :label-width="formLabelWidth">
                    <el-input-number v-model="refundAmount" :max="Number((data.form.still_to_refund / 100).toFixed(2))"
                        :min="0" :step="0.1" :precision="2" v-loading="data.loading" />
                </el-form-item>
                <el-form-item :label="$t('finance.paymentOrder.fields.refundToCredits')" :label-width="formLabelWidth">
                    <el-switch v-model="refundToCredits" v-loading="data.loading" @change="onRefundMethodChanged"
                        :disabled="!data.form.can_refund || data.form.credits_paid" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelRefund" v-loading="data.loading">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onRefund" v-loading="data.loading">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style>
.title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.wrap-text {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.operator {
    font-size: 12px;
    color: #a8abb2;
    font-weight: bold;
}

.log-label {
    font-size: 12px;
    color: #909399 !important;
    font-weight: bold;
}

.circle {
    border-radius: 50%;
    width: 24px;
    height: 24px;
    padding: 5px;
    background: #000;

    /* border: 3px solid #000; */
    color: #fff;
    text-align: center;
    font: 16px Arial, sans-serif;
}
</style>
