{"entities": {"activeUser": "Active user", "addedService": "Added service", "address": "Address", "administrator": "Administrator", "adminOrder": "Admin order", "apOrder": "AP Order", "ccOrder": "CC Order", "costAttribution": "Cost attribution", "csWorkSheet": "CS Worksheet", "deliveryDestination": "Delivery destination", "deliveryOrder": "Delivery Order", "deliveryReport": "Delivery Report", "deliverySetting": "Delivery Setting", "deliverySupplier": "Delivery Supplier", "deliverySupplierStore": "Delivery Supplier Store", "deliveryTime": "Delivery Time", "dispatcherRoute": "Dispatcher Route", "dispatcherSupplier": "Dispatcher Supplier", "distributionCenter": "Distribution Center", "driver": "Driver", "eliteParams": "Elite Params", "email": "Email", "emailCategory": "Email category", "emailSetting": "Email setting", "emailTemplate": "Email template", "extendedDeliveryOrder": "Extended Order", "fcmToken": "FCM Token", "holiday": "Holiday", "holidayFee": "Holiday fee", "invoice": "Invoice", "message": "Message", "messageCategory": "Message category", "messageSubscription": "Message Subscription", "messengerOperationTable": "Messenger operation table", "messengerSetting": "Messenger Setting", "module": "<PERSON><PERSON><PERSON>", "operation": "Operation", "packageType": "Package type", "packaging": "Packaging", "paymentOrder": "Payment Order", "paymentReport": "Payment Report", "paymentSetting": "Payment Setting", "permission": "Permission", "pickupTime": "Pickup time", "postalCode": "Postal code", "promotion": "Promotion", "promotionCategory": "Promotion category", "refundOrder": "Refund Order", "report": "Report", "role": "Role", "serviceOrder": "Service Order", "salary": "Salary", "sizeWeight": "Size & Weight", "supplier": "Supplier", "supplierOrder": "Supplier Order", "systemSettings": "System setting", "task": "Task", "taskFunction": "Task operation", "todo": "TODO", "todoModule": "TODO Module", "todoReport": "TODO Report", "todoSettings": "TODO Settings", "user": "User", "userAddress": "User address", "userCreditCard": "User Credit Card", "userGroup": "User Group", "userOrder": "User Order", "weekDayFee": "Weekday fee", "workSheet": "Worksheet", "workSheetPhoto": "Worksheet photo", "zone": "Zone"}, "messages": {"created": "{entity} has been created", "updated": "{entity} has been updated", "deleted": "{entity} has been deleted", "notUpdated": "{entity} has not been updated", "exists": "{entity} exists already", "notExists": "{entity} does not exists", "loggedIn": "{entity} has logged in", "loggedOut": "{entity} has logged out", "requestReceived": "{entity} Request has been received"}}