<script setup name="Donut<PERSON><PERSON>">
import { currencyFormatter } from '@/utils/formatter';
import { Pie } from '@antv/g2plot';
import { useI18n } from 'vue-i18n'
import { getPaymentGeneralStatistics } from '@/api/modules/payment'

const { t } = useI18n()

let donutPlot = undefined

const props = defineProps({
    k: {
        type: String,
        default: ''
    }
})

const key = ref(props.k)

const data = ref({})

const emit = defineEmits(['updateTotal'])

const config = {
    appendPadding: 10,
    angleField: 'value',
    colorField: 'type',
    radius: 1,
    innerRadius: 0.8,
    label: {
        type: 'outer',
        offset: 20,
        formatter: (v) => currencyFormatter('', '', v.value),
        style: {
            textAlign: 'center',
            fontSize: 14,
        },
    },
    tooltip: {
        formatter: (datum) => {
            return { name: t(`finance.paymentOrder.statistics.${datum.type}`), value: currencyFormatter('', '', datum.value) };
        },
    },
    legend: {
        itemName: {
            formatter: (value, item, idx) => t(`finance.paymentOrder.statistics.${value}`)
        }
    },
    meta: {},
    interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
    statistic: {
        title: {
            content: t(`finance.paymentOrder.statistics.${key.value}`),
            style: {
                color: "#BCBCBC"
            },
        },
        content: {
            style: {
                whiteSpace: 'pre-wrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                fontSize: 20,
            },
            formatter: () => currencyFormatter('', '', data.value.total_sum, ''),
        },
    },
}

onMounted(() => {
    let params = { section: key.value }
    getPaymentGeneralStatistics(params).then(res => {
        data.value = res.data
        emit('updateTotal', res.data)
        donutPlot?.destroy()
        donutPlot = new Pie(`donut-${key.value}`,
            {
                data: res.data.chart_data,
                ...config
            })
        donutPlot.render()
    })

})

onUnmounted(() => {
    donutPlot?.destroy()
})
</script>

<template>
    <div :id="`donut-${key}`" style="width: 100%; height: 300px;" />
    <div style="padding-left: 40px;">
        {{ $t('finance.paymentOrder.statistics.refundedSum') }}:
        {{ data.refunded_sum ? currencyFormatter(_, _, data.refunded_sum) : '-' }}
    </div>
</template>
