import { ref, computed } from 'vue';
import { useAddressStore } from '../../../stores/address';
import maplibregl from 'maplibre-gl';
import { eventBus, EVENT_TYPES } from '../../../utils/eventBus';

export function useWarehouseMarker(map, moveSymbolLayersToTop) {
    const addressStore = useAddressStore();
    
    // 仓库地址标记管理
    const showWarehouse = ref(true);
    const activeWarehousePopup = ref(null);
    
    // 添加仓库地址图层引用
    const warehouseLayer = ref(null);
    
    // 创建五角星图标
    const createStarIcon = (color = '#FF0000') => {
        if (!map.value) return;
        
        const iconId = `warehouse-star-${color.replace('#', '')}`;
        
        // 检查图标是否已存在
        if (map.value.hasImage(iconId)) return iconId;
        
        // 创建一个Canvas元素来绘制五角星图标
        const size = 32;
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        if (!ctx) return null;
        
        // 清除之前的内容
        ctx.clearRect(0, 0, size, size);
        
        // 绘制五角星图标
        ctx.save();
        
        // 设置填充颜色
        ctx.fillStyle = color;
        
        // 绘制五角星
        const centerX = size / 2;
        const centerY = size / 2;
        const outerRadius = size / 2 - 2;
        const innerRadius = outerRadius / 2.5;
        const spikes = 5;
        
        let rot = Math.PI / 2 * 3;
        let x = centerX;
        let y = centerY;
        const step = Math.PI / spikes;
        
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - outerRadius);
        
        for (let i = 0; i < spikes; i++) {
            x = centerX + Math.cos(rot) * outerRadius;
            y = centerY + Math.sin(rot) * outerRadius;
            ctx.lineTo(x, y);
            rot += step;
            
            x = centerX + Math.cos(rot) * innerRadius;
            y = centerY + Math.sin(rot) * innerRadius;
            ctx.lineTo(x, y);
            rot += step;
        }
        
        ctx.lineTo(centerX, centerY - outerRadius);
        ctx.closePath();
        ctx.fill();
        
        // 添加边框
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        ctx.restore();
        
        // 将画布转换为图像并添加到地图
        map.value.addImage(iconId, { 
            width: size, 
            height: size, 
            data: ctx.getImageData(0, 0, size, size).data 
        });
        
        console.log(`已创建仓库五角星图标: ${iconId}`);
        return iconId;
    };
    
    // 添加仓库地址图层
    const addWarehouseLayer = () => {
        if (!map.value) return;
        
        try {
            // 创建红色五角星图标
            createStarIcon('#FF0000');
            
            // 如果数据源不存在，创建数据源
            if (!map.value.getSource('warehouse-source')) {
                map.value.addSource('warehouse-source', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: []
                    }
                });
                console.log('已创建仓库地址数据源');
            }
            
            // 如果图层不存在，添加图层
            if (!map.value.getLayer('warehouse-layer')) {
                map.value.addLayer({
                    id: 'warehouse-layer',
                    type: 'symbol',
                    source: 'warehouse-source',
                    layout: {
                        // 使用五角星图标
                        'icon-image': 'warehouse-star-FF0000',
                        'icon-size': 0.8,
                        'icon-allow-overlap': true,
                        'icon-ignore-placement': true,
                        // 添加仓库标签
                        'text-field': '仓库',
                        'text-size': 12,
                        'text-offset': [0, 1.5], // 在图标下方显示文本
                        'text-anchor': 'top',
                        'text-allow-overlap': false,
                        'text-ignore-placement': false
                    },
                    paint: {
                        // 文本样式
                        'text-color': '#333',
                        'text-halo-color': '#fff',
                        'text-halo-width': 1
                    }
                });
                
                // 设置图层引用
                warehouseLayer.value = 'warehouse-layer';
                
                // 添加事件监听
                map.value.on('click', 'warehouse-layer', onWarehouseMarkerClicked);
                map.value.on('mouseenter', 'warehouse-layer', () => {
                    map.value.getCanvas().style.cursor = 'pointer';
                });
                map.value.on('mouseleave', 'warehouse-layer', () => {
                    map.value.getCanvas().style.cursor = '';
                });
                
                // 确保订单图层显示在仓库地址图层之上
                if (typeof moveSymbolLayersToTop === 'function') {
                    moveSymbolLayersToTop();
                }
                console.log('已添加仓库地址图层');
            } else {
                // 图层已存在，设置引用
                warehouseLayer.value = 'warehouse-layer';
            }
        } catch (error) {
            console.error('添加仓库地址图层时出错:', error);
        }
    };
    
    // 处理仓库标记点击事件
    const onWarehouseMarkerClicked = (e) => {
        if (!e.features || e.features.length === 0) return;
        
        // 获取仓库信息
        const warehouse = addressStore.getWarehouse;
        
        // 触发事件通知其他组件
        eventBus.emit(EVENT_TYPES.WAREHOUSE_SELECTED, warehouse);
        
        // 显示仓库信息弹窗
        showWarehousePopup(e.lngLat, warehouse);
    };
    
    // 显示仓库信息弹窗
    const showWarehousePopup = (lngLat, warehouse) => {
        // 如果已有弹窗，先移除
        if (activeWarehousePopup.value) {
            activeWarehousePopup.value.remove();
        }
        
        // 创建弹窗内容
        const popupContent = document.createElement('div');
        popupContent.className = 'warehouse-popup';
        popupContent.innerHTML = `
            <h3>仓库信息</h3>
            <p>地址: ${warehouse.address}</p>
            <p>城市: ${warehouse.city}</p>
            <p>省份: ${warehouse.province}</p>
            <p>邮编: ${warehouse.postal_code}</p>
        `;
        
        // 创建弹窗
        const popup = new maplibregl.Popup({
            closeButton: true,
            closeOnClick: true,
            maxWidth: '300px'
        })
            .setLngLat(lngLat)
            .setDOMContent(popupContent)
            .addTo(map.value);
        
        // 保存弹窗引用
        activeWarehousePopup.value = popup;
        
        // 监听弹窗关闭事件
        popup.on('close', () => {
            activeWarehousePopup.value = null;
        });
    };
    
    // 更新仓库标记
    const updateWarehouseMarker = () => {
        if (!map.value || !showWarehouse.value) return;
        
        // 获取仓库信息
        const warehouse = addressStore.getWarehouse;
        
        if (!warehouse || !warehouse.coordinates || warehouse.coordinates.length !== 2) {
            console.warn('仓库坐标无效，无法显示仓库标记');
            return;
        }
        
        console.log('更新仓库标记，坐标:', warehouse.coordinates);
        
        // 确保仓库地址图层存在
        const hasWarehouseLayer = warehouseLayer && warehouseLayer.value;
        if (!hasWarehouseLayer || !map.value.getLayer(warehouseLayer.value)) {
            console.log('添加仓库地址图层');
            addWarehouseLayer();
        }
        
        // 创建GeoJSON特征
        const feature = {
            type: 'Feature',
            geometry: {
                type: 'Point',
                // 注意：coordinates 需要是 [lng, lat] 格式
                coordinates: [warehouse.coordinates[1], warehouse.coordinates[0]]
            },
            properties: {
                id: warehouse.id,
                name: '仓库',
                address: warehouse.address,
                city: warehouse.city,
                province: warehouse.province,
                postal_code: warehouse.postal_code
            }
        };
        
        // 更新数据源
        try {
            const source = map.value.getSource('warehouse-source');
            if (source) {
                source.setData({
                    type: 'FeatureCollection',
                    features: [feature]
                });
                console.log('仓库地址数据源更新成功');
            } else {
                console.warn('仓库地址数据源不存在，无法更新');
                console.log('尝试重新添加仓库地址图层');
                addWarehouseLayer();
                
                // 重试一次更新数据源
                setTimeout(() => {
                    const retrySource = map.value.getSource('warehouse-source');
                    if (retrySource) {
                        retrySource.setData({
                            type: 'FeatureCollection',
                            features: [feature]
                        });
                        console.log('重试更新仓库地址数据源成功');
                    } else {
                        console.error('重试更新仓库地址数据源失败');
                    }
                }, 500);
            }
        } catch (error) {
            console.error('更新仓库地址数据源时出错:', error);
        }
    };
    
    // 切换仓库地址显示
    const toggleWarehouse = () => {
        showWarehouse.value = !showWarehouse.value;
        
        if (map.value && map.value.getLayer('warehouse-layer')) {
            const visibility = showWarehouse.value ? 'visible' : 'none';
            map.value.setLayoutProperty('warehouse-layer', 'visibility', visibility);
            console.log(`仓库地址图层可见性设置为: ${visibility}`);
        }
        
        if (showWarehouse.value) {
            updateWarehouseMarker();
        }
    };
    
    return {
        showWarehouse,
        addWarehouseLayer,
        updateWarehouseMarker,
        toggleWarehouse
    };
}
