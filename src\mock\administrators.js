import Mock from 'mockjs'

const AllList = []
for (let i = 0; i < 50; i++) {
    AllList.push(Mock.mock({
        id: '@id',
        title: '@ctitle(10, 20)'
    }))
}

export default [
    {
        url: '/mock/administrators/list',
        method: 'get',
        response: option => {
            let { title, from, limit } = option.query
            from = ~~from
            limit = ~~limit
            let list = AllList.filter(item => {
                return title ? item.title.includes(title) : true
            })
            let pageList = list.filter((item, index) => {
                return index >= from && index < (from + limit)
            })
            return {
                error: '',
                status: 1,
                data: {
                    list: pageList,
                    total: list.length
                }
            }
        }
    },
    {
        url: '/mock/administrators/detail',
        method: 'get',
        response: option => {
            let info = AllList.filter(item => item.id == option.query.id)
            return {
                error: '',
                status: 1,
                data: info[0]
            }
        }
    },
    {
        url: '/mock/administrators/create',
        method: 'post',
        response: {
            error: '',
            status: 1,
            data: {
                isSuccess: true
            }
        }
    },
    {
        url: '/mock/administrators/edit',
        method: 'post',
        response: {
            error: '',
            status: 1,
            data: {
                isSuccess: true
            }
        }
    },
    {
        url: '/mock/administrators/delete',
        method: 'post',
        response: {
            error: '',
            status: 1,
            data: {
                isSuccess: true
            }
        }
    }
]
