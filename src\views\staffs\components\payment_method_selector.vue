<script setup>
import { getPaymentMethods } from '@/api/modules/open';

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false,
    }
})

const methods = ref([])

onMounted(() => {
    getMethods()
})

function getMethods() {
    getPaymentMethods().then(res => {
        methods.value = res.data
    })
}

const emit = defineEmits(['update:modelValue'])

let method = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})

</script>

<template>
    <el-select v-model="method" placeholder="Select" :disabled="props.disabled">
        <el-option v-for="item in methods" :key="item.key" :label="item.value" :value="item.key" />
    </el-select>

</template>