<script setup name="MessengerCsMessagesList">
import ChatMessagesTab from './components/TabContent/chat_messages.vue'
import useMessagesStore from '@/store/modules/messages'
import OfflineMessagesTab from './components/TabContent/offline_messages.vue'
import MessagesTab from './components/TabContent/messages.vue'
const route = useRoute()

const messageStore = useMessagesStore()


const data = ref({
    loading: false,
})


const offlineMessageTabRef = ref()
const messageHistoryRef = ref()

const currentUserPk = ref()
const unreadOfflineMessageCount = ref(0)
const activeMessageTab = ref('0')


onMounted(() => {
    messageStore.getAdminStatus();
    let userId = route.params?.userId;
    if (userId != null) {
        currentUserPk.value = userId;
        console.log(userId);
    }
})


async function onPunchIn() {
    data.value.loading = true;
    await messageStore.punchIn()
    data.value.loading = false;
}

async function onPunchOut() {
    data.value.loading = true;
    await messageStore.punchOut();
    currentUserPk.value = null
    data.value.loading = false;
}

function onSetCurrentUser(id) {
    currentUserPk.value = id;
}

function onSetOfflineMessageCount(count) {
    unreadOfflineMessageCount.value = count;
    if (count > 0) {
        activeMessageTab.value = '0';
    } else {
        activeMessageTab.value = '1';
    }
    messageHistoryRef.value?.reload();
}

function reloadOfflineMessages() {
    offlineMessageTabRef.value?.reload();
}

</script>

<template>
    <div>
        <page-header :title="$t('messenger.titles.csMessages')">
            <template #extra>
                <el-button type="primary" size="large" v-if="!messageStore.adminIsOnline" @click="onPunchIn">
                    {{ $t('messenger.operations.punchIn') }}
                </el-button>
                <el-button type="primary" plain size="large" v-else @click="onPunchOut">
                    {{ $t('messenger.operations.punchOut') }}
                </el-button>
            </template>
        </page-header>
        <page-main style="height: calc(100vh - 322px); width: calc(100% - 40px);" v-if="messageStore.adminIsOnline">
            <el-row>
                <el-col :span="16">
                    <el-tabs v-model="activeMessageTab">
                        <el-tab-pane>
                            <template #label>
                                <el-space>
                                    <span>用户留言</span>
                                    <span class="badge" v-if="unreadOfflineMessageCount > 0">
                                        {{ unreadOfflineMessageCount }}
                                    </span>
                                </el-space>
                            </template>
                            <OfflineMessagesTab ref="offlineMessageTabRef" @set-current-user="onSetCurrentUser"
                                @set-unread-message-count="onSetOfflineMessageCount" />
                        </el-tab-pane>
                        <el-tab-pane label="客服消息">
                            <ChatMessagesTab :current-user-pk="currentUserPk" @set-current-user="onSetCurrentUser"
                                @message-sent="messageHistoryRef?.reload();" />
                        </el-tab-pane>
                    </el-tabs>
                </el-col>
                <el-col :span="8">
                    <el-tabs>
                        <el-tab-pane label="消息记录">
                            <MessagesTab v-if="currentUserPk != null" ref="messageHistoryRef" :id="currentUserPk"
                                @unread-messages="reloadOfflineMessages" />
                            <el-empty v-else />
                        </el-tab-pane>
                        <el-tab-pane label="订单记录">
                            订单记录
                        </el-tab-pane>
                        <el-tab-pane label="知识库">
                            知识库
                        </el-tab-pane>
                    </el-tabs>
                </el-col>
            </el-row>
        </page-main>
        <el-empty v-else />
    </div>
</template>
<style lang="scss" scoped>
.badge {
    background-color: var(--g-badge-bg);
    border-radius: 10px;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    color: var(--g-badge-color);
}
</style>
