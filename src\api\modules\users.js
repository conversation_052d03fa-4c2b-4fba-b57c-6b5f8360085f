import { GET, POST, PUT, PAT, DEL } from '../methods'

const path = 'users/'

export const getUsers = (params) => GET(path, params)
export const updateUser = (params) => PUT(path, params)
export const updateUserStatus = (params) => PAT(path, params)
export const deleteUser = (params) => DEL(path, params)


export const getUserGroups = (params) => GET(path + 'groups/', params)
export const addUserGroup = (params) => POST(path + 'groups/', params)
export const updateUserGroup = (params) => PUT(path + 'groups/', params)
export const updateUserGroupAvailability = (params) => PAT(path + 'groups/', params)
export const bulkUpdateUserGroupAvailability = (params) => POST(path + 'groups/bulk-alter-availability/', params)
export const deleteUserGroup = (params) => DEL(path + 'groups/', params)


export const getUserLogs = (params) => GET(path + 'logs/', params)
export const getCreditLogs = (params) => GET(path + 'credit-logs/', params)
export const getUserFee = params => GET(path + 'user-fee/', params)
export const getUserSimpleList = params => GET(path + 'simple-list/', params)

// Statistics
export const getUserStatistics = () => GET(path + 'statistics/')
export const getUserCreationStatistics = (params) => GET(path + 'statistics/creation/', params)
export const getUserOriginStatistics = (params) => GET(path + 'statistics/origin/', params)
export const getDauStatistics = (params) => GET(path + 'statistics/dau/', params)
export const getUserCreditsStatistics = (params) => GET(path + 'statistics/credits/', params)
