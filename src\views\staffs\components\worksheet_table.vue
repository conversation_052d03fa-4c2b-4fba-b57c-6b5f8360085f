<script setup>
    import {
        addWorksheet, updateWorksheet, getDrivers, removeWorksheetPhoto, uploadWorksheetPhoto, deleteWorksheet,
        addDeliveryStub,
    } from '@/api/modules/staffs'
    import { currencyFormatter } from '@/utils/formatter';
    import EmployeeSelector from './employee_selector.vue'

    const props = defineProps({
        worksheet: {
            type: Object,
            default: null,
        },
        onUpdate: {
            type: Function,
            default: null
        },
        onDelete: {
            type: Function,
            default: null,
        }
    })

    watch(() => props.worksheet, () => {
        data.value.worksheet = props.worksheet

    })
    const formRef = ref()

    const data = ref({
        worksheet: props.worksheet,
        rules: {
            employee: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            start_at: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            end_at: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
        },
        driverList: []
    })

    const readOnly = computed(() => data.value.worksheet.id != '' && !data.value.worksheet.can_update)

    onMounted(() => {
        getDriverList()
    })

    function getDriverList() {
        getDrivers().then(res => {
            data.value.driverList = res.data
        })
    }

    function onSubmitWorksheet() {
        formRef.value.validate(valid => {
            if (valid) {
                if (data.value.worksheet.id == '') {
                    addWorksheet(data.value.worksheet).then((res) => {
                        if (res.data.errCode == 365) props.onUpdate && props.onUpdate(res.data.id);
                    })
                } else {
                    updateWorksheet(data.value.worksheet).then((res) => {
                        if (res.data.errCode == 365) props.onUpdate && props.onUpdate();
                    })
                }
            }
        })
    }

    function onDeleteWorksheet() {
        let params = { id: data.value.worksheet.id }
        deleteWorksheet(params).then(res => {
            if (res.data.errCode == 365) {
                props.onDelete && props.onDelete()
            }
        })
    }

    function onAddStub() {
        let params = { worksheet: data.value.worksheet.id }
        addDeliveryStub(params).then(res => {
            if (res.data.errCode == 365) props.onUpdate && props.onUpdate()

        })
    }



    function disabledDate(time) {
        let date = new Date();
        let previousDate = date.setDate(date.getDate() - 2);
        return time.getTime() < previousDate;
    }

    const workingHours = computed(() => {
        if (data.value.worksheet.end_at != null && data.value.worksheet.start_at != null) {
            return (((new Date(data.value.worksheet.end_at).getTime() - new Date(data.value.worksheet.start_at).getTime()) / 1000 + data.value.worksheet.adjustment_minutes * 60) / 36).toFixed(2)
        }
    })

    const travelKm = computed(() => (data.value.worksheet.speedometer_end_at != null && data.value.worksheet.speedometer_start_at != null) ?
        data.value.worksheet.speedometer_end_at - data.value.worksheet.speedometer_start_at + data.value.worksheet.adjustment_travel_km : 0)

</script>

<template>
    <page-main :title="$t('staffs.fields.worksheet')">
        <template #extra>
            <el-space size="large" v-if="!readOnly">
                <el-button type="danger" text @click="onDeleteWorksheet" v-if="data.worksheet.id != ''">
                    {{ $t('operations.delete') }}
                </el-button>
                <el-button type="warning" text @click="getInfo" v-if="data.worksheet.id != ''">
                    {{ $t('operations.reset') }}
                </el-button>
                <el-button type="primary" @click="onSubmitWorksheet">{{ $t('operations.save') }}</el-button>
                <el-button type="success" plain v-if="data.worksheet.id != ''" @click="onAddStub">
                    {{ $t('operations.settle') }}
                </el-button>
            </el-space>
        </template>
        <el-form ref="formRef" :model="data.worksheet" :rules="data.rules" label-width="170px">
            <el-form-item :label="$t('delivery.worksheets.fields.sn')">
                {{ data.worksheet.sn }}
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.driver')" prop="driver">
                <EmployeeSelector v-model="data.worksheet.employee" :disable="readOnly" />
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.startAt')" prop="start_at">
                <el-date-picker v-model="data.worksheet.start_at" type="datetime" placeholder="Select date and time"
                    :disabled="readOnly" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="disabledDate" />
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.endAt')" prop="end_at">
                <el-date-picker v-model="data.worksheet.end_at" type="datetime" placeholder="Select date and time"
                    :disabled="readOnly" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="disabledDate" />
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.adjustmentHours')" prop="adjustment_minutes">
                <el-space>
                    <el-input-number v-model="data.worksheet.adjustment_minutes" placeholder="请输入标题"
                        style="width: 100%;" :disabled="readOnly" />
                    {{ $t('delivery.worksheets.fields.mins') }}
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.workingHours')">
                <span :class="workingHours < 0 ? 'red' : ''">
                    {{ currencyFormatter(_, _, workingHours) }}
                </span>
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.speedometerStartAt')" prop="speedometer_start_at">
                <el-input-number v-model="data.worksheet.speedometer_start_at" placeholder="请输入标题"
                    :disabled="readOnly" />
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.speedometerEndAt')" prop="speedometer_end_at">
                <el-input-number v-model="data.worksheet.speedometer_end_at" placeholder="请输入标题" :disabled="readOnly"
                    :min="data.worksheet.speedometer_start_at" />
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.adjustmentTravelKm')" prop="adjustment_travel_km">
                <el-space>
                    <el-input-number v-model="data.worksheet.adjustment_travel_km" placeholder="请输入标题"
                        :disabled="readOnly" />
                    Km
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.totalTravel')" prop="total_travel_km">
                <span :class="travelKm < 0 ? 'red' : ''">{{ travelKm }}</span>
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.photos')" prop="photos">
                <el-space size="large">
                    <template v-for="p of data.worksheet.photos">
                        <el-space direction="vertical">
                            <el-image style="width: 100px; height: 100px;" :src="p.photo" fit="cover"
                                :preview-src-list="[p.photo]" hide-on-click-modal />
                            <el-text size="small" tag="b">
                                <code>{{ $t(`staffs.worksheet.selections.photoCategories.${p.category}`) }}</code>
                            </el-text>
                        </el-space>
                    </template>
                </el-space>
            </el-form-item>
        </el-form>
    </page-main>
</template>