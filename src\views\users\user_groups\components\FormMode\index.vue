<script setup>
import DetailForm from '../DetailForm/index.vue'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

const { proxy } = getCurrentInstance()

const props = defineProps({
    // eslint-disable-next-line vue/valid-define-props
    ...DetailForm.props,
    modelValue: {
        type: Boolean,
        default: false
    },
})

const emit = defineEmits(['update:modelValue', 'success'])

let myVisible = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})

const title = computed(() => (props.id == '' ? t('operations.add') : t('operations.edit')) + ' ' + t('user.userGroup.instance'))

function onSubmit() {
    // submit() 为组件内部方法
    proxy.$refs['form'].submit(() => {
        emit('success')
        onCancel()
    })
}

function onCancel() {
    myVisible.value = false
}
</script>

<template>
    <div>
        <el-dialog v-model="myVisible" :title="title" width="600px" :close-on-click-modal="false" append-to-body
            destroy-on-close>
            <DetailForm ref="form" v-bind="$props" />
            <template #footer>
                <el-button size="large" @click="onCancel">{{ $t('operations.cancel') }}</el-button>
                <el-button type="primary" size="large" @click="onSubmit">{{ $t('operations.submit') }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>
