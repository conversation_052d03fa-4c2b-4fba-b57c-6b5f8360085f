<route>
{
    name: 'personalSetting',
    meta: {
        title: "个人设置",
        cache: "personal-edit.password"
    }
}
</route>

<script setup name="PersonalSetting">
import { getAdminProfile, updateAdminProfile, uploadAdminAvatar } from '@/api/modules/profile';
import useUserStore from '@/store/modules/user';
const router = useRouter()
const userStore = useUserStore()

const form = ref({
    avatar: '',
    name: '',
    role: '',
    username: '',
    is_super: null,
    is_available: null,
    updated_at: '',
    created_at: ''
})

onMounted(() => {
    getAdminProfile().then(res => {
        form.value = res.data
    })
})

function update() {
    let params = {
        name: form.value.name
    }
    updateAdminProfile(params).then(() => {
        userStore.checkLoginState()
    })
}

function handleSuccess(res) {
    console.log(res)
    if (res.data.errCode == 365) {
        form.value.avatar = res.data.avatar
        userStore.checkLoginState()
    } else {
        ElMessage.warning(res.data.msg)
    }
}
function editPassword() {
    router.push({
        name: 'personalEditPassword'
    })
}
</script>

<template>
    <div>
        <page-main>
            <el-tabs tab-position="left" style="height: 600px;">
                <el-tab-pane label="基本设置" class="basic">
                    <h2>基本设置</h2>
                    <el-row :gutter="20">
                        <el-col :span="16">
                            <div class="profile-input-group">
                                <span>name</span>
                                <el-input v-model="form.name" placeholder="请输入你的姓名" />
                                <el-button type="primary" @click="update">保存</el-button>
                            </div>
                            <el-descriptions column="1">
                                <el-descriptions-item label="username">{{ form.username }}</el-descriptions-item>
                                <el-descriptions-item label="role">
                                    <el-tag size="small" type="success" v-if="form.is_super">Super</el-tag>
                                    <el-tag size="small" v-else>{{ form.role }}</el-tag>
                                </el-descriptions-item>
                                <el-descriptions-item label="createdAt">{{ form.created_at }}</el-descriptions-item>
                                <el-descriptions-item label="updatedAt">{{ form.updated_at }}</el-descriptions-item>
                            </el-descriptions>
                        </el-col>
                        <el-col :span="8">
                            <image-upload v-model:url="form.avatar" :action="uploadAdminAvatar" noTip
                                class="avatar-upload" @on-success="handleSuccess" />
                        </el-col>
                    </el-row>
                </el-tab-pane>
                <el-tab-pane label="安全设置" class="security">
                    <h2>安全设置</h2>
                    <div class="setting-list">
                        <div class="item">
                            <div class="content">
                                <div class="title">账户密码</div>
                            </div>
                            <div class="action">
                                <el-button type="primary" text @click="editPassword">修改</el-button>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
.profile-input-group {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

:deep(.el-tabs) {

    .el-tabs__header .el-tabs__nav {

        .el-tabs__active-bar {
            z-index: 0;
            width: 100%;
            background-color: var(--el-color-primary-light-9);
            border-right: 2px solid var(--el-color-primary);
            transition: transform 0.3s, background-color 0.3s, var(--el-transition-border);
        }

        .el-tabs__item {
            text-align: left;
            padding-right: 100px;
        }
    }

    .el-tab-pane {
        padding: 0 20px 0 30px;
    }
}

h2 {
    margin: 0;
    margin-bottom: 30px;
    font-weight: normal;
}

.basic {

    :deep(.avatar-upload) {
        text-align: center;

        .el-upload-dragger {
            border-radius: 50%;
        }
    }
}

.security {

    .setting-list {

        .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid var(--el-border-color-lighter);
            transition: var(--el-transition-border);

            .content {

                .title {
                    margin-bottom: 5px;
                    color: var(--el-text-color-primary);
                    transition: var(--el-transition-color);
                }

                .desc {
                    font-size: 14px;
                    color: var(--el-text-color-secondary);
                    transition: var(--el-transition-color);
                }
            }

            &:last-child {
                border-bottom: 0;
            }
        }
    }
}
</style>
