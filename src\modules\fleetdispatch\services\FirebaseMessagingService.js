/**
 * Fleet Dispatch Firebase消息服务
 * 用于处理Fleet Dispatch相关的Firebase消息
 */

import { onMessage, getToken, deleteToken } from 'firebase/messaging';
import { useFirebaseMessaging } from '@/utils/composables/firebase_messaging';
import { useDriverStore } from '../stores/driver';
import { useOrderStore } from '../stores/order';
import { useTimeStore } from '../stores/time';
import { eventBus, EVENT_TYPES } from '../utils/eventBus';
import { orderAPI } from '../api';
import { vapidKey, clearFirebaseMessagingStorage, registerFirebaseServiceWorker } from '@/firebase';
import { storeToken } from '@/api/modules/messenger';
import useUserStore from '@/store/modules/user';

// 司机位置更新节流设置
const POSITION_UPDATE_INTERVAL = 3000; // 3秒更新一次位置

export class FirebaseMessagingService {
    constructor() {
        this.messaging = null;
        this.initialized = false;
        this.currentToken = null;
        this.tokenLastUpdated = null;
        this.tokenCheckInterval = null;

        // 位置更新节流与批处理
        this.positionQueue = [];
        this.positionUpdateTimer = null;
        this.lastFlushTime = 0;

        // 绑定方法到实例
        this.onReceiveMessage = this.onReceiveMessage.bind(this);
        this.handleNewOrder = this.handleNewOrder.bind(this);
        this.handleDriverLocationUpdate = this.handleDriverLocationUpdate.bind(this);
        this.handleOrderStatusChange = this.handleOrderStatusChange.bind(this);
        this.handleDispatcherNotification = this.handleDispatcherNotification.bind(this);
        this.handleDriverUpdate = this.handleDriverUpdate.bind(this);
        this.handleOrderCreate = this.handleOrderCreate.bind(this);
        this.handleOrderUpdate = this.handleOrderUpdate.bind(this);
        this.getToken = this.getToken.bind(this);
        this.checkAndRefreshToken = this.checkAndRefreshToken.bind(this);
        this.handleDriverPositionsUpdate = this.handleDriverPositionsUpdate.bind(this);
        this.flushPositionUpdates = this.flushPositionUpdates.bind(this);
    }

    /**
     * 初始化Firebase消息服务
     */
    initialize() {
        if (this.initialized) return;

        try {
            this.messaging = useFirebaseMessaging();
            this.initialized = true;
            console.log('Fleet Dispatch Firebase消息服务初始化成功');

            // 设置全局控制变量，用于禁用所有声音和通知
            window.__suppressNotificationSounds = true; // 禁用所有通知声音
            window.__suppressNotifications = true;      // 禁用所有通知弹窗

            // 尝试获取令牌
            this.getToken().catch(error => {
                console.error('获取Firebase令牌失败:', error);
            });

            // 设置定期检查令牌有效性的计时器
            this.setupTokenRefresh();

            return true;
        } catch (error) {
            console.error('Fleet Dispatch Firebase消息服务初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置定期检查令牌有效性
     */
    setupTokenRefresh() {
        // 清除之前的定时器
        if (this.tokenCheckInterval) {
            clearInterval(this.tokenCheckInterval);
        }

        // 每小时检查一次令牌是否需要刷新
        this.tokenCheckInterval = setInterval(() => {
            this.checkAndRefreshToken();
        }, 60 * 60 * 1000); // 1小时

        // 页面可见性变化时也检查令牌
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.checkAndRefreshToken();
            }
        });
    }

    /**
     * 检查并刷新令牌
     */
    async checkAndRefreshToken() {
        const userStore = useUserStore();

        // 如果没有令牌或令牌已过期，重新获取
        if (!this.currentToken || userStore.isTokenExpired) {
            console.log('FCM令牌不存在或已过期，正在重新获取...');
            try {
                // 先尝试删除旧token
                if (this.currentToken) {
                    await deleteToken(this.messaging);
                    this.currentToken = null;
                }

                // 重新获取token
                await this.getToken();
            } catch (error) {
                console.error('刷新FCM令牌失败:', error);
            }
        }
    }

    /**
     * 获取Firebase消息推送令牌
     * @param {boolean} forceRefresh 是否强制刷新令牌
     * @returns {Promise<string>} 返回令牌字符串的Promise
     */
    async getToken(forceRefresh = false) {
        if (!this.initialized) {
            this.initialize();
        }

        try {
            // 检查并注册Service Worker - 这是FCM正常工作的前提
            console.log('检查Service Worker状态...');
            const swRegistration = await registerFirebaseServiceWorker();
            if (!swRegistration) {
                console.error('Service Worker注册失败，无法获取FCM令牌');
                throw new Error('Service Worker注册失败');
            }
            console.log('Service Worker状态正常，继续获取FCM令牌');

            // 如果强制刷新，先尝试删除现有令牌
            if (forceRefresh && this.currentToken) {
                console.log('正在强制刷新FCM令牌...');
                try {
                    // 先删除现有令牌
                    await deleteToken(this.messaging);
                    console.log('成功删除旧FCM令牌');
                    this.currentToken = null;

                    // 清理Firebase消息相关存储
                    await clearFirebaseMessagingStorage();
                    console.log('已清理Firebase消息相关存储');

                    // 短暂延迟，确保删除操作和清理操作完成
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // 重新检查Service Worker状态
                    const refreshedSW = await registerFirebaseServiceWorker();
                    if (!refreshedSW) {
                        console.error('刷新后Service Worker注册失败');
                        throw new Error('刷新后Service Worker注册失败');
                    }
                } catch (deleteError) {
                    console.warn('删除旧FCM令牌失败:', deleteError);
                    // 继续尝试获取新令牌
                }
            }

            // 获取新令牌
            try {
                console.log('开始获取FCM令牌...');
                this.currentToken = await getToken(this.messaging, {
                    vapidKey: vapidKey,
                    serviceWorkerRegistration: swRegistration
                });
            } catch (tokenError) {
                console.error('获取FCM令牌时发生错误:', tokenError);

                // 检查是否是Service Worker错误
                if (tokenError.message && (
                    tokenError.message.includes('no active Service Worker') ||
                    tokenError.message.includes('subscribe') ||
                    tokenError.message.includes('PushManager')
                )) {
                    console.log('检测到Service Worker错误，尝试重新注册...');

                    // 彻底清理旧的Service Worker和缓存
                    await clearFirebaseMessagingStorage();

                    // 等待一会儿以确保清理完成
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 强制重新注册Service Worker
                    const forcedSW = await registerFirebaseServiceWorker();
                    if (!forcedSW) {
                        throw new Error('无法重新注册Service Worker');
                    }

                    // 再次尝试获取令牌
                    console.log('使用新注册的Service Worker重新获取令牌');
                    this.currentToken = await getToken(this.messaging, {
                        vapidKey: vapidKey,
                        serviceWorkerRegistration: forcedSW
                    });
                } else {
                    // 其他错误直接抛出
                    throw tokenError;
                }
            }

            if (this.currentToken) {
                console.log('Firebase消息推送令牌:', this.currentToken);

                // 更新令牌时间戳
                this.tokenLastUpdated = Date.now();

                // 使用事件总线广播令牌已获取事件
                eventBus.emit(EVENT_TYPES.FIREBASE_TOKEN_UPDATED, this.currentToken);

                // 将令牌保存到后端
                try {
                    await storeToken({ token: this.currentToken });
                    console.log('Firebase令牌已成功发送到后端');
                } catch (storeError) {
                    console.error('将Firebase令牌发送到后端失败:', storeError);
                }

                return this.currentToken;
            } else {
                console.error('没有可用的消息推送令牌，可能需要请求权限');
                return null;
            }
        } catch (error) {
            console.error('获取Firebase消息推送令牌失败:', error);
            throw error;
        }
    }

    /**
     * 处理接收到的消息
     * @param {Object} message Firebase消息对象
     */
    onReceiveMessage(message) {
        console.log('Fleet Dispatch收到Firebase消息:', message);

        // 检查是否停止接收消息
        const userStore = useUserStore();
        if (userStore.stopReceivingMessages) {
            console.log('用户已设置停止接收消息，不处理该消息');
            return;
        }

        // 优化：添加时间戳跟踪，防止短时间内重复处理
        const now = Date.now();
        const lastMsgTime = this._lastMessageTime || 0;
        const timeSince = now - lastMsgTime;

        // 如果距离上次消息处理不到100ms，记录并继续处理（不跳过）
        if (timeSince < 100) {
            console.log(`警告: 非常频繁的消息处理，距上次仅 ${timeSince}ms`);
        }

        // 记录此次消息处理时间
        this._lastMessageTime = now;

        // 检查消息格式
        let messageData;
        let isTransferredMessage = false;

        // 判断消息是来自Firebase直接推送还是从messageStore转发
        if (message.data && message.notification) {
            // 直接来自Firebase的消息
            console.log('收到来自Firebase的直接推送消息');
            messageData = {
                ...message.data,
                ...message.notification
            };
        } else if (message.data) {
            // 来自Firebase但没有notification部分的消息
            console.log('收到来自Firebase的消息(无notification)');
            messageData = message.data;
        } else {
            // 可能是从messageStore转发的消息
            console.log('检测到从messageStore转发的消息，尝试重新格式化');
            isTransferredMessage = true;

            // 如果是对象格式，直接使用
            if (typeof message === 'object' && message !== null) {
                messageData = message;

                // 这里记录完整消息内容，方便调试
                console.log('转发消息完整内容:', JSON.stringify(messageData));
            } else {
                // 如果是其他格式，尝试处理
                console.warn('转发消息格式不是对象:', typeof message);
                try {
                    if (typeof message === 'string') {
                        messageData = JSON.parse(message);
                    } else {
                        messageData = { raw: message };
                    }
                } catch (error) {
                    console.error('解析转发消息失败:', error);
                    messageData = { raw: message };
                }
            }
        }

        // 前台状态明确记录
        console.log('当前页面在前台状态，直接处理消息');

        // 检查新格式的司机位置更新消息 - 首先处理action字段
        // 检查是否有action字段，优先处理带action的消息
        if (messageData.action) {
            console.log('检测到带action字段的消息:', messageData.action);

            switch (messageData.action) {
                case 'DRIVER_POSITIONS':
                    console.log('检测到新格式的司机位置更新消息，立即处理');
                    // 确保立即强制更新（即使最近刚更新）
                    window.__lastDriverPositionUpdate = 0;
                    this.handleDriverPositionsUpdate(messageData);

                    // 使用事件总线广播消息，方便其他组件订阅
                    messageData.type = 'driver_location_update'; // 为兼容性添加type字段
                    eventBus.emit(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, messageData);
                    return;

                case 'ORDER_STATUS_UPDATED':
                    console.log('检测到订单状态更新消息');

                    // 转发为订单状态变更类型消息
                    messageData.type = 'order_status_change';
                    this.handleOrderStatusChange(messageData);

                    // 使用事件总线广播消息
                    eventBus.emit(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, messageData);
                    return;

                case 'DRIVER_UPDATE':
                    console.log('检测到DRIVER_UPDATE消息，处理为调度系统通知');
                    this.handleDispatcherNotification(messageData);

                    // 使用事件总线广播消息
                    eventBus.emit(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, messageData);
                    return;

                case 'ORDER_UPDATE':
                    console.log('检测到ORDER_UPDATE消息，处理为订单更新通知');
                    this.handleOrderUpdate(messageData);

                    // 使用事件总线广播消息
                    eventBus.emit(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, messageData);
                    return;

                case 'ORDER_STATUS_UPDATES':
                    console.log('检测到ORDER_STATUS_UPDATES消息，处理为订单状态更新通知');
                    this.handleOrderStatusChange(messageData);

                    // 使用事件总线广播消息
                    eventBus.emit(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, messageData);
                    return;

                case 'ORDER_CREATED':
                    console.log('检测到ORDER_CREATED消息，处理为订单创建通知');
                    this.handleOrderCreate(messageData);

                    // 使用事件总线广播消息
                    eventBus.emit(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, messageData);
                    return;

                default:
                    console.log('未识别的action类型:', messageData.action);
                    break;
            }
        }

        // 检查是否为调度系统通知
        if (messageData.catalog === 'DISPATCHER_NOTIFICATION') {
            console.log('收到调度系统通知:', messageData);
            // 调度系统通知不需要播放声音和显示通知
            this.handleDispatcherNotification(messageData);

            return;
        }

        // 处理老格式的消息
        switch (messageData.type) {
            case 'new_order':
                this.handleNewOrder(messageData);
                break;
            case 'driver_location_update':
                this.handleDriverLocationUpdate(messageData);
                break;
            case 'order_status_change':
                this.handleOrderStatusChange(messageData);
                break;
            default:
                console.log('未知的消息类型:', messageData.type || '无类型');

                // 如果是从messageStore转发的消息但未识别类型，尝试检查action字段
                if (isTransferredMessage && messageData.action === 'DRIVER_UPDATE') {
                    console.log('检测到未分类的DRIVER_UPDATE消息，尝试手动处理');
                    this.handleDispatcherNotification(messageData);
                } else {
                    // 对于完全未知的消息，记录更详细的信息
                    console.log('接收到未处理的消息，详细信息:', JSON.stringify(messageData));
                }
                break;
        }

        // 使用事件总线广播消息，方便其他组件订阅
        eventBus.emit(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, messageData);
    }

    /**
     * 处理调度系统通知
     * @param {Object} messageData 消息数据
     */
    handleDispatcherNotification(messageData) {
        console.log('处理调度系统通知:', messageData);

        try {
            // 检查消息格式是否有效
            if (!messageData) {
                console.error('调度系统通知数据无效');
                return;
            }

            // 记录关键字段，便于调试
            console.log('调度系统通知详情:', {
                action: messageData.action,
                catalog: messageData.catalog,
                id: messageData.id,
                created_at: messageData.created_at,
                analytics_label: messageData.analytics_label
            });

            // 根据action不同执行不同操作
            switch (messageData.action) {
                case 'DRIVER_UPDATE':
                    console.log('处理DRIVER_UPDATE消息');
                    this.handleDriverUpdate(messageData);
                    break;
                case 'ORDER_CREATE':
                    console.log('处理ORDER_CREATE消息');
                    this.handleOrderCreate(messageData);
                    break;
                case 'ORDER_UPDATE':
                    console.log('处理ORDER_UPDATE消息');
                    this.handleOrderUpdate(messageData);
                    break;
                // 可以添加其他action的处理
                default:
                    console.log('未知的调度系统通知操作:', messageData.action);
                    break;
            }

            // 发送通用调度系统通知事件
            console.log('广播调度系统通知事件:', EVENT_TYPES.DISPATCHER_NOTIFICATION_RECEIVED);
            eventBus.emit(EVENT_TYPES.DISPATCHER_NOTIFICATION_RECEIVED, messageData);
        } catch (error) {
            console.error('处理调度系统通知时发生错误:', error);
        }
    }

    /**
     * 处理司机更新通知
     * @param {Object} messageData 消息数据
     */
    handleDriverUpdate(messageData) {
        console.log('处理司机位置更新通知:', messageData);

        try {
            // 检查消息格式是否有效
            if (!messageData) {
                console.error('司机位置更新通知数据无效');
                return;
            }

            // 解析消息内容
            let driversPositions = [];
            let bodyContent = messageData.body;

            console.log('司机位置数据类型:', typeof bodyContent);

            // 处理body字段可能是字符串或已经是对象的情况
            try {
                // 如果body是字符串，尝试解析JSON
                if (typeof bodyContent === 'string') {
                    console.log('尝试解析字符串格式的body');
                    driversPositions = JSON.parse(bodyContent);
                }
                // 如果body已经是对象，直接使用
                else if (Array.isArray(bodyContent)) {
                    console.log('body已经是数组格式');
                    driversPositions = bodyContent;
                }
                // 如果body是对象但不是数组，尝试包装成数组
                else if (typeof bodyContent === 'object' && bodyContent !== null) {
                    console.log('body是对象但不是数组，尝试包装');
                    driversPositions = [bodyContent];
                }
                // 如果body是其他格式，记录错误
                else {
                    console.error('无法处理的body格式:', bodyContent);
                    return;
                }

                console.log('解析后的司机位置数据:', driversPositions);

                // 验证解析结果
                if (!Array.isArray(driversPositions)) {
                    console.error('解析后的司机位置数据不是数组');
                    driversPositions = [driversPositions]; // 尝试强制转换为数组
                }

                if (driversPositions.length === 0) {
                    console.warn('司机位置数据为空数组');
                    return;
                }
            } catch (error) {
                console.error('解析司机位置数据失败:', error);
                console.log('原始body数据:', bodyContent);
                return;
            }

            // 获取司机存储器
            const driverStore = useDriverStore();
            console.log('成功获取driverStore');

            // 使用driverStore的批量更新方法更新司机位置
            console.log('尝试更新司机位置数据，数量:', driversPositions.length);
            const updateResult = driverStore.updateMultipleDriverLocations(driversPositions);

            if (updateResult) {
                console.log('司机位置批量更新成功');

                // 记录最后更新时间
                window.__lastDriverPositionUpdate = Date.now();

                // 使用防抖机制触发司机位置更新事件
                console.log('触发司机位置更新事件，数据:', driverStore.allDriverPositions.length, '条');
                eventBus.emit(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, driverStore.allDriverPositions);

                // 移除多余的延迟触发，减少重复更新
            } else {
                console.warn('司机位置批量更新失败或无数据更新');
            }
        } catch (error) {
            console.error('处理司机位置更新失败:', error);
        }
    }

    /**
     * 处理新订单消息
     * @param {Object} messageData 消息数据
     */
    handleNewOrder(messageData) {
        console.log('收到新订单通知:', messageData);
        const orderStore = useOrderStore();

        // 刷新订单列表
        orderStore.fetchOrders().then(() => {
            console.log('订单列表已刷新');

            // 播放通知声音
            this.playNotificationSound('new_order');

            // 显示通知
            this.showNotification('新订单', messageData.body || '有新的订单需要处理');
        });
    }

    /**
     * 处理司机位置更新消息
     * @param {Object} messageData 消息数据
     */
    handleDriverLocationUpdate(messageData) {
        console.log('收到司机位置更新:', messageData);
        const driverStore = useDriverStore();

        // 更新司机位置
        if (messageData.driver_id && messageData.latitude && messageData.longitude) {
            // 如果司机存在，更新其位置
            const driver = driverStore.getDriverById(messageData.driver_id);
            if (driver) {
                // 这里假设driverService有updateDriverLocation方法
                driverStore.driverService.updateDriverLocation(
                    messageData.driver_id,
                    {
                        latitude: messageData.latitude,
                        longitude: messageData.longitude,
                        timestamp: messageData.timestamp || new Date().toISOString()
                    }
                );
            }
        }
    }

    /**
     * 处理订单状态变更消息
     * @param {Object} messageData 消息数据
     */
    async handleOrderStatusChange(messageData) {
        console.log('收到订单状态变更 (优化 v4):', messageData);
        const orderStore = useOrderStore();

        // 解析消息获取 orderId 和 status
        let orderId;
        let newStatus;

        try {
            let bodyData = {};
            if (messageData.body && typeof messageData.body === 'string') {
                bodyData = JSON.parse(messageData.body);
                orderId = bodyData.id;
                newStatus = bodyData.status; // 直接从 body 解析 status
            } else if (messageData.data?.id) { // 尝试从 messageData.data 获取
                 orderId = messageData.data.id;
                 newStatus = messageData.data.status; // 也尝试从 data 获取 status
            } else if (messageData.id) { // 最后才尝试顶层 id (可能性较低)
                 orderId = messageData.id;
                 newStatus = messageData.status;
            }

            if (!orderId) {
                console.error('无法从订单状态变更消息中解析 orderId:', messageData);
                return;
            }

            if (!newStatus) {
                 console.warn('订单状态变更消息中未找到 status 字段:', messageData);
                 return; // 没有新状态则不处理
            }

        } catch (error) {
            console.error('解析订单状态变更消息失败:', error, messageData);
            return;
        }

        console.log(`准备使用 Vue 响应式系统更新订单 ${orderId} 状态为 '${newStatus}'...`);

        try {
            // 使用 OrderStore 的 updateOrderStatus 方法更新订单状态
            const updateResult = orderStore.updateOrderStatus(orderId, newStatus);

            if (updateResult) { // 如果状态更新成功
                // 获取更新后的完整订单数据
                const updatedOrderFullData = orderStore.allOrders.find(order => order.id === orderId);

                if (updatedOrderFullData) {
                    // 发出 ORDER_UPDATED 事件，包含完整的订单数据
                    eventBus.emit(EVENT_TYPES.ORDER_UPDATED, {
                        isStatusChange: true,
                        orders: [updatedOrderFullData], // 将完整的订单数据放入数组
                        status: newStatus, // 添加状态信息
                        type: updatedOrderFullData.type, // 添加订单类型信息
                        timestamp: Date.now() // 添加时间戳
                    });
                    console.log(`[Vue Reactivity] 已发出 ORDER_UPDATED 事件，订单 ID: ${orderId}, 状态: ${newStatus}, 类型: ${updatedOrderFullData.type}`);

                    // 显示通知
                    const notificationTitle = messageData.title || '订单状态更新';
                    const notificationBody = `订单 ${messageData.subtitle || orderId} 状态已更新为 ${newStatus}`;
                    this.showNotification(notificationTitle, notificationBody);
                } else {
                     console.warn(`[Vue Reactivity] 更新订单 ${orderId} 状态后未能从 store 中获取完整数据`);
                     // 降级处理：发送部分数据
                     eventBus.emit(EVENT_TYPES.ORDER_UPDATED, {
                          isStatusChange: true,
                          orders: [{ id: orderId, status: newStatus }] // 最小化数据
                     });
                     // 显示通知
                     const notificationTitle = messageData.title || '订单状态更新';
                     const notificationBody = `订单 ${messageData.subtitle || orderId} 状态已更新为 ${newStatus}`;
                     this.showNotification(notificationTitle, notificationBody);
                }
            } else {
                 console.log(`[Vue Reactivity] 订单 ${orderId} 未在 store 中找到或状态未发生变化，不发出更新事件。`);
            }
        } catch (error) {
            console.error(`[Vue Reactivity] 更新订单状态失败 (ID: ${orderId}):`, error);
        }
    }

    /**
     * 播放通知声音
     * @param {string} type 通知类型
     */
    playNotificationSound(type) {
        // 这里可以根据不同类型播放不同的声音
        try {
            let audioPath;
            // 检查音频文件是否存在
            const audioFiles = {
                'new_order': '@/assets/audios/notification/new_order.mp3',
                'driver_update': '@/assets/audios/notification/driver_update.mp3',
                'notification': '@/assets/audios/notification/notification.mp3'
            };

            // 使用默认路径
            const defaultPath = '/notification.mp3';

            try {
                if (type in audioFiles) {
                    audioPath = new URL(audioFiles[type], import.meta.url).href;
                } else {
                    audioPath = defaultPath;
                }
            } catch (urlError) {
                console.warn('音频文件URL创建失败，使用默认路径', urlError);
                audioPath = defaultPath;
            }

            // 如果不需要播放声音，直接返回
            if (window.__suppressNotificationSounds) {
                console.log('已禁用通知声音');
                return;
            }

            console.log('尝试播放通知声音:', audioPath);

            // 检查浏览器是否允许自动播放
            if (document.visibilityState !== 'visible') {
                console.log('页面不可见，不播放声音');
                return;
            }

            const audio = new Audio(audioPath);
            audio.loop = false;

            // 添加错误处理
            audio.addEventListener('error', (e) => {
                console.warn('通知音频加载失败，错误代码:', e.target.error.code);
            });

            const playPromise = audio.play();

            // 处理自动播放策略限制
            if (playPromise !== undefined) {
                playPromise.catch(e => {
                    console.warn('播放通知声音失败 (可能是自动播放策略限制):', e.name);
                    // 不要尝试重新播放，避免控制台被错误刷屏
                });
            }
        } catch (error) {
            console.warn('播放通知声音功能不可用:', error.message);
            // 静默失败，不影响其他功能
        }
    }

    /**
     * 显示浏览器通知
     * @param {string} title 通知标题
     * @param {string} body 通知内容
     */
    showNotification(title, body) {
        // 如果已经禁用通知，直接返回
        if (window.__suppressNotifications) {
            console.log('通知弹窗已禁用');
            return;
        }

        if ('Notification' in window && Notification.permission === 'granted') {
            try {
                new Notification(title, {
                    body: body,
                    icon: '/logo.png'
                });
            } catch (error) {
                console.warn('显示通知弹窗失败:', error.message);
            }
        }
    }

    /**
     * 处理订单创建通知
     * @param {Object} messageData 消息数据
     */
    async handleOrderCreate(messageData) {
        console.log('处理订单创建通知 (优化 v5):', messageData);

        // 解析订单信息
        let orderId;
        let orderType = 'PICKUP'; // 默认类型

        try {
            // 优先从 body 解析 id
            if (messageData.body && typeof messageData.body === 'string') {
                const bodyData = JSON.parse(messageData.body);
                orderId = bodyData.id;
                // 尝试从消息或 body 中获取类型
                orderType = messageData.type || bodyData.type || 'PICKUP';
            } else if (messageData.id) {
                // 备选：直接从 data 中获取 id
                orderId = messageData.id;
                orderType = messageData.type || 'PICKUP';
            }

            if (!orderId) {
                console.error('[Vue Reactivity] 无法从订单创建消息中获取订单ID');
                return;
            }
        } catch (error) {
            console.error('[Vue Reactivity] 解析订单创建消息失败:', error);
            return;
        }

        console.log(`[Vue Reactivity] 订单 ID: ${orderId}, 类型: ${orderType}`);

        try {
            // 直接调用 getOrder 获取订单完整信息
            console.log(`[Vue Reactivity] 直接调用 getOrder 获取订单 ${orderId} 的完整信息...`);
            const newOrderData = await orderAPI.getOrder(orderId, orderType);
            console.log('[Vue Reactivity] 获取到新创建订单详情:', newOrderData);

            // 获取时间存储和订单存储
            const timeStore = useTimeStore();
            const orderStore = useOrderStore();

            // 使用订单的 date 和 shift_id 进行过滤
            let shouldDisplay = true;

            // 检查日期是否匹配
            if (timeStore.selectedDate && newOrderData.date) {
                const orderDate = new Date(newOrderData.date).toLocaleDateString();
                const selectedDate = new Date(timeStore.selectedDate).toLocaleDateString();
                if (orderDate !== selectedDate) {
                    console.log(`[Vue Reactivity] 订单日期 (${orderDate}) 不匹配当前选择日期 (${selectedDate})，不添加`);
                    shouldDisplay = false;
                }
            } else if (!newOrderData.date) {
                console.warn(`[Vue Reactivity] 订单 ${orderId} 没有日期信息，无法过滤`);
                shouldDisplay = false;
            }

            // 检查班次是否匹配
            if (shouldDisplay && timeStore.selectedShift && newOrderData.shift_id) {
                if (timeStore.selectedShift.id !== newOrderData.shift_id) {
                    console.log(`[Vue Reactivity] 订单班次 (${newOrderData.shift_id}) 不匹配当前选择班次 (${timeStore.selectedShift.id})，不添加`);
                    shouldDisplay = false;
                }
            } else if (!newOrderData.shift_id) {
                console.warn(`[Vue Reactivity] 订单 ${orderId} 没有班次信息，无法过滤`);
                shouldDisplay = false;
            }

            // 如果过滤通过，添加订单到 store
            if (shouldDisplay) {
                console.log(`[Vue Reactivity] 订单 ${orderId} 符合当前显示条件，添加到 store...`);

                // 调用 store 的 addSingleOrder 方法添加单个订单
                // 注意：addSingleOrder 方法内部会触发 ORDER_ADDED 事件和地图刷新
                const added = orderStore.addSingleOrder(newOrderData);

                if (added) {
                    console.log(`[Vue Reactivity] 成功添加订单 ${orderId} 到 store，等待地图更新`);
                } else {
                    console.error(`[Vue Reactivity] 添加订单 ${orderId} 到 store 失败`);
                }

                // 播放通知声音
                this.playNotificationSound('new_order');

                // 显示通知
                const notificationTitle = messageData.title || '新订单';
                const notificationBody = `收到新的${orderType === 'PICKUP' ? '取件' : '派送'}订单: ${newOrderData.name || newOrderData.no || orderId}`;
                this.showNotification(notificationTitle, notificationBody);
            } else {
                console.log(`[Vue Reactivity] 订单 ${orderId} 不符合当前显示条件，不添加到列表`);
            }
        } catch (error) {
            console.error('[Vue Reactivity] 处理订单创建消息失败:', error);
            if (error.config?.url) {
                console.error(`[Vue Reactivity] 请求URL: ${error.config.url}`);
            }
            if (error.response?.data) {
                console.error(`[Vue Reactivity] 响应数据: ${JSON.stringify(error.response.data)}`);
            }
        }
    }

    /**
     * 处理订单更新通知
     * @param {Object} messageData 消息数据
     */
    async handleOrderUpdate(messageData) {
        console.log('处理订单更新通知 (优化 v4):', messageData);

        // 解析 orderId
        let orderId;
        try {
            // 尝试从 data 或 body 解析 id
            if (messageData.id) {
                 orderId = messageData.id;
            } else if (messageData.body && typeof messageData.body === 'string') {
                 const bodyData = JSON.parse(messageData.body);
                 orderId = bodyData.id || messageData.id; // Fallback to messageData.id
            } else {
                 console.error('订单更新通知数据无效，缺少 id 字段', messageData);
                 return;
            }

            if (!orderId) {
                console.error('无法从订单更新通知中解析 orderId:', messageData);
                return;
            }

        } catch(error) {
             console.error('解析订单更新通知消息失败:', error, messageData);
             return;
        }

        const orderStore = useOrderStore();

        try {
            console.log(`[Vue Reactivity] 准备更新订单 ${orderId}...`);

            // 使用 OrderStore 的 updateSingleOrder 方法更新订单
            const updateResult = await orderStore.updateSingleOrder(orderId);

            if (updateResult) { // 如果更新成功
                // 获取更新后的完整订单数据
                const updatedOrderFullData = orderStore.allOrders.find(o => o.id === orderId);

                if (updatedOrderFullData) {
                    // 发出 ORDER_UPDATED 事件，包含完整的订单数据
                    eventBus.emit(EVENT_TYPES.ORDER_UPDATED, {
                        isGeneralUpdate: true, // 添加标记
                        orders: [updatedOrderFullData], // 将完整的订单数据放入数组
                        timestamp: Date.now() // 添加时间戳
                    });
                    console.log(`[Vue Reactivity] 已发出 ORDER_UPDATED 事件，订单 ID: ${orderId}`);
                } else {
                    console.warn(`[Vue Reactivity] 更新订单 ${orderId} 后未能从 store 中获取完整数据`);
                    // 降级处理：发送部分数据
                    eventBus.emit(EVENT_TYPES.ORDER_UPDATED, {
                        id: orderId,
                        isGeneralUpdate: true,
                        timestamp: Date.now()
                    });
                }

                // 显示通知
                const notificationTitle = messageData.title || '订单更新';
                const notificationBody = `订单 ${orderId} 已更新`;
                this.showNotification(notificationTitle, notificationBody);
            } else {
                console.log(`[Vue Reactivity] 订单 ${orderId} 未在 store 中找到或未发生变化，不发出更新事件。`);
            }
        } catch (error) {
            console.error('[Vue Reactivity] 处理订单更新消息失败:', error);
        }
    }

    /**
     * 处理司机位置更新消息 - 新格式，使用节流和批处理
     * @param {Object} messageData 消息数据
     */
    handleDriverPositionsUpdate(messageData) {
        console.log('接收到司机位置更新消息 - 新格式:', messageData.action);

        try {
            // 先检查body字段是否存在（新格式中body是JSON字符串）
            if (!messageData.body) {
                console.error('司机位置更新消息格式无效: 缺少body字段');
                return;
            }

            // 尝试解析body字段中的JSON数据
            let positionsData;
            try {
                positionsData = JSON.parse(messageData.body);
                // 使用简化日志，避免大量数据输出
                console.log(`成功解析司机位置数据: ${positionsData.length}个司机`);
            } catch (error) {
                console.error('解析司机位置数据失败:', error);
                return;
            }

            // 验证解析后的数据
            if (!Array.isArray(positionsData)) {
                console.error('司机位置数据不是数组格式');

                // 尝试将单个对象包装为数组
                if (positionsData && positionsData.id && positionsData.positions) {
                    positionsData = [positionsData];
                    console.log('已将单个位置对象转换为数组');
                } else {
                    console.error('位置数据格式无效，无法处理');
                    return;
                }
            }

            // 转换为driver store需要的格式
            const driversData = positionsData.map(item => ({
                driver: { id: item.id },
                positions: item.positions || []
            }));

            if (driversData.length === 0) {
                console.warn('没有有效的司机位置数据可处理');
                return;
            }

            // 添加到批处理队列
            this.addToPositionQueue(driversData);

        } catch (error) {
            console.error('处理司机位置更新消息失败:', error);
        }
    }

    /**
     * 将司机位置数据添加到队列中，进行批处理
     * @param {Array} driversData 司机位置数据数组
     */
    addToPositionQueue(driversData) {
        // 将数据添加到队列
        this.positionQueue = [...this.positionQueue, ...driversData];

        // 记录队列长度，用于日志
        const queueLength = this.positionQueue.length;
        console.log(`位置数据已加入队列，当前队列长度: ${queueLength}`);

        // 如果队列太大（例如超过50个），立即刷新以避免内存问题
        if (queueLength > 50) {
            console.log('队列长度超过阈值，立即执行批量更新');
            this.flushPositionUpdates();
            return;
        }

        // 如果定时器未启动，则启动定时器
        if (!this.positionUpdateTimer) {
            console.log(`设置位置更新定时器，${POSITION_UPDATE_INTERVAL}毫秒后执行`);
            this.positionUpdateTimer = setTimeout(() => {
                this.flushPositionUpdates();
            }, POSITION_UPDATE_INTERVAL);
        }
    }

    /**
     * 批量处理并更新司机位置
     */
    flushPositionUpdates() {
        // 清除定时器
        if (this.positionUpdateTimer) {
            clearTimeout(this.positionUpdateTimer);
            this.positionUpdateTimer = null;
        }

        // 检查队列是否为空
        if (this.positionQueue.length === 0) {
            console.log('位置更新队列为空，无需处理');
            return;
        }

        // 记录批处理开始时间和队列长度
        const startTime = performance.now();
        const queueLength = this.positionQueue.length;
        console.log(`开始批量处理${queueLength}个司机位置数据`);

        // 合并同一司机的最新位置数据
        const mergedDrivers = {};
        this.positionQueue.forEach(data => {
            const driverId = data.driver.id;
            if (mergedDrivers[driverId] && data.positions && data.positions.length > 0) {
                 if (!mergedDrivers[driverId].positions ||
                    mergedDrivers[driverId].positions.length === 0 ||
                    (data.positions[0].timestamp > mergedDrivers[driverId].positions[0].timestamp)) {
                    mergedDrivers[driverId] = data;
                 }
            } else if (data.driver && data.driver.id) { // 添加检查确保 driver.id 存在
                 mergedDrivers[driverId] = data;
            } else {
                 console.warn("Skipping invalid driver data in position queue:", data);
            }
        });

        // 转回数组格式
        const batchData = Object.values(mergedDrivers);
        if (batchData.length === 0) {
             console.log("位置更新队列合并后为空，无需处理");
             this.positionQueue = []; // 确保清空队列
             this.lastFlushTime = Date.now(); // 更新刷新时间
             return;
        }
        console.log(`合并后共有${batchData.length}个不同司机的位置数据`);

        // 获取driver store
        const driverStore = useDriverStore();

        // 重置上次更新时间，确保能强制更新
        window.__lastDriverPositionUpdate = 0;

        // 更新司机位置
        const updateResult = driverStore.updateMultipleDriverLocations(batchData);

        // 触发司机位置更新事件
        try {
            if (updateResult && batchData.length > 0) { // 检查是否有实际更新的数据
                const updateTime = new Date();
                const timeStr = updateTime.toLocaleTimeString();
                console.log(`批量更新 ${batchData.length} 个司机位置数据完成, 更新时间: ${timeStr}`);

                // !! 修改：发送实际更新的批处理数据 batchData !!
                eventBus.emit(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, batchData);

                // 发送全局位置更新完成事件
                eventBus.emit('driverLocationsUpdated', {
                    count: batchData.length, // 使用 batchData 的长度
                    timestamp: updateTime.getTime(),
                    isBatchUpdate: true
                });

                // 确保地图组件刷新（如果存在）
                if (window.refreshDriverPositionsOnMap) {
                    window.refreshDriverPositionsOnMap(batchData); // 传递 batchData 可能更合适
                }
            } else {
                console.warn('司机位置批量更新失败或无数据更新');
            }
        } catch (eventError) {
            console.error('触发司机位置更新事件出错:', eventError);
        }

        // 清空队列
        this.positionQueue = [];

        // 记录最后刷新时间
        this.lastFlushTime = Date.now();

        // 记录批处理耗时
        const endTime = performance.now();
        console.log(`批量处理完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
    }
}

// 创建单例实例
export const firebaseMessagingService = new FirebaseMessagingService();