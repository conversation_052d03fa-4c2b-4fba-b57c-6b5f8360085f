<script setup name="DeliveryExtendedOrdersList">
    import api from '@/api'
    import { Delete, Upload, Close, Money } from '@element-plus/icons-vue'
    import eventBus from '@/utils/eventBus'
    import { usePagination } from '@/utils/composables'
    import FormMode from './components/FormMode/index.vue'
    import { orderStatusStyles } from '@/utils/constants'

    import { getExtendedOrders, deleteExtendedOrder, submitExtendedOrder, closeExtendedOrder } from '@/api/modules/delivery'
    import OrderAddressDialog from '@/views/components/OrderAddressDialog/index.vue'
    import { currencyFormatter } from '@/utils/formatter'

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        /**
         * 详情展示模式
         * router 路由跳转
         * dialog 对话框
         * drawer 抽屉
         */
        formMode: 'router',
        // 详情
        formModeProps: {
            visible: false,
            id: ''
        },
        // 搜索
        search: {
            sender: null,
            receiver: null,
            no__contains: null
        },
        // 批量操作
        batch: {
            enable: false,
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            sender: null,
            receiver: null,
            no__contains: null
        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
        if (data.value.formMode === 'router') {
            eventBus.on('get-data-list', () => {
                getDataList()
            })
        }
    })

    onBeforeUnmount(() => {
        if (data.value.formMode === 'router') {
            eventBus.off('get-data-list')
        }
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getExtendedOrders(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.order_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        if (data.value.formMode === 'router') {
            router.push({
                name: 'routerName'
            })
        } else {
            data.value.formModeProps.id = ''
            data.value.formModeProps.visible = true
        }
    }

    function onEdit(row) {
        if (data.value.formMode === 'router') {
            router.push({
                name: 'routerName',
                query: {
                    id: row.id
                }
            })
        } else {
            data.value.formModeProps.id = row.id
            data.value.formModeProps.visible = true
        }
    }

    function onClose(row) {
        ElMessageBox.confirm(`确认关闭「${row.no}」吗？`, '确认信息').then(() => {
            data.value.loading = true;
            api.post({ id: row.id }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
                data.value.loading = false;

            })
        }).catch(() => { })
    }

    function onSubmit(row, toPay) {
        ElMessageBox.confirm(`确认提交「${row.no}」吗？`, '确认信息').then(() => {
            data.value.loading = true;
            let params = {
                to_pay: toPay
            }


            submitExtendedOrder(row.id, params).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
                data.value.loading = false;

            })
        }).catch(() => { })
    }

    function onDel(row) {
        ElMessageBox.confirm(`确认删除「${row.no}」吗？`, '确认信息').then(() => {
            data.value.loading = true;

            deleteExtendedOrder(row.id).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
                data.value.loading = false;
            })
        }).catch(() => { })
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.status == null) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

    // Dialogs
    const chosenRow = ref({})
    const senderDialogVisible = ref(false)
    const receiverDialogVisible = ref(false)


    function showSender(row) {
        chosenRow.value = row
        senderDialogVisible.value = true
    }
    function showReceiver(row) {
        chosenRow.value = row
        receiverDialogVisible.value = true
    }


</script>

<template>
    <div>
        <page-header title="默认模块" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.no')">
                                        <el-input v-model="data.search.no__contains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.no') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.sender')">
                                        <el-input v-model="data.search.sender"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.sender') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.receiver')">
                                        <el-input v-model="data.search.receiver"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.receiver') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <!-- <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button> -->
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column type="index" />
                <el-table-column prop="no" :label="$t('fields.no')" width="100" />
                <el-table-column prop="sender_name" :label="$t('delivery.orders.fields.sender')" show-overflow-tooltip
                    width="120">

                    <template #default="scope">
                        <el-link type="warning" @click.stop="showSender(scope.row)">
                            {{ scope.row.sender_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="receiver_name" :label="$t('delivery.orders.fields.receiver')" width="120"
                    show-overflow-tooltip>

                    <template #default="scope">
                        <el-link type="success" @click.stop="showReceiver(scope.row)">
                            {{ scope.row.receiver_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="carrier" :label="$t('delivery.extendedOrders.fields.carrier')" width="160"
                    align="center">
                    <template #default="scope">
                        <el-space direction="vertical" size="small">
                            <code style="line-height: 10px; font-size: 12px;">{{ scope.row.carrier }}</code>
                            <code style="line-height: 10px; font-size: 12px;">{{ scope.row.service_name }}</code>
                            <el-text tag="b" v-if="scope.row.partner_id">{{ scope.row.partner_id }}</el-text>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="status" :label="$t('fields.status')" width="110" sortable="custom">
                    <template #default="scope">
                        <el-tag :type="orderStatusStyles[scope.row.status]" round size="small" v-if="scope.row.status">
                            {{ $t(`delivery.orders.selections.status.${scope.row.status}`) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="weight" :label="$t('delivery.orders.fields.sizeWeight')" width="100">
                    <template #default="scope">
                        {{ scope.row.length }}x{{ scope.row.width }}x{{ scope.row.height }}, {{ scope.row.weight }}
                    </template>
                </el-table-column>
                <el-table-column prop="paid_amount" :label="$t('delivery.extendedOrders.fields.paidAmount')"
                    sortable="custom" :formatter="currencyFormatter" width="100">
                    <template #default="scope">
                        <el-text tag="b" v-if="scope.row.paid_amount > 0">
                            {{ currencyFormatter(null, null, scope.row.paid_amount) }}
                        </el-text>
                        <el-text tag="mark" size="small">{{ $t('delivery.extendedOrders.fields.notPaid') }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="paid_at" :label="$t('delivery.extendedOrders.fields.paidAt')" width="160"
                    sortable="custom" />
                <el-table-column prop="eta" :label="$t('delivery.extendedOrders.fields.eta')" width="140"
                    sortable="custom" />
                <el-table-column prop="total_price" :label="$t('fields.total')" sortable="custom"
                    :formatter="currencyFormatter" />
                <el-table-column prop="freight" :label="$t('fields.fee')" sortable="custom"
                    :formatter="currencyFormatter" />
                <el-table-column prop="extra_fee" :label="$t('delivery.extendedOrders.fields.extraFee')" width="100"
                    sortable="custom" :formatter="currencyFormatter" />
                <el-table-column prop="tax" :label="$t('fields.tax')" sortable="custom"
                    :formatter="currencyFormatter" />
                <el-table-column prop="service_fee" :label="$t('delivery.extendedOrders.fields.serviceFee')" width="100"
                    sortable="custom" :formatter="currencyFormatter" />
                <el-table-column prop="channel" :label="$t('delivery.extendedOrders.fields.channel')" width="160"
                    sortable="custom" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="120" align="right" fixed="right">
                    <template #default="scope">
                        <el-space size="small">
                            <el-tooltip class="box-item" :content="$t('delivery.extendedOrders.operations.open')"
                                placement="top-start" v-if="scope.row.can_submit">
                                <el-button type="success" :icon="Upload" circle size="small"
                                    @click="onSubmit(scope.row, false)" :disabled=data.loading />
                            </el-tooltip>
                            <el-tooltip class="box-item" :content="$t('operations.submit')" placement="top-start"
                                v-if="scope.row.can_submit">
                                <el-button type="success" :icon="Money" circle size="small" plain
                                    @click="onSubmit(scope.row, true)" :disabled=data.loading />
                            </el-tooltip>
                            <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start"
                                v-if="scope.row.can_delete">
                                <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)"
                                    :disabled=data.loading />
                            </el-tooltip>
                            <el-tooltip class="box-item" :content="$t('operations.close')" placement="top-start"
                                v-if="scope.row.can_close">
                                <el-button type="danger" plain :icon="Close" circle size="small"
                                    @click="onClose(scope.row)" :disabled=data.loading />
                            </el-tooltip>
                        </el-space>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode v-if="['dialog', 'drawer'].includes(data.formMode)" :id="data.formModeProps.id"
            v-model="data.formModeProps.visible" :mode="data.formMode" @success="getDataList" />
        <!-- Sender -->
        <OrderAddressDialog v-model="senderDialogVisible" :address-info="chosenRow" address-type="sender" />
        <!-- Receiver -->
        <OrderAddressDialog v-model="receiverDialogVisible" :address-info="chosenRow" address-type="receiver" />

    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>
