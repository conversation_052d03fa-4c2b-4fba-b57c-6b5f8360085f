{
    // Finance
    "paymentSetting": {
        "title": "Payment Settings",
        "fields": {
            "taxRate": "Tax Rate",
            "resultUrl": "Result page URL"
        }
    },
    "paymentSupplier": {
        "title": "Payment Suppliers",
        "fields": {
            "id_1": "ID 1",
            "id_2": "ID 2",
            "code": "Code",
            "pin": "PIN",
            "baseUrl": "Base URL",
            "notifyPath": "Notification Path"
        }
    },
    "paymentOrder": {
        "title": "Payment Orders",
        "detailPage": "Payment Order Detail",
        "fields": {
            "no": "#",
            "amount": "Amount",
            "refunded": "Refunded",
            "status": "Status",
            "paidAt": "Paid at",
            "subtotal": "Subtotal",
            "tax": "HST",
            "tip": "Tip",
            "paymentMethod": "Payment Method",
            "amountMax": "Max Amount",
            "amountMin": "Min Amount",
            "invoice": "Invoice",
            "invoiceStatus": "Invoice Status",
            "refundedMax": "Max Refunded",
            "refundedMin": "Min Refunded",
            "resultRemark": "Result Message",
            "relatedOrders": "Related Orders",
            "refundToCredits": "Refund to credits",
            "refundedTip": "Refunded Tip"
        },
        "selections": {
            "status": {
                "closed": "Closed",
                "created": "Created",
                "paying": "Paying",
                "paid": "Paid",
                "refunding": "Refunding",
                "partially_refunded": "Partially Refunded",
                "refunded": "Refunded",
                "completed": "Completed"
            },
            "logStatus": {
                "closed": "Closed",
                "created": "Created",
                "updated": "Updated",
                "paying": "Paying",
                "paid": "Paid",
                "refunding": "Refunding",
                "partiallyRefunded": "Partially Refunded",
                "refunded": "Refunded",
                "completed": "Completed"
            },
            "invoiceStatus": {
                "all": "All",
                "invoiced": "Invoiced",
                "notInvoiced": "Not Invoiced"
            },
            "noInvoice": "Not invoiced",
            "notPaid": "Not Paid"
        },
        "sections": {
            "log": "Log"
        },
        "operations": {
            "close": "Close",
            "refund": "Refund",
            "complete": "Complete",
            "query": "Query"
        },
        "statistics": {
            "title": "Payment Orders Statistics",
            "totalCounts": "Total Orders",
            "todayCounts": "Today Orders {num}",
            "total": "Total",
            "subSum": "Subtotal Sum",
            "taxSum": "Tax Sum",
            "tipSum": "Tip Sum",
            "refundedSum": "Refunded Amount",
            "refunded_amount": "Refunded Amount",
            "monthOrders": "This Month's Orders",
            "monthDelta": "Increased from last month {num}%",
            "monthIncoming": "This Month's Incoming",
            "cash": "Cash",
            "monthCash": "This Month's Cash {num}",
            "alphapay": "Alphapay",
            "elavon": "Elavon",
            "credit": "User Credits",
            "month": "Monthly Total",
            "cashMonth": "Monthly Cash",
            "paymentStatistics": "Payments Statistics",
            "refunded": "Refunded"
        }
    },
    "ccLog": {
        "title": "Elavon Orders",
        "detailPage": "Elavon Order Detail",
        "fields": {
            "address": "Address",
            "amount": "Amount",
            "amountMax": "Max amount",
            "amountMin": "Min amount",
            "approvalCode": "Approval code",
            "avsResponse": "AVS response",
            "cardNumber": "Card number",
            "cardType": "Card type",
            "city": "City",
            "country": "Country",
            "currency": "Currency",
            "customerId": "Customer ID",
            "expDate": "Exp. date",
            "firstName": "First name",
            "lastName": "Last name",
            "oarData": "OAR",
            "ps2000Data": "PS2000",
            "refundedAmount": "Refunded amount",
            "result": "Result",
            "settlementBatch": "Settlement Batch",
            "settleTime": "Settle time",
            "shortDesc": "Card desc.",
            "state": "Province",
            "status": "Transaction Status",
            "token": "Stored token",
            "txnId": "TXN ID",
            "txnTime": "TXN time",
            "txnType": "Transaction type",
            "zipCode": "Postal code"
        },
        "selections": {
            "status": {
                "PEN": "Pended",
                "OPN": "Unpended / Release / Open",
                "REV": "Review",
                "STL": "Settled",
                "PST": "Failed Due to Post-Auth Rule",
                "FPR": "Failed Due to Fraud Prevention Rules",
                "PRE": "Failed Due to Pre-Auth Rule"
            }
        }
    },
    "apLog": {
        "title": "Alphapay Orders",
        "detailPage": "Alphapay Order Detail",
        "fields": {
            "inputFee": "Input fee",
            "realFee": "Real fee",
            "totalFee": "Total fee",
            "currency": "Currency",
            "rate": "Exg. rate",
            "desc": "Description",
            "resultCode": "Result",
            "orderId": "Order ID",
            "customerId": "Customer ID",
            "createTime": "Create time",
            "payTime": "Pay time",
            "returnCode": "Return code",
            "returnedMsg": "Returned message",
            "channel": "Channel",
            "channelErrCode": "Channel error code",
            "channelErrMsg": "Channel error message",
            "totalFeeMax": "Max total fee",
            "totalFeeMin": "Min total fee"
        }
    },
    "crLog": {
        "title": "Credit Log",
        "fields": {
            "type": "Type",
            "amount": "Amount"
        },
        "selections": {
            "type": {
                "consumed": "Consumed",
                "returned": "Refunded",
                "promoted": "Promoted",
                "directed": "Directly paid"
            }
        }
    },
    "invoice": {
        "title": "Invoices",
        "list": "Invoices List",
        "detail": "Invoice Detail",
        "fields": {
            "period": "Period",
            "periodStart": "Period Start",
            "periodEnd": "Period End",
            "downloaded": "Downloaded",
            "paidAmount": "Paid Amount"
        }
    },
    "userInvoice": {
        "title": "User Invoices",
        "fields": {}
    },
    "bill": {
        "title": "Bills",
        "list": "Bills List",
        "detail": "Bill Detail",
        "fields": {
            "dueDate": "Due Date",
            "paidAt": "Paid at",
            "paymentMethod": "Payment Method"
        }
    },
    "eod": {
        "title": "End Of Day",
        "desc": "Follow the process to complete the EOD",
        "s1": "Start",
        "s2": "Delivery Orders",
        "s3": "Promotions",
        "s4": "Delivery Report",
        "s5": "Salaries",
        "s6": "Salary Report",
        "s7": "Payments",
        "s8": "Payment Report",
        "s9": "Costs",
        "s10": "Cost Report",
        "s11": "EOD",
        "pickADay": "Pick a day",
        "operations": {
            "start": "Start",
            "completeOrders": "Complete Orders",
            "close": "Close",
            "complete": "Complete",
            "noAction": "No Action",
            "executePromotions": "Execute Promotions",
            "generalReport": "General Report"
        }
    }
}