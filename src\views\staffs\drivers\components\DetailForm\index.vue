<script setup>
import { addDriver, getDriverList, updateDriver, validateDriver, invalidateDriver, suspendDriver, resetDriverPassword } from '@/api/modules/staffs'
import { sexes } from '@/utils/constants'
import { useClipboard } from '@vueuse/core'
import { DocumentCopy } from '@element-plus/icons-vue'
import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'
import { replaceNonLatinCharacters } from '@/utils/formatter'


const { text, copy, copied, isSupported } = useClipboard()

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        sn: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        firstname: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        lastname: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        phone_number: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        email: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        username: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        sex: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

const baseSalary = ref(0.00)
const fuelAllowance = ref(0.00)


onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getDriverList({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        baseSalary.value = res.data.base_hourly_salary / 100
        fuelAllowance.value = res.data.base_fuel_allowance_per_km / 100
    })
}

defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.base_hourly_salary = Math.round(baseSalary.value * 100)
        params.base_fuel_allowance_per_km = Math.round(fuelAllowance.value * 100)
        params.addr_db = data.value.form.addr_db
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addDriver(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateDriver(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})

function disabledDate(time) {
    var date = new Date(1960, 1, 1);
    return time.getTime() < date;
}

function alterStatus(status) {
    switch (status) {
        case 'valid':
            validateDriver(data.value.form.id).then(res => {
                if (res.data.errCode == 365) {
                    getInfo()
                }
            })
            return
        case 'invalid':
            invalidateDriver(data.value.form.id).then(res => {
                if (res.data.errCode == 365) {
                    getInfo()
                }
            })
            return
        case 'suspended':
            suspendDriver(data.value.form.id).then(res => {
                if (res.data.errCode == 365) {
                    getInfo()
                }
            })
    }
}

const resetPasswordDialogVisibility = ref(false)
const isConfirmedToReset = ref(false)
const newPassword = ref(null)

function onResetPassword() {
    resetPasswordDialogVisibility.value = true
}

function resetPassword() {
    let params = { id: data.value.form.id, password: newPassword.value }
    isConfirmedToReset.value = false
    resetDriverPassword(params).then(res => {
        if (res.data.errCode == 365) {
            newPassword.value = res.data.newPassword
            getInfo()
        }
    })

}

function hideResetPasswordDialog() {
    resetPasswordDialogVisibility.value = false
    newPassword.value = null
}

function onClearAddress() {
    data.value.form.address = null
    data.value.form.city = null
    data.value.form.province = null
    data.value.form.postal_code = null
    data.value.form.addr_db = null
}

function onRetrieveAddress(val) {
    data.value.form.address = val.Line1
    data.value.form.city = val.City
    data.value.form.province = val.ProvinceName
    data.value.form.postal_code = val.PostalCode
    data.value.form.addr_db = val.Id
}

</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item :label="$t('delivery.drivers.fields.sn')" prop="sn" maxlength="10" show-word-limit
                        :formatter="replaceNonLatinCharacters">
                        <el-input v-model="data.form.sn" placeholder="请输入标题" />
                    </el-form-item>
                    <el-space>
                        <el-form-item :label="$t('fields.firstName')" prop="firstname">
                            <el-input v-model="data.form.firstname" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.lastName')" prop="lastname">
                            <el-input v-model="data.form.lastname" placeholder="请输入标题" />
                        </el-form-item>
                    </el-space>
                    <el-form-item :label="$t('user.fields.phoneNumber')" prop="phone_number">
                        <el-input v-model="data.form.phone_number" placeholder="请输入标题" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.email')" prop="email">
                        <el-input v-model="data.form.email" placeholder="请输入标题" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.sex')" prop="sex">
                        <el-select v-model="data.form.sex" placeholder="Select" :disabled="readOnly">
                            <el-option v-for="item in sexes" :key="item" :label="$t(`selection.sexes.${item}`)"
                                :value="item" />
                        </el-select> </el-form-item>
                    <el-form-item :label="$t('delivery.drivers.fields.birthday')">
                        <el-date-picker v-model="data.form.birthday" placeholder="Select date and time"
                            :disabled-date="disabledDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                    </el-form-item>
                    <el-divider />
                    <el-form-item :label="$t('delivery.drivers.fields.bio')" prop="bio">
                        <el-input v-model="data.form.bio" placeholder="请输入标题" />
                    </el-form-item>
                    <el-form-item :label="$t('delivery.drivers.fields.avatar')" prop="bio">
                        <el-image
                            :src="data.form.avatar ? data.form.avatar : 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'"
                            fit="cover" style="height: 200px;" />
                    </el-form-item>
                    <el-divider />
                    <el-form-item :label="$t('user.fields.address')">
                        <AddressSelect ref="addressSelectRef" :address="data.form.address" :city="data.form.city"
                            :province="data.form.province" :postal-code="data.form.postal_code"
                            :on-clear="onClearAddress" @success="onRetrieveAddress" />
                    </el-form-item>
                    <el-form-item>
                        {{ data.form.city ? `${data.form.city}, ${data.form.province}, ${data.form.postal_code}` : '' }}
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.unitNo')">
                        <el-input v-model="data.form.unit_no" placeholder="请输入标题" />
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.buzzerCode')">
                        <el-input v-model="data.form.buzzer_code" placeholder="请输入标题" />
                    </el-form-item>
                    <el-divider />
                </el-col>
                <el-col :span="12">
                    <el-form-item :label="$t('delivery.drivers.fields.sin')">
                        <el-input v-model="data.form.sin" placeholder="请输入标题" maxlength="20" show-word-limit />
                    </el-form-item>
                    <el-form-item :label="$t('delivery.drivers.fields.baseHourlySalary')">
                        <el-input-number v-model="baseSalary" placeholder="请输入标题" :min="0" :precision="2" :step="1" />
                    </el-form-item>
                    <el-form-item :label="$t('delivery.drivers.fields.fuelAllowancePerLiter')">
                        <el-input-number v-model="fuelAllowance" placeholder="请输入标题" :min="0" :precision="2"
                            :step="0.1" />
                    </el-form-item>
                    <el-form-item :label="$t('delivery.drivers.fields.hstNo')">
                        <el-input v-model="data.form.hst_no" placeholder="请输入标题" maxlength="20" show-word-limit />
                    </el-form-item>
                    <el-form-item :label="$t('delivery.drivers.fields.hstExempted')">
                        <el-switch v-model="data.form.hst_exempted" />
                    </el-form-item>
                    <el-form-item :label="$t('delivery.drivers.fields.bucketName')">
                        <el-input v-model="data.form.bucket_name" placeholder="请输入标题" />
                    </el-form-item>
                    <el-divider />
                    <el-form-item :label="$t('user.fields.username')" prop="username">
                        <el-space size="large">
                            <el-input v-model="data.form.username" placeholder="请输入标题" maxlength="50" show-word-limit
                                :formatter="replaceNonLatinCharacters" />
                            <el-button type="danger" @click="onResetPassword" text>
                                {{ $t('delivery.drivers.operation.resetPassword') }}
                            </el-button>
                        </el-space>
                    </el-form-item>
                    <el-form-item :label="$t('fields.status')" v-if="data.form.id">
                        <el-space>
                            <el-text tag="b">
                                {{ $t(`delivery.drivers.selection.status.${data.form.status}`) }}
                            </el-text>
                            <el-button type="success" @click="alterStatus('valid')" v-if="data.form.status != 'valid'">
                                {{ $t('delivery.drivers.operation.validate') }}
                            </el-button>
                            <el-button type="warning" @click="alterStatus('invalid')" plain size="small"
                                v-if="data.form.status == 'valid'">
                                {{ $t('delivery.drivers.operation.invalidate') }}
                            </el-button>
                            <el-button type="danger" @click="alterStatus('suspended')" plain size="small"
                                v-if="data.form.status == 'valid'">
                                {{ $t('delivery.drivers.operation.suspend') }}
                            </el-button>
                        </el-space>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-dialog v-model="resetPasswordDialogVisibility" :title="$t('delivery.drivers.operation.resetPassword')"
            width="30%" :before-close="handleClose">
            <span v-if="!newPassword && !isConfirmedToReset">
                {{ $t('delivery.drivers.dialog.resetPassword.warning') }}
            </span>
            <template v-else-if="isConfirmedToReset">
                <el-form-item :label="$t('fields.newPassword')">
                    <el-input type="password" v-model="newPassword" />
                </el-form-item>
                <small>*&thinsp; {{ $t('delivery.drivers.dialog.resetPassword.emptyPassword') }}</small>
            </template>

            <template v-else-if="newPassword && !isConfirmedToReset">
                <h4 style="text-align: center;">
                    {{ $t('delivery.drivers.dialog.resetPassword.noteDown') }}
                </h4>
                <el-row align="middle" :gutter="20">
                    <el-col :span="8" :offset="8">
                        <div style="border: 1px dashed black;text-align: center; padding: 10px;">
                            <code style="font-size: 25px;">{{ newPassword }}</code>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <el-space>
                            <el-button @click.stop="copy(newPassword)" size="small" type="primary" plain circle
                                :icon="DocumentCopy" />
                            <small v-if="copied" style="text-align: center;">Copied</small>
                        </el-space>
                    </el-col>
                </el-row>
            </template>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="hideResetPasswordDialog" v-if="!(newPassword && !isConfirmedToReset)">
                        {{ $t('operations.cancel') }}
                    </el-button>
                    <el-button type="primary" @click="isConfirmedToReset = true"
                        v-if="!newPassword && !isConfirmedToReset">
                        {{ $t('operations.continue') }}
                    </el-button>
                    <el-button type="primary" @click="resetPassword" v-else-if="isConfirmedToReset">
                        {{ $t('operations.reset') }}
                    </el-button>
                    <el-button type="primary" @click="hideResetPasswordDialog"
                        v-else-if="newPassword && !isConfirmedToReset">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>