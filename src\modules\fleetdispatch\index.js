// FleetDispatch模块主入口文件
import FleetDispatchApp from './FleetDispatchApp.vue'
import { initializeFleetDispatch, cleanupFleetDispatch } from './utils/initialize'

// 导出主要组件
export { FleetDispatchApp }

// 导出初始化函数
export { initializeFleetDispatch, cleanupFleetDispatch }

// 导出Store
export { useDispatchStore } from './stores/dispatch'
export { useDriverStore } from './stores/driver'
export { useOrderStore } from './stores/order'
export { useRouteStore } from './stores/route'
export { useMapStore } from './stores/map'
export { useTimeStore } from './stores/time'
export { useAddressStore } from './stores/address'

// 导出事件总线
export { useEventBus, EVENT_TYPES } from './utils/eventBus'

// 导出Firebase消息相关
export { firebaseMessagingService } from './services/FirebaseMessagingService'
export { useFirebaseMessages } from './composables/useFirebaseMessages'

// 版本信息
export const version = '1.0.0'

// 模块描述
export const description = 'FleetDispatch是一个车队调度管理系统模块'