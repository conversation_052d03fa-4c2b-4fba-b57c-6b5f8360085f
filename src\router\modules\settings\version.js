/*
* Author: <EMAIL>'
* Date: '2024-01-14 21:06:03'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/settings/version.js'
* File: 'version.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/versions',
    component: Layout,
    redirect: '/settings/versions/index',
    name: 'settingHolidays',
    meta: {
        title: '版本管理',
        icon: 'icon-park-outline:calendar-three',
        auth: ['super'],
        i18n: 'route.system.versions.title'
    },
    children: [
        {
            path: 'index',
            name: 'settingVersions',
            component: () => import('@/views/settings/version/index.vue'),
            meta: {
                title: '列表',
                icon: 'bx:key',
                activeMenu: '/settings/versions/index',
                i18n: 'route.system.versions.title',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
