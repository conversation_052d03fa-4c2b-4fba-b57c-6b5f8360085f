import { OrderRepository } from '../repositories/OrderRepository'
import { RouteRepository } from '../repositories/RouteRepository'
import { DriverRepository } from '../repositories/DriverRepository'
import { Order } from '../models/Order'
import { BackupService } from './BackupService'
import { eventBus, EVENT_TYPES } from '../utils/eventBus'
import { useTimeStore } from '../stores/time'
import { calculateOptimalOrderSequence, updateOrderStopNumbers } from '../utils/tomtomRouting'

// 检查导入的模块是否可用
console.log('[OrderService] 模块导入检查:');
console.log('[OrderService] calculateOptimalOrderSequence 可用:', typeof calculateOptimalOrderSequence === 'function');
console.log('[OrderService] updateOrderStopNumbers 可用:', typeof updateOrderStopNumbers === 'function');

export class OrderService {
    constructor() {
        this.orderRepository = new OrderRepository()
        this.routeRepository = new RouteRepository()
        this.driverRepository = new DriverRepository()
        this.backupService = new BackupService()

        // 本地状态
        this.orders = []           // 未分配订单
        this.assignedOrders = []   // 已分配订单
        this.selectedOrders = []   // 选中的订单
        this.selectedOrderIds = new Set() // 选中订单的ID集合
    }

    // 获取所有订单
    async fetchOrders(params) {
        try {
            const orders = await this.orderRepository.getOrders(params)

            if (!Array.isArray(orders)) {
                console.error('获取到的订单数据不是数组:', orders)
                return []
            }

            // 分离未分配和已分配的订单
            this.orders = orders.filter(order => !order.driver_id)
            this.assignedOrders = orders.filter(order => order.driver_id)

            // 清除缓存
            this._cachedAllOrders = null
            this._cachedDriverOrders = null
            this._cacheValid = false

            // 触发事件通知数据更新
            if (typeof window !== 'undefined') {
                // 使用标准事件类型常量
                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: this.orders.concat(this.assignedOrders),
                    assignedOrders: this.assignedOrders,
                    isTimeRangeChange: true, // 标记这是因为时间范围变化导致的更新
                    timestamp: Date.now(),  // 添加时间戳以区分不同的更新事件
                    params: params // 传递参数以便调试
                })

                // 额外发送一个列表更新事件，确保OrderList组件刷新
                eventBus.emit(EVENT_TYPES.ORDER_LIST_UPDATED, {
                    isTimeRangeChange: true,
                    timestamp: Date.now()
                })
            }

            return orders
        } catch (error) {
            console.error('获取订单失败:', error)
            throw error
        }
    }

    // 获取所有订单
    getAllOrders() {
    // 使用缓存减少数组创建频率
        if (!this._cachedAllOrders || !this._cacheValid) {
            this._cachedAllOrders = [...this.orders, ...this.assignedOrders]
            this._cacheValid = true
        }
        return this._cachedAllOrders
    }

    // 按类型筛选订单
    getOrdersByType(type) {
    // 使用缓存减少重复筛选
        if (!this._cachedOrdersByType) {
            this._cachedOrdersByType = {}
        }

        if (!this._cachedOrdersByType[type]) {
            this._cachedOrdersByType[type] = this.orders.filter(order => order.type === type)
        }

        return this._cachedOrdersByType[type]
    }

    // 获取指定司机的订单
    getDriverOrders(driverId) {
        // 使用缓存减少重复筛选
        if (!this._cachedDriverOrders) {
            this._cachedDriverOrders = {}
        }

        if (!this._cachedDriverOrders[driverId]) {
            // 获取所有订单
            const allOrders = this.getAllOrders();

            // 检查是否有该司机的相关订单
            const driverOrders = allOrders.filter(order => order.driver_id === driverId);
            console.log(`[OrderService] 找到司机 ${driverId} 的订单 ${driverOrders.length} 个`);

            // 按停靠点号排序
            this._cachedDriverOrders[driverId] = driverOrders.sort((a, b) => (a.stop_no || 0) - (b.stop_no || 0));
        }

        return this._cachedDriverOrders[driverId]
    }

    // 获取指定路线的订单
    getOrdersByRouteNumber(routeNumber) {
    // 使用缓存减少重复筛选
        if (!this._cachedRouteOrders) {
            this._cachedRouteOrders = {}
        }

        if (!this._cachedRouteOrders[routeNumber]) {
            this._cachedRouteOrders[routeNumber] = this.getAllOrders().filter(order => order.route_id === routeNumber)
        }

        return this._cachedRouteOrders[routeNumber]
    }

    // *** 新增方法：通过ID和Type从API获取单个订单 ***
    async fetchOrderByIdAndType(id, type) {
        if (!id || !type) {
            console.error('[OrderService] fetchOrderByIdAndType: 无效的 ID 或 Type', { id, type });
            throw new Error('无效的 ID 或 Type');
        }
        console.log(`[OrderService] 尝试通过 API 获取订单: ID=${id}, Type=${type}`);
        try {
            // 直接调用 OrderRepository 中的 getOrder 方法
            const orderData = await this.orderRepository.getOrder(id, type);
            if (orderData) {
                console.log(`[OrderService] 成功从 API 获取到订单 ${id}`);
                // 可以选择在这里返回原始数据，或者包装成 Order 模型实例
                // return new Order(orderData);
                return orderData; // 返回原始数据，让 Store 处理
            } else {
                console.warn(`[OrderService] API 未返回订单 ${id} (Type: ${type}) 的数据`);
                return null; // 或者抛出错误
            }
        } catch (error) {
            console.error(`[OrderService] 调用 API 获取订单 ${id} (Type: ${type}) 失败:`, error);
            throw error; // 将错误向上抛出，让 Store 处理
        }
    }

    // 添加方法使缓存失效
    invalidateCache() {
        this._cacheValid = false
        this._cachedOrdersByType = null
        this._cachedDriverOrders = null
        this._cachedRouteOrders = null
        this._cachedAllOrders = null
        console.log('[OrderService] 已清除所有缓存');
    }

    // 添加已分配订单
    async addAssignedOrders(orders) {
        if (!Array.isArray(orders) || orders.length === 0) return

        const orderObjects = orders.map(order => {
            return order instanceof Order ? order : new Order(order)
        })

        this.assignedOrders.push(...orderObjects)
        this.invalidateCache() // 使缓存失效
        return this.assignedOrders
    }

    // 分配订单给司机
    async assignOrder(orderId, driverId, existingRouteNumber = null) {
    // 查找订单
        const order = this.orders.find(o => o.id === orderId)
        if (!order) return null

        // 查找司机
        const driver = await this.driverRepository.getDriver(driverId)
        if (!driver) return null

        // 确定路线
        let routeNumber = existingRouteNumber
        if (!routeNumber) {
            const existingRoute = await this.routeRepository.getDriverActiveRoute(driverId, null)
            if (existingRoute) {
                routeNumber = existingRoute.routeNumber
            } else {
                // 创建新路线
                const newRoute = this.routeRepository.createRoute(driverId, [])
                routeNumber = newRoute.routeNumber
            }
        }

        // 确定停靠序号
        const driverOrders = this.getDriverOrders(driverId)
        const stopNumber = driverOrders.length + 1

        // 分配订单
        const updatedOrder = order.assignDriver(driverId)
            .assignRoute(routeNumber, stopNumber)

        // 更新本地状态
        this.orders = this.orders.filter(o => o.id !== orderId)
        this.assignedOrders.push(updatedOrder)
        this.invalidateCache() // 使缓存失效

        // 更新路线订单
        const updatedOrders = [...driverOrders, updatedOrder]
        await this.routeRepository.updateRouteOrders(
            { routeNumber },
            updatedOrders
        )

        return updatedOrder
    }

    // 批量分配订单给司机
    async assignOrdersToDriver(orderIds, driverId, existingRouteNumber = null) {
        const results = []
        for (const orderId of orderIds) {
            const result = await this.assignOrder(orderId, driverId, existingRouteNumber)
            if (result) {
                results.push(result)
            }
        }
        return results
    }

    // 使用API分配订单到路线 - 修改：接收 orders 对象数组
    async assignOrdersToRouteWithAPI(orders, routeId) { // <-- 修改参数为 orders
        try {
            if (!orders || !Array.isArray(orders) || orders.length === 0) {
                console.warn('assignOrdersToRouteWithAPI: 传入的订单数组为空');
                return [];
            }

            // 按类型分组订单
            const pickups = orders.filter(o => o.type === 'PICKUP' || o.type === 'pickup');
            const deliveries = orders.filter(o => o.type === 'DELIVERY' || o.type === 'delivery');
            // 可以添加对 SERVICE 类型的处理

            const pickupIds = pickups.map(o => o.id);
            const deliveryIds = deliveries.map(o => o.id);

            const apiResults = [];

            // 调用 Repository 处理 Pickups
            if (pickupIds.length > 0) {
                console.log(`[OrderService] 调用 assignOrdersWithAPI 处理 ${pickupIds.length} 个 PICKUP 订单`);
                const pickupResult = await this.orderRepository.assignOrdersWithAPI(pickupIds, routeId, 'PICKUP');
                apiResults.push(...pickupResult);
            }

            // 调用 Repository 处理 Deliveries
            if (deliveryIds.length > 0) {
                console.log(`[OrderService] 调用 assignOrdersWithAPI 处理 ${deliveryIds.length} 个 DELIVERY 订单`);
                const deliveryResult = await this.orderRepository.assignOrdersWithAPI(deliveryIds, routeId, 'DELIVERY');
                apiResults.push(...deliveryResult);
            }

            // Process API response and get updated local orders
            const processedOrders = this.getOrdersFromApiResponse(apiResults);

            // Ensure the display refreshes (getOrdersFromApiResponse already calls it if changes occur)
            // this.refreshOrderDisplay();

            return processedOrders; // Return updated local orders
        } catch (error) {
            console.error('分配订单失败:', error);
            // Consider rolling back optimistic updates in the Store if needed
            this.invalidateCache(); // Invalidate cache on error
            throw error;
        }
    }

    // 使用API取消分配订单
    async unassignOrdersWithAPI(orderIds) {
        try {
            // 获取订单类型
            const orders = this.getAllOrders().filter(order => orderIds.includes(order.id))
            const pickupOrders = orders.filter(order => order.type === 'PICKUP' || order.type === 'pickup')
            const deliveryOrders = orders.filter(order => order.type === 'DELIVERY' || order.type === 'delivery')

            // 保存每个订单的原始路线和司机信息，以便能追踪变更
            const orderOriginalInfo = orders.map(order => ({
                id: order.id,
                routeId: order.route_id,
                driverId: order.driver_id,
                stopNo: order.stop_no
            }))

            // 分别处理不同类型的订单
            const results = []

            // 处理取件订单
            if (pickupOrders.length > 0) {
                const pickupIds = pickupOrders.map(order => order.id)
                const pickupResults = await this.orderRepository.unassignOrdersWithAPI(pickupIds, 'PICKUP')
                results.push(...pickupResults)
            }

            // 处理派送订单
            if (deliveryOrders.length > 0) {
                const deliveryIds = deliveryOrders.map(order => order.id)
                const deliveryResults = await this.orderRepository.unassignOrdersWithAPI(deliveryIds, 'DELIVERY')
                results.push(...deliveryResults)
            }

            // 将原始信息添加到结果订单中
            results.forEach(resultOrder => {
                const originalInfo = orderOriginalInfo.find(info => info.id === resultOrder.id)
                if (originalInfo) {
                    resultOrder._previousRouteId = originalInfo.routeId
                    resultOrder._previousDriverId = originalInfo.driverId
                    resultOrder._previousStopNo = originalInfo.stopNo
                }
            })

            // 更新本地状态
            this.getOrdersFromApiResponse(results)

            // 额外发送一个专门的取消分配事件，让组件可以更精确地处理
            if (typeof window !== 'undefined') {
                // 收集所有受影响的路线ID
                const affectedRouteIds = []
                orderOriginalInfo.forEach(info => {
                    if (info.routeId && !affectedRouteIds.includes(info.routeId)) {
                        affectedRouteIds.push(info.routeId)
                    }
                })

                // 发送专用的取消分配事件
                eventBus.emit(EVENT_TYPES.ORDERS_UNASSIGNED, {
                    orders: results,
                    originalInfo: orderOriginalInfo,
                    affectedRouteIds: affectedRouteIds
                })

                // 强制刷新显示 - 确保取消分配后立即更新UI
                console.log('[OrderService] 取消分配操作完成，强制刷新UI显示');
                this.refreshOrderDisplay();
            }

            return results
        } catch (error) {
            console.error('使用API取消分配订单失败:', error)
            throw error
        }
    }

    // 从API响应更新本地订单状态
    getOrdersFromApiResponse(apiOrders) {
        if (!apiOrders || !Array.isArray(apiOrders) || apiOrders.length === 0) {
            console.warn('getOrdersFromApiResponse: 传入的订单数组为空');
            return [];
        }

        const processedOrders = [];
        const unassignedOrders = [];
        const assignedOrders = [];
        const previousRouteIds = new Set();

        let hasAssignmentChanges = false;
        const assignedOrderIds = [];
        const unassignedOrderIds = [];

        // 处理每个从API返回的订单对象
        apiOrders.forEach(apiOrder => {
            if (!apiOrder.id) {
                console.warn('API返回的订单缺少ID:', apiOrder);
                return;
            }

            // 查找本地订单对象
            const localOrder = this.getAllOrders().find(o => o.id === apiOrder.id);

            if (localOrder) {
                const wasAssigned = !!localOrder.driver_id || !!localOrder.route_number || !!localOrder.route_id;
                const isNowAssigned = !!apiOrder.route_id || !!apiOrder.driver_id;

                console.log(`[OrderService] 订单 ${localOrder.id} 状态: 原先${wasAssigned ? '已分配' : '未分配'} => 现在${isNowAssigned ? '已分配' : '未分配'}, API路线ID: ${apiOrder.route_id || 'null'}, API司机ID: ${apiOrder.driver_id || 'null'}`);

                if (isNowAssigned) {
                    if (!wasAssigned) {
                        hasAssignmentChanges = true;
                        assignedOrderIds.push(apiOrder.id);
                        console.log(`[OrderService] 订单 ${apiOrder.id} 从未分配变为已分配`);
                    } else if (localOrder.route_id !== apiOrder.route_id || localOrder.stop_no !== apiOrder.stop_no || localOrder.driver_id !== apiOrder.driver_id) {
                        hasAssignmentChanges = true;
                        console.log(`[OrderService] 订单 ${apiOrder.id} 分配信息有变化: 路线ID ${localOrder.route_id} => ${apiOrder.route_id}, 停靠点 ${localOrder.stop_no} => ${apiOrder.stop_no}, 司机ID ${localOrder.driver_id} => ${apiOrder.driver_id}`);
                    }
                    localOrder.route_id = apiOrder.route_id;
                    localOrder.route_number = apiOrder.route_id;

                    // 确保driver_id也被正确更新
                    if (apiOrder.driver_id !== undefined) {
                        // 只有当API返回的司机ID不为null时才更新，或者本地没有司机ID时
                        if (apiOrder.driver_id !== null || !localOrder.driver_id) {
                            localOrder.driver_id = apiOrder.driver_id;
                            console.log(`[OrderService] 更新订单 ${apiOrder.id} 的司机ID为 ${apiOrder.driver_id}`);
                        } else {
                            console.log(`[OrderService] API返回的司机ID为null，保留本地司机ID: ${localOrder.driver_id}`);
                        }
                    } else if (apiOrder.route_id && !localOrder.driver_id) {
                        // 如果API没有返回driver_id，但有route_id，尝试获取路线对应的司机
                        this.getDriverIdFromRoute(apiOrder.route_id)
                            .then(driverId => {
                                if (driverId) {
                                    localOrder.driver_id = driverId;
                                    console.log(`[OrderService] 从路线获取并更新订单 ${apiOrder.id} 的司机ID为 ${driverId}`);
                                }
                            })
                            .catch(err => console.error(`[OrderService] 获取路线司机信息失败:`, err));
                    }

                    if (apiOrder.stop_no !== undefined) {
                        localOrder.stop_no = apiOrder.stop_no;
                    }
                    const unassignedIndex = this.orders.findIndex(o => o.id === apiOrder.id);
                    if (unassignedIndex !== -1) {
                        const order = this.orders.splice(unassignedIndex, 1)[0];
                        this.assignedOrders.push(order);
                        console.log(`[OrderService] 订单 ${apiOrder.id} 已从未分配列表移动到已分配列表`);
                    }
                    assignedOrders.push(localOrder);
                } else {
                    if (wasAssigned) {
                        hasAssignmentChanges = true;
                        unassignedOrderIds.push(apiOrder.id);
                        console.log(`[OrderService] 订单 ${apiOrder.id} 从已分配变为未分配`);
                    }
                    localOrder.route_id = null;
                    localOrder.route_number = null;
                    localOrder.driver_id = null;
                    localOrder.stop_no = null;
                    const assignedIndex = this.assignedOrders.findIndex(o => o.id === apiOrder.id);
                    if (assignedIndex !== -1) {
                        const order = this.assignedOrders.splice(assignedIndex, 1)[0];
                        this.orders.push(order);
                        console.log(`[OrderService] 订单 ${apiOrder.id} 已从已分配列表移动到未分配列表`);
                    }
                }
                if (apiOrder.stop_no !== undefined) {
                     localOrder.stop_no = apiOrder.stop_no;
                }

                processedOrders.push(localOrder);
            } else {
                console.log(`[OrderService] 发现新订单 ${apiOrder.id}，添加到相应列表`);
                const newOrder = new Order(apiOrder);
                if (apiOrder.route_id || apiOrder.driver_id) {
                    this.assignedOrders.push(newOrder);
                    hasAssignmentChanges = true;
                    assignedOrderIds.push(apiOrder.id);
                } else {
                    this.orders.push(newOrder);
                }
                 processedOrders.push(newOrder);
            }
        });

        console.log('[OrderService] 分配状态变化:', hasAssignmentChanges ? '是' : '否');
        if (assignedOrderIds.length > 0) console.log('[OrderService] 新分配订单IDs:', assignedOrderIds);
        if (unassignedOrderIds.length > 0) console.log('[OrderService] 已取消分配订单IDs:', unassignedOrderIds);

        this.invalidateCache()

        // 通过eventBus通知其他组件订单状态已更新
        if (typeof window !== 'undefined') {
            // 确定操作类型
            const isAssigning = hasAssignmentChanges && assignedOrderIds.length > 0;
            const isUnassigning = hasAssignmentChanges && unassignedOrderIds.length > 0;

            // 获取受影响的路线ID列表
            const routeIds = [...new Set(apiOrders.filter(o => o.route_id).map(o => o.route_id))];

            // 对每个路线发送更新通知
            if (isAssigning && routeIds.length > 0) {
                console.log('[OrderService] 分配操作影响的路线:', routeIds);

                routeIds.forEach(routeId => {
                    if (!routeId) return; // 跳过无效路线ID

                    const routeOrders = this.getAllOrders().filter(o => o.route_id === routeId);
                    if (routeOrders.length > 0) {
                        console.log(`[OrderService] 发送路线 ${routeId} 的更新通知，含 ${routeOrders.length} 个订单`);
                        eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                            orders: routeOrders,
                            routeId: routeId,
                            isAssign: true,
                            timestamp: Date.now()
                        });
                    }
                });
            }

            // 对取消分配操作单独发送通知 - 优化后的版本，包含更多信息
            if (isUnassigning) {
                const unassignedOrders = apiOrders.filter(o => !o.route_id && !o.driver_id);
                if (unassignedOrders.length > 0) {
                    // 保存取消分配前的路线ID以便在处理时能找到相应的路线
                    const previousRouteIds = [];
                    unassignedOrders.forEach(order => {
                        const localOrder = this.getAllOrders().find(o => o.id === order.id);
                        if (localOrder && localOrder._previousRouteId) {
                            if (!previousRouteIds.includes(localOrder._previousRouteId)) {
                                previousRouteIds.push(localOrder._previousRouteId);
                            }
                        }
                    });

                    eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                        orders: unassignedOrders,
                        isUnassign: true,
                        previousRouteIds: previousRouteIds,
                        allOrders: true,  // 标记这是影响全局的更新
                        timestamp: Date.now()
                    });

                    // 为每个受影响的路线单独发送更新通知
                    previousRouteIds.forEach(routeId => {
                        if (routeId) {
                            const routeOrders = unassignedOrders.filter(order => {
                                const localOrder = this.getAllOrders().find(o => o.id === order.id);
                                return localOrder && localOrder._previousRouteId === routeId;
                            });

                            if (routeOrders.length > 0) {
                                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                                    orders: routeOrders,
                                    routeId: routeId,
                                    isUnassign: true,
                                    timestamp: Date.now()
                                });
                            }
                        }
                    });
                }
            }

            // 发送全局更新通知
            if (hasAssignmentChanges) {
                console.log('[OrderService] 发送全局订单更新通知，分配状态有变化');
                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: apiOrders,
                    isAssign: isAssigning,
                    isUnassign: isUnassigning,
                    hasAssignmentChanges: hasAssignmentChanges,
                    assignedOrderIds: assignedOrderIds,
                    unassignedOrderIds: unassignedOrderIds,
                    allOrders: true,
                    timestamp: Date.now()
                });

                // 额外发送一个ORDER_LIST_UPDATED事件，确保订单列表组件刷新
                eventBus.emit(EVENT_TYPES.ORDER_LIST_UPDATED, {
                    isAssignmentChange: true,
                    timestamp: Date.now()
                });

                // 如果有分配状态变化，立即强制刷新UI显示
                this.refreshOrderDisplay();
            }
        }

        return processedOrders;
    }

    // 从路线获取司机ID
    async getDriverIdFromRoute(routeId) {
        try {
            const route = await this.routeRepository.getRouteById(routeId);
            if (route && route.driver_id) {
                return route.driver_id;
            }
            return null;
        } catch (error) {
            console.error(`[OrderService] 获取路线 ${routeId} 的司机信息失败:`, error);
            return null;
        }
    }

    // 从API刷新订单信息并更新本地状态 (用于替代assignOrdersToRouteWithAPI)
    async refreshOrdersFromAPI(orderIds, routeId, driverId = null) {
        try {
            if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
                console.warn('[OrderService] refreshOrdersFromAPI: 订单ID列表为空');
                return [];
            }

            console.log(`[OrderService] 从API刷新 ${orderIds.length} 个订单的信息，路线ID: ${routeId}, 司机ID: ${driverId || '未指定'}`);

            // 1. 首先进行本地乐观更新，以确保UI立即响应
            if (driverId) {
                // 使用store中对应的方法进行本地更新
                // 注意：这里需要在store中暴露这些方法以便服务层调用
                // 或者直接在服务层实现类似的更新逻辑
                console.log(`[OrderService] 使用司机ID ${driverId} 进行本地乐观更新`);
                this._updateLocalOrdersInfo(orderIds, routeId, driverId);
            }

            // 2. 然后从API获取最新的订单信息
            // 获取订单类型映射，以便API调用时使用正确的type参数
            const typeMap = {};
            orderIds.forEach(id => {
                const order = this.getAllOrders().find(o => o.id === id);
                if (order && order.type) {
                    typeMap[id] = order.type;
                }
            });

            const updatedOrders = await this.orderRepository.getOrdersBatch(orderIds, typeMap);
            console.log(`[OrderService] 从API获取了 ${updatedOrders.length} 个订单的最新信息`);

            // 3. 使用API返回的数据更新本地状态
            if (updatedOrders.length > 0) {
                // 添加或覆盖driver_id（如果API返回的为null但我们知道正确的值）
                if (driverId) {
                    updatedOrders.forEach(order => {
                        if (!order.driver_id) {
                            order.driver_id = driverId;
                            console.log(`[OrderService] 为订单 ${order.id} 设置司机ID: ${driverId} (API返回为null)`);
                        }
                    });
                }

                // 使用标准的处理方法更新本地状态
                const processedOrders = this.getOrdersFromApiResponse(updatedOrders);
                console.log(`[OrderService] 更新了 ${processedOrders.length} 个订单的本地状态`);

                return processedOrders;
            }

            return [];
        } catch (error) {
            console.error('[OrderService] 从API刷新订单信息失败:', error);
            throw error;
        }
    }

    // 本地更新订单信息的辅助方法（类似于store中的方法，但直接在service层操作）
    _updateLocalOrdersInfo(orderIds, routeId, driverId) {
        // 遍历每个要更新的订单
        orderIds.forEach(orderId => {
            // 查找未分配订单中的订单
            let order = this.orders.find(o => o.id === orderId);
            let inUnassignedList = true;

            // 如果在未分配订单中没找到，在已分配订单中查找
            if (!order) {
                order = this.assignedOrders.find(o => o.id === orderId);
                inUnassignedList = false;
            }

            if (order) {
                // 保存原始状态，便于在API失败时回滚
                order._previousState = {
                    route_id: order.route_id,
                    driver_id: order.driver_id,
                    stop_no: order.stop_no,
                    status: order.status
                };

                // 更新订单属性
                order.route_id = routeId;
                order.route_number = routeId; // 保持兼容性
                order.driver_id = driverId; // 设置司机ID
                order.status = 'assigned';

                // 如果订单在未分配列表中，移到已分配列表
                if (inUnassignedList) {
                    const index = this.orders.findIndex(o => o.id === orderId);
                    if (index !== -1) {
                        const movedOrder = this.orders.splice(index, 1)[0];
                        this.assignedOrders.push(movedOrder);
                        console.log(`[OrderService] 订单 ${orderId} 已从未分配列表移到已分配列表`);
                    }
                }
            }
        });

        // 使缓存失效
        this.invalidateCache();
    }

    // 选择订单
    selectOrder(order) {
        const orderId = typeof order === 'object' ? order.id : order
        const orderObj = this.getAllOrders().find(o => o.id === orderId)
        if (orderObj) {
            this.selectedOrder = orderObj
        }
        return this.selectedOrder
    }

    // 清除选中的订单
    clearSelectedOrder() {
        this.selectedOrder = null
    }

    // 选择/取消选择订单
    toggleOrderSelection(order) {
    // 获取订单ID（支持传入订单对象或订单ID）
        const orderId = typeof order === 'object' ? order.id : order

        // 检查当前选中状态
        const isCurrentlySelected = this.selectedOrderIds.has(orderId)

        // 获取完整订单对象
        const orderObj = typeof order === 'object' ? order : this.getAllOrders().find(o => o.id === orderId)

        // 切换选中状态
        if (isCurrentlySelected) {
            this.selectedOrderIds.delete(orderId)

            // 更新订单对象的isSelected属性
            if (orderObj) {
                orderObj.isSelected = false
            }
        } else {
            this.selectedOrderIds.add(orderId)

            // 更新订单对象的isSelected属性
            if (orderObj) {
                orderObj.isSelected = true
            }
        }

        // 同步更新selectedOrders数组 - 使用缓存减少重复过滤
        this._updateSelectedOrders()

        return this.selectedOrders
    }

    // 批量选择订单
    selectOrders(orderIds) {
    // 清除当前选择
        this.selectedOrderIds.clear()

        // 添加新选择的ID
        orderIds.forEach(id => this.selectedOrderIds.add(id))

        // 获取所有订单
        const allOrders = this.getAllOrders()

        // 优化循环 - 使用Map加速查找
        const selectedIdsSet = this.selectedOrderIds

        // 更新每个订单的isSelected属性
        for (let i = 0; i < allOrders.length; i++) {
            const order = allOrders[i]
            order.isSelected = selectedIdsSet.has(order.id)
        }

        // 同步更新selectedOrders数组 - 使用缓存减少重复过滤
        this._updateSelectedOrders()

        return this.selectedOrders
    }

    // 辅助方法：高效更新selectedOrders数组
    _updateSelectedOrders() {
        if (this.selectedOrderIds.size === 0) {
            this.selectedOrders = []
            return
        }

        const selectedIdsSet = this.selectedOrderIds
        // 通过一次过滤操作更新选中订单
        this.selectedOrders = this.getAllOrders().filter(order => selectedIdsSet.has(order.id))
    }

    // 清除所有选择
    clearSelection() {
        this.selectedOrderIds.clear()

        // 优化循环 - 只对曾经选中的订单重置状态
        if (this.selectedOrders.length > 0) {
            // 重置之前选中的订单
            this.selectedOrders.forEach(order => {
                order.isSelected = false
            })

            this.selectedOrders = []
        }
    }

    // 清除已分配订单
    clearAssignedOrders() {
        this.assignedOrders = []
    }

    /**
   * 将当前订单列表保存为备份格式
   * @returns {Promise<boolean>} - 是否成功保存
   */
    async saveOrdersToBackup() {
        try {
            const allOrders = this.getAllOrders()
            return await this.backupService.saveOrdersToBackup(allOrders)
        } catch (error) {
            console.error('保存订单到备份文件失败:', error)
            return false
        }
    }

    /**
   * 将指定订单列表保存为备份格式
   * @param {Array} orders - 要保存的订单列表
   * @returns {Promise<boolean>} - 是否成功保存
   */
    async saveSpecificOrdersToBackup(orders) {
        try {
            if (!Array.isArray(orders) || orders.length === 0) {
                console.warn('没有可保存的订单')
                return false
            }

            return await this.backupService.saveOrdersToBackup(orders)
        } catch (error) {
            console.error('保存指定订单到备份文件失败:', error)
            return false
        }
    }

    /**
   * 清空备份文件并重新初始化
   * @returns {Promise<boolean>} - 是否成功初始化
   */
    async initializeBackupFile() {
        try {
            return await this.backupService.initializeBackupFile()
        } catch (error) {
            console.error('初始化备份文件失败:', error)
            return false
        }
    }

    // 更新订单的经纬度坐标
    updateOrderCoordinates(orderId, coordinates) {
        if (!coordinates || !Array.isArray(coordinates) || coordinates.length !== 2) {
            console.error('无效的坐标数据:', coordinates);
            return false;
        }

        // 在未分配订单中查找
        let order = this.orders.find(o => o.id === orderId);
        let inUnassigned = true;

        // 如果在未分配订单中没找到，在已分配订单中查找
        if (!order) {
            order = this.assignedOrders.find(o => o.id === orderId);
            inUnassigned = false;
        }

        // 如果找到订单，更新坐标
        if (order) {
            order.lng_lat = coordinates;

            // 使缓存失效
            this.invalidateCache();

            // 触发事件通知坐标已更新
            if (typeof window !== 'undefined') {
                eventBus.emit(EVENT_TYPES.ORDER_COORDINATES_UPDATED, {
                    orderId,
                    coordinates,
                    timestamp: Date.now()
                });
            }

            return true;
        }

        return false;
    }

    // 批量更新多个订单的坐标
    batchUpdateOrderCoordinates(coordinatesData) {
        if (!Array.isArray(coordinatesData) || coordinatesData.length === 0) {
            return 0;
        }

        let updatedCount = 0;
        let updatedOrders = [];

        // 静默更新订单坐标，不触发单个更新事件
        coordinatesData.forEach(item => {
            if (item.id && item.coordinates) {
                // 在未分配订单中查找
                let order = this.orders.find(o => o.id === item.id);
                let inUnassigned = true;

                // 如果在未分配订单中没找到，在已分配订单中查找
                if (!order) {
                    order = this.assignedOrders.find(o => o.id === item.id);
                    inUnassigned = false;
                }

                // 如果找到订单，更新坐标
                if (order) {
                    order.lng_lat = item.coordinates;
                    updatedCount++;
                    updatedOrders.push({
                        id: item.id,
                        coordinates: item.coordinates
                    });
                }
            }
        });

        // 使缓存失效
        if (updatedCount > 0) {
            this.invalidateCache();
        }

        // 如果有更新，只触发一次批量更新事件，而不是每个坐标单独触发
        if (updatedCount > 0 && typeof window !== 'undefined') {
            console.log(`批量更新了 ${updatedCount} 个订单的坐标 - 发送单个事件通知`);

            // 先发送一个聚合的坐标更新事件，供特定组件使用
            eventBus.emit(EVENT_TYPES.ORDER_COORDINATES_BATCH_UPDATED, {
                orders: updatedOrders,
                count: updatedCount,
                timestamp: Date.now()
            });

            // 然后发送一个通用的订单更新事件，减少冗余事件
            eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                orders: this.orders.concat(this.assignedOrders),
                assignedOrders: this.assignedOrders,
                isCoordinatesUpdate: true,
                timestamp: Date.now()
            });
        }

        return updatedCount;
    }

    // 添加方法触发订单显示刷新，强制地图和列表更新
    async refreshOrderDisplay() {
        // 添加标记以指示这是强制刷新
        const updateData = {
            isForceRefresh: true
        }

        // 先触发ORDER_LIST_UPDATED事件，让订单列表先更新
        eventBus.emit(EVENT_TYPES.ORDER_LIST_UPDATED, updateData)

        // 使用短暂延时，确保DOM更新后再触发地图和面板更新
        setTimeout(async () => {
            // 获取当前时间和班次信息 (需要引入 useTimeStore)
            let timeStore;
            try {
                timeStore = useTimeStore(); // 确保在 setup 环境外也能正确获取 store
            } catch (e) {
                console.error("[OrderService] 获取 TimeStore 失败: ", e);
                // 无法获取 timeStore，尝试不带参数获取路线，或者直接放弃
                 console.warn("[OrderService] 无法获取时间参数，将不带参数尝试获取路线");
                 // 或者直接返回，避免调用失败
                 // return;
            }

            // 构建参数，如果 timeStore 获取失败，则为空对象
            const currentParams = timeStore ? {
                date: timeStore.selectedDate,
                priority: timeStore.selectedShift?.value // 使用可选链和假设的 value 属性
            } : {};

            // 检查参数是否完整
            if (!currentParams.date) {
                console.warn("[OrderService] refreshOrderDisplay: 缺少 date 参数，无法获取路线。", currentParams);
                 // 也许不应该继续？或者尝试获取所有路线？
                 // return;
            }

            try {
                // 然后触发所有路线的更新事件 - 使用 await 并传递参数
                console.log("[OrderService] refreshOrderDisplay: 调用 getRoutes，参数:", currentParams);
                const routes = await this.routeRepository.getRoutes(currentParams); // 传递参数并 await

                // 检查routes是否为有效的可迭代对象
                if (routes && Array.isArray(routes) && routes.length > 0) { // 增加长度检查
                    console.log(`[OrderService] 成功获取 ${routes.length} 条路线，准备为每条路线发送更新事件`);
                    for (const route of routes) {
                        // 确保 route.id 存在
                        if (route && route.id) {
                            eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                                routeId: route.id,
                                isForceRefresh: true,
                                isAssign: true
                            })
                        } else {
                            console.warn("[OrderService] refreshOrderDisplay: 发现无效的路线对象，跳过事件发送:", route);
                        }
                    }
                } else {
                    console.warn('[OrderService] getRoutes()返回无效或空结果，无法为路线发送更新事件', routes)
                }
            } catch (error) {
                console.error('[OrderService] refreshOrderDisplay 获取路线失败:', error);
            }

            // 最后触发DRIVERS_UPDATED事件，让驾驶员面板更新
            eventBus.emit(EVENT_TYPES.DRIVERS_UPDATED, updateData)

            console.log('[OrderService] 已强制刷新所有相关组件显示')
        }, 50) // 50毫秒延时，给DOM更新留出时间
    }

    // 处理订单创建消息
    async handleOrderCreatedMessage(messageData) {
        try {
            console.log('[OrderService] 处理订单创建消息:', messageData);

            // 解析消息数据
            if (!messageData || !messageData.action || !messageData.body) {
                console.warn('[OrderService] 无效的订单创建消息:', messageData);
                return null;
            }

            // 确保是ORDER_CREATED类型
            if (messageData.action !== 'ORDER_CREATED') {
                console.warn(`[OrderService] 不支持的消息类型: ${messageData.action}`);
                return null;
            }

            // 解析消息体
            let orderInfo;
            try {
                orderInfo = typeof messageData.body === 'string' ?
                    JSON.parse(messageData.body) : messageData.body;
            } catch (e) {
                console.error('[OrderService] 解析消息体失败:', e);
                return null;
            }

            if (!orderInfo.id) {
                console.warn('[OrderService] 消息中没有订单ID:', orderInfo);
                return null;
            }

            // 1. 通过getorderpickuptime检查订单时间
            const orderId = orderInfo.id;
            console.log(`[OrderService] 获取订单 ${orderId} 取货时间信息`);

            let timeInfo;
            try {
                timeInfo = await this.orderRepository.getOrderPickupTime(orderId);
            } catch (error) {
                console.error(`[OrderService] 获取订单 ${orderId} 取货时间失败:`, error);
                return null;
            }

            if (!timeInfo || !timeInfo.expected_pickup_start_at) {
                console.warn(`[OrderService] 订单 ${orderId} 缺少取货时间信息:`, timeInfo);
                return null;
            }

            // 2. 检查时间是否在当前范围内
            const expectedPickupTime = timeInfo.expected_pickup_start_at;
            console.log(`[OrderService] 订单 ${orderId} 预期取货时间: ${expectedPickupTime}`);

            // 获取当前选择的日期和班次
            const timeStore = useTimeStore();
            if (!timeStore) {
                console.warn('[OrderService] 无法获取时间存储，无法检查时间范围');
                return null;
            }

            const isInCurrentRange = this.isPickupTimeInCurrentRange(
                expectedPickupTime,
                timeStore.selectedDate,
                timeStore.selectedShift
            );

            if (!isInCurrentRange) {
                console.log(`[OrderService] 订单 ${orderId} 不在当前选择的时间范围内，忽略`);
                return null;
            }

            // 3. 获取订单详细信息
            console.log(`[OrderService] 订单 ${orderId} 在当前时间范围内，获取详细信息`);

            let orderDetail;
            try {
                orderDetail = await this.orderRepository.getOrder(orderId, 'PICKUP');
            } catch (error) {
                console.error(`[OrderService] 获取订单 ${orderId} 详细信息失败:`, error);
                return null;
            }

            if (!orderDetail) {
                console.warn(`[OrderService] 未能获取订单 ${orderId} 的详细信息`);
                return null;
            }

            // 4. 添加订单到存储
            console.log(`[OrderService] 添加新订单到存储: ${orderId}`);

            // 检查订单是否已存在
            const existingOrder = this.getAllOrders().find(o => o.id === orderId);
            if (existingOrder) {
                console.log(`[OrderService] 订单 ${orderId} 已存在，更新信息`);

                // 更新现有订单
                Object.assign(existingOrder, orderDetail);

                // 使缓存失效
                this.invalidateCache();

            } else {
                console.log(`[OrderService] 添加新订单 ${orderId}`);

                // 添加新订单到未分配列表
                this.orders.push(orderDetail);

                // 使缓存失效
                this.invalidateCache();
            }

            // 5. 发送事件通知组件更新
            if (typeof window !== 'undefined') {
                console.log(`[OrderService] 发送订单更新事件，包含新订单 ${orderId}`);

                // 发送订单更新事件
                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: [orderDetail],
                    isNewOrder: true,
                    timestamp: Date.now()
                });

                // 发送一个列表更新事件
                eventBus.emit(EVENT_TYPES.ORDER_LIST_UPDATED, {
                    isNewOrder: true,
                    timestamp: Date.now()
                });

                // 强制刷新显示
                this.refreshOrderDisplay();
            }

            return orderDetail;

        } catch (error) {
            console.error('[OrderService] 处理订单创建消息出错:', error);
            return null;
        }
    }

    // 检查取货时间是否在当前选择的时间范围内
    isPickupTimeInCurrentRange(pickupTimeStr, selectedDate, selectedShift) {
        try {
            // 解析预期取货时间
            // 格式: "04-27 11:00:00"
            const match = pickupTimeStr.match(/(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/);
            if (!match) {
                console.warn(`[OrderService] 无法解析取货时间: ${pickupTimeStr}`);
                return false;
            }

            const [_, month, day, hour, minute, second] = match;

            // 获取当前年份（假设订单是当年的）
            const currentYear = new Date().getFullYear();

            // 创建取货时间对象
            const pickupTime = new Date(currentYear, parseInt(month) - 1, parseInt(day),
                                        parseInt(hour), parseInt(minute), parseInt(second));

            // 解析选中的日期 (假设格式为 "YYYY-MM-DD")
            const selectedDateParts = selectedDate.split('-');
            if (selectedDateParts.length !== 3) {
                console.warn(`[OrderService] 无法解析选中的日期: ${selectedDate}`);
                return false;
            }

            const selectedYear = parseInt(selectedDateParts[0]);
            const selectedMonth = parseInt(selectedDateParts[1]) - 1;
            const selectedDay = parseInt(selectedDateParts[2]);

            // 检查日期是否匹配
            if (pickupTime.getDate() !== selectedDay ||
                pickupTime.getMonth() !== selectedMonth) {
                console.log(`[OrderService] 日期不匹配: ${pickupTimeStr} vs ${selectedDate}`);
                return false;
            }

            // 检查是否在选中的班次范围内
            if (selectedShift) {
                // 假设selectedShift有start和end属性，表示班次的开始和结束时间
                const shiftStartHour = selectedShift.start ? parseInt(selectedShift.start.split(':')[0]) : 0;
                const shiftEndHour = selectedShift.end ? parseInt(selectedShift.end.split(':')[0]) : 24;

                const pickupHour = pickupTime.getHours();

                if (pickupHour < shiftStartHour || pickupHour >= shiftEndHour) {
                    console.log(`[OrderService] 时间不在班次范围内: ${pickupHour}点 不在 ${shiftStartHour}-${shiftEndHour}之间`);
                    return false;
                }
            }

            return true;

        } catch (error) {
            console.error('[OrderService] 检查时间范围出错:', error);
            return false;
        }
    }

    // 使用TomTom优化路线订单顺序
    async optimizeRouteOrderSequence(orders, driverId, routeId) {
        if (!orders || !Array.isArray(orders) || orders.length === 0) {
            console.warn('[OrderService] 优化路线顺序：订单列表为空');
            return orders;
        }

        // 记录参数类型，便于调试
        console.log(`[OrderService] 优化路线序列入参: driverId=${driverId}(${typeof driverId}), routeId=${routeId}(${typeof routeId}), 订单数量=${orders.length}`);
        console.log(`[OrderService] 订单示例:`, orders[0] ? JSON.stringify({
            id: orders[0].id,
            driver_id: orders[0].driver_id,
            route_id: orders[0].route_id,
            route_number: orders[0].route_number
        }, null, 2) : 'null');

        // 尝试从订单中获取routeId和driverId（如果传入的值无效）
        if ((!routeId || routeId === 'null' || routeId === 'undefined') && orders.length > 0) {
            // 检查是否是string类型的"null"或"undefined"
            if (typeof routeId === 'string' && (routeId.toLowerCase() === 'null' || routeId.toLowerCase() === 'undefined')) {
                console.log(`[OrderService] 发现routeId是字符串 "${routeId}"，将尝试从订单获取有效值`);
                routeId = null;
            }

            // 尝试从第一个订单获取路线ID
            const firstOrderRouteId = orders[0].route_id || orders[0].route_number;
            if (firstOrderRouteId && firstOrderRouteId !== 'null' && firstOrderRouteId !== 'undefined') {
                routeId = firstOrderRouteId;
                console.log(`[OrderService] 从订单获取到路线ID: ${routeId}`);
            }
        }

        // 确保路线ID有效
        if (!routeId || routeId === 'null' || routeId === 'undefined') {
            console.error('[OrderService] 无法获取有效的路线ID，优化失败');
            return orders;
        }

        // 确保driver_id有效，如果无效则尝试从订单或路线获取
        if (!driverId || driverId === 'null' || driverId === 'undefined') {
            // 检查是否是string类型的"null"或"undefined"
            if (typeof driverId === 'string' && (driverId.toLowerCase() === 'null' || driverId.toLowerCase() === 'undefined')) {
                console.log(`[OrderService] 发现driverId是字符串 "${driverId}"，将尝试从订单获取有效值`);
                driverId = null;
            }

            // 1. 尝试从订单获取
            if (orders.length > 0 && orders[0].driver_id && orders[0].driver_id !== 'null' && orders[0].driver_id !== 'undefined') {
                driverId = orders[0].driver_id;
                console.log(`[OrderService] 从订单获取到司机ID: ${driverId}`);
            }
            // 2. 尝试本地获取路线信息
            else {
                try {
                    const localRoute = this.getAllOrders()
                        .find(o => o.route_id === routeId || o.route_number === routeId);

                    if (localRoute && localRoute.driver_id) {
                        driverId = localRoute.driver_id;
                        console.log(`[OrderService] 从本地订单获取到司机ID: ${driverId}`);
                    } else {
                        // 3. 只有在必要时才调用API
                        console.log(`[OrderService] 从本地数据未能获取到司机ID，尝试从API获取`);
                        try {
                            const route = await this.routeRepository.getRouteById(routeId);
                            if (route && (route.driver_id || route.driverId)) {
                                driverId = route.driver_id || route.driverId;
                                console.log(`[OrderService] 从API获取到司机ID: ${driverId}`);
                            } else {
                                console.warn(`[OrderService] 无法从路线 ${routeId} 获取司机ID`);
                            }
                        } catch (routeError) {
                            console.error(`[OrderService] 获取路线 ${routeId} 信息失败:`, routeError);
                        }
                    }
                } catch (e) {
                    console.error('[OrderService] 获取本地路线数据失败:', e);
                }
            }
        }

        // 确保driverId不为null
        if (!driverId) {
            console.error('[OrderService] 无法获取有效的司机ID，优化失败');
            return orders;
        }

        console.log(`[OrderService] 确认使用参数: driverId=${driverId}, routeId=${routeId} 进行优化`);

        try {
            // 获取司机信息
            const driver = await this.driverRepository.getDriver(driverId);
            if (!driver) {
                console.warn(`[OrderService] 未能获取到司机 ${driverId} 的信息，将使用默认位置`);
                return orders; // 如果找不到司机信息，返回原始订单序列
            }
            console.log(`[OrderService] 成功获取到司机 ${driverId} 的信息`);

            // 获取当前班次信息
            const timeStore = useTimeStore();
            let shift = 'morning'; // 默认班次

            // 安全获取班次值，确保值是字符串
            let shiftValue = 'morning';
            if (timeStore && timeStore.selectedShift) {
                // 检查timeStore.selectedShift是否是对象且有value属性
                if (typeof timeStore.selectedShift === 'object' && timeStore.selectedShift !== null) {
                    if (typeof timeStore.selectedShift.value === 'string') {
                        shiftValue = timeStore.selectedShift.value;
                    } else if (timeStore.selectedShift.value !== undefined) {
                        // 如果value不是字符串但存在，转换为字符串
                        shiftValue = String(timeStore.selectedShift.value);
                    }
                } else if (typeof timeStore.selectedShift === 'string') {
                    // 如果selectedShift本身就是字符串
                    shiftValue = timeStore.selectedShift;
                } else {
                    // 其他情况转为字符串
                    shiftValue = String(timeStore.selectedShift);
                }
            }

            console.log(`[OrderService] 使用班次值: ${shiftValue}`);

            // 将班次值映射到TomTom工具可识别的值
            if (typeof shiftValue === 'string') {
                const shiftLower = shiftValue.toLowerCase();
                if (shiftLower.includes('morning') || shiftLower.includes('上午')) {
                    shift = 'morning';
                } else if (shiftLower.includes('afternoon') || shiftLower.includes('下午')) {
                    shift = 'afternoon';
                } else if (shiftLower.includes('evening') || shiftLower.includes('晚上')) {
                    shift = 'evening';
                }
            }

            // 调用TomTom API获取最优路线顺序
            try {
                console.log(`[OrderService] 请求TomTom优化服务，司机信息: ID=${driverId}, 位置=(${driver.latitude}, ${driver.longitude})`);
                console.log(`[OrderService] 即将调用calculateOptimalOrderSequence，传递${orders.length}个订单和司机/班次信息`);

                try {
                    const optimizedOrders = await calculateOptimalOrderSequence(orders, {
                        driver,
                        shift,
                        // 可以添加其他配置如仓库位置等
                    });

                    console.log(`[OrderService] calculateOptimalOrderSequence调用已完成，返回${optimizedOrders?.length || 0}个订单`);

                    if (!optimizedOrders || !Array.isArray(optimizedOrders) || optimizedOrders.length === 0) {
                        console.warn('[OrderService] TomTom优化服务没有返回有效结果');
                        return orders;
                    }

                    // 更新订单的停靠点编号
                    const ordersWithNewStopNos = updateOrderStopNumbers(optimizedOrders);

                    console.log(`[OrderService] 路线 ${routeId} 订单顺序优化完成，按新顺序更新停靠点编号`);
                    console.log(`[OrderService] 排序后的订单IDs: ${ordersWithNewStopNos.map(o => o.id).join(', ')}`);

                    return ordersWithNewStopNos;
                } catch (optimizeError) {
                    console.error('[OrderService] 调用TomTom优化API失败:', optimizeError);
                    return orders; // 出错时返回原始订单
                }
            } catch (optimizeError) {
                console.error('[OrderService] 调用TomTom优化API失败:', optimizeError);
                return orders; // 出错时返回原始订单
            }
        } catch (error) {
            console.error('[OrderService] 优化路线顺序失败:', error);
            return orders; // 出错时返回原始订单
        }
    }

    // 更新路线订单的停靠点编号，按照优化后的顺序
    async updateOrdersStopNumbers(orders, routeId, type) {
        if (!orders || !Array.isArray(orders) || orders.length === 0) {
            return [];
        }

        console.log(`[OrderService] 更新 ${orders.length} 个订单的停靠点编号，路线ID: ${routeId}`);

        // 创建更新停靠点的Promise数组
        const updatePromises = orders.map((order, index) => {
            const stopNo = index + 1;

            // 记录详细日志
            console.log(`[OrderService] 更新订单 ${order.id} 的停靠点编号为 ${stopNo}, 类型: ${type || order.type || 'PICKUP'}`);

            // 调用API更新停靠点编号
            return this.orderRepository.updateOrderStopNumber(
                order.id,
                stopNo,
                type || order.type || 'PICKUP'
            ).then((result) => {
                // 同时更新本地订单对象
                order.stop_no = stopNo;

                // 确保订单与路线关联
                if (routeId && (!order.route_id || order.route_id !== routeId)) {
                    console.log(`[OrderService] 更新订单 ${order.id} 的路线ID为 ${routeId}`);
                    order.route_id = routeId;
                    order.route_number = routeId; // 保持兼容性
                }

                return order;
            }).catch(error => {
                console.error(`[OrderService] 更新订单 ${order.id} 停靠点编号失败:`, error);
                // 即使失败也返回订单，以便继续处理其他订单
                return order;
            });
        });

        // 等待所有更新完成
        const updatedOrders = await Promise.all(updatePromises);
        console.log(`[OrderService] ${updatedOrders.length} 个订单停靠点编号更新完成`);

        // 使缓存失效，确保下次获取订单时能获取到最新状态
        this.invalidateCache();

        // 发送事件通知地图和其他组件更新
        if (typeof window !== 'undefined' && window.eventBus) {
            window.eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                orders: updatedOrders,
                routeId: routeId,
                isRouteUpdate: true,
                timestamp: Date.now()
            });
        }

        return updatedOrders;
    }

    // 批量分配订单到路线并优化顺序
    async assignOrdersToRouteWithOptimization(orders, routeId, driverId = null) {
        try {
            if (!orders || !Array.isArray(orders) || orders.length === 0) {
                console.warn('assignOrdersToRouteWithOptimization: 传入的订单数组为空');
                return [];
            }

            console.log(`[OrderService] 分配并优化 ${orders.length} 个订单到路线 ${routeId}，司机ID: ${driverId || '未指定'}`);

            // 如果没有传入司机ID，尝试从路线获取
            if (!driverId) {
                try {
                    console.log(`[OrderService] 尝试从路线 ${routeId} 获取司机ID`);
                    const route = await this.routeRepository.getRouteById(routeId);
                    if (route && (route.driver_id || route.driverId)) {
                        driverId = route.driver_id || route.driverId;
                        console.log(`[OrderService] 从路线 ${routeId} 获取到司机ID: ${driverId}`);
                    } else {
                        console.warn(`[OrderService] 无法从路线 ${routeId} 获取司机ID，将跳过优化步骤`);
                    }
                } catch (error) {
                    console.error(`[OrderService] 获取路线 ${routeId} 信息失败:`, error);
                }
            }

            // 1. 先通过API分配订单到路线
            const assignedOrders = await this.assignOrdersToRouteWithAPI(orders, routeId);

            // 2. 如果分配成功并且有司机ID，使用TomTom优化订单顺序
            if (assignedOrders.length > 0 && driverId) {
                console.log(`[OrderService] 订单已分配到路线 ${routeId}，现在使用司机ID ${driverId} 优化顺序`);

                // 优化路线顺序
                const optimizedOrders = await this.optimizeRouteOrderSequence(
                    assignedOrders,
                    driverId,
                    routeId
                );

                // 更新停靠点编号
                const updatedOrders = await this.updateOrdersStopNumbers(
                    optimizedOrders,
                    routeId,
                    optimizedOrders[0]?.type // 使用第一个订单的类型作为默认类型
                );

                // 刷新订单显示，确保UI更新
                this.refreshOrderDisplay();

                return updatedOrders;
            } else if (assignedOrders.length > 0) {
                console.log(`[OrderService] 订单已分配到路线 ${routeId}，但没有有效的司机ID，跳过优化步骤`);
                return assignedOrders;
            }

            return assignedOrders;
        } catch (error) {
            console.error('[OrderService] 分配并优化订单失败:', error);
            // 发生错误时可能需要重新获取数据
            this.invalidateCache();
            throw error;
        }
    }

    // 强制刷新订单显示
    refreshOrderDisplay(newOrderIds) {
        console.log('[Vue Reactivity] [OrderService] 强制刷新订单显示');

        // 使缓存失效
        this.invalidateCache();

        // 触发订单更新事件
        if (typeof window !== 'undefined') {
            // 发送订单更新事件
            eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                orders: this.getAllOrders(),
                timestamp: Date.now(),
                isRefresh: true
            });

            // 发送订单列表更新事件
            eventBus.emit(EVENT_TYPES.ORDER_LIST_UPDATED, {
                timestamp: Date.now(),
                isRefresh: true
            });

            // 发送地图刷新事件，包含新订单ID列表
            eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
                timestamp: Date.now(),
                newOrderIds: newOrderIds || []
            });

            console.log('[Vue Reactivity] [OrderService] 已发送订单更新和地图刷新事件');
            if (newOrderIds && newOrderIds.length > 0) {
                console.log('[Vue Reactivity] [OrderService] 包含新订单ID列表:', newOrderIds);
            }
        }

        return true;
    }
}