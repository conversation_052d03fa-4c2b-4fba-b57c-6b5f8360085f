<script setup>
import CCDetailForm from '../DetailForm/cc_detail.vue'
import APDetailForm from '../DetailForm/ap_detail.vue'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

const { proxy } = getCurrentInstance()


const props = defineProps({
    // eslint-disable-next-line vue/valid-define-props
    id: {
        type: String,
        default: null
    },
    modelValue: {
        type: Boolean,
        default: false
    },
    detail: {
        type: String,
        default: null
    }
})

const emit = defineEmits(['update:modelValue', 'success'])

let myVisible = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})

const title = computed(() => props.detail == 'CC' ? t('finance.ccLog.detailPage') : t('finance.apLog.detailPage'))

function onCancel() {
    myVisible.value = false
}
</script>

<template>
    <div>
        <el-drawer v-model="myVisible" :title="title" size="600px" :close-on-click-modal="false" destroy-on-close>
            <CCDetailForm ref="form" v-bind="$props" v-if="props.detail == 'CC'" />
            <APDetailForm ref="form" v-bind="$props" v-else-if="props.detail == 'AP'" />
            <template #footer>
                <el-button size="large" @click="onCancel" type="primary" plain>{{ $t('operations.back') }}</el-button>
            </template>
        </el-drawer>
    </div>
</template>
