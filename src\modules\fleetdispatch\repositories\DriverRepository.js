import { Driver } from '../models/Driver'
import { driverAPI } from '../api'

export class DriverRepository {
    // 获取司机列表
    async getDrivers(params = {}) {
        try {
            console.log('DriverRepository: 获取司机列表，参数:', params);
            const response = await driverAPI.getDrivers(params);

            if (!response || !response.driver_list) {
                console.warn('DriverRepository: 获取司机列表返回格式不正确:', response);
                return [];
            }

            // 将API返回的普通对象转换为Driver对象
            const drivers = Array.isArray(response.driver_list)
                ? response.driver_list.map(driver => new Driver(driver))
                : [];

            console.log(`DriverRepository: 成功获取 ${drivers.length} 个司机`);
            return drivers;
        } catch (error) {
            console.error('DriverRepository: 获取司机数据失败:', error);
            throw error;
        }
    }

    // 获取单个司机
    async getDriver(id) {
        try {
            const response = await driverAPI.getDriver(id)
            if (!response || !response.driver) {
                return null
            }

            return new Driver(response.driver)
        } catch (error) {
            console.error(`获取司机 ${id} 失败:`, error)
            throw error
        }
    }

    // 更新司机状态
    async updateDriverStatus(driver, status) {
    // 这里可能需要调用API来实际更新服务器数据
    // 目前我们只在本地进行更新
        return driver.updateStatus(status)
    }

    // 批量更新司机状态
    updateDriverStatuses(drivers, activeDriverIds) {
        if (!Array.isArray(drivers)) return []

        return drivers.map(driver => {
            const isActive = activeDriverIds.includes(driver.id)
            return driver.updateStatus(isActive ? '在线' : '离线')
        })
    }

    // 为司机分配路线
    async assignRouteToDriver(driver, routeId) {
    // 这里可能需要调用API来实际更新服务器数据
    // 目前我们只在本地进行更新
        return driver.assignRoute(routeId)
    }

    // 取消司机的路线分配
    async unassignDriver(driver) {
    // 这里可能需要调用API来实际更新服务器数据
    // 目前我们只在本地进行更新
        return driver.unassignRoute()
    }
}