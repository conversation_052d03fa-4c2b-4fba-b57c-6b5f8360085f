<script setup>
    import { getDeliveryTimes, updateDeliveryTime, addDeliveryTime } from '@/api/modules/delivery';
    import { userTypes, weekdays } from '@/utils/constants'
    import { getUserGroups } from '@/api/modules/users';
    import { getZones } from '@/api/modules/dispatcher'
    import { getAllHolidays } from '@/api/modules/settings';

    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            name: '',
            description: '',
            available_until_base: '00:00',
            available_until_hours_offset: 0,
            upload_at: '00:00',
            upload_at_offset: 0,
        },
        rules: {
            name: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ]
        }
    })
    const userGroups = ref([])
    const holidays = ref([])
    const zones = ref([])


    onMounted(() => {
        setUserGroups()
        setZones()
        setHolidays()
        if (data.value.form.id != '') {
            getInfo()
        }
    })
    function setUserGroups() {
        getUserGroups().then(res => {
            userGroups.value = res.data.group_list
        })
    }
    function setZones() {
        getZones().then(res => {
            zones.value = res.data.zone_list
        })
    }
    function setHolidays() {
        getAllHolidays().then(res => { holidays.value = res.data }
        )
    }



    function getInfo() {
        data.value.loading = true
        getDeliveryTimes({ id: data.value.form.id }
        ).then(res => {
            data.value.loading = false
            data.value.form = res.data
            feeTemp.value = Number((res.data.fee / 100).toFixed(2))
        })
    }

    defineExpose({

        submit(callback) {
            var params = JSON.parse(JSON.stringify(data.value.form))
            if (data.value.form?.zones?.length) {
                params.zones = data.value.form.zones.map(x => x.id);
            }
            if (data.value.form?.pickup_zones?.length) {
                params.pickup_zones = data.value.form.pickup_zones.map(x => x.id);
            }

            if (data.value.form.id == '') {
                formRef.value.validate(valid => {
                    if (valid) {
                        addDeliveryTime(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            } else {
                formRef.value.validate(valid => {
                    if (valid) {
                        updateDeliveryTime(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            }
        }
    })

    const feeTemp = ref(0.0)
    function handleFeeChange() {
        data.value.form.fee = Number((feeTemp.value * 100).toFixed(0))
    }


    const availableUntil = ([base, offset]) => {
        const times = base.split(':');
        var timeHours = Number(times[0]) + Number(times[1]) / 60;
        if (Math.abs(offset) > timeHours) {
            return -(timeHours + 12 + offset);
        } else {
            return timeHours + offset
        }
    }
    const isAvailableUntilToday = ([base, offset]) => availableUntil([base, offset]) >= 0;


</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" maxlength="200"
                    show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.zone')" prop="zone">
                <el-select v-model="data.form.zones" multiple value-key="id">
                    <el-option v-for="item in zones" :value="item" :key="item.id" :label="item.name">
                        <el-space>
                            <span style="float: left;">{{ item.name }}</span>
                            <span style="float: left; color: var(--el-text-color-secondary); font-size: 13px;">{{
                                item.description }}</span>
                        </el-space>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.pickupZones')" prop="pickup_zones">
                <el-select v-model="data.form.pickup_zones" multiple value-key="id">
                    <el-option v-for="item in zones" :value="item" :key="item.id" :label="item.name">
                        <el-space>
                            <span style="float: left;">{{ item.name }}</span>
                            <span style="float: left; color: var(--el-text-color-secondary); font-size: 13px;">{{
                                item.description }}</span>
                        </el-space>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.startAt')" prop="start_at">
                <el-space>
                    <el-time-select style="min-width:  200px;" v-model="data.form.start_at" :max-time="data.form.end_at"
                        start="08:30" step="00:05" end="23:55" format="HH:mm:ss" />
                    ~
                    <el-time-select style="min-width:  200px;" v-model="data.form.end_at" :min-time="data.form.start_at"
                        start="08:30" step="00:05" end="23:55" format="HH:mm:ss" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.uploadAt')" prop="upload_at">
                <div style="display: flex; flex-flow: row nowrap; align-items: center;">
                    <el-time-select v-model="data.form.upload_at" start="00:00" end="23:30" step="00:10"
                        format="HH:mm:ss" />
                    <div style="width: 12px;"></div>
                    <el-input-number v-model="data.form.upload_at_offset" controls-position="right" :max="0"
                        @change="handleFeeChange" style="min-width: 100px;" />
                    <div style="width: 24px;"></div>
                    {{
                        availableUntil([data.form.upload_at, data.form.upload_at_offset]) >= 0
                            ? data.form.upload_at
                            : `${String(
                                Math.floor(
                                    availableUntil([data.form.upload_at, data.form.upload_at_offset]) / 1) + 24)}:${String(
                                        Math.abs(availableUntil([data.form.upload_at, data.form.upload_at_offset]) % 1 *
                                            60).toFixed().padStart(2, '0'))}:00`
                    }}
                    <div style="width: 12px;"></div>
                    <el-tag type="primary" plain
                        v-if="isAvailableUntilToday([data.form.upload_at, data.form.upload_at_offset])">当天</el-tag>
                    <el-tag type="warning" plain v-else>前一天</el-tag>
                </div>
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.availableUntil')" prop="available_until">
                <div style="display: flex; flex-flow: row nowrap; align-items: center;">
                    <el-time-select v-model="data.form.available_until_base" start="00:00" end="23:30" step="00:30"
                        format="HH:mm:ss" />
                    <div style="width: 12px;"></div>
                    <el-input-number v-model="data.form.available_until_hours_offset" controls-position="right" :max="0"
                        @change="handleFeeChange" style="min-width: 100px;" />
                    <div style="width: 24px;"></div>

                    {{ availableUntil([data.form.available_until_base,
                    data.form.available_until_hours_offset]) >= 0 ? data.form.available_until_base :
                        `${String(Math.floor(availableUntil([data.form.available_until_base,
                        data.form.available_until_hours_offset]) / 1)
                            + 24)}:${String(Math.abs(availableUntil([data.form.available_until_base,
                            data.form.available_until_hours_offset]) % 1 * 60).toFixed().padStart(2, '0'))}:00` }}
                    <div style="width: 12px;"></div>

                    <el-tag type="primary" plain
                        v-if="isAvailableUntilToday([data.form.available_until_base, data.form.available_until_hours_offset])">当天</el-tag>
                    <el-tag type="warning" plain v-else>前一天</el-tag>
                </div>

            </el-form-item>
            <el-form-item :label="$t('user.fields.type')" prop="user_type">
                <el-space>
                    <el-select v-model="data.form.user_type" style="min-width:200px;">
                        <el-option v-for="item in userTypes" :value="item" :key="item"
                            :label="$t(`user.selection.userType.${item}`)">
                            {{ $t(`user.selection.userType.${item}`) }}
                        </el-option>
                    </el-select>
                    <el-button text @click="data.form.user_type = null" type="primary">
                        {{ $t('operations.clear') }}
                    </el-button>
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('user.fields.group')" prop="user_group">
                <el-space>
                    <el-select v-model="data.form.user_group_id" style="min-width:200px;">
                        <el-option v-for="item in userGroups" :value="item.id" :key="item.id" :label="item.name" />
                    </el-select>
                    <el-button text @click="data.form.user_group_id = null" type="primary">
                        {{ $t('operations.clear') }}
                    </el-button>
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('fields.weekdays')" prop="weekdays">
                <el-checkbox-group v-model="data.form.weekdays">
                    <el-checkbox v-for="item in weekdays" :key="item" :label="item">
                        {{ $t(`selection.weekdayShort.${item}`) }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="$t('fields.holidays')" prop="holidays" v-if="holidays.length">
                <el-checkbox-group v-model="data.form.holidays">
                    <el-checkbox v-for="item in holidays" :key="item.id" :label="item.id">
                        {{ item.name }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.fee')" prop="fee">
                <el-input-number v-model="feeTemp" :min="0" :step="0.1" :precision="2" controls-position="right"
                    :placeholder="$t('placeholder', { field: $t('delivery.services.fields.fee') })"
                    @change="handleFeeChange" />
            </el-form-item>

            <el-form-item :label="$t('fields.isAvailable')" prop="priority">
                <el-switch v-model="data.form.is_available" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
    .zone-description {
        color: #999;
        font-size: 0.9em;
    }
</style>
