/**
 * 性能监控工具
 * 用于跟踪应用性能指标和监控潜在问题
 */

const DEV_MODE = process.env.NODE_ENV === 'development'

export class PerformanceMonitor {
    constructor() {
        this.metrics = {
            renders: {},      // 组件渲染次数
            renderTimes: {},  // 组件渲染耗时
            events: {},       // 事件触发次数
            memory: [],       // 内存使用情况
            longTasks: [],    // 长任务
            queries: []       // 查询性能
        }
    
        this.isRecording = false
        this.observers = []
        this.measureId = 0
    }
  
    // 开始记录性能数据
    start() {
        if (this.isRecording || !DEV_MODE) return
    
        this.isRecording = true
        this.startTime = performance.now()
    
        // 初始化性能观察者
        this.initObservers()
    
        // 定期记录内存使用情况
        this.memoryInterval = setInterval(() => {
            this.recordMemoryUsage()
        }, 5000)
    
        console.log('性能监控已启动')
        return this
    }
  
    // 停止记录性能数据
    stop() {
        if (!this.isRecording) return
    
        // 清除观察者
        this.observers.forEach(observer => observer.disconnect())
        this.observers = []
    
        // 清除内存监控定时器
        if (this.memoryInterval) {
            clearInterval(this.memoryInterval)
        }
    
        this.isRecording = false
        console.log('性能监控已停止')
        return this
    }
  
    // 初始化性能观察者
    initObservers() {
    // 如果浏览器支持PerformanceObserver
        if (typeof PerformanceObserver !== 'undefined') {
            // 监控长任务
            const longTaskObserver = new PerformanceObserver(list => {
                list.getEntries().forEach(entry => {
                    this.metrics.longTasks.push({
                        duration: entry.duration,
                        startTime: entry.startTime,
                        name: entry.name
                    })
          
                    // 仅在长任务超过100ms时发出警告
                    if (entry.duration > 100) {
                        console.warn(`检测到长任务: ${entry.duration.toFixed(2)}ms`, entry)
                    }
                })
            })
      
            try {
                longTaskObserver.observe({ entryTypes: ['longtask'] })
                this.observers.push(longTaskObserver)
            } catch (e) {
                console.log('不支持长任务监控', e)
            }
        }
    }
  
    // 记录内存使用情况
    recordMemoryUsage() {
        if (window.performance && window.performance.memory) {
            const memory = window.performance.memory
            const usage = {
                timestamp: Date.now(),
                used: memory.usedJSHeapSize / (1024 * 1024),
                total: memory.totalJSHeapSize / (1024 * 1024),
                limit: memory.jsHeapSizeLimit / (1024 * 1024)
            }
      
            this.metrics.memory.push(usage)
      
            // 如果内存使用超过80%，发出警告
            if (usage.used / usage.limit > 0.8) {
                console.warn(`内存使用率高: ${(usage.used / usage.limit * 100).toFixed(2)}%`, usage)
            }
        }
    }
  
    // 记录组件渲染
    recordRender(componentName) {
        if (!this.isRecording) return () => {}
    
        // 初始化组件记录
        if (!this.metrics.renders[componentName]) {
            this.metrics.renders[componentName] = 0
            this.metrics.renderTimes[componentName] = []
        }
    
        // 增加渲染计数
        this.metrics.renders[componentName]++
    
        // 记录开始时间
        const startTime = performance.now()
    
        // 返回完成函数
        return () => {
            const endTime = performance.now()
            const duration = endTime - startTime
      
            // 记录渲染时间
            this.metrics.renderTimes[componentName].push(duration)
      
            // 如果渲染时间过长，发出警告
            if (duration > 16.67) { // 60fps = 16.67ms per frame
                console.warn(`组件渲染时间过长: ${componentName} - ${duration.toFixed(2)}ms`)
            }
        }
    }
  
    // 记录事件触发
    recordEvent(eventName) {
        if (!this.isRecording) return
    
        if (!this.metrics.events[eventName]) {
            this.metrics.events[eventName] = {
                count: 0,
                timestamps: []
            }
        }
    
        this.metrics.events[eventName].count++
        this.metrics.events[eventName].timestamps.push(Date.now())
    
        // 检测事件风暴 - 如果1秒内同一事件超过10次
        const recentEvents = this.metrics.events[eventName].timestamps.filter(
            ts => (Date.now() - ts) < 1000
        )
    
        if (recentEvents.length > 10) {
            console.warn(`事件风暴检测: ${eventName} 在1秒内触发 ${recentEvents.length} 次`)
        }
    }
  
    // 测量函数执行时间
    measureFn(fn, name) {
        if (!this.isRecording) return fn()
    
        const startTime = performance.now()
        const result = fn()
        const endTime = performance.now()
    
        console.log(`函数[${name || 'anonymous'}]执行耗时: ${(endTime - startTime).toFixed(2)}ms`)
    
        return result
    }
  
    // 开始测量
    startMeasure(label) {
        if (!this.isRecording) return -1
    
        const id = this.measureId++
        performance.mark(`${label}-start-${id}`)
        return id
    }
  
    // 结束测量
    endMeasure(label, id) {
        if (!this.isRecording || id < 0) return 0
    
        performance.mark(`${label}-end-${id}`)
        performance.measure(
            `${label}-${id}`,
            `${label}-start-${id}`,
            `${label}-end-${id}`
        )
    
        const entries = performance.getEntriesByName(`${label}-${id}`, 'measure')
        const duration = entries[0]?.duration || 0
    
        // 清理
        performance.clearMarks(`${label}-start-${id}`)
        performance.clearMarks(`${label}-end-${id}`)
        performance.clearMeasures(`${label}-${id}`)
    
        return duration
    }
  
    // 获取性能报告
    getReport() {
        if (!this.isRecording) {
            return 'Performance monitor is not recording'
        }
    
        // 计算平均渲染时间
        const avgRenderTimes = {}
        for (const component in this.metrics.renderTimes) {
            const times = this.metrics.renderTimes[component]
            if (times.length > 0) {
                const sum = times.reduce((acc, time) => acc + time, 0)
                avgRenderTimes[component] = sum / times.length
            }
        }
    
        // 计算事件频率
        const eventRates = {}
        for (const event in this.metrics.events) {
            const timestamps = this.metrics.events[event].timestamps
            // 最近1分钟的事件数
            const recentCount = timestamps.filter(ts => (Date.now() - ts) < 60000).length
            eventRates[event] = recentCount / 60 // 每秒事件数
        }
    
        // 构建报告
        return {
            duration: (performance.now() - this.startTime) / 1000,
            components: {
                renderCounts: this.metrics.renders,
                avgRenderTimes
            },
            events: {
                counts: Object.fromEntries(
                    Object.entries(this.metrics.events).map(([k, v]) => [k, v.count])
                ),
                rates: eventRates
            },
            memory: this.metrics.memory.length > 0 
                ? this.metrics.memory[this.metrics.memory.length - 1]
                : null,
            longTasks: {
                count: this.metrics.longTasks.length,
                avgDuration: this.metrics.longTasks.length > 0
                    ? this.metrics.longTasks.reduce((acc, task) => acc + task.duration, 0) / this.metrics.longTasks.length
                    : 0
            }
        }
    }
  
    // 输出HTML格式的报告
    printHTMLReport() {
        const report = this.getReport()
        if (typeof report === 'string') {
            console.log(report)
            return
        }
    
        console.log('%c性能报告', 'font-size: 16px; font-weight: bold;')
        console.log(`总运行时间: ${report.duration.toFixed(2)}秒`)
    
        console.log('%c组件渲染', 'font-weight: bold;')
        for (const component in report.components.renderCounts) {
            console.log(
                `%c${component}%c - ${report.components.renderCounts[component]}次渲染, ` +
        `平均${report.components.avgRenderTimes[component]?.toFixed(2) || 0}ms/次`,
                'color: blue;',
                'color: black;'
            )
        }
    
        console.log('%c事件', 'font-weight: bold;')
        for (const event in report.events.counts) {
            console.log(
                `%c${event}%c - ${report.events.counts[event]}次触发, ` +
        `频率: ${report.events.rates[event].toFixed(2)}次/秒`,
                'color: green;',
                'color: black;'
            )
        }
    
        if (report.memory) {
            console.log('%c内存', 'font-weight: bold;')
            console.log(
                `已用: ${report.memory.used.toFixed(2)}MB / ` +
        `总计: ${report.memory.total.toFixed(2)}MB / ` +
        `限制: ${report.memory.limit.toFixed(2)}MB`
            )
        }
    
        console.log('%c长任务', 'font-weight: bold;')
        console.log(
            `数量: ${report.longTasks.count}, ` +
      `平均持续时间: ${report.longTasks.avgDuration.toFixed(2)}ms`
        )
    }
}

// 创建单例
export const performanceMonitor = new PerformanceMonitor()

// 开发环境自动启动
if (DEV_MODE) {
    performanceMonitor.start()
  
    // 在全局对象上暴露
    window.$perf = performanceMonitor
  
    // 添加控制台命令
    console.perf = {
        report: () => performanceMonitor.printHTMLReport(),
        stop: () => performanceMonitor.stop(),
        start: () => performanceMonitor.start()
    }
} 