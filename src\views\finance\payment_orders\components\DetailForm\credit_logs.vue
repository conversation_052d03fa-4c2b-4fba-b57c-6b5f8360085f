<script setup>
import { usePagination } from '@/utils/composables'
import { getPaymentCreditLogs } from '@/api/modules/payment';
import { currencyFormatter } from '@/utils/formatter'

const { pagination, getParams, onCurrentChange } = usePagination()

const props = defineProps({
    orderId: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
    dataList: [],
})

const logStyles = {
    consumed: {
        type: 'primary'
    },
    returned: {
        type: 'danger',
    },
    promoted: {
        type: 'warning',
    },
    directed: {
        type: 'warning',
        hollow: true
    },
}

defineExpose({
    reload() {
        getDataList()
    }
})


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams({ paymentOrderId: props.orderId })
    getPaymentCreditLogs(params).then(res => {
        pagination.value.total = res.data.total
        data.value.dataList = res.data.log_list
        data.value.loading = false
    })
}

function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

</script>
<template>
    <page-main :title="$t('finance.crLog.title')">
        <el-timeline v-if="data.dataList.length">
            <el-timeline-item v-for="(item, index) in data.dataList" :key="index" :timestamp="item.created_at"
                :type="logStyles[item.type]?.type" :hollow="logStyles[item.type]?.hollow" placement="top">
                <el-space direction="vertical" alignment="start" size="large" class="logs">
                    <el-space>
                        <el-tag :type="logStyles[item.type]?.type" size="small">
                            {{ $t(`finance.crLog.selections.type.${item.type}`) }}
                        </el-tag>
                        <span class="operator">{{ item.operator }}</span>
                    </el-space>
                    <el-descriptions :column="1" v-if="item.record">
                        <el-descriptions-item :label="$t('finance.crLog.fields.amount')" label-align="right"
                            label-class-name="log-label">
                            <span>{{ currencyFormatter(_, _, item.delta) }}</span>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-space>
            </el-timeline-item>
        </el-timeline>
        <el-empty v-else :description="$t('noData')" />
        <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
            :layout="pagination.layoutM" :hide-on-single-page="true" class="pagination" @current-change="currentChange" />
    </page-main>
</template>
