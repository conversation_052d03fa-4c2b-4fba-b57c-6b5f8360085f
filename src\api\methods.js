/*
@author: Tchiang<PERSON>, <EMAIL>
*/
import axios from '@/http'

const baseUrl = '/api/management/'

export const GET = (url, params) => {
    return axios({
        url: baseUrl + url,
        method: 'get',
        data: params
    })
}

export const DL = (url, params) => {
    return axios({
        url: baseUrl + url,
        method: 'get',
        data: params,
        config: { responseType: 'document' }
    })
}

export const POST = (url, params) => {
    return axios({
        url: baseUrl + url,
        method: 'post',
        data: params
    })
}


export const PUT = (url, params) => {
    return axios({
        url: baseUrl + url,
        method: 'put',
        data: params
    })
}
export const PAT = (url, params) => {
    return axios({
        url: baseUrl + url,
        method: 'patch',
        data: params
    })
}

export const DEL = (url, params) => {
    return axios({
        url: baseUrl + url,
        method: 'delete',
        data: params
    })

}

export const POSTFormData = (url, params) => {

    return axios({
        url: baseUrl + url,
        method: 'postForm',
        data: params,
    })
}


export const PUTFormData = (url, params) => {

    return axios({
        url: baseUrl + url,
        method: 'putForm',
        data: params,
    })
}




export const UPL = (url, params, data) => {
    let config = {
        headers: { 'content-type': 'multipart/form-data' },
    }
    return axios({
        url: baseUrl + url,
        method: 'post',
        data: { ...params, ...data },
        config
    })
}
