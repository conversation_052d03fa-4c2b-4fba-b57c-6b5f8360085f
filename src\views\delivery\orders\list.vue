<script setup name="DeliveryOrdersList">
    import { DocumentCopy, Watch, DArrowRight, Upload, Download, Right, Select } from '@element-plus/icons-vue'
    import eventBus from '@/utils/eventBus'
    import { usePagination } from '@/utils/composables'

    import { getDeliveryOrders, alterDeliveryOrdersStatus, processDeliveryOrder, process2DeliveryOrder, processPickupOrders, processDeliveryOrders } from '@/api/modules/delivery'
    import { currencyFormatter, orderNoFormatter, phoneNumberFormatter } from '@/utils/formatter'
    import { useI18n } from 'vue-i18n'
    import { orderStatusStyles } from '@/utils/constants'
    import { throttle } from '@/utils'
    import OrderAddressDialog from '@/views/components/OrderAddressDialog/index.vue'
    import { completeOrders } from '@/api/modules/dispatcher'
    import { useClipboard } from '@vueuse/core'

    const { text, copy, copied, isSupported } = useClipboard()

    const { t } = useI18n()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        formModeProps: {
            visible: false,
            id: ''
        },
        search: {
            no__icontains: null,
            user__name__icontains: null,
            sender__icontains: null,
            receiver__icontains: null,
            total__gte: 0,
            total__lte: null,
            status__in: ['inQueue', 'processing'],
            created_at__lte: null,
            created_at__gte: null,
            updated_at__lte: null,
            updated_at__gte: null,
            expected_pickup_start_at__lte: null,
            expected_pickup_start_at__gte: null,
            expected_delivery_start_at__lte: null,
            expected_delivery_start_at__gte: null

        },
        batch: {
            selectionDataList: []
        },
        dataList: []
    })

    watch(copied, (val) => {
        val && ElMessage.success(`${t('messages.copied')}：${text.value}`)
    })



    // Filters
    const searchBarCollapsed = ref(0)
    function resetFilters() {
        data.value.search =
        {
            no__icontains: null,
            user__name__icontains: null,
            sender__icontains: null,
            receiver__icontains: null,
            total__gte: 0,
            total__lte: null,
            status__in: ['inQueue', 'processing'],
            created_at__lte: null,
            created_at__gte: null,
            updated_at__lte: null,
            updated_at__gte: null,
            expected_pickup_start_at__lte: null,
            expected_pickup_start_at__gte: null,
            expected_delivery_start_at__lte: null,
            expected_delivery_start_at__gte: null

        }
        onSearch()
    }
    const status = Object.keys(orderStatusStyles)
    function onAllStatus(val) {
        data.value.search.status__in = val ? status : []
    }

    function onSearchStatusChanged(val) {
        const checkedStatusCount = val.length
        allStatus.value = checkedStatusCount === status.length
        onSearch();

    }

    const allStatus = ref(false)
    const partialStatus = computed(() => data.value.search.status__in?.length && data.value.search.status__in.length < status.length)


    onMounted(() => {
        getDataList()
        eventBus.on('get-data-list', () => {
            getDataList()
        })
    })

    onBeforeUnmount(() => {
        eventBus.off('get-data-list')
    })

    const onSearch = throttle(() => {
        currentChange();
    }, 800)

    // Api
    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getDeliveryOrders(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.order_list
            pagination.value.total = res.data.total
        })
    }

    function alterStatus(row, status) {
        let params = { id: row.id, status: status }
        alterDeliveryOrdersStatus(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }

    function batchAlterStatus(status) {
        const ids = data.value.batch.selectionDataList.map(o => o.id)

        let params = { id: ids, status: status }
        alterDeliveryOrdersStatus(params).then(res => {
            if (res.data.errCode == 365) {
                ElMessage.success(t('messages.alterStatusNum', { num: res.data.num }))
                getDataList()
            }
        })
    }

    function onProcess(row) {
        let params = { ids: [row.id] }
        processDeliveryOrder(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }

    function onBatchProcessPickups() {
        const ids = data.value.batch.selectionDataList.map(o => o.id)
        let params = { ids: ids, status: status }
        processPickupOrders(params).then(res => {
        })
    }

    function onBatchProcessDeliveries() {
        const ids = data.value.batch.selectionDataList.map(o => o.id)
        let params = { ids: ids, status: status }
        processDeliveryOrders(params).then(res => {
        })
    }

    function onCompleteDispatchedOrders(part) {
        const ids = data.value.batch.selectionDataList.map(o => o.id)
        let params = { ids: ids, part: part }
        completeOrders(params).then(res => {
        })

    }


    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }


    function onEdit(row) {
        router.push({
            name: 'deliveryOrderDetail',
            query: {
                id: row.id
            }
        })
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.status === 'closed') {
            return 'not-available-row'
        } else {
            return ''
        }
    }

    // Dialogs
    const chosenRow = ref({})
    const userDialogVisible = ref(false)
    const senderDialogVisible = ref(false)
    const receiverDialogVisible = ref(false)

    function showUser(row) {
        chosenRow.value = row
        userDialogVisible.value = true
    }

    function showSender(row) {
        chosenRow.value = row
        senderDialogVisible.value = true
    }
    function showReceiver(row) {
        chosenRow.value = row
        receiverDialogVisible.value = true
    }

</script>

<template>
    <div>
        <page-header :title="t('delivery.orders.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search">
                            <el-row :gutter="50">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.no')">
                                        <el-input v-model="data.search.no__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.no') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="onSearch" @clear="onSearch" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="onSearch" @clear="onSearch" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.sender')">
                                        <el-input v-model="data.search.sender__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.sender') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="onSearch" @clear="onSearch" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.receiver')">
                                        <el-input v-model="data.search.receiver__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.receiver') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="onSearch" @clear="onSearch" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item :label="$t('fields.status')">
                                        <el-space size="large">
                                            <el-checkbox @change="onAllStatus" v-model="allStatus"
                                                :indeterminate="partialStatus">
                                                {{ $t('all') }}
                                            </el-checkbox>
                                            <el-checkbox-group v-model="data.search.status__in"
                                                @change="onSearchStatusChanged">
                                                <el-checkbox v-for="item of status" :label="item">
                                                    {{ $t('delivery.orders.selections.status.' + item) }}
                                                </el-checkbox>
                                            </el-checkbox-group>
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('fields.createdAtMin')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="onSearch" @clear="onSearch"
                                                @change="onSearch" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('fields.createdAtMax')" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="onSearch" @clear="onSearch"
                                                @change="onSearch" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('delivery.orders.fields.expectedPickupTime')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.expected_pickup_start_at__gte"
                                                type="datetime" :placeholder="$t('fields.from')" clearable
                                                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                                                @keydown.enter="onSearch" @clear="onSearch" @change="onSearch" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.expected_pickup_start_at__lte"
                                                type="datetime" :placeholder="$t('fields.to')" clearable
                                                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                                                @keydown.enter="onSearch" @clear="onSearch" @change="onSearch" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('delivery.orders.fields.expectedDeliveryTime')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.expected_delivery_start_at__gte"
                                                type="datetime" :placeholder="$t('fields.from')" clearable
                                                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                                                @keydown.enter="onSearch" @clear="onSearch" @change="onSearch" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.expected_delivery_start_at__lte"
                                                type="datetime" :placeholder="$t('fields.to')" clearable
                                                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                                                @keydown.enter="onSearch" @clear="onSearch" @change="onSearch" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="onSearch">

                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                <el-button-group>
                    <!-- <el-button size="default" type="danger" plain @click="batchAlterStatus('closed')">
                        {{$t('delivery.orders.operations.close') }}
                    </el-button> -->
                    <el-button size="default" type="warning" plain @click="batchAlterStatus('pending')">
                        {{ $t('delivery.orders.operations.pend') }}
                    </el-button>
                    <el-button size="default" type="warning" plain @click="batchAlterStatus('inQueue')">
                        {{ $t('delivery.orders.operations.queue') }}
                    </el-button>
                    <el-button size="default" type="warning" @click="onBatchProcessPickups">
                        {{ $t('delivery.orders.operations.process') + ' P' }}
                    </el-button>
                    <el-button size="default" type="success" plain @click="batchAlterStatus('pickedUp')">
                        {{ $t('delivery.orders.operations.pickup') }}
                    </el-button>
                    <el-button size="default" type="warning" @click="onBatchProcessDeliveries">
                        {{ $t('delivery.orders.operations.process') + 'D' }}
                    </el-button>

                    <el-button size="default" type="success" plain @click="batchAlterStatus('transporting')">
                        {{ $t('delivery.orders.operations.transport') }}
                    </el-button>
                    <el-button size="default" type="success" @click="batchAlterStatus('delivered')">
                        {{ $t('delivery.orders.operations.deliver') }}
                    </el-button>
                    <el-button size="default" type="primary" @click="batchAlterStatus('completed')">
                        {{ $t('delivery.orders.operations.complete') }}
                    </el-button>
                </el-button-group>
                <el-button-group size="small">
                    <el-button size="small" type="success" text @click="onCompleteDispatchedOrders('pickup')">
                        {{ 'OR' + $t('delivery.orders.operations.pickup') }}
                    </el-button>
                    <el-button size="small" type="success" text @click="onCompleteDispatchedOrders('delivery')">
                        {{ 'OR' + $t('delivery.orders.operations.deliver') }}
                    </el-button>
                </el-button-group>
            </batch-action-bar>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column type="index" width="50" fixed />
                <el-table-column prop="no" :label="$t('delivery.orders.fields.no')" width="140">

                    <template #default="scope">
                        <el-space>
                            <el-tooltip placement="top">
                                <template #content>
                                    {{ $t('operations.copy') }}
                                </template>
                                <el-button @click.stop="copy(scope.row.no)" size="small" type="primary" plain circle
                                    :icon="DocumentCopy" />
                            </el-tooltip>
                            <div class="order-number">
                                {{ orderNoFormatter(_, _, scope.row.no) }}
                            </div>
                        </el-space>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="user_name" :label="$t('delivery.orders.fields.user')" width="150"
                    show-overflow-tooltip>
                    <template #default="scope">
                        <el-link type="primary" @click.stop="showUser(scope.row)">{{ scope.row.user_name }}</el-link>
                    </template>
                </el-table-column> -->
                <el-table-column prop="sender_name" :label="$t('delivery.orders.fields.sender')" show-overflow-tooltip>

                    <template #default="scope">
                        <el-link type="warning" @click.stop="showSender(scope.row)">
                            {{ scope.row.sender_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="receiver_name" :label="$t('delivery.orders.fields.receiver')"
                    show-overflow-tooltip>

                    <template #default="scope">
                        <el-link type="success" @click.stop="showReceiver(scope.row)">
                            {{ scope.row.receiver_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="status" :label="$t('delivery.orders.fields.status')" align="center" width="110">

                    <template #default="scope">
                        <el-tag :type="orderStatusStyles[scope.row.status]" round size="small">
                            {{ $t(`delivery.orders.selections.status.${scope.row.status}`) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="quantity" :label="$t('delivery.orders.fields.quantity')" align="center"
                    width="80" sortable="custom" />
                <el-table-column prop="paid_amount" :label="$t('delivery.orders.fields.paidAmount')" sortable="custom"
                    :formatter="currencyFormatter" class-name="order-number" align="right" />
                <!-- <el-table-column prop=" refunded" :label="$t('delivery.orders.fields.refunded')"
                    :formatter="currencyFormatter" align="center" width="90">
                    <template #default="scope">
                        <span v-if="scope.row.refunded" class="refunded-column">
                            ({{ currencyFormatter(_, _, scope.row.refunded) }})
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="tip" :label="$t('delivery.orders.fields.tip')" :formatter="currencyFormatter"
                    align="center" /> -->
                <el-table-column prop="dots" :label="$t('delivery.orders.fields.services')" width="75" align="center">

                    <template #default="scope">
                        <el-tooltip v-for="dot of scope.row.dots" :key="dot"
                            :content="$t('delivery.orders.fields.' + dot[1])">
                            <span :class="dot[0]">●&ThickSpace;</span>
                        </el-tooltip>
                        <template v-if="(scope.row.marks?.length ?? 0) > 0">
                            <el-tooltip v-for="mark of scope.row.marks" :key="mark" :content="mark.notes">
                                <span :style="{ 'color': mark.color }">●&ThickSpace;</span>
                            </el-tooltip>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column prop="expected_pickup_start_at"
                    :label="$t('delivery.orders.fields.expectedPickupTime')" sortable="custom" width="180">

                    <template #default="scope">
                        {{ scope.row.expected_pickup_start_at?.slice(0, -3) }} ~ {{ scope.row.expected_pickup_end_at }}
                    </template>
                </el-table-column>
                <el-table-column prop="expected_delivery_start_at"
                    :label="$t('delivery.orders.fields.expectedDeliveryTime')" sortable="custom" width="180">

                    <template #default="scope">
                        <span v-if="scope.row.expected_delivery_start_at">
                            {{ scope.row.expected_delivery_start_at?.slice(0, -3) }} ~
                            {{ scope.row.expected_delivery_end_at }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column align="right" fixed="right" width="160" :label="$t('fields.operations')">

                    <template #default="scope">
                        <el-space :size="2">
                            <!-- <el-tooltip :content="$t('delivery.orders.operations.close')" placement="top-start">
                                <span>
                                    <el-button type="danger" plain v-if="scope.row.status == 0" circle size="small"
                                        :icon="CloseBold" @click="alterStatus(scope.row, -1)" />
                                </span>
                            </el-tooltip> -->
                            <el-tooltip :content="$t('delivery.orders.operations.pend')" placement="top-start">
                                <span>
                                    <el-button type="warning" plain v-if="scope.row.can_pend" circle size="small"
                                        @click="alterStatus(scope.row, 'pending')">
                                        <svg-icon name="material-symbols:pause" />

                                    </el-button>
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('delivery.orders.operations.queue')" placement="top-start">
                                <span>
                                    <el-button type="warning" plain v-if="scope.row.can_queue" circle size="small"
                                        :icon="Watch" @click="alterStatus(scope.row, 'inQueue')" />
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('delivery.orders.operations.process')" placement="top-start">
                                <span>
                                    <el-button type="warning" v-if="scope.row.can_process" circle size="small"
                                        :icon="DArrowRight" @click="onProcess(scope.row)" />
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('delivery.orders.operations.pickup')" placement="top-start">
                                <span>
                                    <el-button type="success" plain v-if="scope.row.can_pickup" circle size="small"
                                        :icon="Upload" @click="alterStatus(scope.row, 'pickedUp')" />
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('delivery.orders.operations.transport')" placement="top-start">
                                <span>
                                    <el-button type="success" plain v-if="scope.row.can_transport" circle size="small"
                                        :icon="Right" @click="alterStatus(scope.row, 'transporting')" />
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('delivery.orders.operations.deliver')" placement="top-start">
                                <span>
                                    <el-button type="success" v-if="scope.row.can_delivery" circle size="small"
                                        :icon="Download" @click="alterStatus(scope.row, 'delivered')" />
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('delivery.orders.operations.complete')" placement="top-start">
                                <span>
                                    <el-button type="primary" v-if="scope.row.can_complete" circle size="small"
                                        :icon="Select" @click="alterStatus(scope.row, 'completed')" />
                                </span>
                            </el-tooltip>
                            <!-- <el-tooltip :content="$t('delivery.orders.operations.exception')" placement="top-start">
                                <span>
                                    <el-button type="danger" v-if="scope.row.status>0 && scope.row.status < 6" circle
                                        size="small" :icon="Flag"  @click="alterStatus(scope.row, 99)"/>
                                </span>
                        </el-tooltip> -->
                        </el-space>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>

        <!-- Dialogs -->
        <!-- User -->
        <el-dialog v-model="userDialogVisible" :title="$t('delivery.orders.dialogs.userDetail')" width="30%">
            <el-descriptions :title="chosenRow.user_name" :column="2">

                <template #extra>
                    <el-space>
                        <span class="desc-label">{{ $t('user.fields.credits') }}</span>
                        <span class="bold-content">{{ currencyFormatter(_, _, chosenRow.user.credits) }}</span>
                    </el-space>
                </template>
                <el-descriptions-item :label="$t(`user.fields.username`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.username }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.code`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.user_code }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.name`)" :span="2" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.name }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.companyName`)" :span="2" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.company_name }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.type`)" class-name="bold-content"
                    label-class-name="desc-label">
                    <span v-if="chosenRow.user.user_type != null">
                        {{ $t('user.selection.userType.' + chosenRow.user.user_type) }}
                    </span>
                    <span v-else> - </span>
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.group`)" class-name="bold-content"
                    label-class-name="desc-label">
                    <span v-if="chosenRow.user.user_group != null">
                        {{ chosenRow.user.user_group.name }}
                    </span>
                    <span v-else> {{ $t('user.selection.noGroup') }} </span>
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.phoneNumber`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ phoneNumberFormatter(chosenRow.user.phone_number) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.email`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.email }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.secondaryPhone`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ phoneNumberFormatter(chosenRow.user.sec_phone_number) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.extensionPhone`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.ext_phone_number }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.unitNo`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.unit_no }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.buzzerCode`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.buzzer_code }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.address`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.address }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.postalCode`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.postal_code }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.city`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.city }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.province`)" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.province }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.country`)" :span="2" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.country }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t(`user.fields.operationHours`)" :span="2" class-name="bold-content"
                    label-class-name="desc-label">
                    {{ chosenRow.user.open_at }} ~ {{ chosenRow.user.close_at }}
                </el-descriptions-item>
            </el-descriptions>
        </el-dialog>
        <!-- Sender -->
        <OrderAddressDialog v-model="senderDialogVisible" :address-info="chosenRow" address-type="sender" />
        <!-- Receiver -->
        <OrderAddressDialog v-model="receiverDialogVisible" :address-info="chosenRow" address-type="receiver" />

    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.75em;

        a {
            font-size: 1em;
            font-weight: normal;
        }

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: var(--g-unavailable-color);
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }

    .bold-content {
        font-weight: bolder;
    }

    .desc-label {
        font-size: 0.9em;
        color: #676767 !important;
    }

    .sender-dialog {

        header.el-dialog__header {
            background-color: #eebe77 !important;
            margin-right: 0;
            padding-right: 16px;

            .el-dialog__title,
            .el-dialog__close {
                color: white !important;
            }
        }
    }

    .receiver-dialog {

        header.el-dialog__header {
            background-color: #b3e19d !important;
            margin-right: 0;
            padding-right: 16px;

            .el-dialog__title,
            .el-dialog__close {
                color: white !important;
            }
        }
    }

    .success {
        color: #67c23a;
    }

    .info {
        color: #909399;
    }

    .warning {
        color: #e6a23c;
    }
</style>