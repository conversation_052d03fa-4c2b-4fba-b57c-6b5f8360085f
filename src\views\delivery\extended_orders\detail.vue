<script setup name="DeliveryExtendedOrdersDetail">
    import useSettingsStore from '@/store/modules/settings'
    import DetailForm from './components/DetailForm/index.vue'
    import eventBus from '@/utils/eventBus'
    import { useTabbar } from '@/utils/composables'

    const route = useRoute()
    const router = useRouter()

    const settingsStore = useSettingsStore()

    const formRef = ref()

    function onSubmit() {
        formRef.value.submit(() => {
            eventBus.emit('get-data-list')
            goBack()
        })
    }

    function onCancel() {
        goBack()
    }

    // 返回列表页
    function goBack() {
        if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
            useTabbar().close({ name: 'pagesExampleGeneralFormModeList' })
        } else {
            router.push({ name: 'pagesExampleGeneralFormModeList' })
        }
    }
</script>

<template>
    <div>
        <page-header
            :title="(route.query.id == null ? $t('operations.add'): $t('operations.add'))+默认模块">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{$t('operations.back')}}
            </el-button>
        </page-header>
        <page-main>
            <el-row>
                <el-col :md="24" :lg="16">
                    <DetailForm :id="route.query.id" ref="formRef" />
                </el-col>
            </el-row>
        </page-main>
        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onSubmit">{{$t('operations.submit')}}</el-button>
            <el-button size="large" @click="onCancel">{{$t('operations.cancel')}}</el-button>
        </fixed-action-bar>
    </div>
</template>

<style lang="scss" scoped>
    // scss
</style>
