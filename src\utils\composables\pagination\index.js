function usePagination() {
    const pagination = ref({
        page: 1,
        size: 10,
        total: 0,
        sizes: [10, 20, 50, 100],
        layout: "total, sizes, ->, prev, pager, next, jumper",
        layoutM: "total,  ->, prev, pager, next",
        ordering: null,
    });

    function getParams(params = {}) {
        const baseParams = {
            pageNo: pagination.value.page,
            size: pagination.value.size,
            ordering: pagination.value.ordering
        };
        Object.assign(baseParams, params);
        return baseParams;
    }

    function onSizeChange(size) {
        return new Promise((resolve) => {
            pagination.value.size = size;
            pagination.value.page = 1;
            resolve();
        });
    }

    function onCurrentChange(page) {
        return new Promise((resolve) => {
            pagination.value.page = page;
            resolve();
        });
    }
    function onSortChange({ prop, order }) {
        return new Promise(resolve => {
            if (order === "descending") {
                prop = `-${prop}`
            }
            pagination.value.ordering = order ? prop : null
            resolve()
        })
    }


    return {
        pagination,
        getParams,
        onSizeChange,
        onCurrentChange,
        onSortChange
    };
}

export default usePagination;
