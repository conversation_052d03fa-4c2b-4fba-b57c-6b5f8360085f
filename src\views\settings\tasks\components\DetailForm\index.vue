<script setup>
    import { getTasks, getTaskFunctions, addTask, updateTask, rescheduleTask } from '@/api/modules/tasks'
    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        },
        toReschedule: {
            type: Boolean,
            default: false
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        toReschedule: props.toReschedule,
        form: {
            id: props.id,
        },
        rules: {
            title: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ]
        }
    })

    const functions = ref([])

    const selectedModule = ref({})
    const selectedOperation = ref('')

    function disabledDate(time) {
        var date = new Date();
        return time.getTime() + 3600 * 1000 * 24 < date;
    }


    onMounted(() => {
        if (data.value.form.id != '') {
            getInfo()
        }

        getFunctions()
    })

    function getInfo() {
        data.value.loading = true
        getTasks({ id: data.value.form.id }).then(res => {
            data.value.loading = false
            data.value.form = res.data
            selectedModule.value = { module: res.data.command.split('.')[0], operations: [res.data.command.split('.')[1]] }
            selectedOperation.value = res.data.command.split('.')[1]
        })
    }

    function getFunctions() {
        getTaskFunctions().then(res => {
            functions.value = res.data
        })
    }


    defineExpose({
        submit(callback) {
            if (data.value.form.id == '') {
                formRef.value.validate(valid => {
                    if (valid) {
                        let params = JSON.parse(JSON.stringify(data.value.form));
                        params.module = selectedModule.value.module
                        params.operation = selectedOperation.value
                        addTask(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            } else {
                formRef.value.validate(valid => {
                    if (valid) {
                        let params = JSON.parse(JSON.stringify(data.value.form));
                        params.module = selectedModule.value.module
                        params.operation = selectedOperation.value

                        if (data.value.toReschedule) {
                            rescheduleTask(params).then((res) => {
                                if (res.data.errCode == 365) { callback && callback() }
                            })

                        } else {
                            updateTask(params).then((res) => {
                                if (res.data.errCode == 365) { callback && callback() }
                            })
                        }
                    }
                })
            }
        }
    })
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" :disabled="data.toReschedule" />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" :rows="5"
                    :disabled="data.toReschedule" />
            </el-form-item>
            <el-form-item :label="$t('settings.tasks.fields.module')" prop="command">
                <el-select v-model="selectedModule" value-key="module" placeholder="Select"
                    :disabled="data.form.id != ''">
                    <el-option v-for="item in functions" :key="item.id" :label="item.module" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('settings.tasks.fields.operation')" prop="command">
                <el-select v-model="selectedOperation" placeholder="Select" :disabled="data.form.id != ''">
                    <el-option v-for="item in selectedModule.operations" :key="item" :label="item" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('settings.tasks.fields.scheduledOn')" prop="scheduled_on_date">
                <el-date-picker v-model="data.form.scheduled_on_date" type="date" placeholder="Pick a day" :size="size"
                    :disabled-date="disabledDate" value-format="YYYY-MM-DD"
                    :disabled="!data.toReschedule && data.form.id != ''" />
            </el-form-item>
            <el-form-item :label="$t('settings.tasks.fields.scheduledAt')" prop="scheduled_at_time">
                <el-time-picker v-model="data.form.scheduled_at_time" arrow-control placeholder="Arbitrary time"
                    value-format="HH:mm" :disabled="!data.toReschedule && data.form.id != ''" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
    // scss
</style>
