<script setup name="HomeContents">
import { VideoCamera, RemoveFilled, Edit, Picture, Top, Bottom, Plus } from '@element-plus/icons-vue'

import { getWebContents, updateWebContents } from '@/api/modules/web_contents';
const form = ref({
    main_video: null,
    main_video_small: null,
    vip_price_text: { 'en-us': '', 'zh-cn': '', 'zh-tw': '', 'fr-fr': '', 'es-es': '' },
    app_download_text: { 'en-us': '', 'zh-cn': '', 'zh-tw': '', 'fr-fr': '', 'es-es': '' }
})

onMounted(() => {
    getInfo();
})

function getInfo() {
    getWebContents().then(res => {
        form.value = res.data
    });
}

function openNewTab(url) {
    window.open(url, '_blank', 'noreferrer');
}


const mainVideo = ref()
function onMainVideoChanged(file, fileList) {
    console.log(file)
    mainVideo.value = file
}

const mainSmallVideo = ref()
function onMainSmallVideoChanged(file, fileList) {
    mainSmallVideo.value = file
}

const bottomBg = ref()
function onBottomBGChanged(file, fileList) {
    bottomBg.value = file
}

const priceRowImage = ref()
function onPriceRowImageChanged(file, fileList) {
    priceRowImage.value = file
}

const priceRowVipImage = ref()
function onPriceRowVipImageChanged(file, fileList) {
    priceRowVipImage.value = file
}

function deleteCity(idx) {
    form.value.service_cities.splice(idx, 1)
}
function moveUpCity(idx) {
    form.value.service_cities.splice(idx - 1, 2, form.value.service_cities[idx], form.value.service_cities[idx - 1])
}
function moveDownCity(idx) {
    form.value.service_cities.splice(idx, 2, form.value.service_cities[idx + 1], form.value.service_cities[idx])
}

const cityDialogVisible = ref(null)
const cityIndexToUpdate = ref(null)
const cityToUpdate = ref(null)

function onUpdateCity(idx) {
    cityIndexToUpdate.value = idx;
    cityToUpdate.value = form.value.service_cities[idx];
    cityDialogVisible.value = true;
}

function onCreateCity() {
    cityDialogVisible.value = true;
}

function onConfirmUpdateCity() {
    if (cityIndexToUpdate.value) {
        form.value.service_cities.splice(cityIndexToUpdate.value, 1, cityToUpdate.value);
    } else {
        form.value.service_cities.push(cityToUpdate.value);
    }
    onCancelUpdateCity();

}

function onCancelUpdateCity() {
    cityIndexToUpdate.value = null;
    cityToUpdate.value = null;
    cityDialogVisible.value = false;
}

function onSubmit() {
    const formData = new FormData();
    for (const k in form.value) {
        if (!['main_video', 'main_video_small', 'bottom_image', 'price_row_image', 'price_row_vip_image'].includes(k)) {
            formData.append(k, form.value[k])
        }
        if (['app_download_text', 'vip_price_text'].includes(k)) {
            formData.append(k, JSON.stringify(form.value[k]))
        }
    }
    if (mainVideo.value) {
        formData.append('main_video', mainVideo.value.raw)
    }
    if (mainSmallVideo.value) {
        formData.append('main_video_small', mainSmallVideo.value.raw)
    }
    if (bottomBg.value) {
        formData.append('bottom_image', bottomBg.value.raw)
    }
    if (priceRowImage.value) {
        formData.append('price_row_image', priceRowImage.value.raw)
    }
    if (priceRowVipImage.value) {
        formData.append('price_row_vip_image', priceRowVipImage.value.raw)
    }

    updateWebContents(formData).then(res => {
        if (res.data.errCode == 365) {
            getInfo();
        }
    })

}

</script>
<template>
    <div>
        <page-main :title="$t('webContents.webContents.title')">
            <el-form :model="form" label-width="200px">
                <el-form-item :label="$t('webContents.webContents.fields.mainVideo')">
                    <div style="display: flex; flex-direction:row;">
                        <el-upload :auto-upload="false" @on-change="onMainVideoChanged">
                            <template #trigger>
                                <el-button type="primary">{{ $t('operations.update') }}</el-button>
                            </template>
                        </el-upload>
                        <div style="width: 12px;"></div>
                        <el-button type="success" v-if="form.main_video" @click="openNewTab(form.main_video)"
                            :icon="VideoCamera">{{ $t('operations.view') }}</el-button>
                    </div>
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.mainVideoSmall')">
                    <div style="display: flex; flex-direction:row;">
                        <el-upload :auto-upload="false" @on-change="onMainSmallVideoChanged">
                            <template #trigger>
                                <el-button type="primary">{{ $t('operations.update') }}</el-button>
                            </template>
                        </el-upload>
                        <div style="width: 12px;"></div>
                        <el-button type="success" v-if="form.main_video_small" @click="openNewTab(form.main_video_small)"
                            :icon="VideoCamera">{{ $t('operations.view') }}</el-button>
                    </div>
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.bottomBg')">
                    <div style="display: flex; flex-direction:row;">
                        <el-upload :auto-upload="false" @on-change="onBottomBGChanged">
                            <template #trigger>
                                <el-button type="primary">{{ $t('operations.update') }}</el-button>
                            </template>
                        </el-upload>
                        <div style="width: 12px;"></div>
                        <el-button type="success" v-if="form.bottom_image" @click="openNewTab(form.bottom_image)"
                            :icon="Picture">{{ $t('operations.view') }}</el-button>
                    </div>
                </el-form-item>
                <div style="display: flex; flex-direction: row; margin-bottom: 18px;">
                    <div
                        style="font-size: var(--el-form-label-font-size);color: var(--el-text-color-regular); width:200px; text-align: right;padding-right: 12px; height: 32px;">
                        {{ $t('webContents.webContents.fields.serviceCities') }}
                    </div>
                    <div style="display: block; ">
                        <div style="display: flex; flex-wrap: column nowrap; height: 32px;"
                            v-for="( city, index ) in  form.service_cities ">
                            <div style="margin-right: 20px;">{{ city }}</div>
                            <div>
                                <el-button type="danger" :icon="RemoveFilled" link @click="deleteCity(index)" />
                                <el-button type="warning" :icon="Edit" link @click="onUpdateCity(index)" />
                                <el-button type="primary" :icon="Top" link :disabled="index === 0"
                                    @click="moveUpCity(index)" />
                                <el-button type="primary" :icon="Bottom" link
                                    :disabled="index === form.service_cities.length - 1" @click="moveDownCity(index)" />
                            </div>
                        </div>
                        <div style="display: flex; flex-wrap: column nowrap; height: 32px;">
                            <el-button type="primary" :icon="Plus" size="small" @click="onCreateCity" />
                        </div>
                    </div>
                </div>
                <el-form-item :label="$t('webContents.webContents.fields.csEmail')">
                    <el-input v-model="form.cs_email" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.csPhone')">
                    <el-input v-model="form.cs_phone" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.csPhoneText')">
                    <el-input v-model="form.cs_phone_text" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.priceRowImage')">
                    <el-upload class="uploader" :show-file-list="false" :auto-upload="false"
                        :on-change="onPriceRowImageChanged">
                        <img v-if="form.price_row_image" :src="form.price_row_image" class="avatar" />
                        <el-icon v-else style=" color: #8c939d; width: 178px; height: 30px; text-align: center;">
                            <Plus />
                        </el-icon>
                    </el-upload> </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.priceRowVipImage')">
                    <el-upload class="uploader" :show-file-list="false" :auto-upload="false"
                        :on-change="onPriceRowVipImageChanged">
                        <img v-if="form.price_row_vip_image" :src="form.price_row_vip_image" class="avatar" />
                        <el-icon v-else style=" color: #8c939d; width: 178px; height: 30px; text-align: center;">
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.priceText')">
                    <el-input v-model="form.vip_price_text['en-us']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.priceTextCn')">
                    <el-input v-model="form.vip_price_text['zh-cn']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.priceTextTw')">
                    <el-input v-model="form.vip_price_text['zh-tw']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.priceTextFr')">
                    <el-input v-model="form.vip_price_text['fr-fr']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.priceTextEs')">
                    <el-input v-model="form.vip_price_text['es-es']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.downloadText')">
                    <el-input v-model="form.app_download_text['en-us']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.downloadTextCn')">
                    <el-input v-model="form.app_download_text['zh-cn']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.downloadTextTw')">
                    <el-input v-model="form.app_download_text['zh-tw']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.downloadTextFr')">
                    <el-input v-model="form.app_download_text['fr-fr']" type="textarea" />
                </el-form-item>
                <el-form-item :label="$t('webContents.webContents.fields.downloadTextEs')">
                    <el-input v-model="form.app_download_text['es-es']" type="textarea" />
                </el-form-item>
            </el-form>
        </page-main>
        <el-dialog v-model="cityDialogVisible" :title="cityIndexToUpdate ? $t('operations.update') : $t('operations.add')"
            width="30%" align-center :show-close="false">
            <el-input v-model="cityToUpdate" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelUpdateCity">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmUpdateCity">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onSubmit">{{ $t('operations.save') }}</el-button>
            <el-button size="large" @click="getInfo">{{ $t('operations.cancel') }}</el-button>
        </fixed-action-bar>
    </div>
</template>
<style lang="scss">
.uploader {

    .el-upload {
        border: 1px dashed var(--el-border-color);
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: var(--el-transition-duration-fast);
    }

    .avatar {
        min-width: 237;
        height: 180px;
        display: block;
    }

    .el-upload:hover {
        border-color: var(--el-color-primary);
    }

}
</style>
