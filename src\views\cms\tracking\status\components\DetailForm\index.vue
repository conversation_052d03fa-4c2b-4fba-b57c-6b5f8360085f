<script setup>
import { addTrackingStatus, getTrackingStatus, updateTrackingStatus } from '@/api/modules/tracking';
import status from '@/views/delivery/orders/status'
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const statusList = Object.keys(status)

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        status: '',
        descriptions: {
            'en-us': '',
            'zh-cn': '',
            'zh-tw': '',
            'fr-fr': '',
            'es-es': '',
        }
    },
    rules: {
        status: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        descriptions: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getTrackingStatus({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addTrackingStatus(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateTrackingStatus(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.status')" prop="status">
                <el-select v-model="data.form.status">
                    <el-option v-for="s in statusList" :label="$t(`delivery.orders.selections.status.${s}`)"
                        :value="s" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="descriptions">
                <el-input v-model="data.form.descriptions['en-us']" placeholder="请输入标题" type="textarea" />
            </el-form-item>
            <el-form-item :label="$t('fields.descZh')">
                <el-input v-model="data.form.descriptions['zh-cn']" placeholder="请输入标题" type="textarea" />
            </el-form-item>
            <el-form-item :label="$t('fields.descTw')">
                <el-input v-model="data.form.descriptions['zh-tw']" placeholder="请输入标题" type="textarea" />
            </el-form-item>
            <el-form-item :label="$t('fields.descFr')">
                <el-input v-model="data.form.descriptions['fr-fr']" placeholder="请输入标题" type="textarea" />
            </el-form-item>
            <el-form-item :label="$t('fields.descEs')">
                <el-input v-model="data.form.descriptions['es-es']" placeholder="请输入标题" type="textarea" />
            </el-form-item>

        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>