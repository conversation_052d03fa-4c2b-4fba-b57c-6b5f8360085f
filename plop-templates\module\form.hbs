<script setup>
    import api from '@/api'
    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            title: ''
        },
        rules: {
            title: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ]
        }
    })

    onMounted(() => {
        if (data.value.form.id != '') {
            getInfo()
        }
    })

    function getInfo() {
        data.value.loading = true
        api.get({ id: data.value.form.id }).then(res => {
            data.value.loading = false
            data.value.form = res.data
        })
    }

    defineExpose({
        submit(callback) {
            if (data.value.form.id == '') {
                formRef.value.validate(valid => {
                    if (valid) {
                        api.post(data.value.form).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            } else {
                formRef.value.validate(valid => {
                    if (valid) {
                        api.post(data.value.form).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            }
        }
    })
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item label="标题" prop="title">
                <el-input v-model="data.form.title" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
    // scss
</style>
