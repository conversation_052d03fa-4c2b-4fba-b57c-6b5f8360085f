<script setup name="DeliveryOrdersDetail">
import DetailIndex from './components/DetailForm/index.vue'
import eventBus from '@/utils/eventBus'
import { useTabbar } from '@/utils/composables'
import { useI18n } from 'vue-i18n'
import useSettingsStore from '@/store/modules/settings'
const settingsStore = useSettingsStore()

const { t } = useI18n()

const route = useRoute()
const router = useRouter()

// 返回列表页
function goBack() {
    eventBus.emit('get-data-list')
    if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
        useTabbar().close({
            name: route.query.redirect ? 'deliveryAdminOrderDetail' : 'deliveryOrdersList',
            query: {
                id: route.query.redirect
            }
        })
    } else {
        router.push({
            name: route.query.redirect ? 'deliveryAdminOrderDetail' : 'deliveryOrdersList',
            query: {
                id: route.query.redirect
            }
        })
    }

}
</script>

<template>
    <div>
        <page-header :title="t('delivery.orders.detail')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>
        <DetailIndex v-if="route.query.id" :id="route.query.id" />
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
