{"title": "Delivery", "services": {"packageType": "Package Types", "sizeWeight": "Sizes & Weights", "addedService": "Added Services", "packaging": "Packaging", "pickupTime": "Pickup Time", "deliveryTime": "Delivery Time", "servicePack": "Service Pack", "serviceItem": "Service Item", "fields": {"availableUntil": "Available until", "endAt": "End at", "fee": "Fee", "feeMax": "<PERSON><PERSON>", "feeMin": "<PERSON><PERSON>", "inDays": "+ Days", "inDaysMax": "+ Days Max", "inDaysMin": "+ Days Min", "pickupZones": "Pickup Zones", "startAt": "Start at", "uploadAt": "Upload at", "zone": "Zone", "zones": "Zones", "packs": "Packs", "items": "Items", "isRecommended": "Recommended?", "canBeCombined": "Combined?", "selectedDefault": "Selected Default", "banner": "Banner"}}, "addressBook": {"title": "Address Book", "fields": {"user": "User", "name": "Contact Name", "companyName": "Company Name", "phone": "Phone Number", "email": "Email", "secPhone": "Secondary Phone", "extPhone": "Phone Ext.", "unitNo": "Unit", "buzzerCode": "Buzzer", "address": "Address", "postalCode": "PC", "city": "City", "province": "Province", "country": "Country", "inDB": "In DB", "isDefault": "Is default"}}, "orders": {"title": "All Orders", "detail": "Order detail", "newOrder": "New Order", "fields": {"addedServices": "Added services", "addedServicesFee": "Added services fee", "addressFee": "Address fee", "addressInfo": "Address info", "adminNotes": "Admin notes", "completedAt": "Completed at", "deliveredAt": "Delivered at", "deliveredBy": "Delivered by", "deliveryFee": "Delivery fee", "deliveryOption": "Delivery option", "expectedDeliveryDate": "Expected delivery on", "expectedDeliveryTime": "Expected delivery at", "expectedPickupDate": "Expected pickup on", "expectedPickupTime": "Expected pickup at", "expressFee": "Express fee", "holidayFee": "Holiday fee", "holidayInfo": "Holiday info", "insurance": "Insurance policy", "insuranceFee": "Insurance fee", "insurancePolicy": "Insurance policy", "isExpress": "Express", "marks": "Marks", "no": "#", "notes": "Notes", "null": "", "packageType": "Package type", "packaging": "Packaging", "packagingFee": "Packaging fee", "paid": "Paid", "paidAmount": "<PERSON><PERSON>", "pcFee": "PC fee", "pcInfo": "PC info", "pickedUpAt": "Picked up at", "pickedUpBy": "Picked up by", "promotionalDeduction": "Promotional deduction", "promotionInfos": "Promotion infos", "promotions": "Promotions", "quantity": "Qty", "receiver": "Receiver", "receiverAddress": "Receiver address", "receiverBuzzerCode": "Receiver buzzer code", "receiverCity": "Receiver city", "receiverCompany": "Receiver company", "receiverEmail": "Receiver email", "receiverName": "Receiver name", "receiverPhone": "Receiver phone", "receiverPostalCode": "Receiver postal code", "receiverProvince": "Receiver province", "receiverUnitNo": "Receiver unit no.", "refunded": "Refunded", "return": "Return", "scheduledUploadAt": "Scheduled upload at", "scheduledPickupUploadAt": "Scheduled upload Pickup Order at", "scheduledDeliveryUploadAt": "Scheduled upload Delivery Order at", "sender": "Sender", "senderAddress": "Sender address", "senderBuzzerCode": "Sender buzzer code", "senderCity": "Sender city", "senderCompany": "Sender company", "senderEmail": "Sender email", "senderName": "Sender name", "senderPhone": "Sender phone", "senderPostalCode": "Sender postal code", "senderProvince": "Sender province", "senderUnitNo": "Sender unit no.", "services": "Services", "sizeFee": "Size & weight", "sizeWeight": "Size & weight", "status": "Status", "subtotal": "Subtotal", "tax": "Tax", "timeFee": "Time fee", "timeInfos": "Time infos", "tip": "Tip", "total": "Total", "typeFee": "Package type fee", "uploadedAt": "Uploaded at", "user": "User", "weekdayFee": "Weekday fee", "weekdayInfo": "Weekday info", "zoneFee": "Zone fee", "zoneInfos": "Zone infos"}, "selections": {"status": {"closed": "Closed", "created": "Created", "pending": "Pending", "inQueue": "In Queue", "processing": "Processing", "pickedUp": "Picked up", "sorting": "Sorting", "transporting": "Transporting", "delivered": "Delivered", "completed": "Completed", "exceptional": "Exceptional", "stillToPay": "Still to pay", "undefined": ""}, "extendedInfo": {"pickup": "from", "delivery": "to"}}, "dialogs": {"userDetail": "User Detail", "senderDetail": "Sender Detail", "receiverDetail": "Receiver Detail"}, "sections": {"user": "User Detail", "extendedInfo": "Extended infos", "feeInfo": "Fee infos", "paymentInfo": "Payment infos", "logs": "Order logs", "packages": "Packages"}, "operations": {"close": "Close", "complete": "Complete", "deliver": "Deliver", "exception": "Exception", "pickup": "Pick up", "printLabel": "Print Label", "process": "Process", "pend": "Pend", "queue": "Queue", "refund": "Refund", "refundToCredits": "Refund to credits", "transport": "Transport"}, "logs": {"created": "Created", "updated": "Updated", "deleted": "Deleted", "noted": "Admin Noted", "noteRemoved": "Note removed"}, "messages": {"totalBeenUpdated": "Please be acknowledged: Order total been updated, delta is"}}, "adminOrders": {"title": "Admin Orders", "detail": "Admin Order Detail", "fields": {"admin": "Administrator"}, "operations": {"generatePaymentOrder": "Generate payment order", "pay": "Pay Online"}, "sections": {"package": "Package", "createdBy": "Created by", "createdAt": "at"}, "messages": {"checkPackage": "Check this package", "noSenderName": "Please input sender name", "noSenderPhone": "Please input sender phone", "noSenderEmail": "Please input sender email", "noSenderAddress": "Please input sender address", "noPickupTime": "Please select pickup time", "noReceiverName": "Please input receiver name", "noReceiverPhone": "Please input receiver phone", "noReceiverEmail": "Please input receiver email", "noReceiverAddress": "Please input receiver address", "noDeliveryTime": "Please select delivery time", "noQuantity": "Quantity can't be null", "noPackageType": "Please select package type", "noSize": "Please select package size & weight", "optionalPhoneNumber": "Please check phone number format or leave it blank", "wrongPhoneFormat": "Please check phone number format", "wrongEmailFormat": "Please check email format"}}, "openOrders": {"title": "Open Orders"}, "settings": {"title": "Settings", "sections": {"baseFee": "Base Fee", "holidayFees": "Holiday Fees", "weekdayFees": "WeekDay Fees"}, "fields": {"deliveryFee": "Delivery fee", "taxRate": "Tax Rate", "maxDateRange": "Max Date Range", "userSelectionsAvailableGroups": "Default Selections Available Groups", "userSelectionsAvailableType": "Default Selections Available User type"}}, "distributionCenters": {"title": "Distribution Centers", "fields": {"volume": "Volume", "operationHours": "Operation Hours", "drivers": "Drivers", "handlers": "Handlers", "pickupTimes": "Pickup times", "deliveryTimes": "Delivery times"}}, "suppliers": {"title": "Suppliers", "fields": {"destinations": "Destinations"}}, "supplierStores": {"title": "Supplier Stores", "fields": {"packageTypes": "Package Types", "sizeWeights": "Sizes & Weights"}}, "worksheets": {"title": "Worksheets", "fields": {"sn": "S/N", "driver": "Driver", "startAt": "Start at", "endAt": "End at", "workingHours": "Working hours", "adjustmentHours": "Adjustment Hours", "speedometerStartAt": "Speedometer start at", "speedometerEndAt": "Speedometer end at", "totalTravel": "Total travel", "adjustmentTravelKm": "Adjustment Travel KM", "photos": "Photos", "mins": "mins", "startAtMin": "Start at min", "startAtMax": "Start at max", "endAtMin": "End at min", "endAtMax": "End at max"}}, "drivers": {"title": "Drivers", "fields": {"avatar": "Avatar", "birthday": "Birthday", "sin": "SIN", "baseHourlySalary": "Base Hourly Salary", "fuelAllowancePerLiter": "Fuel Allowance /KM", "sn": "S/N", "bucketName": "Bucket", "hstExempted": "HST Exempted", "hstNo": "HST No.", "bio": "Bio"}, "selection": {"status": {"suspended": "Suspended", "created": "Created", "valid": "<PERSON><PERSON>", "invalid": "Invalid"}}, "operation": {"validate": "Validate", "invalidate": "Invalidate", "suspend": "Suspend", "resetPassword": "Reset Password"}, "dialog": {"resetPassword": {"warning": "Reset password will reset driver's status.", "emptyPassword": "By leaving new password empty, system will generate a random one for you.", "noteDown": "This new password will only show on this time, please note that down and keep it safe."}}}, "extendedOrders": {"fields": {"extraFee": "Extra Fee", "serviceFee": "Service Fee", "paidAmount": "<PERSON><PERSON>", "paidAt": "Paid at", "channel": "Channel", "eta": "ETA", "carrier": "Carrier", "notPaid": "Not Paid"}, "operations": {"open": "Open"}}}