<script setup name="DeliveryWorksheetsList">
    import { Delete } from '@element-plus/icons-vue'
    import { usePagination } from '@/utils/composables'
    import { deleteWorksheet, getWorksheets } from '@/api/modules/staffs'
    import { currencyFormatter } from '@/utils/formatter'
    import { useI18n } from 'vue-i18n'


    const { t } = useI18n()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            employee__lastname__icontains: null,
            employee__firstname__icontains: null,
            start_at__lte: null,
            start_at__gte: null,
            end_at__lte: null,
            end_at__gte: null,
        },
        // 批量操作
        batch: {
            enable: false,
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            employee__lastname__icontains: null,
            employee__firstname__icontains: null,
            start_at__lte: null,
            start_at__gte: null,
            end_at__lte: null,
            end_at__gte: null

        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
    })


    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getWorksheets(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.sheet_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        router.push({
            name: 'deliveryWorksheetDetail',
        })
    }

    function onEdit(row) {
        router.push({
            name: 'deliveryWorksheetDetail',
            query: {
                id: row.id
            }
        })
    }

    function onDel(row) {
        ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.driver.name }), t('dialog.titles.confirmation')).then(() => {
            deleteWorksheet({ id: row.id }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (row.working_hours == null) {
            return 'highlighted-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-header :title="$t('delivery.worksheets.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.lastName')">
                                        <el-input v-model="data.search.employee__lastname__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.worksheets.fields.driver') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.firstname')">
                                        <el-input v-model="data.search.employee__firstname__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.worksheets.fields.driver') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.worksheets.fields.startAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.start_at__gte" type="date"
                                                :placeholder="$t('delivery.worksheets.fields.startAtMin')" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD" @clear="onSearch"
                                                @change="onSearch" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.start_at__lte" type="date"
                                                :placeholder="$t('delivery.worksheets.fields.startAtMax')" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD" @clear="onSearch"
                                                @change="onSearch" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.worksheets.fields.endAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.end_at__gte" type="date"
                                                :placeholder="$t('delivery.worksheets.fields.endAtMin')" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD" @clear="onSearch"
                                                @change="onSearch" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.end_at__lte" type="date"
                                                :placeholder="$t('delivery.worksheets.fields.endAtMax')" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD" @clear="onSearch"
                                                @change="onSearch" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">

                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">

                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column type="index" align="center" fixed width="50" />
                <el-table-column prop="employee" :label="$t('delivery.worksheets.fields.driver')" width="150"
                    show-overflow-tooltip />
                <el-table-column prop="sn" :label="$t('delivery.worksheets.fields.sn')" show-overflow-tooltip />
                <el-table-column prop="date" :label="$t('fields.date')" width="240">
                    <template #default="scope">
                        <el-text tag="b" size="small">{{ scope.row.date }}</el-text>&ThickSpace;
                        {{ scope.row.start_at }} ~ {{ scope.row.end_at }}
                    </template>
                </el-table-column>
                <el-table-column prop="adjustment_minutes" :label="$t('delivery.worksheets.fields.adjustmentHours')"
                    align="center" />
                <el-table-column prop="working_hours" :label="$t('delivery.worksheets.fields.workingHours')"
                    align="center" :formatter="currencyFormatter" />
                <el-table-column prop="speedometer_start_at"
                    :label="$t('delivery.worksheets.fields.speedometerStartAt')" align="center" width="110" />
                <el-table-column prop="speedometer_end_at" :label="$t('delivery.worksheets.fields.speedometerEndAt')"
                    align="center" width="110" />
                <el-table-column prop="adjustment_travel_km"
                    :label="$t('delivery.worksheets.fields.adjustmentTravelKm')" align="center" />
                <el-table-column prop="total_travel_km" :label="$t('delivery.worksheets.fields.totalTravel')"
                    align="center" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">

                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start"
                            v-if="scope.row.can_update">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .highlighted-row {
            font-weight: bolder;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>