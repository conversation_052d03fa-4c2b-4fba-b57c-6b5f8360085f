<!--
Author: <EMAIL>'
Date: '2023-06-03 16:38:35'
Project: 'FleetNowV3'
Path: 'src/views/messenger/cs_messages/components/TabContent/messages.vue'
File: 'messages.vue'
Version: '1.0.0'
-->

<script setup name="CSMessagesChatTab">
import { usePagination } from '@/utils/composables'
import { getCSMessages, readCSMessages, resendCSMessage } from '@/api/modules/messenger'
import { SuccessFilled, Promotion, WarnTriangleFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: null
    },
})

const emit = defineEmits(['unreadMessages'])

const data = ref({
    userId: props.id,
    messages: [],
    search: {}
})


defineExpose({
    reload() { getDataList(); }
})

onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            id: data.value.userId,
            filters: JSON.stringify(data.value.search),
        }
    )
    getCSMessages(params).then(res => {
        data.value.loading = false
        data.value.messages = res.data.message_list
        pagination.value.total = res.data.total
    })
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

function onUnread(messages) {
    ElMessageBox.confirm(t('dialog.messages.unreadMessages'), t('dialog.titles.confirmation')).then(() => {
        let params = { ids: messages.map(e => e.id), isRead: false }
        readCSMessages(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList();
                emit('unreadMessages');
            }
        })
    }).catch(() => { })
}

function onSend(message) {
    resendCSMessage({ id: message.id }).then(res => {
        if (res.data.errCode == 365) {
            getDataList();
        }
    })
}

</script>

<template>
    <div style="height:calc(100vh - 422px);">
        <div class="messages-box">
            <template v-for="msg in data.messages">
                <div class="message-item-title">
                    <el-space>
                        <el-text tag="b" :type="msg.admin_name ? 'warning' : 'success'">
                            {{ msg.admin_name ? msg.admin_name : msg.user_name }}
                        </el-text>
                        <el-tooltip content="标记为未处理" v-if="msg.type == 'csOfflineMessage' && msg.is_read">
                            <el-button size="small" text type="primary" :icon="SuccessFilled" @click="onUnread([msg])" />
                        </el-tooltip>
                        <el-tooltip content="发送" v-if="msg.admin_name != null && msg.status == 'created'">
                            <el-button size="small" text type="success" :icon="Promotion" @click="onSend(msg)" />
                        </el-tooltip>
                        <el-tooltip content="再次发送" v-else-if="msg.admin_name != null && msg.status == 'failed'">
                            <el-button size="small" text type="warning" :icon="WarnTriangleFilled" @click="onSend(msg)" />
                        </el-tooltip>
                    </el-space>
                    <el-text tag="sup" size="small">{{ msg.created_at }}</el-text>
                </div>
                <el-text tag="p">{{ msg.content }}</el-text>
            </template>
        </div>
        <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="20"
            :page-sizes="pagination.sizes" :hide-on-single-page="true" class="pagination" layout="prev, pager, next"
            @current-change="currentChange" />

    </div>
</template >

<style lang="css">
.messages-box {
    height: calc(100vh - 470px);
    position: relative;
    overflow-y: auto;
}

.el-pagination {
    position: absolute;
    margin-top: 20px;
    bottom: 0;
    right: 0;
}

.message-item-title {
    display: flex;
    flex-wrap: row nowrap;
    justify-content: space-between;
    margin-top: 20px;
}
</style>
