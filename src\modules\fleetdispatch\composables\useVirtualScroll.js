import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

/**
 * 虚拟滚动组合式函数
 * @param {Object} options 配置选项
 * @param {Array} options.items 完整数据列表
 * @param {Number} options.itemHeight 每项高度
 * @param {Number} options.visibleCount 可见项数量
 * @param {Number} options.buffer 缓冲区项数
 * @param {HTMLElement} options.container 容器元素
 * @returns {Object} 虚拟滚动状态和方法
 */
export function useVirtualScroll(options) {
    const defaultOptions = {
        itemHeight: 30,
        visibleCount: 10,
        buffer: 5
    }
  
    const config = { ...defaultOptions, ...options }
  
    const items = ref(config.items || [])
    const container = ref(config.container || null)
    const itemHeight = ref(config.itemHeight)
    const visibleCount = ref(config.visibleCount)
    const buffer = ref(config.buffer)
    const scrollTop = ref(0)
  
    // 计算可视区域内的数据
    const visibleItems = computed(() => {
        const startIndex = Math.floor(scrollTop.value / itemHeight.value) - buffer.value
        const start = Math.max(0, startIndex)
        const end = Math.min(start + visibleCount.value + buffer.value * 2, items.value.length)
    
        return items.value.slice(start, end)
    })
  
    // 计算总高度和偏移量
    const totalHeight = computed(() => items.value.length * itemHeight.value)
    const offsetY = computed(() => {
        const startIndex = Math.floor(scrollTop.value / itemHeight.value) - buffer.value
        return Math.max(0, startIndex) * itemHeight.value
    })
  
    // 滚动到指定索引
    const scrollToIndex = index => {
        if (!container.value) return
    
        const targetScrollTop = index * itemHeight.value
        container.value.scrollTop = targetScrollTop
    }
  
    // 处理滚动事件
    const handleScroll = event => {
        const target = event.target
        scrollTop.value = target.scrollTop
    }
  
    // 在组件挂载后设置容器和事件监听
    onMounted(() => {
        if (container.value) {
            container.value.addEventListener('scroll', handleScroll)
      
            // 计算可见区域能容纳的项目数
            const containerHeight = container.value.clientHeight
            visibleCount.value = Math.ceil(containerHeight / itemHeight.value) + 1
        }
    })
  
    // 在组件卸载前移除事件监听
    onUnmounted(() => {
        if (container.value) {
            container.value.removeEventListener('scroll', handleScroll)
        }
    })
  
    // 监听items变化，保持引用同步
    watch(() => config.items, newItems => {
        items.value = newItems || []
    }, { deep: false })
  
    return {
        visibleItems,
        totalHeight,
        offsetY,
        scrollToIndex
    }
} 