/*
* Author: <EMAIL>'
* Date: '2023-08-31 17:32:49'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/website/topics.js'
* File: 'topics.js'
* Version: '1.0.0'
*/



const Layout = () => import('@/layout/index.vue')

export default {
    path: '/website/topics',
    component: Layout,
    redirect: '/website/topics/list',
    name: 'websiteManagement',
    meta: {
        title: '文章管理',
        icon: 'ri:article-fill',
        auth: ['super'],
        i18n: 'route.website.topics.title'
    },
    children: [
        {
            path: 'categories/list',
            name: 'websiteTopicCategoryList',
            component: () => import('@/views/website_management/topic_categories/list.vue'),
            meta: {
                title: '文章目录',
                icon: 'quill:stack',
                i18n: 'route.website.topics.categories',
                activeMenu: '/website/topics/categories/list',
                auth: ['super'],

            }
        },
        {
            path: 'list',
            name: 'websiteTopicList',
            component: () => import('@/views/website_management/topics/list.vue'),
            meta: {
                title: '文章列表',
                icon: 'ri:article-line',
                i18n: 'route.website.topics.topics',
                activeMenu: '/website/topics/list',
                auth: ['super'],

            }
        },
        {
            path: 'detail',
            name: 'websiteTopicDetail',
            component: () => import('@/views/website_management/topics/detail.vue'),
            meta: {
                title: '文章详情',
                icon: 'icon-park-outline:transaction-order',
                i18n: 'route.website.topics.topics',
                activeMenu: '/website/topics/list',
                auth: ['super'],
                sidebar: false,
            }
        },
        {
            path: 'videos',
            name: 'websiteTopicVideos',
            component: () => import('@/views/website_management/videos/list.vue'),
            meta: {
                title: '文章视频',
                icon: 'icon-park-outline:transaction-order',
                i18n: 'route.website.topics.videos',
                activeMenu: '/website/topics/videos',
                auth: ['super'],
            }
        },
        {
            path: 'privacy-policies/list',
            name: 'websitePrivacyPolicyList',
            component: () => import('@/views/website_management/topics/privacy_policies.vue'),
            meta: {
                title: '隐私政策',
                icon: 'ooui:article-ltr',
                i18n: 'route.website.topics.privacyPolicies',
                activeMenu: '/website/topics/privacy-policies/list',
                auth: ['super'],

            }
        },
        {
            path: 'terms-and-conditions/list',
            name: 'websiteTermsConditionsList',
            component: () => import('@/views/website_management/topics/terms_conditions.vue'),
            meta: {
                title: '使用条款&协议',
                icon: 'ooui:article-rtl',
                i18n: 'route.website.topics.termsConditions',
                activeMenu: '/website/topics/terms-and-conditions/list',
                auth: ['super'],

            }
        },
        {
            path: 'about-us/list',
            name: 'websiteAboutUsList',
            component: () => import('@/views/website_management/topics/about_us.vue'),
            meta: {
                title: '关于我们',
                icon: 'ooui:article-rtl',
                i18n: 'route.website.topics.aboutUs',
                activeMenu: '/website/topics/about-us/list',
                auth: ['super'],
            }
        },


    ]
}
