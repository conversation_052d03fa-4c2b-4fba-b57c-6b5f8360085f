<script setup>
import { getWorksheets } from '@/api/modules/staffs'
import eventBus from '@/utils/eventBus'
import StubTable from '@/views/staffs/components/stub_table.vue'
import WorksheetTable from '@/views/staffs/components/worksheet_table.vue'

const router = useRouter()
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const data = ref({
    loading: false,
    form: {
        id: props.id,
        driver: null,
        adjustment_minutes: 0,
        adjustment_travel_km: 0,
        photos: [],
        stub: null
    },
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    } else {
    }
})

function getInfo(id) {
    if (id) {
        data.value.form.id = id
    }
    data.value.loading = true
    getWorksheets({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function onDelete() {
    eventBus.emit('get-data-list')
    router.push({ name: 'deliveryWorksheets' })
}

</script>

<template>
    <div v-loading="data.loading">
        <el-row>
            <el-col :span="12">
                <WorksheetTable :worksheet="data.form" :on-update="getInfo" :on-delete="onDelete" />
            </el-col>
            <el-col :span="12" v-if="data.form.stub">
                <StubTable :stub="data.form.stub" :on-update="getInfo" :on-delete="getInfo" />
            </el-col>
        </el-row>

    </div>
</template>

<style lang="scss">
.red {
    color: red;
}
</style>