<script setup>
import { getAdminOrders, generatePaymentOrder } from '@/api/modules/delivery'
import { orderNoFormatter} from '@/utils/formatter'
import { DocumentCopy } from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'
import statusStyles from '../../../orders/status'
import { useI18n } from 'vue-i18n'
import { usePagination } from '@/utils/composables'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const router = useRouter()

const { t } = useI18n()
const { text, copy, copied, isSupported } = useClipboard()


const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const data = ref({
    loading: false,
    data: {
        id: props.id,
    },
})
watch(copied, (val) => {
    val && ElMessage.success(`${t('messages.copied')}：${text.value}`)
})

onMounted(() => {
    if (data.value.data.id != '') {
        getInfo()
    }
})


// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}


// Api
function getInfo() {
    data.value.loading = true
    getAdminOrders({ id: data.value.data.id }).then(res => {
        data.value.loading = false
        data.value.data = res.data
    })
}

function generatePo() {
    let params = {
        id: data.value.data.id
    }
    generatePaymentOrder(params).then(res => {
        if (res.data.errCode === 365) {
            getInfo()
        }
    })
}

function pay() {
    let params = {
        id: data.value.data.id
    }
}


const goDetail = row => {
    router.push({
        name: 'deliveryOrderDetail',
        query: {
            id: row.id,
            redirect: data.value.data.id
        }
    })
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.status === 'closed') {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <template #title>
                <strong>{{ data.data.sn }}</strong>
            </template>
            <template #extra>
                <el-space>
                    <el-button type="primary" v-if="!data.data.paymentOrderGenerated" @click="generatePo">
                        {{ $t('delivery.adminOrders.operations.generatePaymentOrder') }}
                    </el-button>
                    <el-button type="success" v-if="data.data.paymentOrderGenerated && !data.data.isPaid" @click="pay">
                        {{ $t('delivery.adminOrders.operations.pay') }}
                    </el-button>
                    <small>{{ $t('delivery.adminOrders.sections.createdBy') }} <b>{{ data.data.admin?.name }}</b>
                        {{ $t('delivery.adminOrders.sections.createdAt') }} <b>{{ data.data.created_at }}</b></small>
                </el-space>
            </template>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.data.orders" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="goDetail" @sort-change="sortChange">
                <el-table-column prop="no" :label="$t('delivery.orders.fields.no')" width="140">
                    <template #default="scope">
                        <el-space>
                            <el-tooltip placement="top">
                                <template #content>
                                    {{ $t('operations.copy') }}
                                </template>
                                <el-button @click.stop="copy(scope.row.no)" size="small" type="primary" plain circle
                                    :icon="DocumentCopy" />
                            </el-tooltip>
                            <div class="order-number">
                                {{ orderNoFormatter(_, _, scope.row.no) }}
                            </div>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="sender_name" :label="$t('delivery.orders.fields.sender')"
                    show-overflow-tooltip />
                <el-table-column prop="receiver_name" :label="$t('delivery.orders.fields.receiver')"
                    show-overflow-tooltip />
                <el-table-column prop="status" :label="$t('delivery.orders.fields.status')" align="center" width="110">
                    <template #default="scope">
                        <el-tag :type="statusStyles[scope.row.status]" round size="small">
                            {{ $t(`delivery.orders.selections.status.${scope.row.status }`)}}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="quantity" :label="$t('delivery.orders.fields.quantity')" align="center"
                    width="80" sortable="custom" />
                <el-table-column prop="expected_pickup_start_at"
                    :label="$t('delivery.orders.fields.expectedPickupTime')" sortable="custom" width="180">
                    <template #default="scope">
                        {{scope.row.expected_pickup_start_at?.slice(0,-3)}} ~ {{scope.row.expected_pickup_end_at}}
                    </template>
                </el-table-column>
                <el-table-column prop="expected_delivery_start_at"
                    :label="$t('delivery.orders.fields.expectedDeliveryTime')" sortable="custom" width="190">
                    <template #default="scope">
                        <span v-if="scope.row.expected_delivery_start_at">
                            {{scope.row.expected_delivery_start_at?.slice(0,-3)}} ~
                            {{scope.row.expected_delivery_end_at}}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
            </el-table>
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
