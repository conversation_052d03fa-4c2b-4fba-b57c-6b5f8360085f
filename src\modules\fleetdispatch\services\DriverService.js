import { DriverRepository } from '../repositories/DriverRepository'
import { Driver } from '../models/Driver'

export class DriverService {
    constructor() {
        this.driverRepository = new DriverRepository()

        // 本地状态
        this.allDrivers = []
        this.selectedDriver = null

        // 添加司机位置状态跟踪
        this.driversLocations = new Map() // 使用Map存储司机位置信息
    }

    // 获取所有司机
    async fetchDrivers(params = {}) {
        try {
            console.log('DriverService: 获取司机列表，参数:', params);
            const drivers = await this.driverRepository.getDrivers(params);
            this.allDrivers = drivers;
            console.log(`DriverService: 成功获取 ${drivers.length} 个司机`);
            return drivers;
        } catch (error) {
            console.error('DriverService: 获取司机数据失败:', error);
            this.allDrivers = [];
            throw error;
        }
    }

    // 根据ID获取司机
    getDriverById(id) {
        if (!Array.isArray(this.allDrivers)) {
            console.warn('allDrivers不是数组类型')
            return null
        }

        const driver = this.allDrivers.find(driver => driver.id === id)
        return driver || null
    }

    // 获取所有司机列表
    getDrivers() {
        return Array.isArray(this.allDrivers) ? this.allDrivers : []
    }

    // 更新司机位置
    updateDriverLocation(driverId, locationData) {
        // 验证参数是否有效
        if (!driverId || !locationData || !locationData.latitude || !locationData.longitude) {
            console.warn('更新司机位置失败: 无效的参数', { driverId, locationData })
            return false
        }

        try {
            // 查找司机
            const driver = this.getDriverById(driverId)

            // 如果司机不存在，则记录警告并存储位置信息
            if (!driver) {
                console.warn(`找不到司机(${driverId})，但仍会记录其位置信息`)
            }

            // 准备位置数据
            const driverLocation = {
                id: driverId,
                latitude: locationData.latitude,
                longitude: locationData.longitude,
                timestamp: locationData.timestamp || Math.floor(Date.now() / 1000),
                speed: locationData.speed || 0,
                accuracy: locationData.accuracy || 0,
                lastUpdated: new Date(),
                driver: driver // 可能为null
            }

            // 存储位置信息
            this.driversLocations.set(driverId, driverLocation)

            // 如果司机存在，更新司机对象的位置信息
            if (driver) {
                // 更新司机对象上的位置数据
                driver.latitude = locationData.latitude
                driver.longitude = locationData.longitude
                driver.lastLocationUpdate = new Date()
            }

            console.log(`司机(${driverId})位置已更新:`, driverLocation)
            return true
        } catch (error) {
            console.error(`更新司机(${driverId})位置失败:`, error)
            return false
        }
    }

    // 获取所有有位置信息的司机
    getDriversWithLocation() {
        const driversWithLocation = []

        // 遍历位置Map，提取有效位置数据
        this.driversLocations.forEach((locationData, driverId) => {
            // 检查位置数据是否在5分钟内
            const fiveMinutesAgo = new Date()
            fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5)

            // 如果位置数据过期，跳过
            if (locationData.lastUpdated < fiveMinutesAgo) {
                return
            }

            // 找到对应的司机对象
            const driver = this.getDriverById(driverId)

            // 添加位置信息到结果数组
            driversWithLocation.push({
                ...locationData,
                driver: driver || { id: driverId, name: `未知司机 (${driverId})` }
            })
        })

        return driversWithLocation
    }

    // 清除过期的司机位置信息（可以定期调用此方法）
    cleanupDriverLocations() {
        const fiveMinutesAgo = new Date()
        fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5)

        // 删除过期位置数据
        this.driversLocations.forEach((locationData, driverId) => {
            if (locationData.lastUpdated < fiveMinutesAgo) {
                this.driversLocations.delete(driverId)
            }
        })
    }

    // 更新司机状态
    updateDriverStatuses(activeDriverIds) {
        if (!Array.isArray(this.allDrivers)) return

        this.allDrivers = this.driverRepository.updateDriverStatuses(
            this.allDrivers,
            activeDriverIds
        )

        return this.allDrivers
    }

    // 设置选中的司机
    setSelectedDriver(driver) {
        if (driver instanceof Driver) {
            this.selectedDriver = driver
        } else if (typeof driver === 'object') {
            this.selectedDriver = new Driver(driver)
        } else {
            this.selectedDriver = null
        }

        return this.selectedDriver
    }

    // 清除选中的司机
    clearSelectedDriver() {
        this.selectedDriver = null
    }

    // 选择司机
    selectDriver(driver) {
        return this.setSelectedDriver(driver)
    }

    // 为司机分配路线
    async assignRouteToDriver(driverId, routeId) {
        const driver = this.getDriverById(driverId)
        if (!driver) return null

        return this.driverRepository.assignRouteToDriver(driver, routeId)
    }

    // 取消司机的路线分配
    async unassignDriver(driverId) {
        const driver = this.getDriverById(driverId)
        if (!driver) return null

        return this.driverRepository.unassignDriver(driver)
    }

    // 直接更新司机数据（用于切换全部/在线模式）
    updateDrivers(drivers) {
        if (!Array.isArray(drivers)) {
            console.warn('DriverService: updateDrivers 失败，参数不是数组')
            return false
        }

        // 直接替换司机数据
        this.allDrivers = drivers.map(driver => {
            // 如果已经是 Driver 实例，直接返回
            if (driver instanceof Driver) {
                return driver
            }

            // 记录调试信息，检查地址和坐标数据
            if (driver.address_lng_lat) {
                console.log(`司机 ${driver.name} (${driver.id}) 的地址坐标:`, {
                    original: driver.address_lng_lat,
                    lng: driver.address_lng_lat[0],
                    lat: driver.address_lng_lat[1]
                });
            }

            // 否则创建新的 Driver 实例
            const driverInstance = new Driver(driver);

            // 再次记录转换后的坐标，确保转换正确
            if (driverInstance.address_lng_lat) {
                console.log(`司机 ${driverInstance.name} (${driverInstance.id}) 的转换后坐标:`, {
                    converted: driverInstance.address_lng_lat,
                    lat: driverInstance.address_lng_lat[0],
                    lng: driverInstance.address_lng_lat[1],
                    original: driverInstance.original_address_lng_lat
                });
            }

            return driverInstance;
        })

        console.log(`DriverService: 已更新 ${this.allDrivers.length} 个司机数据`)
        return this.allDrivers
    }
}