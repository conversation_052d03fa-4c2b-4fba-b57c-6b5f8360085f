// Driver模型类
export class Driver {
    constructor(data = {}) {
        this.id = data.id || null
        this.sn = data.sn || ''
        this.name = data.name || ''
        this.phone = data.phone || ''
        this.email = data.email || ''
        this.status = data.status || '离线'
        // 使用传入的颜色或随机分配一个颜色，确保每个司机都有唯一的颜色标识
        this.color = data.color || this.getRandomColor()
        this.assignedRouteId = data.assignedRouteId || null
        this.created_at = data.created_at || new Date().toISOString()
        this.updated_at = data.updated_at || new Date().toISOString()

        // 添加地址相关字段
        this.address_id = data.address_id || null
        this.address_label = data.address_label || null

        // 处理经纬度坐标 - API返回的是 [longitude, latitude]，我们需要转换为 [latitude, longitude]
        if (data.address_lng_lat && Array.isArray(data.address_lng_lat) && data.address_lng_lat.length === 2) {
            // 转换坐标顺序：从 [longitude, latitude] 到 [latitude, longitude]
            this.address_lng_lat = [data.address_lng_lat[1], data.address_lng_lat[0]]

            // 记录原始坐标，以便调试
            this.original_address_lng_lat = [...data.address_lng_lat]
        } else {
            this.address_lng_lat = null
            this.original_address_lng_lat = null
        }

        // 添加班次信息
        this.shifts = data.shifts || []
    }

    // 获取随机颜色
    getRandomColor() {
    // 使用HSL色彩空间来生成高对比度、明显不同的颜色
    // 避免灰色和纯红色，确保颜色之间有明显区别
        const distinctColors = [
            '#FF5733', // 鲜橙红色
            '#33A8FF', // 鲜蓝色
            '#33FF57', // 荧光绿
            '#FF33A8', // 粉红色
            '#A833FF', // 紫色
            '#FFDD33', // 金黄色
            '#33FFF5', // 青绿色
            '#FF3333', // 鲜红色(偏暗)
            '#00CC66', // 翠绿色
            '#6600CC', // 深紫色
            '#FF9900', // 橙色
            '#009999', // 青色
            '#FF0099', // 品红色
            '#00CCFF', // 天蓝色
            '#9933FF', // 亮紫色
            '#00FF00', // 亮绿色
            '#0066FF', // 皇家蓝
            '#FF6600', // 深橙色
            '#CC00FF', // 紫红色
            '#FFCC00', // 金色
            '#00FFCC', // 碧绿色
            '#3366FF', // 蓝紫色
            '#FF3399', // 深粉色
            '#00FF99', // 薄荷绿
            '#9900FF', // 蓝紫色
            '#FFFF00', // 黄色
            '#00FFFF', // 青色
            '#FF00FF', // 洋红色
            '#66FF00', // 酸橙绿
            '#0099FF', // 深天蓝
            '#CC6600', // 棕色
            '#9900CC', // 深紫色
            '#00CC00', // 深绿色
            '#FF99FF', // 淡紫色
            '#FFCC99', // 杏色
            '#99FFCC', // 薄荷奶油
            '#CC99FF', // 淡紫色
            '#FF9999', // 淡红色
            '#99CCFF', // 淡蓝色
            '#CCFF99'  // 淡绿色
        ]

        return distinctColors[Math.floor(Math.random() * distinctColors.length)]
    }

    // 更新司机状态
    updateStatus(status) {
        this.status = status
        this.updated_at = new Date().toISOString()
        return this
    }

    // 分配路线
    assignRoute(routeId) {
        this.assignedRouteId = routeId
        this.status = '在线'
        this.updated_at = new Date().toISOString()
        return this
    }

    // 取消路线分配
    unassignRoute() {
        this.assignedRouteId = null
        this.updated_at = new Date().toISOString()
        return this
    }

    // 是否可用（可分配新订单）
    isAvailable() {
        return this.status === '在线'
    }

    // 克隆方法
    clone() {
        return new Driver({ ...this })
    }

    // 从普通对象转换为Driver对象
    static fromObject(obj) {
        return new Driver(obj)
    }

    // 将Driver对象数组转换为普通对象数组
    static toObjectArray(drivers) {
        return drivers.map(driver => ({ ...driver }))
    }
}