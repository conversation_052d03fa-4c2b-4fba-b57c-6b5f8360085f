<script setup>
    import { findAddresses, retrieveAddress } from '@/api/modules/dispatcher'
    import { useThrottleFn } from '@vueuse/core'
    const props = defineProps({
        id: {
            type: String,
            default: null
        },
        address: {
            type: String,
        },
        city: {
            type: String,
            default: null
        },
        province: {
            type: String,
            default: null
        },
        postalCode: {
            type: String,
            default: null
        },

        onClear: {
            type: Function,
            default: () => { }
        },
        disabled: {
            type: Boolean,
            default: false,
        }
    })

    const emit = defineEmits(['success'])

    defineExpose({
        reset(addr) {
            address.value.Line1 = addr
        },
    })

    const loading = ref(false)

    const selectRef = ref()
    const addressesList = ref([])

    const address = ref({
        Id: props.id,
        Line1: props.address,
        City: props.city,
        ProvinceName: props.province,
        PostalCode: props.postalCode,
    })

    watch(() => props, (val) => {
        address.value = {
            Id: props.id,
            Line1: props.address,
            City: props.city,
            ProvinceName: props.province,
            PostalCode: props.postalCode,
        }
    }, { deep: true })


    const searchTermTemp = ref(null)
    const lastId = ref(null)


    let throttle1 = useThrottleFn(find, 1000)
    const update = useThrottleFn(() => { find(address.Line1) }, 1000)

    // Api
    function find(val) {
        console.log(1, val)
        if (!val || val == undefined || val == '') {
            return
        }
        searchTermTemp.value = val

        loading.value = true

        let params = {
            searchTerm: searchTermTemp.value,
            lastId: lastId.value
        }
        findAddresses(params).then(res => {
            if (!res.data.errCode) {
                addressesList.value = res.data
                showOptions()
            }
            loading.value = false
        })
    }

    function retrieve(id) {
        let params = { id: id }
        retrieveAddress(params).then(res => {
            if (!res.data.errCode) {
                address.value = res.data
                addressesList.value = []
                selectRef.value.blur()
                emit('success', address.value)
            }
        })
    }

    // Event
    function onAddressChange(val) {
        if (val.next == 'f') {
            find();
            lastId.value = val.id
        } else if (val.next == 'r') {
            retrieve(val.id)
        }
    }

    // Action
    function showOptions() {
        if (!selectRef.value.visible) {
            selectRef.value.focus()
        }
    }
</script>
<template>
    <div class="main">
        <el-select v-model="address.Line1" filterable remote reserve-keyword clearable ref="selectRef" value-key="id"
            style="width: 100%;" @change="onAddressChange" loading-text="Finding addresses" no-data-text="Keep typing"
            no-match-text="No matched address" @clear="onClear" placeholder="Please enter a keyword" remote-show-suffix
            :remote-method="throttle1" :loading="loading" :disabled="props.disabled">
            <el-option v-for="item in addressesList" :key="item.id" :label="item.label" :value="item">
                <div v-html="item.text" />
            </el-option>
        </el-select>
        <el-button type="primary" text @click="update" v-if="!address.Id && !props.disabled">Update</el-button>
    </div>
</template>

<style scoped>
    .main {
        display: flex;
        flex-flow: row nowrap;
        width: 100%;
    }
</style>