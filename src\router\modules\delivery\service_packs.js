/*
* Author: <EMAIL>'
* Date: '2023-11-29 20:37:06'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/delivery/service_packs.js'
* File: 'service_packs.js'
* Version: '1.0.0'
*/
const Layout = () => import('@/layout/index.vue')

export default {
    path: '/delivery/service-packs',
    component: Layout,
    redirect: '/delivery/service-packs/packs',
    name: 'deliveryServicePacks',
    meta: {
        title: '服务包设置',
        icon: 'solar:medal-ribbons-star-linear',
        auth: ['super'],
        i18n: 'route.delivery.servicePacks.title'
    },
    children: [
        {
            path: 'packs',
            name: 'deliveryServicePackList',
            component: () => import('@/views/delivery/service_packs/packs/list.vue'),
            meta: {
                title: '服务包',
                icon: 'pixelarticons:list',
                i18n: 'route.delivery.servicePacks.packs',
                activeMenu: '/delivery/service-packs/packs',
                auth: ['super']
            }
        },
        {
            path: 'pack',
            name: 'deliveryServicePackDetail',
            component: () => import('@/views/delivery/service_packs/packs/detail.vue'),
            meta: {
                title: '服务包',
                icon: 'pixelarticons:list',
                i18n: 'route.delivery.servicePacks.packs',
                activeMenu: '/delivery/service-packs/packs',
                auth: ['super'],
                sidebar: false
            }
        },
        {
            path: 'items',
            name: 'deliveryServicePackItem',
            component: () => import('@/views/delivery/service_packs/items/list.vue'),
            meta: {
                title: '服务项',
                icon: 'system-uicons:list',
                i18n: 'route.delivery.servicePacks.items',
                activeMenu: '/delivery/service-packs/items',
                auth: ['super']
            }
        }
    ]
}
