import { ref } from 'vue';

export class GeocodingService {
    constructor() {
        this.cache = new Map();
        this.pendingRequests = new Map();
        this.apiKey = '5bQg5kAn8T496IA5D54PeBi4WC07lqm9';
    }

    // 单个地址地理编码
    async geocodeAddress(address, city, postalCode, id = null) {
        const fullAddress = `${address}, ${city}, ${postalCode}`;
        const cacheKey = fullAddress.toLowerCase().trim();

        // 检查缓存
        if (this.cache.has(cacheKey)) {
            return {
                id,
                coordinates: this.cache.get(cacheKey),
                cached: true
            };
        }

        // 检查是否有正在进行的相同请求
        if (this.pendingRequests.has(cacheKey)) {
            const result = await this.pendingRequests.get(cacheKey);
            return {
                id,
                coordinates: result,
                cached: false
            };
        }

        // 创建新请求
        const requestPromise = fetch(
            `https://api.tomtom.com/search/2/geocode/${encodeURIComponent(fullAddress)}.json?key=${this.apiKey}`
        )
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Geocoding API error: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.results && data.results.length > 0) {
                    const position = data.results[0].position;
                    // TomTom返回的是{lat, lon}格式，转换为[lat, lng]
                    const coordinates = [position.lat, position.lon];
                    
                    // 存入缓存
                    this.cache.set(cacheKey, coordinates);
                    return coordinates;
                }
                return null;
            })
            .catch(error => {
                console.error('地理编码请求失败:', error);
                return null;
            })
            .finally(() => {
                // 请求完成后从pending列表移除
                this.pendingRequests.delete(cacheKey);
            });

        // 将请求添加到pending列表
        this.pendingRequests.set(cacheKey, requestPromise);
        
        const result = await requestPromise;
        return {
            id,
            coordinates: result,
            cached: false
        };
    }

    // 批量处理订单地理编码
    async batchGeocodeOrders(orders) {
        if (!Array.isArray(orders) || orders.length === 0) {
            return [];
        }

        const ordersNeedingCoords = orders.filter(order => 
            !order.lng_lat || !Array.isArray(order.lng_lat) || order.lng_lat.length !== 2
        );
        
        // 如果没有需要地理编码的订单，直接返回
        if (ordersNeedingCoords.length === 0) {
            return [];
        }

        console.log(`开始批量地理编码，共 ${ordersNeedingCoords.length} 个地址`);
        
        // 分批处理
        const batchSize = 10;
        const results = [];
        const startTime = Date.now();
        
        for (let i = 0; i < ordersNeedingCoords.length; i += batchSize) {
            const batch = ordersNeedingCoords.slice(i, i + batchSize);
            console.log(`处理批次 ${i/batchSize + 1}/${Math.ceil(ordersNeedingCoords.length/batchSize)}, ${batch.length} 个地址`);
            
            const batchPromises = batch.map(order => 
                this.geocodeAddress(order.address, order.city, order.postal_code, order.id)
            );
            
            const batchResults = await Promise.all(batchPromises);
            
            // 收集有效结果
            const validResults = batchResults.filter(result => result && result.coordinates);
            results.push(...validResults);
            
            // 显示进度
            console.log(`批次完成，获取到 ${validResults.length}/${batch.length} 个有效坐标`);
        }
        
        const endTime = Date.now();
        console.log(`批量地理编码完成，处理了 ${ordersNeedingCoords.length} 个地址，获得 ${results.length} 个有效坐标，耗时 ${(endTime-startTime)/1000}秒`);
        
        return results;
    }

    // 清除缓存
    clearCache() {
        this.cache.clear();
    }

    // 导出缓存数据（用于持久化存储）
    exportCache() {
        return Array.from(this.cache.entries());
    }

    // 导入缓存数据
    importCache(cacheData) {
        if (Array.isArray(cacheData)) {
            this.cache = new Map(cacheData);
            return true;
        }
        return false;
    }
}

// 提供组合式API
export function useGeocodingService() {
    const geocodingService = new GeocodingService();
    const isProcessing = ref(false);
    
    // 保存和加载缓存到本地存储
    const saveCache = () => {
        try {
            const cacheData = geocodingService.exportCache();
            localStorage.setItem('geocoding_cache', JSON.stringify(cacheData));
            console.log(`已保存 ${cacheData.length} 条地理编码缓存到本地存储`);
            return true;
        } catch (error) {
            console.error('保存地理编码缓存失败:', error);
            return false;
        }
    };
    
    const loadCache = () => {
        try {
            const cacheStr = localStorage.getItem('geocoding_cache');
            if (!cacheStr) return false;
            
            const cacheData = JSON.parse(cacheStr);
            const result = geocodingService.importCache(cacheData);
            if (result) {
                console.log(`已从本地存储加载 ${cacheData.length} 条地理编码缓存`);
            }
            return result;
        } catch (error) {
            console.error('加载地理编码缓存失败:', error);
            return false;
        }
    };
    
    // 自动加载缓存
    loadCache();
    
    return {
        geocodeAddress: async (address, city, postalCode, id) => {
            return geocodingService.geocodeAddress(address, city, postalCode, id);
        },
        batchGeocodeOrders: async (orders) => {
            isProcessing.value = true;
            try {
                const results = await geocodingService.batchGeocodeOrders(orders);
                // 保存缓存
                saveCache();
                return results;
            } finally {
                isProcessing.value = false;
            }
        },
        clearCache: () => {
            geocodingService.clearCache();
            localStorage.removeItem('geocoding_cache');
        },
        isProcessing,
        cacheSize: () => geocodingService.cache.size
    };
} 