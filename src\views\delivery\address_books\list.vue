<script setup name="DeliveryAddressBooksList">
import { CircleCheckFilled } from '@element-plus/icons-vue'
import { usePagination } from '@/utils/composables'
import FormMode from './components/FormMode/index.vue'
import { getUserAddressBooks } from '@/api/modules/delivery'

import { phoneNumberFormatter } from '@/utils/formatter'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    /**
     * 详情展示模式
     * router 路由跳转
     * dialog 对话框
     * drawer 抽屉
     */
    formMode: 'router',
    // 详情
    formModeProps: {
        visible: false,
        id: ''
    },
    // 搜索
    search: {
        user__name__icontains: null,
        name__icontains: null,
        phone_number__icontains: null,
        email__icontains: null,
        address__icontains: null,
        postal_code__icontains: null,
        city__icontains: null,
        province__icontains: null,
        addr_db__isnull: null,
    },
    // 列表数据
    dataList: []
})

// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
        description: null,
        fee__gte: 0,
        fee__lte: null,
        weekdays__contains: [],
        holidays__contains: [],
        zone: null,
        user_type: null,
        user_group: null,
    }
    currentChange()
}

onMounted(() => {
    getDataList()
})



function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )

    data.value.search.title && (params.title = data.value.search.title)
    getUserAddressBooks(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.address_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}


function onDetail(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.visible = true
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.user) {
        return 'not-available-row'
    } else {
        return ''
    }
}
</script>

<template>
    <div>
        <page-header :title="$t('delivery.addressBook.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.name')">
                                        <el-input v-model="data.search.name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.name') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.phone')">
                                        <el-input v-model="data.search.phone_number__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.phone') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.email')">
                                        <el-input v-model="data.search.email__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.email') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.address')">
                                        <el-input v-model="data.search.address__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.address') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.postalCode')">
                                        <el-input v-model="data.search.postal_code__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.postalCode') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.city')">
                                        <el-input v-model="data.search.city__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.city') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.province')">
                                        <el-input v-model="data.search.province__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.province') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>

        </page-main>
        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onDetail" @sort-change="sortChange">
                <el-table-column type="index" align="center" fixed />
                <el-table-column width="50">
                    <template #default="scope">
                        <el-tag v-if="scope.row.is_default" type="primary" size="small">D</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="user.name" :label="$t('delivery.addressBook.fields.user')" width="100"
                    show-overflow-tooltip />
                <el-table-column prop="name" :label="$t('delivery.addressBook.fields.name')" width="150"
                    show-overflow-tooltip />
                <el-table-column prop="phone_number" :label="$t('delivery.addressBook.fields.phone')" width="140">
                    <template #default="scope">
                        {{ phoneNumberFormatter(scope.row.phone_number) }}
                    </template>
                </el-table-column>
                <el-table-column prop="email" :label="$t('delivery.addressBook.fields.email')" width="150"
                    show-overflow-tooltip />
                <el-table-column prop="address" :label="$t('delivery.addressBook.fields.address')" min-width="150"
                    show-overflow-tooltip />
                <el-table-column prop="postal_code" :label="$t('delivery.addressBook.fields.postalCode')" width="100" />
                <el-table-column prop="city" :label="$t('delivery.addressBook.fields.city')" width="100"
                    show-overflow-tooltip />
                <el-table-column prop="province" :label="$t('delivery.addressBook.fields.province')" width="100"
                    show-overflow-tooltip />
                <el-table-column prop="addr_db_id" :label="$t('delivery.addressBook.fields.inDB')" width="80">
                    <template #default="scope">
                        <el-icon :size="20" v-if="scope.row.addr_db_id" color="#67C23A">
                            <CircleCheckFilled />
                        </el-icon>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode v-if="['dialog', 'drawer'].includes(data.formMode)" :id="data.formModeProps.id"
            v-model="data.formModeProps.visible" :mode="data.formMode" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}
</style>
