import { defineStore } from 'pinia'
import { useOrderStore } from './order'
import { computed, ref, watch } from 'vue'

export const useMapStore = defineStore('map', () => {
    // 订单 store
    const orderStore = useOrderStore()
  
    // 状态定义
    const points = ref(new Map()) // 所有地图点位
    const visiblePoints = ref(new Map()) // 当前视野内的点位
    const selectedPoints = ref(new Set()) // 选中的点位
    const mapBounds = ref(null) // 地图当前视野范围
    const hoveredPoint = ref(null)  // 新增：当前悬浮的点位
    const isMapReady = ref(false) // 地图是否已准备好
    const defaultCenter = [116.397428, 39.90923] // 默认中心点（北京）
    const defaultZoom = 13 // 默认缩放级别
  
    // 计算属性
    const currentPoints = computed(() => {
        console.log('Computing currentPoints:', {
            hasPoints: points.value instanceof Map,
            pointsSize: points.value?.size || 0
        })
    
        const result = new Map()
    
        points.value.forEach((point, coordinate) => {
            const filteredPoint = {
                ...point,
                orders: {
                    pickup: point.orders.pickup,
                    delivery: point.orders.delivery
                }
            }
      
            // 添加所有有订单的点位
            if (filteredPoint.orders.pickup.length > 0 || filteredPoint.orders.delivery.length > 0) {
                result.set(coordinate, filteredPoint)
            }
        })
    
        console.log('Computed currentPoints:', {
            resultSize: result.size,
            firstPoint: result.size > 0 ? Array.from(result.entries())[0] : null
        })
    
        return result
    })

    // 添加用于检查 store 状态的计算属性
    const storeState = computed(() => ({
        hasPoints: points.value instanceof Map,
        pointsSize: points.value?.size || 0,
        hasSelectedPoints: selectedPoints.value instanceof Set,
        selectedSize: selectedPoints.value?.size || 0
    }))

    // 方法
    const initMapPoints = orders => {
        console.log('=== Initializing Map Points ===')
    
        // 检查 orders 是否有效
        if (!orders || !Array.isArray(orders)) {
            console.error('initMapPoints: 无效的订单数据', orders)
      
            // 尝试从 store 获取所有订单
            orders = orderStore.allOrders || []
      
            if (!orders.length) {
                console.warn('initMapPoints: 无法获取订单数据')
                return
            }
      
            console.log('使用 orderStore.allOrders 作为数据源:', orders.length)
        }
    
        console.log('接收到的订单数据:', {
            orderCount: orders.length,
            sampleOrder: orders.length > 0
                ? {
                    id: orders[0].id,
                    type: orders[0].type,
                    status: orders[0].status,
                    hasCoords: !!orders[0].lng_lat || !!orders[0].location
                }
                : null
        })

        // 如果有样本订单，打印其坐标
        if (orders.length > 0) {
            const sampleOrder = orders[0]
            if (sampleOrder.lng_lat) {
                console.log('样本订单坐标 (lng_lat):', sampleOrder.lng_lat)
            } else if (sampleOrder.location) {
                console.log('样本订单坐标 (location):', sampleOrder.location)
            }
        }

        const newPoints = new Map()
    
        // 检查是否有选中的订单
        const selectedOrderIds = new Set(orderStore.selectedOrders.map(o => o.id))
        const hasSelectedOrders = selectedOrderIds.size > 0
    
        orders.forEach(order => {
            // 检查订单有效性
            if (!order || (!order.lng_lat && !order.location)) {
                return // 跳过无效订单
            }
      
            // 检查并标准化坐标
            let coordinates = null
      
            // 处理不同格式的坐标
            if (order.lng_lat) {
                if (Array.isArray(order.lng_lat) && order.lng_lat.length === 2) {
                    // 从[纬度, 经度]转换为[经度, 纬度]
                    const lat = Number(order.lng_lat[0])
                    const lng = Number(order.lng_lat[1])
                    coordinates = [lng, lat] // 正确格式：[经度, 纬度]
                } else if (typeof order.lng_lat === 'string') {
                    // 处理字符串格式的坐标 "lat,lng"
                    const parts = order.lng_lat.split(',').map(p => parseFloat(p.trim()))
                    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                        const [lat, lng] = parts
                        coordinates = [lng, lat]
                    }
                }
            } else if (order.location) {
                if (Array.isArray(order.location) && order.location.length === 2) {
                    // 假设location格式也是[纬度, 经度]
                    const lat = Number(order.location[0])
                    const lng = Number(order.location[1])
                    coordinates = [lng, lat] // 正确格式：[经度, 纬度]
                } else if (typeof order.location === 'string') {
                    // 处理字符串格式的坐标 "lat,lng"
                    const parts = order.location.split(',').map(p => parseFloat(p.trim()))
                    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                        const [lat, lng] = parts
                        coordinates = [lng, lat]
                    }
                } else if (order.location.lat && order.location.lng) {
                    // 处理对象格式的坐标 { lat, lng }
                    const lat = Number(order.location.lat)
                    const lng = Number(order.location.lng)
                    coordinates = [lng, lat]
                }
            }

            if (!coordinates) {
                // console.warn('Order without valid coordinates:', order.id);
                return
            }

            // 验证坐标范围
            const [lng, lat] = coordinates
            if (isNaN(lng) || isNaN(lat) || lng < -180 || lng > 180 || lat < -90 || lat > 90) {
                // console.warn('Invalid coordinate values:', { orderId: order.id, lng, lat });
                return
            }
      
            const coordinate = `${lng},${lat}`
            const isOrderSelected = hasSelectedOrders ? selectedOrderIds.has(order.id) : false
      
            const existingPoint = newPoints.get(coordinate)
            if (existingPoint) {
                // 使用大写的订单类型
                const orderType = (order.type || '').toUpperCase() === 'PICKUP' ? 'pickup' : 'delivery'
                if (!existingPoint.orders[orderType]) {
                    existingPoint.orders[orderType] = []
                }
        
                // 添加订单到集合
                existingPoint.orders[orderType].push(order)
                existingPoint.totalCount++
        
                // 如果有订单被选中，则整个点被选中
                if (isOrderSelected) {
                    existingPoint.isSelected = true
                }
            } else {
                // 确定订单类型，默认为delivery
                const orderType = (order.type || '').toUpperCase() === 'PICKUP' ? 'PICKUP' : 'DELIVERY'
        
                // 创建新的点位
                newPoints.set(coordinate, {
                    id: order.id,
                    coordinate: coordinates, // 使用标准化后的坐标
                    type: orderType,
                    status: order.status || 'pending',
                    driver_id: order.driver_id,
                    orders: {
                        pickup: orderType === 'PICKUP' ? [order] : [],
                        delivery: orderType === 'DELIVERY' ? [order] : []
                    },
                    totalCount: 1,
                    isSelected: isOrderSelected || selectedPoints.value.has(coordinate)
                })
            }
        })

        console.log('New points created:', {
            count: newPoints.size,
            firstPoint: newPoints.size > 0 ? Array.from(newPoints.entries())[0] : null
        })

        // 更新状态
        points.value = newPoints

        return newPoints
    }

    // 获取指定坐标的所有订单
    const getOrdersByCoordinate = coordinate => {
        const point = points.value.get(coordinate)
        if (!point) return null
        return point.orders
    }

    // 选择/取消选择坐标点
    const togglePointSelection = coordinate => {
        if (!coordinate) {
            console.warn('No coordinate provided for toggle selection')
            return
        }

        // 确保 coordinate 是字符串格式
        const coordKey = Array.isArray(coordinate) ? coordinate.toString() : coordinate
    
        console.log('Toggling point selection:', {
            coordinate,
            coordKey,
            hasPoints: !!points.value,
            pointsSize: points.value?.size,
            pointExists: points.value?.has(coordKey)
        })

        if (!points.value) {
            console.warn('Points map is not initialized')
            return
        }

        if (points.value.has(coordKey)) {
            const point = points.value.get(coordKey)
            point.isSelected = !point.isSelected
            points.value.set(coordKey, { ...point })
      
            if (point.isSelected) {
                selectedPoints.value.add(coordKey)
            } else {
                selectedPoints.value.delete(coordKey)
            }
        } else {
            console.warn('Point not found:', coordinate)
        }
    }

    // 更新可见点位
    const updateVisiblePoints = bounds => {
        if (!bounds) return
    
        mapBounds.value = bounds
        const visible = new Map()
    
        currentPoints.value.forEach((point, coordinate) => {
            const [lng, lat] = point.coordinate
            if (lng >= bounds.west && lng <= bounds.east &&
          lat >= bounds.south && lat <= bounds.north) {
                visible.set(coordinate, point)
            }
        })
    
        visiblePoints.value = visible
    }

    // 清除选中状态
    const clearSelection = () => {
        if (selectedPoints.value) {
            selectedPoints.value.clear()
        }
    
        if (points.value) {
            points.value.forEach(point => {
                if (point) {
                    point.isSelected = false
                }
            })
        }
    }

    // 设置悬浮点位
    const setHoveredPoint = coordinate => {
        hoveredPoint.value = coordinate ? points.value.get(coordinate) : null
    }

    // 监听订单数据变化
    watch(
        () => orderStore.orders,
        orders => {
            console.log('Order store updated:', {
                orders: orders.map(o => ({
                    id: o.id,
                    type: o.type,
                    location: o.location,
                    lng_lat: o.lng_lat
                }))
            })
      
            console.log('Combined orders for map:', orders.length)
      
            if (!orders || orders.length === 0) {
                console.warn('No orders received from orderStore')
                return
            }
      
            // 检查是否有坐标数据
            const ordersWithCoords = orders.filter(o => o.lng_lat && o.lng_lat.length === 2)
            console.log('Orders with valid coordinates:', ordersWithCoords.length)
      
            if (ordersWithCoords.length === 0) {
                console.warn('No orders with valid coordinates found')
                return
            }
      
            initMapPoints(ordersWithCoords)
        },
        { deep: true }
    )

    // 添加点位
    const addPoint = (coordinate, orderType, order) => {
    // 确保 coordinate 是字符串格式
        const coordKey = Array.isArray(coordinate) ? coordinate.toString() : coordinate
    
        if (!points.value.has(coordKey)) {
            points.value.set(coordKey, {
                coordinate: Array.isArray(coordinate) ? coordinate : coordinate.split(',').map(Number),
                orders: {
                    pickup: [],
                    delivery: []
                },
                isSelected: false
            })
        }
    
        const point = points.value.get(coordKey)
        if (!point.orders[orderType]) {
            point.orders[orderType] = []
        }
    
        if (!point.orders[orderType].find(o => o.id === order.id)) {
            point.orders[orderType].push(order)
        }
    
        points.value.set(coordKey, { ...point })
    }

    // 添加 watch 来监控 points 的变化
    watch(() => points.value, newPoints => {
        console.log('Points changed:', {
            hasPoints: !!newPoints,
            size: newPoints?.size,
            currentPointsSize: currentPoints.value?.size
        })
    }, { deep: true })

    // 设置地图准备状态
    const setMapReady = ready => {
        isMapReady.value = ready
        console.log('Map ready state:', ready)
    }

    return {
    // 状态
        points,
        visiblePoints,
        selectedPoints,
        mapBounds,
        hoveredPoint,
        isMapReady,
        defaultCenter,
        defaultZoom,
    
        // 计算属性
        currentPoints,
        storeState, // 导出新的计算属性
    
        // 方法
        initMapPoints,
        getOrdersByCoordinate,
        togglePointSelection,
        updateVisiblePoints,
        clearSelection,
        setHoveredPoint,
        addPoint,
        setMapReady
    }
})
