{"extends": ["stylelint-config-standard", "stylelint-config-recommended-vue/scss"], "overrides": [{"files": ["**/*.scss"], "customSyntax": "postcss-scss"}], "plugins": ["stylelint-scss"], "rules": {"indentation": 4, "rule-empty-line-before": "always", "at-rule-empty-line-before": "always", "at-rule-no-unknown": null, "no-descending-specificity": null, "selector-pseudo-class-no-unknown": null, "selector-combinator-space-before": "never", "selector-combinator-space-after": "never", "property-no-unknown": null, "font-family-no-missing-generic-family-keyword": null, "selector-id-pattern": null, "selector-class-pattern": null, "scss/no-global-function-names": null, "scss/at-import-partial-extension": null, "function-no-unknown": [true, {"ignoreFunctions": ["v-bind", "map-get", "lighten", "darken", "themed"]}], "property-no-vendor-prefix": [true, {"ignoreProperties": ["mask-image"]}], "max-line-length": null}}