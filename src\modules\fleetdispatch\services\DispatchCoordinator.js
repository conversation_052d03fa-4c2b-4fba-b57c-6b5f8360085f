import { OrderService } from './OrderService'
import { RouteService } from './RouteService'
import { DriverService } from './DriverService'
import { useTimeStore } from '../stores/time'

// 调度协调器，用于协调不同服务之间的交互
export class DispatchCoordinator {
    constructor() {
        this.orderService = new OrderService()
        this.routeService = new RouteService()
        this.driverService = new DriverService()
    }

    // 加载所有数据
    async loadAllData() {
        const timeStore = useTimeStore()
    
        // 构建查询参数
        const params = {
            date: timeStore.selectedDate,
            from: timeStore.selectedShift.start,
            to: timeStore.selectedShift.end,
            priority: 'all'
        }
    
        // 并行加载数据
        await Promise.all([
            this.orderService.fetchOrders(params),
            this.routeService.fetchRoutes(params),
            this.driverService.fetchDrivers()
        ])
    
        return {
            orders: this.orderService.getAllOrders(),
            routes: this.routeService.routes,
            drivers: this.driverService.allDrivers
        }
    }

    // 为司机分配订单并创建/更新路线
    async assignOrdersToDriver(orderIds, driverId) {
    // 检查司机是否存在
        const driver = this.driverService.getDriverById(driverId)
        if (!driver) {
            throw new Error(`司机ID ${driverId} 不存在`)
        }
    
        // 检查司机是否已有活动路线
        let route = this.routeService.getDriverActiveRoute(driverId)
    
        // 如果没有，创建新路线
        if (!route) {
            route = this.routeService.createRoute(driverId, [])
            // 更新司机的路线分配
            await this.driverService.assignRouteToDriver(driverId, route.routeNumber)
        }
    
        // 分配订单
        const assignedOrders = await this.orderService.assignOrdersToDriver(
            orderIds, 
            driverId, 
            route.routeNumber
        )
    
        // 如果分配成功，更新路线
        if (assignedOrders.length > 0) {
            // 获取司机所有订单
            const allDriverOrders = this.orderService.getDriverOrders(driverId)
      
            // 更新路线订单
            this.routeService.updateRouteOrders(route.routeNumber, allDriverOrders)
        }
    
        return {
            driver,
            route,
            assignedOrders
        }
    }

    // 从路线中移除订单
    async removeOrderFromRoute(orderId, driverId) {
    // 查找订单
        const order = this.orderService.getAllOrders().find(o => o.id === orderId)
        if (!order) {
            throw new Error(`订单ID ${orderId} 不存在`)
        }
    
        // 检查订单是否已分配给指定司机
        if (order.driver_id !== driverId) {
            throw new Error(`订单 ${orderId} 不属于司机 ${driverId}`)
        }
    
        // 获取司机的路线
        const route = this.routeService.getDriverActiveRoute(driverId)
        if (!route) {
            throw new Error(`司机 ${driverId} 没有活动路线`)
        }
    
        // 取消订单分配
        await this.orderService.orderRepository.unassignOrder(order)
    
        // 从路线中移除订单
        await this.routeService.removeOrderFromRoute(orderId, route.routeNumber)
    
        // 更新本地状态
        this.orderService.assignedOrders = this.orderService.assignedOrders.filter(
            o => o.id !== orderId
        )
        this.orderService.orders.push(order)
    
        return {
            order,
            route
        }
    }

    // 更新路线的订单顺序
    async reorderRouteStops(routeNumber, orderedOrderIds) {
    // 查找路线
        const route = this.routeService.routes.find(r => r.routeNumber === routeNumber)
        if (!route) {
            throw new Error(`路线 ${routeNumber} 不存在`)
        }
    
        // 重新排序路线中的订单
        route.reorderStops(orderedOrderIds)
    
        // 更新订单的停靠序号
        orderedOrderIds.forEach((orderId, index) => {
            const order = this.orderService.assignedOrders.find(o => o.id === orderId)
            if (order) {
                order.stop_no = index + 1
            }
        })
    
        return route
    }

    // 完成路线
    async completeRoute(routeNumber) {
    // 更新路线状态为已完成
        await this.routeService.updateRouteStatus(routeNumber, 'completed')
    
        // 获取路线信息
        const route = this.routeService.routes.find(r => r.routeNumber === routeNumber)
        if (!route) {
            throw new Error(`路线 ${routeNumber} 不存在`)
        }
    
        // 取消司机的路线分配
        await this.driverService.unassignDriver(route.driverId)
    
        return route
    }

    // 取消路线
    async cancelRoute(routeNumber) {
    // 更新路线状态为已取消
        await this.routeService.updateRouteStatus(routeNumber, 'cancelled')
    
        // 获取路线信息
        const route = this.routeService.routes.find(r => r.routeNumber === routeNumber)
        if (!route) {
            throw new Error(`路线 ${routeNumber} 不存在`)
        }
    
        // 取消司机的路线分配
        await this.driverService.unassignDriver(route.driverId)
    
        // 获取路线的所有订单
        const routeOrders = this.orderService.getOrdersByRouteNumber(routeNumber)
    
        // 取消所有订单的分配
        for (const order of routeOrders) {
            await this.orderService.orderRepository.unassignOrder(order)
      
            // 更新本地状态
            this.orderService.assignedOrders = this.orderService.assignedOrders.filter(
                o => o.id !== order.id
            )
            this.orderService.orders.push(order)
        }
    
        return {
            route,
            unassignedOrders: routeOrders
        }
    }

    // 添加使用订单服务保存订单到备份文件的方法
    async saveOrdersToBackupFile() {
        try {
            // 获取所有订单
            const allOrders = this.orderService.getAllOrders()
      
            // 保存订单到备份文件
            const result = await this.orderService.saveSpecificOrdersToBackup(allOrders)
      
            if (result) {
                console.log(`成功将${allOrders.length}个订单保存到备份文件`)
            } else {
                console.error('保存订单到备份文件失败')
            }
      
            return result
        } catch (error) {
            console.error('保存订单到备份文件时出错:', error)
            return false
        }
    }
} 