<script setup>
import { getPaymentSettings, updatePaymentSettings } from '@/api/modules/payment'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        tax_rate: 0,
        payment_result_url: null,
        updated_at: null
    },
})

const taxRate = ref(0.0)

onMounted(() => {
    getInfo()
})

function getInfo() {
    data.value.loading = true
    getPaymentSettings().then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit() {
        updatePaymentSettings(data.value.form).then(() => {
            getInfo()
        })
    },
    reset() {
        getInfo()
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" label-width="220px">
            <el-form-item :label="$t('finance.paymentSetting.fields.taxRate')" prop="tax_rate">
                <el-input-number v-model="data.form.tax_rate" controls-position="right" :step="1" :max="99"
                    :min="0" />&ThickSpace; %
            </el-form-item>
            <el-form-item :label="$t('finance.paymentSetting.fields.resultUrl')" prop="payment_result_url">
                <el-input v-model="data.form.payment_result_url" clearable />
            </el-form-item>
            <el-form-item :label="$t('fields.updatedAt')" prop="updated_at">
                <el-input v-model="data.form.updated_at" disabled />
            </el-form-item>
        </el-form>
    </div>
</template>
