<script setup name="Login">
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import { useI18n } from 'vue-i18n'

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()
const userStore = useUserStore()

const { t } = useI18n()

const banner = new URL('../assets/images/login-banner.png', import.meta.url).href
const title = import.meta.env.VITE_APP_TITLE

const formType = ref('login')

const loginFormRef = ref()
const loginForm = ref({
    username: '',
    password: ''
})
const loginRules = ref({
    username: [
        { required: true, trigger: 'blur', message: '请输入用户名' }
    ],
    password: [
        { required: true, trigger: 'blur', message: '请输入密码' },
        { min: 6, max: 18, trigger: 'blur', message: '密码长度为6到18位' }
    ]
})
function handleLogin() {
    loginFormRef.value.validate(valid => {
        if (valid) {
            loading.value = true
            userStore.login(loginForm.value).then(() => {
                loading.value = false
                router.push(redirect.value)
            }).catch(() => {
                loading.value = false
            })
        }
    })
}

const loading = ref(false)
const passwordType = ref('password')
const redirect = ref(null)
const passwordRef = ref()

onMounted(() => {
    redirect.value = route.query.redirect?.replace('login', '') ?? '/'

})

function showPassword() {
    passwordType.value = passwordType.value === 'password' ? '' : 'password'
    nextTick(() => {
        passwordRef.value.focus()
    })
}
</script>

<template>
    <div>
        <div class="bg-banner" />
        <i18n-selector class="i18n-selector">
            <el-icon>
                <svg-icon name="i-ri:translate" />
            </el-icon>
        </i18n-selector>
        <span class="theme-selector"
            @click=" settingsStore.setColorScheme(settingsStore.app.colorScheme === 'dark' ? 'light' : 'dark')">
            <el-icon>
                <svg-icon :name="settingsStore.app.colorScheme === 'light' ? 'i-ri:sun-line' : 'i-ri:moon-line'" />
            </el-icon>
        </span>
        <div id="login-box">
            <div class="login-banner">
                <div class="logo" />
                <div class="banner-box">
                    <div class="background-box">
                        <img :src="banner" class="banner">
                        <div class="left"></div>
                        <div class="right"></div>
                    </div>
                    <div class="slogan">Dependable on time delivery</div>
                </div>
            </div>
            <el-form v-show="formType == 'login'" ref="loginFormRef" :model="loginForm" :rules="loginRules"
                class="login-form" autocomplete="on">
                <div class="title-container">
                    <h3 class="title">Fleetnow Management</h3>
                </div>
                <div>
                    <el-form-item prop="account">
                        <el-input ref="name" v-model="loginForm.username" :placeholder="t('app.username')" type="text"
                            tabindex="1" autocomplete="on">
                            <template #prefix>
                                <el-icon>
                                    <svg-icon name="i-ri:user-3-fill" />
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input ref="passwordRef" v-model="loginForm.password" :type="passwordType"
                            :placeholder="t('app.password')" tabindex="2" autocomplete="on" @keyup.enter="handleLogin">
                            <template #prefix>
                                <el-icon>
                                    <svg-icon name="i-ri:lock-2-fill" />
                                </el-icon>
                            </template>
                            <template #suffix>
                                <el-icon @click="showPassword">
                                    <svg-icon
                                        :name="passwordType === 'password' ? 'i-ri:eye-close-line' : 'i-ri:eye-line'" />
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </div>
                <el-button :loading="loading" type="primary" size="large" style="width: 100%; margin-top: 20px;"
                    @click.prevent="handleLogin">{{ t('app.login') }}</el-button>

            </el-form>
        </div>
        <Copyright v-if="settingsStore.copyright.enable" />
    </div>
</template>

<style lang="scss" scoped>
[data-mode="mobile"] {

    #login-box {
        position: relative;
        width: 100%;
        height: 100%;
        top: inherit;
        left: inherit;
        transform: translateX(0) translateY(0);
        flex-direction: column;
        justify-content: start;
        border-radius: 0;
        box-shadow: none;

        .login-banner {
            width: 100%;
            padding: 20px 0;

            .banner {
                position: relative;
                right: inherit;
                width: 229px;
                max-width: 330px;
                margin: 0 auto;
                display: inherit;
                top: inherit;
                transform: translateY(0);
            }
        }

        .login-form {
            width: 100%;
            min-height: auto;
            padding: 30px;
        }
    }

    .copyright {
        position: relative;
        bottom: 0;
        padding: 20px 0;
    }
}

:deep(input[type="password"]::-ms-reveal) {
    display: none;
}

:deep(.theme-selector) {
    position: absolute;
    z-index: 1;
    top: 20px;
    right: 50px;
    cursor: pointer;
    font-size: 18px;
    color: var(--el-text-color-primary);
}

:deep(.i18n-selector) {
    position: absolute;
    z-index: 1;
    top: 20px;
    right: 20px;
    cursor: pointer;
    font-size: 18px;
    color: var(--el-text-color-primary);
}

.bg-banner {
    position: fixed;
    z-index: 0;
    width: 100%;
    height: 100%;
    // background: radial-gradient(circle at center, var(--g-app-bg), var(--g-main-bg));
    background-color: var(--g-login-bg);
}

#login-box {
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    background-color: var(--g-app-bg);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--el-box-shadow);

    .login-banner {
        position: relative;
        width: 450px;
        background-color: var(--g-login-banner-bg);
        overflow: hidden;

        .logo {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 30px;
            height: 30px;
            border-radius: 4px;
            background: url("../assets/images/logo.png") no-repeat;
            background-size: contain;
            // box-shadow: var(--el-box-shadow-light);
        }

        .banner-box {
            display: flex;
            flex-flow: column;
            align-items: center;
            justify-content: flex-end;
            height: 100%;

            .background-box {
                width: 100%;
                display: flex;
                flex-flow: row;
                align-items: center;
                justify-content: center;

                @include position-center(y);

                .banner {
                    position: absolute;
                    z-index: 1000;
                    max-width: 229px;
                    height: 310px;
                    transform: translate(-2px, 15px);
                }

                .left {
                    width: 105px;
                    height: 210px;
                    border-radius: 105px 0 0 105px;
                    background-color: var(--g-login-banner-left-bg);
                }

                .right {
                    width: 166px;
                    height: 332px;
                    border-radius: 0 166px 166px 0;
                    background-color: var(--g-login-banner-right-bg);
                }
            }

            .slogan {
                font-weight: bolder;
                text-align: center;
                font-size: 14px;
                transform: translateY(-55px);
            }
        }
    }

    .login-form {
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 500px;
        width: 500px;
        padding: 50px;
        overflow: hidden;
        background-color: var(--g-login-form-bg);

        .title-container {
            position: relative;

            .title {
                font-size: 60px;
                color: var(--g-login-title-color);
                margin: 0 auto 30px;
                font-weight: bolder;
                font-family: Rockwell-regular, sans-serif;
            }
        }
    }

    .el-form-item {
        margin-bottom: 24px;

        :deep(.el-input) {
            height: 48px;
            line-height: inherit;
            width: 100%;

            input {
                height: 48px;
            }

            .el-input__prefix,
            .el-input__suffix {
                display: flex;
                align-items: center;
            }

            .el-input__prefix {
                left: 10px;
            }

            .el-input__suffix {
                right: 10px;
            }
        }
    }

    :deep(.el-divider__text) {
        background-color: var(--g-app-bg);
    }

    .flex-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .sub-link {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
        font-size: 14px;
        color: var(--el-text-color-secondary);

        .text {
            margin-right: 10px;
        }
    }
}

.copyright {
    position: absolute;
    bottom: 30px;
    width: 100%;
    margin: 0;
}
</style>
