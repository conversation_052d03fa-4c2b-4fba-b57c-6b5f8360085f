const Layout = () => import('@/layout/index.vue')

export default {
    path: '/statistics',
    component: Layout,
    redirect: '/statistics/general',
    name: 'settingHolidays',
    meta: {
        title: '经营数据',
        auth: ['super'],
        i18n: 'route.statistics.general'
    },
    children: [
        {
            path: 'general',
            name: 'statisticsGeneral',
            component: () => import('@/views/statistics/general/index.vue'),
            meta: {
                title: '总表',
                icon: 'bx:key',
                activeMenu: '/statistics/general',
                i18n: 'route.statistics.general',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
