import React from 'react'
import { But<PERSON>, message, Tooltip, Space } from 'antd'
import { SaveOutlined, ReloadOutlined, FileAddOutlined } from '@ant-design/icons'

const OperationPanel = ({ dispatchCoordinator, refreshData }) => {
    return (
        <div className="p-4 bg-white shadow rounded mb-4">
            <h2 className="text-lg font-semibold mb-4">操作面板</h2>
            <Space>
                {/* 刷新订单数据 */}
                <Tooltip title="刷新订单数据">
                    <Button 
                        onClick={refreshData} 
                        icon={<ReloadOutlined />}
                        type="primary"
                    >
            刷新数据
                    </Button>
                </Tooltip>
        
                {/* 加载本地数据 */}
                <Tooltip title="从本地文件加载订单数据">
                    <Button 
                        onClick={async() => {
                            try {
                                await dispatchCoordinator.orderService.fetchOrders({ useLocalFile: true })
                                message.success('已从本地文件加载订单数据')
                            } catch (error) {
                                console.error('加载本地数据失败:', error)
                                message.error('加载本地数据失败')
                            }
                        }} 
                        icon={<FileAddOutlined />}
                        type="default"
                    >
            加载示例数据
                    </Button>
                </Tooltip>
        
                {/* 备份订单按钮 */}
                <Tooltip title="将当前订单数据备份到文件">
                    <Button 
                        onClick={async() => {
                            try {
                                await dispatchCoordinator.saveOrdersToBackupFile()
                                message.success('订单已成功备份')
                            } catch (error) {
                                console.error('备份订单失败:', error)
                                message.error('备份订单失败')
                            }
                        }} 
                        icon={<SaveOutlined />}
                        type="default"
                    >
            备份订单
                    </Button>
                </Tooltip>
            </Space>
        </div>
    )
}

export default OperationPanel 