<template>
    <div class="fleet-dispatch-wrapper">
        <App />
    </div>
</template>

<script>
import { defineComponent, onMounted, onUnmounted } from 'vue'
import App from './App.vue'
import { cleanupFleetDispatch, initializeFleetDispatch } from './utils/initialize'
import useUserStore from '@/store/modules/user'
import { firebaseMessagingService } from './services/FirebaseMessagingService'

export default defineComponent({
    name: 'FleetDispatchApp',
    components: {
        App
    },
    setup() {
        const userStore = useUserStore()
        
        // 初始化过程
        onMounted(async () => {
            console.log('FleetDispatch模块已挂载')
            
            try {
                // 先确保Firebase消息服务初始化
                if (!firebaseMessagingService.initialized) {
                    firebaseMessagingService.initialize()
                    console.log('FleetDispatch组件已初始化Firebase消息服务')
                }
                
                // 然后初始化模块（这会再次初始化消息服务并注册监听器）
                await initializeFleetDispatch()
                
                // 模块初始化已经请求了FCM权限，这里不需要重复请求
                // userStore.requestFCMPermission()
            } catch (error) {
                console.error('FleetDispatch模块初始化失败:', error)
            }
        })
    
        onUnmounted(() => {
            console.log('FleetDispatch模块已卸载')
            // 清理模块资源
            cleanupFleetDispatch()
        })
    
        return {}
    }
})
</script>

<style scoped>
.fleet-dispatch-wrapper {
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
</style> 