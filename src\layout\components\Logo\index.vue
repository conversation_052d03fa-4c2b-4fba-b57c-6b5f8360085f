<script setup name="Logo">
import imgLogo from '@/assets/images/logo-fn.png';
import useSettingsStore from '@/store/modules/settings';

const settingsStore = useSettingsStore()

defineProps({
    showLogo: {
        type: Boolean,
        default: true
    },
    showTitle: {
        type: Boolean,
        default: true
    }
})

const title = ref(import.meta.env.VITE_APP_TITLE)
const logo = ref(imgLogo)

const to = computed(() => {
    let rtn = {}
    if (settingsStore.dashboard.enable) {
        rtn.name = 'dashboard'
    }
    return rtn
})
</script>

<template>
    <router-link :to="to" class="title" :class="{ 'is-link': settingsStore.dashboard.enable }" :title="title">
        <img v-if="showLogo" :src="logo" class="logo">
        <span v-if="showTitle" class="title-name">
            <!-- {{ title }}
        </span>
        <span> -->
            Management UI</span>
    </router-link>
</template>

<style lang="scss" scoped>
@import "@/assets/fonts/font.css";

.title {
    position: fixed;
    z-index: 1000;
    top: 0;
    width: inherit;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: var(--g-sidebar-logo-height);
    text-align: center;
    overflow: hidden;
    text-decoration: none;
    font-family: Rockwell-regular, sans-serif;

    &.is-link {
        cursor: pointer;
    }

    .logo {
        height: 30px;
        width: px;

        &+span {
            margin-left: 10px;
        }
    }

    span {
        display: block;
        font-weight: bold;
        color: #fff;

        @include text-overflow;
    }

    .title-name {
        font-family: Rockwell-bold, sans-serif;
        margin-right: 30px;
    }
}
</style>
