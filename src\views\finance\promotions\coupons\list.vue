<script setup name="FinancePromotionsCouponsList">
    import { Delete, Close } from '@element-plus/icons-vue'
    import eventBus from '@/utils/eventBus'
    import { usePagination } from '@/utils/composables'
    import { getCoupons, updateCouponsAvailability, deleteCoupons, batchAddTag, exportCouponsToExcel, getAllTags, getCouponExclusiveDiscounts, batchCreateCoupons, closeCoupon, } from '@/api/modules/promotions'
    import { codeStyles } from './constants.js'

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            tag__icontains: null,
        },
        // 批量操作
        batch: {
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            tag__icontains: null,
        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
        eventBus.on('get-data-list', () => {
            getDataList()
        })
    })

    onBeforeUnmount(() => {
        eventBus.off('get-data-list')
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getCoupons(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.coupon_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        router.push({
            name: 'paymentPromotionCouponDetail'
        })
    }

    function onEdit(row) {
        router.push({
            name: 'paymentPromotionCouponDetail',
            query: {
                id: row.id
            }
        })
    }

    function onAlterAvailability(row) {
        let params = {
            'ids': [row.id],
            'isAvailable': !row.is_available,
        }
        updateCouponsAvailability(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList();
            }
            data.value.loading = false;

        })
    }

    function onBatchAlterAvailability(isAvailable) {
        let params = {
            'ids': data.value.batch.selectionDataList.map(e => e.id),
            'isAvailable': isAvailable,
        }
        data.value.loading = true;
        updateCouponsAvailability(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList();
            }
            data.value.loading = false;

        })

    }

    function onBatchDelete() {
        ElMessageBox.confirm(`确认删除 ${data.value.batch.selectionDataList.length} 项吗？`, '确认信息').then(() => {
            deleteCoupons({ ids: JSON.stringify(data.value.batch.selectionDataList.map(e => e.id)) }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }

    function onDel(row) {
        ElMessageBox.confirm(`确认删除「${row.code}」吗？`, '确认信息').then(() => {
            deleteCoupons({ ids: JSON.stringify([row.id]) }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }

    function onClose(row) {
        ElMessageBox.confirm(`确认关闭「${row.code}」吗？`, '确认信息').then(() => {
            closeCoupon({ ids: row.id }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })

        closeCoupon
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.is_available) {
            return 'not-available-row'
        } else if (row.used_at != null) {
            return 'used-row'
        } else {
            return ''
        }
    }

    // Add Tag
    const tag = ref()
    const addTagDialogVisible = ref(false)

    function onBatchAddTag() {
        addTagDialogVisible.value = true;
    }

    function onCancelBatchAddTag() {
        tag.value = null;
        addTagDialogVisible.value = false;
    }

    function onConfirmBatchAddTag() {
        let params = {
            ids: data.value.batch.selectionDataList.map(e => e.id),
            tag: tag.value
        }
        data.value.loading = true
        batchAddTag(params).then((res) => {
            if (res.data.errCode == 365) {
                onCancelBatchAddTag();
                getDataList();
            }
            data.value.loading = false
        })
    }

    // Export to excel
    function exportSelectedToExcel() {
        let params = {
            ids: data.value.batch.selectionDataList.map(e => e.id),
        }
        data.value.loading = true;
        exportCouponsToExcel(params).then(res => {
            if (res.data.errCode == 365) {
                window.open(res.data.url);
            }
            data.value.loading = false;

        })

    }
    const tags = ref()
    const selectedTag = ref(
        {
            tag: null,
            isAvailable: null
        }
    )
    const exportTagToExcelDialogVisible = ref(false)

    function onExportTagToExcel() {
        getAllTags().then(res => {
            tags.value = res.data;
        })
        exportTagToExcelDialogVisible.value = true;
    }

    function onCancelExportTagToExcel() {
        selectedTag.value.tag = null;
        selectedTag.value.isAvailable = null;
        exportTagToExcelDialogVisible.value = false;
    }

    function onConfirmExportTagToExcel() {
        let params = selectedTag.value;
        data.value.loading = true
        exportCouponsToExcel(params).then(res => {
            if (res.data.errCode == 365) {
                window.open(res.data.url);
            }
            onCancelExportTagToExcel();
            data.value.loading = false;
        })
    }

    // Batch add
    const discounts = ref([])
    const batchAddParams = ref({
        codeLength: 8,
        codeStyle: 'A',
        quantity: 1,
        discount: null,
        isAvailable: false,
        tag: null,
    })
    const batchAddDialogVisible = ref(false)

    function onBatchAdd() {
        getCouponExclusiveDiscounts().then(res => {
            discounts.value = res.data;
        })
        batchAddDialogVisible.value = true;
    }

    function onCancelBatchAdd() {
        batchAddParams.value = {
            codeLength: 8,
            codeStyle: 'A',

            quantity: 1,
            discount: null,
            isAvailable: false,
            tag: null,
        }
        batchAddDialogVisible.value = false;
    }
    function onConfirmBatchAdd() {
        let params = batchAddParams.value;
        batchCreateCoupons(params);

    }


</script>

<template>
    <div>
        <page-header :title="$t('promotions.coupon.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('promotions.coupon.fields.tag')">
                                        <el-input v-model="data.search.tag__icontains"
                                            :placeholder="$t('placeholder', { field: $t('promotions.coupon.fields.tag') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button size="default" type="success" @click="onBatchAddTag">
                        {{ $t('operations.batch', { op: $t('promotions.coupon.operations.addTag') }) }}
                    </el-button>

                    <el-button-group>
                        <el-button size="default" type="success" plain @click="onBatchAlterAvailability(true)">
                            {{ $t('operations.batch', { op: $t('operations.enable') }) }}
                        </el-button> <el-button size="default" type="warning" plain
                            @click="onBatchAlterAvailability(false)">
                            {{ $t('operations.batch', { op: $t('operations.disable') }) }}
                        </el-button>
                    </el-button-group>
                    <el-button size="default" type="danger" plain @click="onBatchDelete">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>
                    <el-button size="default" type="info" @click="exportSelectedToExcel">
                        {{ $t('operations.batch', { op: $t('operations.export') }) }}
                    </el-button>
                </batch-action-bar>
                <div>
                    <el-button type="primary" @click="onCreate">
                        <template #icon>
                            <el-icon>
                                <svg-icon name="ep:plus" />
                            </el-icon>
                        </template>
                        {{ $t('operations.add') }}
                    </el-button>
                    <el-button size="default" type="primary" plain @click="onBatchAdd">
                        {{ $t('operations.batch', { op: $t('operations.add') }) }}
                    </el-button>
                    <el-button size="default" @click="onExportTagToExcel">
                        {{ $t('operations.export') }}
                    </el-button>

                </div>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="tag" :label="$t('promotions.coupon.fields.tag')" />
                <el-table-column prop="code" :label="$t('promotions.coupon.fields.code')" />
                <el-table-column prop="discount.name" :label="$t('promotions.coupon.fields.discount')" />
                <el-table-column prop="users" :label="$t('promotions.coupon.fields.users')" />
                <el-table-column prop="used" :label="$t('promotions.coupon.fields.usedBy')" />
                <el-table-column prop="used_at" :label="$t('promotions.coupon.fields.usedAt')" width="160"
                    sortable="custom" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" v-if="scope.row.can_update"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onAlterAvailability(scope.row)">
                                <svg-icon
                                    :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" v-if="scope.row.can_update" :content="$t('operations.close')"
                            placement="top-start">
                            <el-button type="warning" plain circle size="small" @click="onClose(scope.row)"
                                :icon="Close" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start"
                            v-if="scope.row.can_update">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <el-dialog v-model="addTagDialogVisible" :title="$t('promotions.coupon.operations.addTag')" width="500"
            align-center>

            <p>Add tag for {{ data.batch.selectionDataList.length }} coupons:</p>
            <el-input v-model="tag" placeholder="Please input" type="textarea" maxlength="50" clearable
                show-word-limit />

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelBatchAddTag">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmBatchAddTag">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- Export Dialog -->
        <el-dialog v-model="exportTagToExcelDialogVisible" :title="$t('operations.export')" width="500" align-center>
            <el-form :model="selectedTag" label-width="auto" style="max-width: 600px">
                <el-form-item :label="$t('promotions.coupon.fields.tag')">
                    <el-select v-model="selectedTag.tag" placeholder="please select tag">
                        <el-option v-for="tag in tags" :value="tag.tag">
                            {{ tag.tag }} ({{ tag.count }})
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('fields.isAvailable')">
                    <el-radio-group v-model="selectedTag.isAvailable">
                        <el-radio :value="null">All</el-radio>
                        <el-radio :value="true">Enabled</el-radio>
                        <el-radio :value="false">Disabled</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelExportTagToExcel">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmExportTagToExcel">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- Batch Add dialog -->
        <el-dialog v-model="batchAddDialogVisible" :title="$t('operations.batch', { op: $t('operations.add') })"
            width="500" align-center>
            <el-form :model="batchAddParams" label-width="auto" style="max-width: 600px">
                <el-form-item :label="$t('promotions.coupon.fields.codeLength')">
                    <el-input-number v-model="batchAddParams.codeLength" :min="6" :max="20" />
                </el-form-item>
                <el-form-item :label="$t('promotions.coupon.fields.codeStyle')">
                    <el-radio-group v-model="batchAddParams.codeStyle">
                        <el-radio v-for="s of codeStyles"
                            :label="$t(`promotions.coupon.selections.codeStyles.${s.label}`)" :value="s.value" />
                    </el-radio-group>
                </el-form-item>

                <el-form-item :label="$t('fields.quantity')">
                    <el-input-number v-model="batchAddParams.quantity" :min="1" />
                </el-form-item>
                <el-form-item :label="$t('promotions.coupon.fields.discount')">
                    <el-select v-model="batchAddParams.discount" placeholder="please select discount">
                        <el-option v-for="d in discounts" :value="d.id" :label="d.name" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('promotions.coupon.fields.tag')">
                    <el-input v-model="batchAddParams.tag" placeholder="Please input" type="textarea" maxlength="50"
                        clearable show-word-limit />
                </el-form-item>
                <el-form-item :label="$t('fields.isAvailable')">
                    <el-switch v-model="batchAddParams.isAvailable" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelBatchAdd">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmBatchAdd">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }

        .used-row {
            color: #aaa;
            text-decoration: line-through;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>