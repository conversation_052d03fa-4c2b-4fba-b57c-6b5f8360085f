{ // Users
    "title": "Users",
    "pages": {
        "list": "User List",
        "detail": "User Detail"
    },
    "sections": {
        "logs": "User Logs"
    },
    "fields": {
        "address": "Address",
        "buzzerCode": "Buzzer",
        "city": "City",
        "code": "User Code",
        "companyName": "Company",
        "country": "Country",
        "createdAt": "Created",
        "createdAtMax": "Created to",
        "createdAtMin": "Created from",
        "credits": "Credits",
        "creditsMax": "Max credits",
        "creditsMin": "Min credits",
        "email": "Email",
        "extensionPhone": "Ext.",
        "group": "User Group",
        "language": "Language",
        "name": "Name",
        "operationHours": "Operation hours",
        "phoneNumber": "Phone",
        "postalCode": "PC",
        "province": "Province",
        "secondaryPhone": "Aux.",
        "sex": "Sex",
        "status": "User Status",
        "type": "User Type",
        "unitNo": "Unit #",
        "updatedAt": "Last updated",
        "updatedAtMax": "Last updated to",
        "updatedAtMin": "Last updated from",
        "username": "Username",
        "openInfo": "API Info",
        "appId": "App ID",
        "appSecret": "App Secret"
    },
    "selection": {
        "userType": {
            "business": "Business User",
            "personal": "Personal User"
        },
        "status": {
            "disabled": "Disabled",
            "created": "Created",
            "valid": "Valid",
            "complete": "Complete"
        },
        "language": {
            "zh-cn": "SC",
            "zh-tw": "TC",
            "en-us": "En",
            "fr-fr": "Fr",
            "es-es": "Es",
            "all": "All"
        },
        "languageSelections": {
            "zh-cn": "Simple Chinese",
            "zh-tw": "Traditional Chinese",
            "en-us": "English",
            "fr-fr": "French",
            "es-es": "Spanish"
        },
        "noGroup": "No Group Assigned",
        "noCode": "No Code Assigned",
        "noLanguage": "No Language Selected"
    },
    "log": {
        "operation": {
            "deleted": "Deleted",
            "updated": "Updated",
            "created": "Created",
            "direct": "Direct"
        },
        "record": "Records",
        "delta": "Delta",
        "deltaMin": "Delta Min.",
        "deltaMax": "Delta Max.",
        "type": "Type",
        "creditType": {
            "consumed": "Consumed",
            "returned": "Refunded",
            "promoted": "Promotion",
            "directed": "Directed"
        }
    },
    "operations": {},
    // Group
    "userGroup": {
        "title": "User Groups",
        "instance": "User Group",
        "pages": {
            "list": "User Groups"
        },
        "fields": {
            "fee": "Excusive Fee",
            "deductionRate": "Deduction Rate",
            "feeMin": "Min fee",
            "feeMax": "Max fee",
            "rateMin": "Min Rate",
            "rateMax": "Max Rate"
        },
        "operations": {}
    },
    // Statics
    "userStatistics": {
        "title": "User Statistics",
        "sections": {
            "creationStat": "Registered Users",
            "originStat": "Users' Origin",
            "dauStat": "DAU",
            "creditStat": "Users' Credits"
        },
        "fields": {
            "total": "Total Users",
            "monthlyRegistered": "Registered in this Month",
            "avgDau": "Average DAU",
            "cities": "Covered Cities",
            "todayDelta": "Increased today {num}",
            "lastMonthDelta": "Increased from last month {num}%",
            "returned": "Returned",
            "consumed": "Consumed",
            "promoted": "Promoted",
            "creditBalance": "Credits Balance"
        }
    },
    // Open Users
    "openUser": {
        "title": "Open Users",
        "fields": {},
        "operations": {}
    }
}