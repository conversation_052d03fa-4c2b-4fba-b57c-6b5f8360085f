<script setup>
import { getCostReports } from '@/api/modules/statistics'
import StepButtons from './step_buttons.vue'
import { reimbursementStatusStyles } from '@/utils/constants'
import { currencyFormatter } from '@/utils/formatter'

const route = useRoute()

const props = defineProps({
    date: {
        type: String,
        default: null
    }
})
const emit = defineEmits(['setCost'])


const costReport = ref({
    id: route.query.id,
    date: props.date,
})


onMounted(() => {
    getData();
})

function getData() {
    let params = {
        id: costReport.value.id,
        date: costReport.value.date,
    }
    getCostReports(params).then(res => {
        costReport.value = res.data
        emit('setCost', res.data.id)
    })
}

const getCostsSummaries = ({ columns, data }) => {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = 'Total'
            return
        } else if (index > 1 && index < 5) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)
            sums[index] = currencyFormatter(null, null, sum, null)
        }
    })
    return sums
}

const getReimbursementsSummaries = ({ columns, data }) => {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = 'Total'
            return
        } else if (index >= 6 && index <= 8) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)
            sums[index] = currencyFormatter(null, null, sum, null)
        }
    })
    return sums
}


</script>
<template>
    <el-descriptions :title="$t('finance.eod.s10')" :column="3" border style="margin-bottom: 40px;">
        <el-descriptions-item :label="$t('fields.date')" label-align="left" align="center" :span="3">
            {{ costReport.date }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('fields.preTax')" label-align="left" align="center">
            {{ currencyFormatter(_, _, costReport.amount) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('fields.tax')" label-align="left" align="center">
            {{ currencyFormatter(_, _, costReport.hst) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('fields.total')" label-align="left" align="center">
            {{ currencyFormatter(_, _, costReport.total) }}
        </el-descriptions-item>
    </el-descriptions>
    <el-text size="large" tag="b">{{ $t('statistics.fields.costs') }}</el-text>
    <el-table :data="costReport.costs" border show-summary :summary-method="getCostsSummaries" stripe
        highlight-current-row style="margin-top: 20px; margin-bottom: 40px;">
        <el-table-column type="index" width="50" fixed="left" align="center" />
        <el-table-column prop="name" :label="$t('fields.name')" sortable align="center" />
        <el-table-column prop="amount" :label="$t('fields.preTax')" sortable :formatter="currencyFormatter"
            align="center" />
        <el-table-column prop="hst" :label="$t('fields.tax')" sortable :formatter="currencyFormatter" align="center" />
        <el-table-column prop="total" :label="$t('fields.total')" sortable :formatter="currencyFormatter"
            align="center" />
    </el-table>
    <el-text size="large" tag="b">{{ $t('staffs.fields.reimbursements') }}</el-text>
    <el-table :data="costReport.reimbursements" stripe highlight-current-row border show-summary
        :summary-method="getReimbursementsSummaries" style="margin-top: 20px;">
        <el-table-column type="index" align="center" fixed width="50" />
        <el-table-column prop="date" :label="$t('fields.date')" width="120" />
        <el-table-column prop="employee" :label="$t('staffs.fields.employee')" width="150" />
        <el-table-column prop="description" :label="$t('fields.desc')" show-overflow-tooltip width="150" />
        <el-table-column prop="purpose" :label="$t('staffs.reimbursement.fields.purpose')" width="150"
            show-overflow-tooltip />
        <el-table-column prop="vendor" :label="$t('staffs.reimbursement.fields.vendor')" width="150"
            show-overflow-tooltip />
        <el-table-column prop="amount" :label="$t('fields.preTax')" :formatter="currencyFormatter" />
        <el-table-column prop="hst" :label="$t('fields.tax')" :formatter="currencyFormatter" />
        <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter" />
        <el-table-column prop="status" :label="$t('fields.status')">

            <template #default="scope">
                <el-tag :type="reimbursementStatusStyles[scope.row.status]" round size="small">
                    {{ $t(`staffs.reimbursement.selections.status.${scope.row.status}`) }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="reviewed_by" :label="$t('staffs.reimbursement.fields.reviewedBy')" width="160" />
        <el-table-column prop="reviewed_at" :label="$t('staffs.reimbursement.fields.reviewedAt')" width="160"
            sortable="custom" />
        <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
    </el-table>

    <StepButtons v-bind="$attrs" />
</template>