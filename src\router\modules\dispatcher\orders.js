/*
* Author: <EMAIL>'
* Date: '2024-02-03 20:58:51'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/dispatcher/orders.js'
* File: 'orders.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/orders',
    component: Layout,
    redirect: '/dispatcher/orders/routes',
    name: 'dispatcherOrders',
    meta: {
        title: 'Orders',
        icon: 'carbon:image-service',
        auth: ['super'],
        i18n: 'route.dispatcher.console'
    },
    children: [
        {
            path: 'routes',
            name: 'dispatcherRouteList',
            component: () => import('@/views/dispatcher/routes/all/list.vue'),
            meta: {
                title: 'All Routes',
                icon: 'gis:route',
                activeMenu: '/dispatcher/orders/routes',
                i18n: 'route.dispatcher.routes',
                auth: ['super']
            }
        },
        {
            path: 'service-orders',
            name: 'dispatcherServiceOrderList',
            component: () => import('@/views/dispatcher/service_order/list.vue'),
            meta: {
                title: 'Service Orders',
                icon: 'clarity:tasks-outline-alerted',
                activeMenu: '/dispatcher/service-orders',
                i18n: 'route.dispatcher.serviceOrders',
                auth: ['super']
            }
        },
        {
            path: 'service-order-detail',
            name: 'dispatcherServiceOrderDetail',
            component: () => import('@/views/dispatcher/service_order/detail.vue'),
            meta: {
                title: 'Service Orders',
                icon: 'fluent:task-list-add-20-filled',
                activeMenu: '/dispatcher/service-orders',
                i18n: 'route.dispatcher.serviceOrders',
                auth: ['super'],
                sidebar: false,
            }
        },
        {
            path: 'list',
            name: 'dispatcherOrderList',
            component: () => import('@/views/dispatcher/orders/list.vue'),
            meta: {
                title: 'Orders',
                icon: 'clarity:tasks-outline-alerted',
                activeMenu: '/dispatcher/orders/list',
                i18n: 'route.dispatcher.orders',
                auth: ['super']
            }
        },
        {
            path: 'sort',
            name: 'dispatcherSortOrders',
            component: () => import('@/views/dispatcher/orders/sort.vue'),
            meta: {
                title: 'Sort',
                icon: 'solar:square-sort-horizontal-bold',
                activeMenu: '/dispatcher/orders/sort',
                i18n: 'route.dispatcher.sort',
                auth: ['super']
            }
        },
    ]
}
