<script setup>
import { usePagination } from '@/utils/composables'
import { getCreditLogs } from '@/api/modules/users';
import { currencyFormatter } from '@/utils/formatter'

const { pagination, getParams, onCurrentChange } = usePagination()

const props = defineProps({
    userId: {
        type: String,
        default: null
    },
    credits: {
        type: Number,
        default: 0
    }
})

const data = ref({
    loading: false,
    dataList: [],
})

const logStyles = {
    consumed: {
        type: 'success'
    },
    returned: {
        type: 'primary'
    },
    promoted: {
        type: 'danger',
        hollow: true
    },
}


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams({ userId: props.userId })
    getCreditLogs(params).then(res => {
        pagination.value.total = res.data.total
        data.value.dataList = res.data.log_list
        data.value.loading = false
    })
}

function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

</script>
<template>
    <page-main>
        <template #title>
            <el-row justify="space-between">
                <el-col :span="12">
                    {{ $t('user.fields.credits') }}
                </el-col>
                <el-col :span="12" class="credits">
                    {{ currencyFormatter(_, _, props.credits) }}
                </el-col>
            </el-row>
        </template>
        <el-timeline v-if="data.dataList.length" placement="top">
            <el-timeline-item v-for="(item, index) in data.dataList" :key="index" :timestamp="item.created_at"
                placement="top" :type="logStyles[item.type].type">
                <el-space direction="vertical" alignment="start">
                    <el-space>
                        <el-tag :type="logStyles[item.type].type" round size="small">
                            {{ $t(`user.log.creditType.${item.type}`) }}
                        </el-tag>
                        <span class="log-operator">{{ item.operator }}</span>
                        <span>{{ currencyFormatter(_, _, item.delta) }}</span>
                    </el-space>
                    <el-space>
                        <span v-for="r in item?.record" class="order-no">{{ r }}</span>
                    </el-space>


                </el-space>
            </el-timeline-item>
        </el-timeline>
        <el-empty v-else :description="$t('noData')" />
        <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
            :layout="pagination.layoutM" :hide-on-single-page="true" class="pagination" @current-change="currentChange" />
    </page-main>
</template>
<style>
.credits {
    text-align: right;
    font-weight: bolder;
    padding-right: 50px;
}

.order-no {
    font-size: 0.75em;
    color: gray;
}
</style>

