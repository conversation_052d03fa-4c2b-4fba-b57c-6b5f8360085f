<script setup nam="deliverySettingsSettings">

import {
    getDeliverySettings,
    getHolidayFees, addHolidayFee, updateHolidayFee, updateHolidayFeeAvailability, deleteHolidayFee,
    getWeekDayFees, addWeekDayFee, updateWeekDayFee, updateWeekDayFeeAvailability, deleteWeekDayFee
} from '@/api/modules/delivery';

import {
    Plus,
    Delete,
} from '@element-plus/icons-vue'

import { userTypes, weekdays } from '@/utils/constants'

import { currencyFormatter } from '@/utils/formatter';
import { updateDeliverySettings } from '@/api/modules/delivery';
import { useI18n } from 'vue-i18n';
import { getAllHolidays } from '@/api/modules/settings';
import { getUserGroups } from '@/api/modules/users';
const { t } = useI18n();

const data = ref({
    loading: false,
    settings: {
        delivery_fee: 0,
        tax_rate: 0,
        max_date_range_in_days: 31,
        user_default_selections_available_type: null,
        user_default_selections_available_groups: []
    },
    holidayFees: [],
    weekdayFees: [],
    groups: [],
})


onMounted(() => {
    getInfo()
})

// API
function getInfo() {
    data.value.loading = true
    _getSettings();
    _getHolidayFees();
    _getWeekDayFees();
    _getHolidays();
    _getUserGroups();
}

function _getSettings() {
    getDeliverySettings().then(res => {
        data.value.loading = false
        data.value.settings.delivery_fee = res.data.delivery_fee / 100
        data.value.settings.tax_rate = res.data.tax_rate / 100
        data.value.settings.max_date_range_in_days = res.data.max_date_range_in_days
        data.value.settings.user_default_selections_available_type = res.data.user_default_selections_available_type
        data.value.settings.user_default_selections_available_groups = res.data.user_default_selections_available_groups
    })
}

function _getHolidayFees() {
    getHolidayFees().then(res => {
        data.value.holidayFees = res.data
    })
}

function _getWeekDayFees() {
    getWeekDayFees().then(res => {
        data.value.weekdayFees = res.data
    })
}

function _getHolidays() {
    getAllHolidays().then(res => {
        holidays.value = res.data
    })
}

function _getUserGroups() {
    getUserGroups().then(res => {
        data.value.groups = res.data.group_list;

    })
}

// Settings
function updateSettings() {
    if (Number(data.value.settings.delivery_fee) >= 0 && Number(data.value.settings.tax_rate) >= 0)
        var params = {
            delivery_fee: parseInt(Math.round(data.value.settings.delivery_fee * 100)),
            tax_rate: parseInt(Math.round(data.value.settings.tax_rate * 100)),
            max_date_range_in_days: data.value.settings.max_date_range_in_days,
            user_default_selections_available_type: data.value.settings.user_default_selections_available_type,
            user_default_selections_available_groups: data.value.settings.user_default_selections_available_groups.map(e => e.id)
        }
    updateDeliverySettings(params).then(() => { _getSettings(); })
}

function _hideWeekdayFeeDialog() {
    showWeekDayFeeDialog.value = false;
    weekDayFee.value = {
        id: null,
        days: null,
        fee: 0,
        is_available: true
    }
}
// Holiday Fees
const holidays = ref([])
const showHolidayFeeDialog = ref(false)
const holidayFee = ref({
    id: null,
    holidays: [],
    fee: 0,
    is_available: true
})

function _showHolidayFeeDialog() {
    showHolidayFeeDialog.value = true;
}
function _hideHolidayFeeDialog() {
    showHolidayFeeDialog.value = false;
    holidayFee.value = {
        id: null,
        holidays: [],
        fee: 0,
        is_available: null
    }

}
function onUpdateHolidayFee(row) {
    holidayFee.value = {
        id: row.id,
        holidays: row.holiday,
        fee: row.fee / 100,
        is_available: row.is_available
    }
    _showHolidayFeeDialog();
}

function _updateHolidayFee() {
    let params = JSON.parse(JSON.stringify(holidayFee.value));
    params.fee = parseInt(Math.round(holidayFee.value.fee * 100))

    if (holidayFee.value.id != null) {
        updateHolidayFee(params).then(res => {
            if (res.data.errCode == 365) {
                _hideHolidayFeeDialog();
                _getHolidayFees();
            }
        })
    } else {
        if (holidayFee.value.holidays.length > 0) {
            params.holidays = holidayFee.value.holidays.map(x => x.id);
            addHolidayFee(params).then(res => {
                if (res.data.errCode == 365) {
                    _hideHolidayFeeDialog();
                    _getHolidayFees();
                }
            })
        }
    }
}

function onDeleteHolidayFee(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.holiday.name }), t('dialog.titles.confirmation')).then(() => {
        let params = { id: row.id }
        deleteHolidayFee(params).then((res) => {
            if (res.data.errCode == 365) {
                _getHolidayFees();
            }
        })
    }).catch(() => { })
}
function alterHolidayFeeAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    ElMessageBox.confirm(
        t(row.is_available ? 'dialog.messages.disable' : 'dialog.messages.enable', { name: row.holiday.name }),
        t('dialog.titles.confirmation')
    ).then(() => {
        data.value.loading = true
        updateHolidayFeeAvailability(params).then(res => {
            if (res.data.errCode === 365) {
                _getHolidayFees();
            }
            data.value.loading = false
        })
    }).catch(() => { })
}



// Weekday Fees
const showWeekDayFeeDialog = ref(false)
const weekDayFee = ref({
    id: null,
    days: null,
    fee: 0,
    is_available: true
})

function _showWeekdayFeeDialog() {
    showWeekDayFeeDialog.value = true;
}

function onUpdateWeekdayFee(row) {
    weekDayFee.value = {
        id: row.id,
        days: row.day,
        fee: row.fee / 100,
        is_available: row.is_available
    }
    _showWeekdayFeeDialog();

}

function _updateWeekDayFee() {
    let params = JSON.parse(JSON.stringify(weekDayFee.value));
    params.fee = parseInt(Math.round(weekDayFee.value.fee * 100))

    if (weekDayFee.value.id == null) {
        addWeekDayFee(params).then(res => {
            if (res.data.errCode == 365) {
                _hideWeekdayFeeDialog();
                _getWeekDayFees();

            }
        })
    } else {
        updateWeekDayFee(params).then(res => {
            if (res.data.errCode == 365) {
                _hideWeekdayFeeDialog();
                _getWeekDayFees();
            }
        })
    }
}

function onDeleteWeekdayFee(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: t(`selection.weekday.${row.day}`) }), t('dialog.titles.confirmation')).then(() => {
        let params = { id: row.id }
        deleteWeekDayFee(params).then((res) => {
            if (res.data.errCode == 365) {
                _getWeekDayFees();
            }
        })
    }).catch(() => { })
}

function alterWeekdayFeeAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    ElMessageBox.confirm(
        t(row.is_available ? 'dialog.messages.disable' : 'dialog.messages.enable', { name: t(`selection.weekday.${row.day}`) }),
        t('dialog.titles.confirmation')
    ).then(() => {
        data.value.loading = true
        updateWeekDayFeeAvailability(params).then(res => {
            if (res.data.errCode === 365) {
                _getWeekDayFees();
            }
            data.value.loading = false
        })
    }).catch(() => { })
}
</script>

<template>
    <div>
        <page-header :title="$t('delivery.settings.title')">
        </page-header>
        <page-main :title="$t('delivery.settings.sections.baseFee')">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form :model="data.settings" label-width="120px" :loading="data.loading">
                        <el-form-item :label="$t('delivery.settings.fields.deliveryFee')" prop="delivery_fee">
                            <el-input-number v-model="data.settings.delivery_fee" :min="0" :step="1" :precision="2" />
                        </el-form-item>
                        <el-form-item :label="$t('delivery.settings.fields.taxRate')" prop="tax_rate">
                            <el-input-number v-model="data.settings.tax_rate" :min="0" :max="1" :precision="2"
                                :step="0.01" />
                        </el-form-item>
                        <el-form-item :label="$t('delivery.settings.fields.maxDateRange')" prop="max_date_range">
                            <el-input-number v-model="data.settings.max_date_range_in_days" :min="7" :max="31" :step="1" />
                        </el-form-item>
                    </el-form>
                </el-col>
                <el-col :span="18">
                    <el-form :model="data.settings" :loading="data.loading">
                        <el-form-item :label="$t('delivery.settings.fields.userSelectionsAvailableGroups')">
                            <el-select v-model="data.settings.user_default_selections_available_groups" value-key="id"
                                multiple placeholder="Select">
                                <el-option v-for="item of data.groups" :label="item.name" :value="item" :key="item.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('delivery.settings.fields.userSelectionsAvailableType')" prop="user_type">
                            <el-space>
                                <el-select v-model="data.settings.user_default_selections_available_type">
                                    <el-option v-for="item in userTypes" :value="item" :key="item"
                                        :label="$t(`user.selection.userType.${item}`)">
                                        {{ $t(`user.selection.userType.${item}`) }}
                                    </el-option>
                                </el-select>
                                <el-button text @click="data.settings.user_default_selections_available_type = null"
                                    type="primary">
                                    {{ $t('operations.clear') }}
                                </el-button>
                            </el-space>
                        </el-form-item>
                    </el-form></el-col>
            </el-row>

            <el-space>
                <el-button type="primary" size="large" @click="updateSettings">{{ $t('operations.submit') }}</el-button>
                <el-button size="large" @click="_getSettings">{{ $t('operations.reset') }}</el-button>
            </el-space>
        </page-main>
        <page-main :title="$t('delivery.settings.sections.holidayFees')">
            <template #extra>
                <el-button type="primary" circle @click="_showHolidayFeeDialog" :icon="Plus"
                    v-if="data.holidayFees.length < holidays.length" />
            </template>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.holidayFees" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onUpdateHolidayFee">
                <el-table-column prop="holiday.name" :label="$t('fields.name')" />
                <el-table-column prop="holiday.description" :label="$t('fields.desc')" />
                <el-table-column prop="holiday.start_at" :label="$t('fields.period')">
                    <template #default="scope">
                        {{ scope.row.holiday.start_at }} ~ {{ scope.row.holiday.end_at }}
                    </template>
                </el-table-column>
                <el-table-column prop="fee" :label="$t('fields.fee')" :formatter="currencyFormatter" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="alterHolidayFeeAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small"
                                @click="onDeleteHolidayFee(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
        <page-main :title="$t('delivery.settings.sections.weekdayFees')">
            <template #extra>
                <el-button type="primary" circle @click="_showWeekdayFeeDialog" :icon="Plus"
                    v-if="data.weekdayFees.length < weekdays.length"></el-button>
            </template>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.weekdayFees" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onUpdateWeekdayFee">
                <el-table-column prop="day" :label="$t('fields.weekdays')">
                    <template #default="scope">
                        {{ $t(`selection.weekday.${scope.row.day}`) }}
                    </template>
                </el-table-column>
                <el-table-column prop="fee" :label="$t('fields.fee')" :formatter="currencyFormatter" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.status ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="alterWeekdayFeeAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small"
                                @click="onDeleteWeekdayFee(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
        <!-- Holiday Fee Dialog -->
        <el-dialog v-model="showHolidayFeeDialog" title="Shipping address" width="30%" center>
            <el-form :model="holidayFee">
                <el-form-item :label="$t('fields.holidays')" :label-width="120">
                    <el-select v-model="holidayFee.holidays" placeholder="Please select a holiday"
                        :multiple="holidayFee.id == null" :disabled="holidayFee.id != null" value-key="id">
                        <el-option v-for="item in holidays" :label="item.name" :value="item"
                            :disabled="data.holidayFees.map(x => x.holiday.id).includes(item.id)" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('fields.fee')" :label-width="120">
                    <el-input-number v-model="holidayFee.fee" autocomplete="off" :min="0" :step="0.1" :precision="2" />
                </el-form-item>
                <el-form-item :label="$t('fields.isAvailable')" :label-width="120">
                    <el-switch v-model="holidayFee.is_available" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="_hideHolidayFeeDialog">
                        {{ $t('operations.cancel') }}
                    </el-button>
                    <el-button type="primary" @click="_updateHolidayFee">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- Weekday Fee Dialog -->
        <el-dialog v-model="showWeekDayFeeDialog" title="Shipping address" width="30%" center>
            <el-form :model="weekDayFee">
                <el-form-item :label="$t('fields.weekdays')" :label-width="120">
                    <el-select v-model="weekDayFee.days" placeholder="Please select a zone"
                        :multiple="weekDayFee.id == null" :disabled="weekDayFee.id != null">
                        <el-option v-for="item in weekdays" :label="$t(`selection.weekday.${item}`)" :value="item"
                            :disabled="data.weekdayFees.map(x => x.day).includes(item)" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('fields.fee')" :label-width="120">
                    <el-input-number v-model="weekDayFee.fee" autocomplete="off" :min="0" :step="0.1" :precision="2" />
                </el-form-item>
                <el-form-item :label="$t('fields.isAvailable')" :label-width="120">
                    <el-switch v-model="weekDayFee.is_available" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="_hideWeekdayFeeDialog">Cancel</el-button>
                    <el-button type="primary" @click="_updateWeekDayFee">
                        Confirm
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
