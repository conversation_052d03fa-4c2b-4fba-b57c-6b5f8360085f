<script setup>
import { getUserGroups } from '@/api/modules/users';
import { getAllServicePacks, uploadAddedServiceBannerImg } from '@/api/modules/delivery'
import { Plus } from '@element-plus/icons-vue'

import { useI18n } from 'vue-i18n';
import { userTypes, weekdays } from '@/utils/constants'
import { getAllHolidays } from '@/api/modules/settings';
import ServicePackItemSelector from '@/views/components/ServicePackItems/selector.vue'

const { t } = useI18n()

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    },
    getData: {
        type: Function
    },
    onAdd: {
        type: Function
    },
    onUpdate: {
        type: Function
    },
    module: {
        type: String,
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        names: { 'en-us': '', 'zh-cn': '', 'zh-tw': '', 'fr-fr': '', 'es-es': '' },
        descriptions: { 'en-us': '', 'zh-cn': '', 'zh-tw': '', 'fr-fr': '', 'es-es': '' },
        priority: 10,
        fee: 0
    },
    rules: {
        'names.en-us': [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        'priority': [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        'fee': [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

const feeTemp = ref(0.0)
const selectedItems = ref([])
const packs = ref([])

onMounted(() => {
    setUserGroups()
    setHolidays()
    if (props.module == 'as') {
        getPacks();
    }
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getPacks() {
    getAllServicePacks().then(res => {
        packs.value = res.data
    })

}


function getInfo() {
    data.value.loading = true
    props.getData({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        data.value.form.user_group = res.data.user_group?.id
        feeTemp.value = Number((res.data.fee / 100).toFixed(2))
        selectedItems.value = JSON.parse(JSON.stringify(res.data.items))
    })
}

function setUserGroups() {
    getUserGroups().then(res => {
        userGroups.value = res.data.group_list
    })
}
function setHolidays() {
    getAllHolidays().then(res => { holidays.value = res.data }
    )
}


function handleFeeChange() {
    data.value.form.fee = Number((feeTemp.value * 100).toFixed(0))
}

defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.items = selectedItems.value;

        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    props.onAdd(params).then((res) => {
                        callback && callback()
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    props.onUpdate(params).then((res) => {
                        callback && callback()
                    })
                }
            })
        }
    }
})

function onUploaderChange(_file, fileList) {
    let params = new FormData()
    params.append('id', data.value.form.id)
    params.append('file', _file.raw)

    uploadAddedServiceBannerImg(params).then(res => {
        if (res.data.errCode == 365) {
            data.value.form.banner_img = res.data.banner_img;
        }
    })
}

const userGroups = ref([])
const holidays = ref([])

</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.code')" prop="code">
                <el-input v-model="data.form.code" :placeholder="$t('placeholder', { field: $t('fields.code') })"
                    maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.priority')" prop="priority">
                <el-input-number v-model="data.form.priority"
                    :placeholder="$t('placeholder', { field: $t('fields.priority') })" />
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')">
                <el-switch v-model="data.form.is_available" />
            </el-form-item>
            <el-divider />
            <el-row>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.name')" prop="names.en-us">
                        <el-input v-model="data.form.names['en-us']"
                            :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50" show-word-limit />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.desc')">
                        <el-input v-model="data.form.descriptions['en-us']" style="width: 100%;"
                            :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                            show-word-limit />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.nameZh')">
                        <el-input v-model="data.form.names['zh-cn']"
                            :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50" show-word-limit />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.descZh')">
                        <el-input v-model="data.form.descriptions['zh-cn']"
                            :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                            show-word-limit />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.nameTw')">
                        <el-input v-model="data.form.names['zh-tw']"
                            :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50" show-word-limit />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.descTw')">
                        <el-input v-model="data.form.descriptions['zh-tw']"
                            :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                            show-word-limit />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.nameFr')">
                        <el-input v-model="data.form.names['fr-fr']"
                            :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50" show-word-limit />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.descFr')">
                        <el-input v-model="data.form.descriptions['fr-fr']"
                            :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                            show-word-limit />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.nameEs')">
                        <el-input v-model="data.form.names['es-es']"
                            :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50" show-word-limit />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item :label="$t('fields.descEs')">
                        <el-input v-model="data.form.descriptions['es-es']"
                            :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                            show-word-limit />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item :label="$t('delivery.services.fields.banner')" v-if="data.form.id != ''">
                <el-space alignment="start">
                    <el-upload class="uploader" :show-file-list="false" :auto-upload="false" :on-change="onUploaderChange">
                        <img v-if="data.form.banner_img" :src="data.form.banner_img" class="avatar" />
                        <el-icon v-else style=" color: #8c939d; width: 150px; height: 45px; text-align: center;">
                            <Plus />
                        </el-icon>
                    </el-upload>
                    <div style="display: inline-block;">
                        <el-alert :title="`上传图片支持 png  格式，且图片大小不超过 200 kb，建议图片尺寸为 150 x 45`" type="info" show-icon
                            :closable="false" />
                    </div>
                </el-space>
            </el-form-item>
            <el-divider />
            <el-form-item :label="$t('delivery.services.fields.fee')" prop="fee">
                <el-input-number v-model="feeTemp" :step="0.1" :precision="2" controls-position="right"
                    :placeholder="$t('placeholder', { field: $t('delivery.services.fields.fee') })"
                    @change="handleFeeChange" />
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.isRecommended')">
                <el-switch v-model="data.form.is_recommended" />
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.selectedDefault')">
                <el-switch v-model="data.form.selected_default" />
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.canBeCombined')">
                <el-switch v-model="data.form.can_be_combined" />
            </el-form-item>
            <el-divider />
            <el-form-item :label="$t('user.fields.type')" prop="user_type">
                <el-space>
                    <el-select v-model="data.form.user_type">
                        <el-option v-for="item in userTypes" :value="item" :key="item"
                            :label="$t(`user.selection.userType.${item}`)">
                            {{ $t(`user.selection.userType.${item}`) }}
                        </el-option>
                    </el-select>
                    <el-button text @click="data.form.user_type = null" type="primary">
                        {{ $t('operations.clear') }}
                    </el-button>
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('user.fields.group')" prop="user_group">
                <el-space>
                    <el-select v-model="data.form.user_group">
                        <el-option v-for="item in userGroups" :value="item.id" :key="item.id" :label="item.name" />
                    </el-select>
                    <el-button text @click="data.form.user_group = null" type="primary">
                        {{ $t('operations.clear') }}
                    </el-button>
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('fields.weekdays')" prop="weekdays">
                <el-checkbox-group v-model="data.form.weekdays">
                    <el-checkbox v-for="item in weekdays" :key="item" :label="item">
                        {{ $t(`selection.weekdayShort.${item}`) }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="$t('fields.holidays')" prop="holidays" v-if="holidays.length">
                <el-checkbox-group v-model="data.form.holidays">
                    <el-checkbox v-for="item in holidays" :key="item.id" :label="item.id">
                        {{ item.name }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-divider />
            <template v-if="props.module == 'as'">
                <el-form-item :label="$t('delivery.services.fields.packs')">
                    <el-checkbox-group v-model="data.form.packs" v-if="packs.length > 0">
                        <div style="flex-direction: column;" v-for="item of packs">
                            <el-checkbox :label="item.id">
                                {{ item.names['en-us'] }}
                            </el-checkbox>
                        </div>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item :label="$t('delivery.services.fields.items')">
                    <ServicePackItemSelector v-model="selectedItems" />
                </el-form-item>
            </template>

        </el-form>
    </div>
</template>

<style lang="scss">
.uploader {

    .el-upload {
        border: 1px dashed var(--el-border-color);
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: var(--el-transition-duration-fast);
    }

    .avatar {
        width: 150px;
        height: 45px;
        display: block;
    }

    .el-upload:hover {
        border-color: var(--el-color-primary);
    }
}
</style>
