/*
* Author: <EMAIL>'
* Date: '2024-03-04 00:28:20'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/staffs/reimbursement.js'
* File: 'reimbursement.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/staffs/reimbursements',
    component: Layout,
    redirect: '/staffs/reimbursements/list',
    name: 'reimbursements',
    meta: {
        title: '报销管理',
        icon: 'material-symbols:punch-clock-outline',
        auth: ['super'],
        i18n: 'route.staffs.reimbursements.title'
    },
    children: [
        {
            path: 'list',
            name: 'reimbursementsList',
            component: () => import('@/views/staffs/reimbursements/list.vue'),
            meta: {
                title: '报销单',
                icon: 'ep:odometer',
                activeMenu: '/staffs/reimbursements/list',
                i18n: 'route.staffs.reimbursements.reimbursements',
                auth: ['super']
            }
        },
    ]
}
