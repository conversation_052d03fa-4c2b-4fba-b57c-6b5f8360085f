{"discounts": {"title": "Discounts", "fields": {"addedServicesExcluded": "Added Services Excluded", "addedServicesIncluded": "Added Services Included", "availableFrom": "Available From", "availableUntil": "Available Until", "canBeCombined": "Can be combined", "deliveryAddressesExcluded": "Delivery Addresses Excluded", "deliveryAddressesIncluded": "Delivery Addresses Included", "deliveryHolidaysExcluded": "Delivery Holidays Excluded", "deliveryHolidaysIncluded": "Delivery Holidays Included", "deliveryPostalCodesExcluded": "Delivery Postal Codes Excluded", "deliveryPostalCodesIncluded": "Delivery Postal Codes Included", "deliveryTimesExcluded": "Delivery Times Excluded", "deliveryTimesIncluded": "Delivery Times Included", "deliveryWeekdaysExcluded": "Delivery Weekdays Excluded", "deliveryWeekdaysIncluded": "Delivery Weekdays Included", "deliveryZonesExcluded": "Delivery Zones Excluded", "deliveryZonesIncluded": "Delivery Zones Included", "description": "Description", "descriptionEs": "Description(ES)", "descriptionFr": "Description(FR)", "descriptionZhCn": "Description(SC)", "descriptionZhTw": "Description(TC)", "discountType": "Discount Type", "couponExclusive": "Coupon Exclusive", "fixedAmount": "Fixed Amount", "isAvailable": "Is available", "isExclusiveForCoupon": "Coupon exclusive", "isReturnedToCredits": "Return to credits", "languagesExcluded": "Languages Excluded", "languagesIncluded": "Languages Included", "maxUsageCount": "<PERSON> Count", "maxUsageCountPerUser": "Max Usage Count Per User", "name": "Name", "orderConditions": "Order's Conditions", "orderType": "Order Type", "packageConditions": "Package's Conditions", "packageTypesExcluded": "Package Types Excluded", "packageTypesIncluded": "Package Types Included", "paymentMethodsExcluded": "Payment Methods Excluded", "paymentMethodsIncluded": "Payment Methods Included", "percentage": "Percentage", "pickupAddressesExcluded": "Pickup Addresses Excluded", "pickupAddressesIncluded": "Pickup Addresses Included", "pickupHolidaysExcluded": "Pickup Holidays Excluded", "pickupHolidaysIncluded": "Pickup Holidays Included", "pickupPostalCodesExcluded": "Pickup Postal Codes Excluded", "pickupPostalCodesIncluded": "Pickup Postal Codes Included", "pickupTimesExcluded": "Pickup Times Excluded", "pickupTimesIncluded": "Pickup Times Included", "pickupWeekdaysExcluded": "Pickup Weekdays Excluded", "pickupWeekdaysIncluded": "Pickup Weekdays Included", "pickupZonesExcluded": "Pickup Zones Excluded", "pickupZonesIncluded": "Pickup Zones Included", "refundRule": "Refund Rule", "samePatchCount": "Same Patch Order Count", "samePatchOrderCountMax": "Same Patch Order Count Max", "samePatchOrderCountMin": "Same Patch Order Count Min", "sizeWeightsExcluded": "Size & Weights Excluded", "sizeWeightsIncluded": "Size & Weights Included", "subtotal": "Subtotal", "subtotalMax": "Subtotal Max", "subtotalMin": "Subtotal Min", "total": "Total", "totalMax": "Total max", "totalMin": "Total min", "totalOrderCountMax": "Total order count max", "totalOrderCountMin": "Total order count min", "totalOrders": "Total Orders", "totalSubtotal": "Total Subtotal", "totalSubtotalMax": "Total subtotal max", "totalSubtotalMin": "Total subtotal min", "userConditions": "User's Conditions", "userGroups": "User groups", "userRegisteredAfter": "User registered after", "userRegisteredBefore": "User registered before", "userType": "User type"}, "selections": {"discountType": {"instant": "Instant", "completion": "Completion"}, "orderTypes": {"PAYMENT": "Payment", "DELIVERY": "Delivery", "RECHARGEMENT": "Rechargement"}, "refundRule": {"all": "All", "none": "None", "some": "Some", "any": "Any"}}}, "coupon": {"title": "Coupons", "fields": {"tag": "Tag", "code": "Code", "discount": "Discount", "users": "Users", "usedBy": "Used by", "usedAt": "Used at", "codeLength": "Code Length", "codeStyle": "Code Style"}, "selections": {"codeStyles": {"letters": "Letters", "digits": "Digits", "alphanumeric": "Letter & Digits"}}, "operations": {"addTag": "Add Tag"}}, "logs": {"title": "Usage Logs", "coupon": "Coupon", "couponUsage": "Coupon Usage Logs", "discountUsage": "Discount Usage Logs", "discount": "Discount"}}