<script setup name="DeliverySuppliersList">
import { Delete } from '@element-plus/icons-vue'
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import {
    getDeliverySuppliers,
    updateDeliverySupplierAvailabilityPriority,
    deleteDeliverySupplier
} from '@/api/modules/delivery'
import { phoneNumberFormatter } from '@/utils/formatter'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        name__icontains: null,
    },
    // 批量操作
    // 列表数据
    dataList: []
})
// const searchBarCollapsed = ref(0)


// // Filters
// function resetFilters() {
//     data.value.search =
//     {
//         name__icontains: null,
//     }
//     currentChange()
// }


onMounted(() => {
    getDataList()
    if (data.value.formMode === 'router') {
        eventBus.on('get-data-list', () => {
            getDataList()
        })
    }
})

onBeforeUnmount(() => {
    if (data.value.formMode === 'router') {
        eventBus.off('get-data-list')
    }
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getDeliverySuppliers(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.supplier_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'deliverySupplierDetail'
    })
}

function onEdit(row) {
    router.push({
        name: 'deliverySupplierDetail',
        query: {
            id: row.id
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deleteDeliverySupplier({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onUpdatePriority(row) {
    let params = {
        id: row.id,
        priority: row.priority
    }
    updateDeliverySupplierAvailabilityPriority(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}


function onUpdateAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    updateDeliverySupplierAvailabilityPriority(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('delivery.suppliers.title')" />
        <!-- <page-main>
                                                    <el-collapse v-model="searchBarCollapsed">
                                                        <el-collapse-item :title="$t('fields.filters')" name="1">
                                                            <search-bar>
                                                                <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                                                                    <el-row :gutter="20">
                                                                        <el-col :span="6">
                                                                            <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                                                                <el-input v-model="data.search.user__name__icontains"
                                                                                    :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                                                                    clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                                                            </el-form-item>
                                                                        </el-col>
                                                                    </el-row>
                                                                    <el-form-item>
                                                                        <el-button type="warning" @click="resetFilters()" plain>
                                                                            <template #icon>
                                                                                <el-icon>
                                                                                    <svg-icon name="ep:refresh-left" />
                                                                                </el-icon>
                                                                            </template>
                                                                            Reset
                                                                        </el-button>
                                                                        <el-button type="primary" @click="currentChange()">
                                                                            <template #icon>
                                                                                <el-icon>
                                                                                    <svg-icon name="ep:search" />
                                                                                </el-icon>
                                                                            </template>
                                                                            Filter
                                                                        </el-button>
                                                                    </el-form-item>
                                                                </el-form>
                                                            </search-bar>
                                                        </el-collapse-item>
                                                    </el-collapse>
                                                </page-main> -->
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange">
                <el-table-column prop="priority" :label="$t('fields.priority')" align="center" width="80">
                    <template #default="scope">
                        <el-input v-model="scope.row.priority" :input-style="{ 'text-align': 'center' }"
                            @blur="onUpdatePriority(scope.row)" @keyup.enter="onUpdatePriority(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="sku" :label="$t('fields.sku')" width="100" />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="contact.name" :label="$t('fields.contact')" width="160" align="center" />
                <el-table-column prop="phone_number" :label="$t('user.fields.phoneNumber')" width="160" align="center">
                    <template #default="scope">
                        {{ phoneNumberFormatter(scope.row.phone_number) }}
                    </template>
                </el-table-column>
                <el-table-column prop="email" :label="$t('user.fields.email')" width="160" align="center"
                    show-overflow-tooltip />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>

                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
