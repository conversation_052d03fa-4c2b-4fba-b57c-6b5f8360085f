<script setup name="DispatcherDriversList">
import { getDrivers } from '@/api/modules/staffs'
import { <PERSON><PERSON><PERSON>, Scissor, Link } from '@element-plus/icons-vue'

import { getEliteExtraDrivers, loadEliteExtraDrivers, bindEliteExtraDrivers } from '@/api/modules/dispatcher'

import EmployeeSelector from '@/views/staffs/components/employee_selector.vue'

const data = ref({
    loading: false,
    dataList: []
})

const drivers = ref([])



onMounted(() => {
    getAllDrivers();
    getDataList();
})

function getDataList() {
    data.value.loading = true
    getEliteExtraDrivers().then(res => {
        data.value.loading = false
        data.value.dataList = res.data
    })
}

function getAllDrivers() {
    // 传递空参数对象，确保 API 接受参数
    getDrivers({}).then(res => { drivers.value = res.data })
}

function onLoad() {
    loadEliteExtraDrivers().then(res => {
        if (res.data.errCode == 365) {
            getDataList();
        }
    })
}

function onUnbind(row) {
    let params = {
        id: row.id,
    }
    bindEliteExtraDrivers(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList();
        }
    })
}

function onEdit(row) {
    selectedDriver.value = row.related_to?.id
    driverToEdit.value = { id: row.id, name: row.name }
    bindDialogVisible.value = true
}

const selectedDriver = ref()
const driverToEdit = ref({})

const bindDialogVisible = ref(false)

function onConfirmBind() {
    let params = {
        id: driverToEdit.value.id,
        driver_id: selectedDriver.value
    }
    bindEliteExtraDrivers(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList();
        }
    })

    onCancelBind();
}

function onCancelBind() {
    selectedDriver.value = null
    driverToEdit.value = {}
    bindDialogVisible.value = false
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.related_to) {
        return 'not-available-row'
    } else {
        return ''
    }
}
</script>

<template>
    <div>
        <page-header :title="$t('delivery.worksheets.fields.driver')" />
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onLoad">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.load') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-class-name="tableRowClassName">
                <el-table-column type="index" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="related_to.name" :label="$t('fields.name')" />
                <el-table-column prop="bucket_sn" :label="$t('delivery.drivers.fields.bucketName')" />
                <el-table-column :label="$t('fields.operations')" width="250" align="right">
                    <template #default="scope">
                        <el-tooltip v-if="scope.row.related_to" class="box-item" :content="$t('operations.edit')"
                            placement="top-start">
                            <el-button type="primary" plain :icon="EditPen" circle size="small"
                                @click="onEdit(scope.row)" />
                        </el-tooltip>
                        <el-tooltip v-if="scope.row.related_to" class="box-item" :content="$t('operations.unbind')"
                            placement="top-start">
                            <el-button type="danger" :icon="Scissor" circle size="small" @click="onUnbind(scope.row)" />
                        </el-tooltip>
                        <el-tooltip v-else class="box-item" :content="$t('operations.bind')" placement="top-start">
                            <el-button type="success" :icon="Link" circle size="small" @click="onEdit(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
        <el-dialog v-model="bindDialogVisible" title="Bind Driver" width="500">

            <el-form :model="form">
                <el-form-item :label="`Bind ${driverToEdit.name}  to:`" :label-width="formLabelWidth">
                    <EmployeeSelector v-model="selectedDriver" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelBind">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmBind">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: #bbb;
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>