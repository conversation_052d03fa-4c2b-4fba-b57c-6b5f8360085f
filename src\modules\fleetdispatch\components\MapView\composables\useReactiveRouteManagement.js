import { ref, computed, watch, nextTick } from 'vue'
import { useOrderStore } from '../../../stores/order'
import { useRouteStore } from '../../../stores/route'
import { useAddressStore } from '../../../stores/address'
import { useTimeStore } from '../../../stores/time'

/**
 * 响应式路线管理系统
 * 实现订单与路线的深度绑定，支持实时更新
 */
export function useReactiveRouteManagement(map, isMapLoaded) {
    const orderStore = useOrderStore()
    const routeStore = useRouteStore()
    const addressStore = useAddressStore()
    const timeStore = useTimeStore()

    // 路线类型
    const routeType = ref('direct') // 'direct' | 'realistic'

    // 路线数据缓存
    const routeSegmentsCache = ref(new Map()) // routeId -> segments[]
    const routeGeometryCache = ref(new Map()) // routeId -> geometry

    // 正在处理的路线
    const processingRoutes = ref(new Set())

    // Mapbox API 配置
    const MAPBOX_API_KEY = 'pk.eyJ1IjoiaGFpcm9uZyIsImEiOiJjbTJkdWVqZGcwMGNzMmtzNzVqbGNqOGNuIn0.Q7E_5Ej_Ej_Ej_Ej_Ej_Ej'

    /**
     * 计算路线的起点和终点
     */
    const getRouteEndpoints = (route) => {
        const shiftName = timeStore.selectedShift?.name || ''
        const warehouseAddressId = addressStore.warehouse?.id || 'CA|CP|B|10440098'

        // 获取司机地址（如果有的话）
        const driver = window.driverStore?.getDriverById(route.driverId || route.driver)
        const driverAddressId = driver?.address_id || warehouseAddressId

        let startPoint = null
        let endPoint = null

        if (shiftName.includes('AM')) {
            // 早班: 司机家 -> 仓库
            startPoint = driverAddressId
            endPoint = warehouseAddressId
        } else if (shiftName.includes('PM')) {
            // 下午班: 仓库 -> 仓库
            startPoint = warehouseAddressId
            endPoint = warehouseAddressId
        } else if (shiftName.includes('NT')) {
            // 晚班: 仓库 -> 司机家
            startPoint = warehouseAddressId
            endPoint = driverAddressId
        } else {
            // 默认: 仓库 -> 仓库
            startPoint = warehouseAddressId
            endPoint = warehouseAddressId
        }

        return { startPoint, endPoint }
    }

    /**
     * 获取地址坐标
     */
    const getAddressCoordinates = (addressId) => {
        if (!addressId) return null

        const coords = addressStore.getCoordinatesByAddressId(addressId)
        if (coords && Array.isArray(coords) && coords.length === 2) {
            return [coords[1], coords[0]] // [lng, lat] for Mapbox
        }
        return null
    }

    /**
     * 从 Mapbox API 获取路段数据
     */
    const fetchRouteSegment = async (startCoords, endCoords) => {
        if (!startCoords || !endCoords) return null

        const coordinates = `${startCoords[0]},${startCoords[1]};${endCoords[0]},${endCoords[1]}`
        const url = `https://api.mapbox.com/directions/v5/mapbox/driving/${coordinates}?geometries=geojson&access_token=${MAPBOX_API_KEY}&overview=full&steps=false`

        try {
            const response = await fetch(url)
            if (!response.ok) {
                throw new Error(`Mapbox API请求失败: ${response.status}`)
            }

            const data = await response.json()
            if (data.routes && data.routes.length > 0) {
                const route = data.routes[0]
                return {
                    distance: route.distance, // 米
                    duration: route.duration, // 秒
                    geometry: route.geometry,
                    coordinates: route.geometry.coordinates
                }
            }
        } catch (error) {
            console.error('获取路段数据失败:', error)
        }

        return null
    }

    /**
     * 计算路线的所有路段
     */
    const calculateRouteSegments = async (routeId) => {
        console.log(`开始计算路线 ${routeId} 的路段`)

        const route = routeStore.routes.find(r => r.id === routeId)
        if (!route) {
            console.error(`路线 ${routeId} 不存在`)
            return null
        }

        // 获取订单列表（按 stop_no 排序）
        const orders = orderStore.getOrdersByRouteNumber(routeId)
            .filter(order => order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2)
            .sort((a, b) => (a.stop_no || 0) - (b.stop_no || 0))

        if (orders.length === 0) {
            console.log(`路线 ${routeId} 没有有效订单`)
            return null
        }

        // 获取起点和终点
        const { startPoint, endPoint } = getRouteEndpoints(route)
        const startCoords = getAddressCoordinates(startPoint)
        const endCoords = getAddressCoordinates(endPoint)

        console.log(`路线 ${routeId} 起点:`, startPoint, startCoords)
        console.log(`路线 ${routeId} 终点:`, endPoint, endCoords)
        console.log(`路线 ${routeId} 订单数量:`, orders.length)

        // 构建所有路段的起止点
        const segments = []
        const allCoordinates = []

        // 1. 起点 -> 第一个订单
        if (startCoords) {
            const firstOrderCoords = [orders[0].lng_lat[1], orders[0].lng_lat[0]] // [lng, lat]
            segments.push({
                type: 'start_to_first',
                startCoords,
                endCoords: firstOrderCoords,
                startAddress: startPoint,
                endAddress: orders[0].address_id,
                orderIndex: 0 // 这个路段的距离/时间属于第一个订单
            })
            allCoordinates.push(...[startCoords, firstOrderCoords])
        }

        // 2. 订单之间的路段
        for (let i = 0; i < orders.length - 1; i++) {
            const currentOrderCoords = [orders[i].lng_lat[1], orders[i].lng_lat[0]]
            const nextOrderCoords = [orders[i + 1].lng_lat[1], orders[i + 1].lng_lat[0]]

            segments.push({
                type: 'order_to_order',
                startCoords: currentOrderCoords,
                endCoords: nextOrderCoords,
                startAddress: orders[i].address_id,
                endAddress: orders[i + 1].address_id,
                orderIndex: i + 1 // 这个路段的距离/时间属于下一个订单
            })
            allCoordinates.push(...[currentOrderCoords, nextOrderCoords])
        }

        // 3. 最后一个订单 -> 终点
        if (endCoords && orders.length > 0) {
            const lastOrderCoords = [orders[orders.length - 1].lng_lat[1], orders[orders.length - 1].lng_lat[0]]
            segments.push({
                type: 'last_to_end',
                startCoords: lastOrderCoords,
                endCoords,
                startAddress: orders[orders.length - 1].address_id,
                endAddress: endPoint,
                orderIndex: -1 // 这个路段不属于任何订单（到终点）
            })
            allCoordinates.push(...[lastOrderCoords, endCoords])
        }

        console.log(`路线 ${routeId} 总共 ${segments.length} 个路段`)

        // 并行获取所有路段的数据
        const segmentPromises = segments.map(async (segment, index) => {
            console.log(`获取路段 ${index + 1}/${segments.length}: ${segment.type}`)
            const segmentData = await fetchRouteSegment(segment.startCoords, segment.endCoords)

            return {
                ...segment,
                index,
                distance: segmentData?.distance || 0,
                duration: segmentData?.duration || 0,
                geometry: segmentData?.geometry || null,
                coordinates: segmentData?.coordinates || []
            }
        })

        const segmentResults = await Promise.all(segmentPromises)

        // 过滤掉失败的路段
        const validSegments = segmentResults.filter(segment => segment.coordinates.length > 0)

        console.log(`路线 ${routeId} 成功获取 ${validSegments.length}/${segments.length} 个路段`)

        return {
            routeId,
            segments: validSegments,
            orders,
            totalDistance: validSegments.reduce((sum, seg) => sum + seg.distance, 0),
            totalDuration: validSegments.reduce((sum, seg) => sum + seg.duration, 0),
            allCoordinates
        }
    }

    /**
     * 更新订单的路段信息
     */
    const updateOrderSegmentInfo = (routeId, segmentData) => {
        if (!segmentData || !segmentData.segments) return

        console.log(`更新路线 ${routeId} 的订单路段信息`)

        // 为每个订单分配对应的路段信息
        segmentData.orders.forEach((order, orderIndex) => {
            // 找到属于这个订单的路段
            const orderSegment = segmentData.segments.find(seg => seg.orderIndex === orderIndex)

            if (orderSegment) {
                // 更新订单的路段信息
                order.segment_distance = orderSegment.distance
                order.segment_duration = orderSegment.duration
                order.segment_type = orderSegment.type

                console.log(`订单 ${order.id} (位置 ${orderIndex + 1}): ${(orderSegment.distance / 1000).toFixed(2)}km, ${(orderSegment.duration / 60).toFixed(1)}min`)
            } else {
                // 清除路段信息
                order.segment_distance = 0
                order.segment_duration = 0
                order.segment_type = null

                console.log(`订单 ${order.id} (位置 ${orderIndex + 1}): 无路段信息`)
            }
        })

        // 触发订单更新事件，通知 DriverRoutePanel 更新显示
        if (window.eventBus) {
            window.eventBus.emit('ROUTE_SEGMENTS_UPDATED', {
                routeId,
                orders: segmentData.orders,
                totalDistance: segmentData.totalDistance,
                totalDuration: segmentData.totalDuration,
                timestamp: Date.now()
            })
        }
    }

    /**
     * 在地图上绘制路线
     */
    const drawRouteOnMap = (routeId, segmentData) => {
        if (!map.value || !isMapLoaded.value || !segmentData) return

        console.log(`在地图上绘制路线 ${routeId}`)

        // 清除现有路线
        clearRouteFromMap(routeId)

        const route = routeStore.routes.find(r => r.id === routeId)
        const routeColor = route?.color || '#3388ff'

        if (routeType.value === 'realistic' && segmentData.segments.length > 0) {
            // 绘制真实路线
            drawRealisticRouteOnMap(routeId, segmentData, routeColor)
        } else if (routeType.value === 'direct' && segmentData.orders.length > 0) {
            // 绘制直线路线
            drawDirectRouteOnMap(routeId, segmentData, routeColor)
        }
    }

    /**
     * 绘制真实路线
     */
    const drawRealisticRouteOnMap = (routeId, segmentData, routeColor) => {
        // 合并所有路段的坐标
        const allCoordinates = []
        segmentData.segments.forEach(segment => {
            if (segment.coordinates && segment.coordinates.length > 0) {
                allCoordinates.push(...segment.coordinates)
            }
        })

        if (allCoordinates.length === 0) {
            console.warn(`路线 ${routeId} 没有有效的坐标数据`)
            return
        }

        const sourceId = `route-realistic-${routeId}`
        const layerId = `route-realistic-layer-${routeId}`

        try {
            // 添加数据源
            map.value.addSource(sourceId, {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: {
                        routeId,
                        totalDistance: segmentData.totalDistance,
                        totalDuration: segmentData.totalDuration
                    },
                    geometry: {
                        type: 'LineString',
                        coordinates: allCoordinates
                    }
                }
            })

            // 添加图层
            map.value.addLayer({
                id: layerId,
                type: 'line',
                source: sourceId,
                layout: {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                paint: {
                    'line-color': routeColor,
                    'line-width': 4,
                    'line-opacity': 0.8
                }
            })

            console.log(`真实路线 ${routeId} 绘制完成，坐标点数: ${allCoordinates.length}`)
        } catch (error) {
            console.error(`绘制真实路线 ${routeId} 失败:`, error)
        }
    }

    /**
     * 绘制直线路线
     */
    const drawDirectRouteOnMap = (routeId, segmentData, routeColor) => {
        const { startPoint, endPoint } = getRouteEndpoints(routeStore.routes.find(r => r.id === routeId))
        const startCoords = getAddressCoordinates(startPoint)
        const endCoords = getAddressCoordinates(endPoint)

        const coordinates = []

        // 添加起点
        if (startCoords) {
            coordinates.push(startCoords)
        }

        // 添加所有订单坐标
        segmentData.orders.forEach(order => {
            if (order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2) {
                coordinates.push([order.lng_lat[1], order.lng_lat[0]]) // [lng, lat]
            }
        })

        // 添加终点
        if (endCoords) {
            coordinates.push(endCoords)
        }

        if (coordinates.length < 2) {
            console.warn(`路线 ${routeId} 坐标点不足，无法绘制直线`)
            return
        }

        const sourceId = `route-direct-${routeId}`
        const layerId = `route-direct-layer-${routeId}`

        try {
            // 添加数据源
            map.value.addSource(sourceId, {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: {
                        routeId
                    },
                    geometry: {
                        type: 'LineString',
                        coordinates
                    }
                }
            })

            // 添加图层
            map.value.addLayer({
                id: layerId,
                type: 'line',
                source: sourceId,
                layout: {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                paint: {
                    'line-color': routeColor,
                    'line-width': 3,
                    'line-opacity': 0.7
                }
            })

            console.log(`直线路线 ${routeId} 绘制完成，坐标点数: ${coordinates.length}`)
        } catch (error) {
            console.error(`绘制直线路线 ${routeId} 失败:`, error)
        }
    }

    /**
     * 从地图清除路线
     */
    const clearRouteFromMap = (routeId) => {
        if (!map.value || !isMapLoaded.value) return

        const layerIds = [
            `route-realistic-layer-${routeId}`,
            `route-direct-layer-${routeId}`
        ]

        const sourceIds = [
            `route-realistic-${routeId}`,
            `route-direct-${routeId}`
        ]

        layerIds.forEach(layerId => {
            if (map.value.getLayer(layerId)) {
                map.value.removeLayer(layerId)
            }
        })

        sourceIds.forEach(sourceId => {
            if (map.value.getSource(sourceId)) {
                map.value.removeSource(sourceId)
            }
        })
    }

    /**
     * 清除所有路线
     */
    const clearAllRoutes = () => {
        if (!map.value || !isMapLoaded.value) return

        const layers = map.value.getStyle().layers
        const sources = map.value.getStyle().sources

        // 清除所有路线图层
        layers.forEach(layer => {
            if (layer.id.includes('route-realistic-layer-') || layer.id.includes('route-direct-layer-')) {
                if (map.value.getLayer(layer.id)) {
                    map.value.removeLayer(layer.id)
                }
            }
        })

        // 清除所有路线数据源
        Object.keys(sources).forEach(sourceId => {
            if (sourceId.includes('route-realistic-') || sourceId.includes('route-direct-')) {
                if (map.value.getSource(sourceId)) {
                    map.value.removeSource(sourceId)
                }
            }
        })

        // 清除缓存
        routeSegmentsCache.value.clear()
        routeGeometryCache.value.clear()
        processingRoutes.value.clear()

        console.log('已清除所有路线')
    }

    /**
     * 处理单个路线的完整更新流程
     */
    const processRoute = async (routeId) => {
        if (processingRoutes.value.has(routeId)) {
            console.log(`路线 ${routeId} 正在处理中，跳过`)
            return
        }

        console.log(`开始处理路线 ${routeId}`)
        processingRoutes.value.add(routeId)

        try {
            // 1. 计算路段数据
            const segmentData = await calculateRouteSegments(routeId)

            if (segmentData) {
                // 2. 缓存路段数据
                routeSegmentsCache.value.set(routeId, segmentData)

                // 3. 更新订单的路段信息
                updateOrderSegmentInfo(routeId, segmentData)

                // 4. 在地图上绘制路线
                drawRouteOnMap(routeId, segmentData)

                console.log(`路线 ${routeId} 处理完成`)
            } else {
                console.warn(`路线 ${routeId} 路段计算失败`)
            }
        } catch (error) {
            console.error(`处理路线 ${routeId} 时出错:`, error)
        } finally {
            processingRoutes.value.delete(routeId)
        }
    }

    /**
     * 处理多个路线
     */
    const processRoutes = async (routeIds) => {
        console.log(`开始批量处理 ${routeIds.length} 条路线`)

        const promises = routeIds.map(routeId => processRoute(routeId))
        await Promise.all(promises)

        console.log(`批量处理完成`)
    }

    /**
     * 处理所有活动路线
     */
    const processAllRoutes = async () => {
        const activeRoutes = routeStore.routes.filter(route => route.status === 'active')
        const routeIds = activeRoutes.map(route => route.id)

        if (routeIds.length > 0) {
            await processRoutes(routeIds)
        }
    }

    /**
     * 响应式监听订单变更
     */
    const setupReactiveUpdates = () => {
        // 监听路线类型变化
        watch(routeType, async (newType, oldType) => {
            if (newType !== oldType) {
                console.log(`路线类型从 ${oldType} 变为 ${newType}`)

                // 清除所有路线
                clearAllRoutes()

                // 重新处理所有路线
                await processAllRoutes()
            }
        })

        // 监听订单变更（通过事件总线）
        if (window.eventBus) {
            // 监听拖拽变更
            window.eventBus.on('MAP_REFRESH', async (data) => {
                if (data.routeId && (data.isDragging || data.needRedrawRoute)) {
                    console.log(`收到路线 ${data.routeId} 的更新请求`)
                    await processRoute(data.routeId)
                }
            })

            // 监听路线段更新请求
            window.eventBus.on('ROUTE_SEGMENTS_UPDATE_REQUEST', async (data) => {
                if (data.routeId) {
                    console.log(`收到路线 ${data.routeId} 的路段更新请求`)
                    await processRoute(data.routeId)
                }
            })
        }
    }

    /**
     * 获取路线的路段信息（用于显示）
     */
    const getRouteSegmentInfo = (routeId) => {
        return routeSegmentsCache.value.get(routeId) || null
    }

    /**
     * 获取订单的路段信息
     */
    const getOrderSegmentInfo = (orderId) => {
        // 查找包含该订单的路线
        for (const [routeId, segmentData] of routeSegmentsCache.value.entries()) {
            const order = segmentData.orders.find(o => o.id === orderId)
            if (order) {
                return {
                    routeId,
                    distance: order.segment_distance || 0,
                    duration: order.segment_duration || 0,
                    type: order.segment_type || null
                }
            }
        }
        return null
    }

    /**
     * 强制刷新路线
     */
    const refreshRoute = async (routeId) => {
        console.log(`强制刷新路线 ${routeId}`)

        // 清除缓存
        routeSegmentsCache.value.delete(routeId)
        routeGeometryCache.value.delete(routeId)

        // 清除地图显示
        clearRouteFromMap(routeId)

        // 重新处理
        await processRoute(routeId)
    }

    /**
     * 强制刷新所有路线
     */
    const refreshAllRoutes = async () => {
        console.log('强制刷新所有路线')

        // 清除所有缓存和地图显示
        clearAllRoutes()

        // 重新处理所有路线
        await processAllRoutes()
    }

    // 初始化响应式更新
    setupReactiveUpdates()

    return {
        // 状态
        routeType,
        routeSegmentsCache,
        routeGeometryCache,
        processingRoutes,

        // 核心功能
        calculateRouteSegments,
        updateOrderSegmentInfo,
        drawRouteOnMap,
        clearRouteFromMap,
        clearAllRoutes,

        // 路线处理
        processRoute,
        processRoutes,
        processAllRoutes,

        // 信息获取
        getRouteSegmentInfo,
        getOrderSegmentInfo,
        getRouteEndpoints,
        getAddressCoordinates,

        // 刷新功能
        refreshRoute,
        refreshAllRoutes,

        // 工具函数
        fetchRouteSegment,
        drawRealisticRouteOnMap,
        drawDirectRouteOnMap,
        setupReactiveUpdates
    }
}
