<script setup>
import { getCompanyInfo, updateCompanyInfo } from '@/api/modules/settings';


const formRef = ref()
const data = ref({
    loading: false,
    form: {
        title: ''
    },
    rules: {
        company_name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        address: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        address2: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        phone: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        email: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getCompanyInfo().then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function onUpdate() {
    formRef.value.validate(valid => {
        if (valid) {
            updateCompanyInfo(data.value.form).then((res) => {
                if (res.data.errCode == 365) { callback && callback() }
            })
        }
    })

}
</script>

<template>
    <div>
        <page-header title="公司信息设置" />
        <page-main>
            <el-row>
                <el-col :span="16">
                    <div v-loading="data.loading">
                        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="220px">
                            <el-form-item :label="$t('fields.name')" prop="company_name">
                                <el-input v-model="data.form.company_name" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="Address" prop="address">
                                <el-input v-model="data.form.address" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="Address2" prop="address2">
                                <el-input v-model="data.form.address2" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="Phone" prop="phone">
                                <el-input v-model="data.form.phone" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="Email" prop="email">
                                <el-input v-model="data.form.email" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="Website" prop="website">
                                <el-input v-model="data.form.website" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="HST No." prop="hst">
                                <el-input v-model="data.form.hst" placeholder="请输入标题" />
                            </el-form-item>
                        </el-form>
                    </div>

                </el-col>
            </el-row>

        </page-main>
        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onUpdate">{{ $t('operations.submit') }}</el-button>
            <el-button size="large" @click="getInfo">{{ $t('operations.cancel') }}</el-button>
        </fixed-action-bar>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>