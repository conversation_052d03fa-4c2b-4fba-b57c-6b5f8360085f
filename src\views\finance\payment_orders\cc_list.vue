<script setup name="ElavonOrderList">
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import FormMode from './components/FormMode/index.vue'
import { getElavonOrders } from '@/api/modules/payment'
import { orderNoFormatter, userNameFormatter } from '@/utils/formatter'
import { useClipboard } from '@vueuse/core'
import { DocumentCopy } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { text, copy, copied, isSupported } = useClipboard()
const { t } = useI18n()
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        id: '',
        detail: 'CC'
    },
    // 搜索
    search: {
        ssl_txn_id__icontains: null,
        payment_order__no__icontains: null,
        // user__icontains: null,
        ssl_amount__gte: 0,
        ssl_amount__lte: null,
        ssl_transaction_type: null,
        ssl_trans_status: null,
        ssl_txn_time__lte: null,
        ssl_txn_time__gte: null,
        ssl_settle_time__lte: null,
        ssl_settle_time__gte: null,
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})

const statusList = ["PEN", "OPN", "REV", "STL", "PST", "FPR", "PRE"]
const typeList = ['SALE', 'RETURN']
const searchBarCollapsed = ref(0)

watch(copied, (val) => {
    val && ElMessage.success(`${t('message.copied')}：${text.value}`)
})


onMounted(() => {
    getDataList()
    if (data.value.formMode === 'router') {
        eventBus.on('get-data-list', () => {
            getDataList()
        })
    }
})

onBeforeUnmount(() => {
    if (data.value.formMode === 'router') {
        eventBus.off('get-data-list')
    }
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getElavonOrders(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.order_list
        pagination.value.total = res.data.total
    })
}

// Filters
function resetFilters() {
    data.value.search =
    {
        ssl_txn_id__icontains: null,
        payment_order__no__icontains: null,
        // user__icontains: null,
        ssl_amount__gte: 0,
        ssl_amount__lte: null,
        ssl_transaction_type: null,
        ssl_trans_status: null,
        ssl_txn_time__lte: null,
        ssl_txn_time__gte: null,
        ssl_settle_time__lte: null,
        ssl_settle_time__gte: null,
    }
    currentChange()
}



// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.visible = true
}
</script>

<template>
    <div>
        <page-header :title="$t('finance.ccLog.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('finance.paymentOrder.fields.no')">
                                        <el-input v-model="data.search.payment_order__no__icontains"
                                            :placeholder="$t('placeholder', { field: $t('finance.paymentOrder.fields.no') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.user')">
                                        <el-input v-model="data.search.user__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('finance.ccLog.fields.txnTime')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.ssl_txn_time__gte" type="date"
                                                :placeholder="$t('fields.dateMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.ssl_txn_time__lte" type="date"
                                                :placeholder="$t('fields.dateMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('finance.ccLog.fields.settleTime')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.ssl_settle_time__gte" type="date"
                                                :placeholder="$t('fields.dateMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.ssl_settle_time__lte" type="date"
                                                :placeholder="$t('fields.dateMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('finance.ccLog.fields.txnId')">
                                        <el-input v-model="data.search.ssl_txn_id__icontains"
                                            :placeholder="$t('placeholder', { field: $t('finance.ccLog.fields.txnId') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>

                                <el-col :span="8">
                                    <el-form-item :label="$t('finance.ccLog.fields.amount')">
                                        <el-input-number v-model="data.search.ssl_amount__gte" :min="0"
                                            :placeholder="$t('finance.ccLog.fields.amountMin')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.ssl_amount__lte" :min="0"
                                            :placeholder="$t('finance.ccLog.fields.amountMax')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>

                                <el-col :span="4">
                                    <el-form-item :label="$t('finance.ccLog.fields.status')">
                                        <el-select v-model="data.search.ssl_trans_status"
                                            :placeholder="$t('statSelectHolder', { field: $t('fields.status') })"
                                            size="large" clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in statusList" :key="item" :label="item" :value="item" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('finance.ccLog.fields.txnType')">
                                        <el-select v-model="data.search.ssl_transaction_type"
                                            :placeholder="$t('statSelectHolder', { field: $t('finance.ccLog.fields.txnType') })"
                                            size="large" clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <batch-action-bar v-if="data.batch.enable" :data="data.dataList" :selection-data="data.batch.selectionDataList">
                <el-button size="default"> 单个批量操作按钮</el-button>
                <el-button-group>
                    <el-button size="default">批量操作按钮组1</el-button>
                    <el-button size="default">批量操作按钮组2</el-button>
                </el-button-group>
            </batch-action-bar>
            <el-table ref="table" v-loading="data.loading" :data="data.dataList" stripe highlight-current-row
                :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column prop="no" :label="$t('finance.ccLog.fields.txnId')" width="160">
                    <template #default="scope">
                        <el-space>
                            <el-tooltip placement="top">
                                <template #content>
                                    {{ $t('operations.copy') }}
                                </template>
                                <el-button @click="copy(scope.row.ssl_txn_id)" size="small" type="primary" plain circle
                                    :icon="DocumentCopy" />
                            </el-tooltip>
                            <div class="order-number">{{ orderNoFormatter(_, _, scope.row.ssl_txn_id) }}</div>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="user" :label="$t('fields.user')" :show-overflow-tooltip="true" min-width="120">
                    <template #default="scope">
                        <el-space>
                            <el-tag v-if="scope.row.user.user_code" size="small" type="info" effect="plain">
                                {{ scope.row.user.user_code }}
                            </el-tag>
                            <span>{{ userNameFormatter(scope.row) }}</span>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="ssl_amount" :label="$t('finance.ccLog.fields.amount')" sortable="custom"
                    align="right" />
                <el-table-column prop="ssl_transaction_type" :label="$t('finance.ccLog.fields.txnType')" align="center" />
                <el-table-column prop="ssl_txn_time" :label="$t('finance.ccLog.fields.txnTime')" align="center" />
                <el-table-column prop="ssl_trans_status" :label="$t('finance.ccLog.fields.status')" align="center" />
                <el-table-column prop="ssl_settle_time" :label="$t('finance.ccLog.fields.settleTime')" align="center" />
                <el-table-column prop="payment_order.no" :label="$t('finance.paymentOrder.fields.no')" width="160">
                    <template #default="scope">
                        <el-space>
                            <el-tooltip placement="top">
                                <template #content>
                                    {{ $t('operations.copy') }}
                                </template>
                                <el-button @click="copy(scope.row.payment_order.no)" size="small" type="primary" plain
                                    circle :icon="DocumentCopy" />
                            </el-tooltip>
                            <div class="order-number">{{ orderNoFormatter(_, _, scope.row.payment_order.no) }}</div>
                        </el-space>
                    </template>
                </el-table-column>

            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" :detail="data.formModeProps.detail"
            @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-table {
    font-size: 0.8em;

    .banned-row {
        color: var(--g-unavailable-color);
    }
}

.el-pagination {
    margin-top: 20px;
}

.order-number {
    font-weight: bolder;
    font-size: 1em;
}

.refunded-column {
    color: orangered;
}
</style>
