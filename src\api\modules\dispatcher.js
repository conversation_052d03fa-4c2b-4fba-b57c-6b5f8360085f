import { GET, PUT, PAT, POST, DEL } from "../methods";

const path = 'dispatcher/'

// Zones
const zonePath = path + 'zones/'
export const getZones = (params) => GET(zonePath, params)
export const addZone = (params) => POST(zonePath, params)
export const updateZone = (params) => PUT(zonePath, params)
export const updateZoneAvailability = (params) => PAT(zonePath, params)
export const deleteZoneAvailability = (params) => DEL(zonePath, params)
export const getZonePC = (params) => GET(zonePath + 'postal-codes/', params)
export const removeZonePc = params => GET(zonePath + 'postal-codes/remove/', params)
export const addZonePcs = params => POST(zonePath + 'postal-codes/add/', params)

// Addresses
const addressPath = path + 'addresses/'
export const findAddresses = (params) => GET(addressPath + 'find/', params)
export const retrieveAddress = (params) => PUT(addressPath, params)

export const getAddresses = params => GET(addressPath, params)
export const updateAddressFee = params => PAT(addressPath, params)

// Postal Codes
const pcPath = path + 'postal-codes/'
export const getPostalCodes = (params) => GET(pcPath, params)
export const addUpdatePostalCode = (params) => PUT(pcPath, params)
export const alterPostalCodesAvailabilityFee = (params) => PAT(pcPath, params)
export const deletePostalCodes = (params) => DEL(pcPath, params)
export const getNewPostalCodes = (params) => GET(pcPath + 'get-by-code1/', params)

// Suppliers
const supplierPath = path + 'suppliers/'
export const getDispatcherSuppliers = () => GET(supplierPath)
export const addDispatcherSupplier = (params) => POST(supplierPath, params)
export const updateDispatcherSupplier = (params) => PUT(supplierPath, params)
export const updateDispatcherSupplierAvailability = (params) => PAT(supplierPath, params)
export const deleteDispatcherSupplier = (params) => DEL(supplierPath, params)

export const getAppleMapJwt = () => GET(supplierPath + 'apple-map-jwt/');

// Elite Extra
const elitePath = path + 'elite-extra/'
export const getEliteTemplates = params => GET(elitePath + 'params/', params)
export const addEliteTemplate = (params) => POST(elitePath + 'params/', params)
export const updateEliteTemplate = (params) => PUT(elitePath + 'params/', params)
export const updateEliteTemplateAvailability = (params) => PAT(elitePath + 'params/', params)
export const deleteEliteTemplate = (params) => DEL(elitePath + 'params/', params)

export const getEliteLogs = params => GET(elitePath + 'logs/', params)
export const getEliteParams = () => GET(elitePath + 'custom-params/')

// Drivers
const eeDriverPath = elitePath + 'drivers/'
export const getEliteExtraDrivers = () => GET(eeDriverPath);
export const loadEliteExtraDrivers = () => POST(eeDriverPath);
export const bindEliteExtraDrivers = (params) => PUT(eeDriverPath, params);

const wwDriverPath = path + 'ww-route-manager/drivers/'
export const getWorkWaveDrivers = () => GET(wwDriverPath);
export const loadWorkWaveDrivers = () => POST(wwDriverPath);
export const bindWorkWaveDrivers = (params) => PUT(wwDriverPath, params);

// Orders
const orderPath = path + 'orders/'
export const getOrders = (params) => GET(orderPath, params);
export const getOrdersByDriver = (params) => POST(orderPath + 'by-driver/', params);
export const sortOrders = (params) => PUT(orderPath, params);
export const removeAssignments = (params) => POST(orderPath + 'remove-assignments/', params);
export const completeOrders = (params) => POST(orderPath + 'complete/', params);

// Synchronizer
const synchronizerPath = orderPath + 'sync/'
export const getSynToken = () => GET(synchronizerPath + 'token/');
export const startOrderSync = (params) => POST(synchronizerPath + 'start/', params);
export const queryOrderSync = () => GET(synchronizerPath + 'query/');
export const stopOrderSync = () => GET(synchronizerPath + 'stop/');


// Exceptions
const exceptionPath = path + 'exceptions/'
export const getExceptions = (params) => GET(exceptionPath, params);
export const addException = (params) => POST(exceptionPath, params);
export const updateException = (params) => PUT(exceptionPath, params);
export const updateExceptionsAvailability = (params) => PAT(exceptionPath, params);
export const deleteExceptions = (params) => DEL(exceptionPath, params);

export const getNotifyUsers = () => GET(exceptionPath + 'notify-users/');
export const getNotifyMethods = () => GET(exceptionPath + 'notify-methods/');
export const getExceptionTypes = () => GET(exceptionPath + 'types/');

export const getExceptionLogs = (params) => GET(exceptionPath + 'logs/', params);
export const ackExceptionLog = (params) => PAT(exceptionPath + 'logs/', params);

// Console
const consolePath = path + 'console/'

export const getActiveDriversCurrentPosition = () => GET(consolePath + 'positions/drivers/current/');

// Routes
const routesPath = path + 'routes/'
export const getRoutes = (params) => GET(routesPath, params);
export const updateRoutePriority = (params) => PAT(routesPath, params);
export const completeReviseRoute = (params) => PUT(routesPath, params);
export const deleteRoute = (params) => DEL(routesPath, params);

// Settings
const settingsPath = path + 'settings/'


// Work Shifts
const shiftsPath = settingsPath + 'shifts/'
export const getWorkShifts = (params) => GET(shiftsPath, params);
export const getAllWorkShifts = () => GET(shiftsPath + 'all/');
export const getWorkShift = (id) => GET(`${shiftsPath}${id}/`);
export const createWorkShift = (params) => POST(shiftsPath, params);
export const updateWorkShift = (id, params) => PUT(`${shiftsPath}${id}/`, params);
export const deleteWorkShift = (id) => DEL(`${shiftsPath}${id}/`);



// Service Orders
const serviceOrderPath = path + 'tasks/'
export const getServiceOrders = (params) => GET(serviceOrderPath, params)
export const getServiceOrder = (id) => GET(`${serviceOrderPath}${id}/`)
export const createServiceOrders = (params) => POST(serviceOrderPath, params)
export const updateServiceOrder = (id, params) => PUT(`${serviceOrderPath}${id}/`, params)
export const updateServiceOrderStatus = (id, params) => PAT(`${serviceOrderPath}${id}/`, params)
export const deleteServiceOrder = (id) => DEL(`${serviceOrderPath}${id}/`)
export const duplicateServiceOrder = (id, params) => POST(`${serviceOrderPath}duplicate/${id}/`, params)
export const reassignServiceOrder = (id, params) => POST(`${serviceOrderPath}reassign/${id}/`, params)
export const uploadServiceOrder = (params) => POST(`${serviceOrderPath}upload/`, params)
export const removeServiceOrderFromRoute = (params) => POST(`${serviceOrderPath}remove/`, params)
