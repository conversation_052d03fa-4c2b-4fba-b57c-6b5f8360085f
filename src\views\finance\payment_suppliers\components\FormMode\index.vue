<script setup>
import DetailForm from '../DetailForm/index.vue'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const { proxy } = getCurrentInstance()

const props = defineProps({
    // eslint-disable-next-line vue/valid-define-props
    ...DetailForm.props,
    modelValue: {
        type: Boolean,
        default: false
    },
})

const emit = defineEmits(['update:modelValue', 'success'])

let myVisible = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})

const title = computed(() => props.id == '' ? t('operations.add') : t('operations.edit'))

function onSubmit() {
    proxy.$refs['form'].submit(() => {
        emit('success')
        onCancel()
    })
}

function onCancel() {
    myVisible.value = false
}
</script>

<template>
    <div>
        <el-drawer v-model="myVisible" :title="title" size="600px" :close-on-click-modal="false" destroy-on-close>
            <DetailForm ref="form" v-bind="$props" />
            <template #footer>
                <el-button size="large" @click="onCancel">{{ $t('operations.cancel') }}</el-button>
                <el-button type="primary" size="large" @click="onSubmit">{{ $t('operations.confirm') }}</el-button>
            </template>
        </el-drawer>
    </div>
</template>
