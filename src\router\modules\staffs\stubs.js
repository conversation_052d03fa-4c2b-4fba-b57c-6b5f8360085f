/*
* Author: <EMAIL>'
* Date: '2024-03-03 23:17:41'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/staffs/stubs.js'
* File: 'stubs.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/staffs/stubs',
    component: Layout,
    redirect: '/staffs/stubs/delivery/list',
    name: 'stubs',
    meta: {
        title: '工资管理',
        icon: 'material-symbols:punch-clock-outline',
        auth: ['super'],
        i18n: 'route.staffs.stubs.title'
    },
    children: [
        {
            path: 'delivery/list',
            name: 'deliveryStubs',
            component: () => import('@/views/staffs/stubs/list.vue'),
            meta: {
                title: '司机工资',
                icon: 'ep:odometer',
                activeMenu: '/staffs/stubs/delivery/list',
                i18n: 'route.staffs.stubs.drivers',
                auth: ['super']
            }
        },
        {
            path: 'delivery/detail',
            name: 'deliveryStubDetail',
            component: () => import('@/views/staffs/stubs/detail.vue'),
            meta: {
                title: '工单详情',
                activeMenu: '/staffs/stubs/delivery/list',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
