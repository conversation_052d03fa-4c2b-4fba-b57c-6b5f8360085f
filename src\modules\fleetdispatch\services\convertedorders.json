{"orders": [{"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L3R 1G6", "address_1": "1-110 Torbay Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503201020288807", "city": "Markham", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "安速空运", "email": "", "telephone": "4379254588", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503201020288807", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "1-110 Torbay Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L3R 1G6", "sender_city": "Markham", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "安速空运", "sender_email": "", "sender_telephone": "4379254588", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.8187644, "longitude": -79.3453229, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "", "name": "<PERSON><PERSON><PERSON>", "email": "", "system_code": ""}, "a_scan_at": 0, "sorting_code": 0, "travel_time": 598, "distance": 6495, "receiver_name": "<PERSON><PERSON><PERSON>", "receiver_company_name": "16757", "receiver_telephone": "6478928351", "receiver_address_1": "106 Byng Ave", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M2N 4K4", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "M2J 0E8", "address_1": "128 Fairview Mall Dr", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503200950334787", "city": "North York", "province": "ON", "country": "Canada", "name": "duoduo", "company_name": "duoduo", "email": "", "telephone": "16479784023", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503200950334787", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "duoduo", "sender_address_1": "128 Fairview Mall Dr", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "M2J 0E8", "sender_city": "North York", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "duoduo", "sender_email": "", "sender_telephone": "16479784023", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.77998, "longitude": -79.3449211, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "", "name": "<PERSON>", "email": "", "system_code": ""}, "a_scan_at": 0, "sorting_code": 0, "travel_time": 33, "distance": 40, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "6475448135", "receiver_address_1": "81 Jopling Ave N", "receiver_address_2": "", "receiver_city": "Etobicoke", "receiver_province": "ON", "receiver_postcode": "M9B 4G5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5B 0J8", "address_1": "510 Curran Pl", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503192127243909", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "King", "company_name": "", "email": "", "telephone": "4165628088", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503192127243909", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "King", "sender_address_1": "510 Curran Pl", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5B 0J8", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "", "sender_email": "", "sender_telephone": "4165628088", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5856931, "longitude": -79.6464523, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "", "name": "Ada", "email": "", "system_code": ""}, "a_scan_at": 0, "sorting_code": 0, "travel_time": 568, "distance": 7234, "receiver_name": "Ada", "receiver_company_name": "王府井鼎鲜超市服务台", "receiver_telephone": "4168822806", "receiver_address_1": "9390 Woodbine Ave", "receiver_address_2": "", "receiver_city": "Markham", "receiver_province": "ON", "receiver_postcode": "L6C 0M5", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5L 5S2", "address_1": "2828 Council Ring Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503182152032344", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "追味酒酿米糕", "email": "", "telephone": "16479962688", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503182152032344", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2828 Council Ring Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5L 5S2", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "追味酒酿米糕", "sender_email": "", "sender_telephone": "16479962688", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5316842, "longitude": -79.6912268, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "", "name": "<PERSON>", "email": "", "system_code": ""}, "a_scan_at": 0, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON>", "receiver_company_name": "", "receiver_telephone": "4373491362", "receiver_address_1": "90 Queens Wharf Rd", "receiver_address_2": "", "receiver_city": "Toronto", "receiver_province": "ON", "receiver_postcode": "M5V 0J4", "receiver_country": "Canada"}, {"type": "P", "order_channel_code": "SMALL", "self_pickup": 0, "customer_id": null, "customer_code": null, "postcode": "L5L 5S2", "address_1": "2828 Council Ring Rd", "address_2": "", "combine_address": "false", "buzz_code": "", "ref": "DO202503182152016411", "city": "Mississauga", "province": "ON", "country": "Canada", "name": "<PERSON>", "company_name": "追味酒酿米糕", "email": "", "telephone": "16479962688", "need_pick_up": 1, "packages": 1, "packagesDetail": [{"ref": "DO202503182152016411", "weight": 1000, "length": 300, "width": 200, "height": 100, "dimension_unit": 1, "weight_unit": 1, "package_value": 200, "insurance_value": 200, "external_tracking_number": ""}], "allow_dropoff": 1, "shipping_from": "", "sender_name": "<PERSON>", "sender_address_1": "2828 Council Ring Rd", "sender_address_2": "", "sender_address_type": 1, "sender_postcode": "L5L 5S2", "sender_city": "Mississauga", "sender_province": "ON", "sender_country": "Canada", "sender_company_name": "追味酒酿米糕", "sender_email": "", "sender_telephone": "16479962688", "sender_buzz_code": "", "pickup_instruction": "", "delivery_instruction": "", "note": "", "signature_option": 1, "insurance_option": 1, "insurance_value": 200, "scheduled_date": "2025-03-20T00:00:00.000Z", "time_window_start": "2025-03-20T15:00:00.000Z", "time_window_end": "2025-03-20T16:45:00.000Z", "pickup_date": "2025-03-20T00:00:00.000Z", "pickup_time_window_start": "2025-03-20T15:00:00.000Z", "pickup_time_window_end": "2025-03-20T16:45:00.000Z", "service_time": 1, "pickup_service_time": 1, "notification_language": "en", "skills": "", "latitude": 43.5316842, "longitude": -79.6912268, "order_batch": "2025-03-20", "auto_deduplication": 1, "external_customer": {"id": "", "name": "<PERSON><PERSON><PERSON>", "email": "", "system_code": ""}, "a_scan_at": 0, "sorting_code": 0, "travel_time": 0, "distance": 0, "receiver_name": "<PERSON><PERSON><PERSON>", "receiver_company_name": "", "receiver_telephone": "12498751991", "receiver_address_1": "175 Wynford Dr", "receiver_address_2": "", "receiver_city": "North York", "receiver_province": "ON", "receiver_postcode": "M3C 1J3", "receiver_country": "Canada"}]}