/*
* Author: <EMAIL>'
* Date: '2024-03-03 19:26:00'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/staffs/employees.js'
* File: 'employees.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/staffs/employees',
    component: Layout,
    redirect: '/staffs/employees/drivers/list',
    name: 'staffs',
    meta: {
        title: '雇员管理',
        icon: 'clarity:employee-group-line',
        auth: ['super'],
        i18n: 'route.staffs.staffs.title'
    },
    children: [
        // {
        //     path: 'list',
        //     name: 'staffsEmployeeList',
        //     component: () => import('@/views/staffs/employees/list.vue'),
        //     meta: {
        //         title: '雇员列表',
        //         icon: 'clarity:employee-solid',
        //         activeMenu: '/staffs/employees/list',
        //         i18n: 'route.staffs.employees',
        //         auth: ['super']
        //     }
        // },
        // {
        //     path: 'employees/detail',
        //     name: 'staffsEmployeeDetail',
        //     component: () => import('@/views/staffs/employees/detail.vue'),
        //     meta: {
        //         title: '司机详情',
        //         activeMenu: '/staffs/employees/list',
        //         sidebar: false,
        //         auth: ['super']
        //     }
        // },
        {
            path: 'drivers/list',
            name: 'deliveryDriverList',
            component: () => import('@/views/staffs/drivers/list.vue'),
            meta: {
                title: '司机列表',
                icon: 'healthicons:truck-driver',
                activeMenu: '/staffs/employees/drivers/list',
                i18n: 'route.delivery.drivers.drivers',
                auth: ['super']
            }
        },
        {
            path: 'drivers/detail',
            name: 'deliveryDriverDetail',
            component: () => import('@/views/staffs/drivers/detail.vue'),
            meta: {
                title: '司机详情',
                activeMenu: '/staffs/employees/drivers/list',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
