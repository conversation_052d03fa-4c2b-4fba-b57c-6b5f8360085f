<script setup name="FinanceEodList">
import { usePagination } from '@/utils/composables'
import { getPaymentReports, deletePaymentReport } from '@/api/modules/statistics'
import { currencyFormatter, countFormatter } from '@/utils/formatter'

import _ from 'lodash'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        name__icontains: null,
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getPaymentReports(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.report_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'financeEOD'
    })
}

function onEdit(row) {
    console.log(row.id)
    router.push({
        name: 'financeEODPaymentReportDetail',
        query: {
            id: row.id
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
        deletePaymentReport({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}


const getSummaries = ({ columns, data }) => {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = 'Total'
            return
        } else if (index < columns.length - 1) {
            const values = data.map((item) => Number(_.get(item, column.property)))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                }
                else {
                    return prev
                }
            }, 0)

            if ([2, 3, 4, 6, 8, 10, 12, 14, 16].includes(index)) {
                sums[index] = currencyFormatter(null, null, sum, null)
            } else {
                sums[index] = sum
            }
        }
    })
    return sums
}


</script>

<template>
    <div>
        <page-header :title="$t('statistics.payment.title')" />
        <!-- <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
Reset
</el-button>
<el-button type="primary" @click="currentChange()">
    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
    Filter
</el-button>
</el-form-item>

</el-form>
</search-bar>
</el-collapse-item>
</el-collapse>
</page-main> -->
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe show-summary
                :summary-method="getSummaries" highlight-current-row :row-style="{ cursor: 'pointer' }"
                @row-dblclick="onEdit" @sort-change="sortChange">
                <el-table-column prop="date" :label="$t('fields.date')" fixed width="110" />
                <el-table-column prop="count" :label="$t('fields.count')" align="center" />
                <el-table-column prop="total" :label="$t('statistics.fields.total')" :formatter="currencyFormatter"
                    width="110" align="center" />
                <el-table-column prop="refund_total" :label="$t('delivery.orders.fields.refunded')" align="center"
                    width="100" :formatter="currencyFormatter" />

                <el-table-column :label="$t('statistics.payment.fields.paymentMethods')" align="center">
                    <el-table-column :label="$t('statistics.payment.fields.ap')" align="center">
                        <el-table-column prop="stats.ap.sum" :label="$t('fields.amount')" :formatter="currencyFormatter"
                            align="center" />
                        <el-table-column prop="stats.ap.count" :label="$t('fields.count')" align="center"
                            :formatter="countFormatter" />
                    </el-table-column>
                    <el-table-column :label="$t('statistics.payment.fields.cc')" align="center">
                        <el-table-column prop="stats.cc.sum" :label="$t('fields.amount')" :formatter="currencyFormatter"
                            align="center" />
                        <el-table-column prop="stats.cc.count" :label="$t('fields.count')" align="center"
                            :formatter="countFormatter" />
                    </el-table-column>
                    <el-table-column :label="$t('statistics.payment.fields.cr')" align="center">
                        <el-table-column prop="stats.cr.sum" :label="$t('fields.amount')" :formatter="currencyFormatter"
                            align="center" />
                        <el-table-column prop="stats.cr.count" :label="$t('fields.count')" align="center"
                            :formatter="countFormatter" />
                    </el-table-column>
                </el-table-column>
                <el-table-column :label="$t('statistics.payment.fields.refundMethods')" align="center">
                    <el-table-column :label="$t('statistics.payment.fields.ap')" align="center">
                        <el-table-column prop="stats.ap.refund" :label="$t('fields.amount')"
                            :formatter="currencyFormatter" align="center" />
                        <el-table-column prop="stats.ap.refund_count" :label="$t('fields.count')" align="center"
                            :formatter="countFormatter" />
                    </el-table-column>
                    <el-table-column :label="$t('statistics.payment.fields.cc')" align="center">
                        <el-table-column prop="stats.cc.refund" :label="$t('fields.amount')"
                            :formatter="currencyFormatter" align="center" />
                        <el-table-column prop="stats.cc.refund_count" :label="$t('fields.count')" align="center"
                            :formatter="countFormatter" />
                    </el-table-column>
                    <el-table-column :label="$t('statistics.payment.fields.cr')" align="center">
                        <el-table-column prop="stats.cr.refund" :label="$t('fields.amount')"
                            :formatter="currencyFormatter" align="center" />
                        <el-table-column prop="stats.cr.refund_count" :label="$t('fields.count')" align="center"
                            :formatter="countFormatter" />
                    </el-table-column>
                </el-table-column>
                <el-table-column prop="net_incoming" :label="$t('statistics.payment.fields.netIncome')" width="100"
                    :formatter="currencyFormatter" align="center" />
                <!-- <el-table-column :label="$t('statistics.payment.fields.commission')" align="center">
                    <el-table-column prop="ap_commission" :label="$t('statistics.payment.fields.ap')"
                        :formatter="currencyFormatter" align="center" />
                    <el-table-column prop="cc_commission" :label="$t('statistics.payment.fields.cc')"
                        :formatter="currencyFormatter" align="center" />
                </el-table-column> -->
                <el-table-column align="center" fixed="right" width="90">
                    <template #default="scope">
                        <el-button type="danger" size="small" @click="onDel(scope.row)" v-if="scope.row.can_update">
                            {{ $t('operations.delete') }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>