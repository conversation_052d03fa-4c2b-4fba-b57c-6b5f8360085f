export default {
    path: '/dispatcher/fleetdispatch',
    component: () => import('@/layout/index.vue'),
    name: 'dispatcher_fleetdispatch',
    meta: {
        title: '车队调度',
        i18n: 'route.dispatcher.fleetdispatch',
        icon: 'mdi:truck-delivery',
        badge: null,
        auth: 'dispatcher.fleetdispatch'
    },
    children: [
        {
            path: '',
            component: () => import('@/modules/fleetdispatch/FleetDispatchApp.vue'),
            meta: {
                title: '车队调度',
                i18n: 'route.dispatcher.fleetdispatch',
                auth: 'dispatcher.fleetdispatch'
            }
        }
    ]
} 