<script setup>
import { addModule, addOperation, getModules, getOperations, updateModule, updateOperation } from '@/api/modules/permissions'

const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    page: {
        type: String,
        default: ''
    }

})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: '',
        desc: '',
        is_available: true
    },
    page: props.page,
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    let fn = getOperations
    if (data.value.page === 'm') {
        fn = getModules
    }
    fn({ id: data.value.form.id }).then(res => {
        if (!res.data.errCode) {
            data.value.form = res.data
        }
        data.value.loading = false
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    let fn = addOperation
                    if (data.value.page === 'm') {
                        fn = addModule
                    }
                    fn(data.value.form).then(res => {
                        if (res.data.errCode === 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    let fn = updateOperation
                    if (data.value.page === 'm') {
                        fn = updateModule
                    }
                    fn(data.value.form).then(res => {
                        if (res.data.errCode === 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" :disabled="data.form.id != ''" />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="desc">
                <el-input v-model="data.form.desc" placeholder="请输入标题" type="textarea" rows="5" maxlength="200" />
            </el-form-item>
            <el-form-item :label="$t('fields.availability')">
                <el-switch v-model="data.form.is_available" class="availability-switch" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
.availability-switch {
    --el-switch-on-color: #13ce66;
    --el-switch-off-color: #ff4949;
}
</style>
