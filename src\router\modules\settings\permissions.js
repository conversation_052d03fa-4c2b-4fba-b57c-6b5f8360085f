const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/permissions',
    component: Layout,
    redirect: '/settings/permissions/list',
    name: 'permissions',
    meta: {
        title: '权限管理',
        icon: 'bx:key',
        auth: ['super'],
        i18n: 'route.system.permissions.title'
    },
    children: [
        {
            path: 'list',
            name: 'permissionList',
            component: () => import('@/views/settings/permissions/list.vue'),
            meta: {
                title: '列表',
                icon: 'bx:key',
                activeMenu: '/settings/permissions/list',
                i18n: 'route.system.permissions.title',
                auth: ['super']
            }
        },
        // Modules
        {
            path: 'modules/list',
            name: 'moduleList',
            component: () => import('@/views/settings/permissions/module_list.vue'),
            meta: {
                title: '列表',
                icon: 'mdi-light:view-module',
                activeMenu: '/settings/permissions/modules/list',
                i18n: 'route.system.permissions.modules',
                auth: ['super']
            }
        },
        {
            path: 'modules/create',
            name: 'moduleCreate',
            component: () => import('@/views/settings/permissions/detail.vue'),
            meta: {
                title: '新增',
                sidebar: false,
                activeMenu: '/settings/permissions/modules/list',
                auth: ['super']
            }
        },
        {
            path: 'modules/edit/:id',
            name: 'moduleEdit',
            component: () => import('@/views/settings/permissions/detail.vue'),
            meta: {
                title: '编辑',
                sidebar: false,
                activeMenu: '/settings/permissions/modules/list',
                auth: ['super']
            }
        },
        // Operations
        {
            path: 'operations/list',
            name: 'operationList',
            component: () => import('@/views/settings/permissions/operation_list.vue'),
            meta: {
                title: '列表',
                icon: 'ep:operation',
                activeMenu: '/settings/permissions/operations/list',
                i18n: 'route.system.permissions.operations',
                auth: ['super']
            }
        },
        {
            path: 'operations/create',
            name: 'moduleCreate',
            component: () => import('@/views/settings/permissions/detail.vue'),
            meta: {
                title: '新增',
                sidebar: false,
                activeMenu: '/settings/permissions/operations/list',
                auth: ['super']
            }
        },
        {
            path: 'operations/edit/:id',
            name: 'moduleEdit',
            component: () => import('@/views/settings/permissions/detail.vue'),
            meta: {
                title: '编辑',
                sidebar: false,
                activeMenu: '/settings/permissions/operations/list',
                auth: ['super']
            }
        }
    ]
}
