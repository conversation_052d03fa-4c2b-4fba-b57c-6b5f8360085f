<script setup>
    import instance from '@/http/request'
    import { getElementLocales } from '@/locales'
    import useMenuStore from '@/store/modules/menu'
    import useSettingsStore from '@/store/modules/settings'
    import useTabbarStore from '@/store/modules/tabbar'
    import useMessagesStore from './store/modules/messages'
    import { useI18n } from 'vue-i18n'
    import storage from '@/utils/storage'

    // import ReloadPrompt from '@/pwa/reloadPrompt.vue'

    const route = useRoute()

    const settingsStore = useSettingsStore()
    const menuStore = useMenuStore()
    const tabbarStore = useTabbarStore()
    const messagesStore = useMessagesStore()

    const { t, te } = useI18n()

    provide('generateI18nTitle', generateI18nTitle)

    const locales = computed(() => getElementLocales())

    const buttonConfig = ref({
        autoInsertSpace: true
    })

    // 侧边栏主导航当前实际宽度
    const mainSidebarActualWidth = computed(() => {
        let actualWidth = getComputedStyle(document.documentElement).getPropertyValue('--g-main-sidebar-width')
        actualWidth = parseInt(actualWidth)
        if (settingsStore.menu.menuMode === 'single' || (['head', 'only-head'].includes(settingsStore.menu.menuMode) && settingsStore.mode !== 'mobile')) {
            actualWidth = 0
        }
        return `${actualWidth}px`
    })

    // 侧边栏次导航当前实际宽度
    const subSidebarActualWidth = computed(() => {
        let actualWidth = getComputedStyle(document.documentElement).getPropertyValue('--g-sub-sidebar-width')
        actualWidth = parseInt(actualWidth)
        if (settingsStore.menu.subMenuCollapse && settingsStore.mode !== 'mobile') {
            actualWidth = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--g-sub-sidebar-collapse-width'))
        }
        if (['only-side', 'only-head'].includes(settingsStore.menu.menuMode) && settingsStore.mode !== 'mobile') {
            actualWidth = 0
        }
        if (
            settingsStore.menu.subMenuOnlyOneHide &&
            menuStore.sidebarMenus.length === 1 &&
            (
                !menuStore.sidebarMenus[0].children ||
                menuStore.sidebarMenus[0]?.children.every(item => item.meta.sidebar === false)
            )
        ) {
            actualWidth = 0
        }
        return `${actualWidth}px`
    })

    watch(() => settingsStore.app.colorScheme, val => {
        if (val === '') {
            settingsStore.$patch(state => {
                state.app.colorScheme = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
            })
        } else {
            if (settingsStore.app.colorScheme === 'dark') {
                document.documentElement.classList.add('dark')
            } else {
                document.documentElement.classList.remove('dark')
            }
        }
    }, {
        immediate: true
    })

    watch(() => settingsStore.mode, () => {
        if (settingsStore.mode === 'pc') {
            settingsStore.$patch(state => {
                state.menu.subMenuCollapse = settingsStore.subMenuCollapseLastStatus
            })
        } else if (settingsStore.mode === 'mobile') {
            settingsStore.$patch(state => {
                state.menu.subMenuCollapse = true
            })
        }
        document.body.setAttribute('data-mode', settingsStore.mode)
    }, {
        immediate: true
    })

    watch(() => settingsStore.app.theme, () => {
        document.body.setAttribute('data-theme', settingsStore.app.theme)
    }, {
        immediate: true
    })

    watch(() => settingsStore.menu.menuMode, () => {
        document.body.setAttribute('data-menu-mode', settingsStore.menu.menuMode)
    }, {
        immediate: true
    })

    watch(() => settingsStore.layout.widthMode, () => {
        document.body.setAttribute('data-app-width-mode', settingsStore.layout.widthMode)
    }, {
        immediate: true
    })

    watch([
        () => settingsStore.app.enableDynamicTitle,
        () => settingsStore.title
    ], () => {
        if (settingsStore.app.enableDynamicTitle && settingsStore.title) {
            let title = settingsStore.titleFirst ? settingsStore.title : generateI18nTitle(route.meta.i18n, settingsStore.title)
            document.title = `${title} - ${import.meta.env.VITE_APP_TITLE}`
        } else {
            document.title = import.meta.env.VITE_APP_TITLE
        }
        if (settingsStore.title && settingsStore.tabbar.enable) {
            tabbarStore.editTitle({
                tabId: tabbarStore.mergeTabs ? (route.meta.activeMenu || route.fullPath) : route.fullPath,
                title: settingsStore.title
            })
        }
    }, {
        immediate: true
    })

    watch(() => settingsStore.app.defaultLang, () => {
        instance.defaults.headers.common['Accept-Language'] = settingsStore.app.defaultLang
    }, {
        immediate: true
    })

    onMounted(() => {
        let theme = storage.local.get('fleetnowTheme')
        if (theme) settingsStore.setColorScheme(theme)
        window.onresize = () => {
            settingsStore.setMode(document.documentElement.clientWidth)
        }
        window.onresize()

        let activeUsers = storage.local.get('messengerCSActiveUsers')
        if (activeUsers) { messagesStore.setActiveUsers(activeUsers) }
        let csMessages = storage.local.get('messengerCSMessages')
        if (csMessages) {
            messagesStore.setCSMessages(csMessages)
        }
    })

    document.addEventListener('visibilitychange', async () => {
        if (document.visibilityState === 'visible') {
            let messages = await readBackgroundMessages()
            if (Object.keys(messages).length > 0) {
                for (let msg of messages) {
                    messagesStore.addMessage(msg);
                }

            }
        }
    });

    async function readBackgroundMessages() {
        return new Promise((resolve, reject) => {
            const DBOpenRequest = window.indexedDB.open('fcmBackgroundMessages', 3);
            DBOpenRequest.onerror = (event) => {
                console.log(`DB Open failed`);
                reject('Error');
            };
            let messages;
            DBOpenRequest.onsuccess = (event) => {
                // Store the result of opening the database in the db variable. This is used a lot below
                let db = DBOpenRequest.result;
                const objectStore = db.transaction(["fcmBackgroundMessages"], "readwrite").objectStore("fcmBackgroundMessages");
                objectStore.getAll().onsuccess = (event) => {
                    messages = event.target.result
                    resolve(messages);
                };
                objectStore.clear();
            }
        })
    }

    function generateI18nTitle(key, defaultTitle) {
        return settingsStore.toolbar.enableI18n && !!key && te(key) ? t(key) : (typeof defaultTitle === 'function' ? defaultTitle() : defaultTitle)
    }
</script>

<template>
    <el-config-provider :locale="locales[settingsStore.app.defaultLang]" :size="settingsStore.app.elementSize"
        :button="buttonConfig">
        <RouterView :style="{
            '--g-main-sidebar-actual-width': mainSidebarActualWidth,
            '--g-sub-sidebar-actual-width': subSidebarActualWidth
        }" />
        <!-- <ReloadPrompt /> -->
    </el-config-provider>
</template>

<style>
    .log-label {
        font-weight: bolder;
        font-size: 0.9em;
    }

    .log-content {
        font-weight: normal !important;
    }

    .el-select {
        width: 100%;
    }

    .el-descriptions__content {
        font-weight: bolder;
    }
</style>