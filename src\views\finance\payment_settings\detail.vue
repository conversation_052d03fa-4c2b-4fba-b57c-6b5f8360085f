<script setup name="PaymentSettingsDetail">
import DetailForm from './components/DetailForm/index.vue'

const formRef = ref()

function onSubmit() {
    formRef.value.submit()
}

function onCancel() {
    formRef.value.reset()
}
</script>

<template>
    <div>
        <page-header :title="$t('finance.paymentSetting.title')">
        </page-header>
        <page-main>
            <el-row>
                <el-col :span="24">
                    <DetailForm ref="formRef" />
                </el-col>
            </el-row>
        </page-main>
        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onSubmit">{{ $t('operations.submit') }}</el-button>
            <el-button size="large" @click="onCancel">{{ $t('operations.reset') }}</el-button>
        </fixed-action-bar>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
