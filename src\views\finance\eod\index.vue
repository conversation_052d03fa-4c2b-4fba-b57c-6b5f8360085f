<script setup>
import Step1 from './components/eod/step1.vue'
import Step2 from './components/eod/step2.vue'
import Step3 from './components/eod/step3.vue'
import Step4 from './components/eod/step4.vue'
import Step5 from './components/eod/step5.vue'
import Step6 from './components/eod/step6.vue'
import Step9 from './components/eod/step9.vue'
import Step10 from './components/eod/step10.vue'
import Step11 from './components/eod/step11.vue'


const data = ref({
    step: 1,
    loading: false,
    date: "2023-06-07",
    reportParams: {
        delivery: null,
        salary: null,
        cost: null
    },
})


function toStep(step) {
    data.value.step = step
}

const setDate = (date) => {
    data.value.date = date
    toStep(2)
}

const setDeliveryReport = (id) => data.value.reportParams.delivery = id;
const setSalary = (id) => data.value.reportParams.salary = id;
const setCost = (id) => data.value.reportParams.cost = id;


</script>

<template>
    <div>
        <page-header :title="$t('finance.eod.title')" :content="$t('finance.eod.desc')" />
        <page-main>
            <el-row justify="center">
                <el-col :span="20">
                    <el-steps :active="data.step - 1" finish-status="success" align-center style="margin: 20px 0 40px;">
                        <el-step :title="$t('finance.eod.s1')" />
                        <el-step :title="$t('finance.eod.s2')" />
                        <el-step :title="$t('finance.eod.s3')" />
                        <el-step :title="$t('finance.eod.s4')" />
                        <el-step :title="$t('finance.eod.s5')" />
                        <el-step :title="$t('finance.eod.s6')" />
                        <el-step :title="$t('finance.eod.s9')" />
                        <el-step :title="$t('finance.eod.s10')" />
                        <el-step :title="$t('finance.eod.s11')" />
                    </el-steps>
                    <Step1 v-if="data.step == 1" @start="setDate" />
                    <Step2 v-else-if="data.step == 2" :date="data.date" :step="2" @set-step="toStep" />
                    <Step3 v-else-if="data.step == 3" :date="data.date" :step="3" @set-step="toStep" />
                    <Step4 v-else-if="data.step == 4" :date="data.date" :step="4" @set-step="toStep"
                        @set-delivery-report="setDeliveryReport" />
                    <Step5 v-else-if="data.step == 5" :date="data.date" :step="5" @set-step="toStep"
                        @set-salary="setSalary" />
                    <Step6 v-else-if="data.step == 6" @set-step="toStep" :step="6" :date="data.date"
                        @set-salary="setSalary" />
                    <Step9 v-else-if="data.step == 7" @set-step="toStep" :step="7" :date="data.date"
                        @set-cost="setCost" />
                    <Step10 v-else-if="data.step == 8" @set-step="toStep" :step="8" :date="data.date"
                        @set-cost="setCost" />
                    <Step11 v-else :date="data.date" @set-step="toStep" :step="9" :params="data.reportParams" />
                </el-col>
            </el-row>
        </page-main>
    </div>
</template>

<style lang="scss">
.el-select {
    width: 100%;
}

.el-table {
    font-size: small;
    margin-bottom: 20px;
}

.el-table__footer-wrapper tbody td.el-table__cell {
    font-weight: bolder;
}

.my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.bold {
    font-weight: bolder;
}
</style>