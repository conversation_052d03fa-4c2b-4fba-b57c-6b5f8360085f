/*
* Author: <EMAIL>'
* Date: '2024-02-03 20:58:51'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/dispatcher/orders.js'
* File: 'orders.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/routes',
    component: Layout,
    redirect: '/dispatcher/routes/all/list',
    name: 'dispatcherRoutes',
    meta: {
        title: 'Routes',
        icon: 'i-gis:route',
        auth: ['super'],
        i18n: 'route.dispatcher.routes.title'
    },
    children: [
        {
            path: 'all/list',
            name: 'dispatcherAllRouteList',
            component: () => import('@/views/dispatcher/routes/all/list.vue'),
            meta: {
                title: 'All Routes',
                icon: 'i-gis:route-start ',
                activeMenu: '/dispatcher/routes/all/list',
                i18n: 'route.dispatcher.allRoutes',
                auth: ['super'],
                cache: ['deliveryOrderDetail']
            }
        },
        // {
        //     path: 'driver/list',
        //     name: 'dispatcherDriverRouteList',
        //     component: () => import('@/views/dispatcher/routes/driver/list.vue'),
        //     meta: {
        //         title: 'Drivers\' Routes',
        //         icon: 'tabler:message-2-up',
        //         activeMenu: '/dispatcher/routes/driver/list',
        //         i18n: 'route.dispatcher.driverRoutes',
        //         auth: ['super']
        //     }
        // },
    ]
}
