<script setup name="DeliveryOrderDetail">
    import {
        getDeliveryOrders,
        alterDeliveryOrdersStatus,
        addOrderNote,
        removeOrderNote,
        processDeliveryOrder,
        closeDeliveryOrder,
        refundDeliveryOrder,
        downloadShippingLabel,
        updateDeliveryOrder,
        addPickupPhoto,
        addDeliveryPhoto,
        removePickupPhoto,
        removeDeliveryPhoto,
        getAllOrderMarks,
    } from '@/api/modules/delivery';
    import { currencyFormatter, phoneNumberFormatter } from '@/utils/formatter'
    import statusStyles from '../../status'
    import { EditPen, Plus, Delete, Finished, Link, DocumentCopy, Postcard } from '@element-plus/icons-vue'

    import ServicesDialog from './services_dialog.vue'
    import AddServiceDialog from './add_service_dialog.vue'
    import LogList from './logs_list.vue'
    import BaseInfoForm from './base_info_form.vue'
    import DateTimeForm from './date_time_form.vue'
    import AddressForm from './address_form.vue'
    import SMSTable from './sms_table.vue'
    import EmailTable from './email_table.vue'
    import { useClipboard } from '@vueuse/core'

    import { useI18n } from 'vue-i18n'

    const { t } = useI18n()

    const { text, copy, copied, isSupported } = useClipboard()
    watch(copied, (val) => {
        val && ElMessage.success(`${t('messages.copied')}：${text.value}`)
    })


    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })
    const data = ref({
        loading: false,
        dataInfo: {
            id: props.id,
            user: {},
            extended_info: {

            },
            fee_info: {},
            payment_orders: {
                total: null,
                order_list: []
            },
            logs: [],
            marks: [],
        },
        marks: []
    })

    const canEdit = computed(() => {
        return data.value.dataInfo.can_update
    })

    const logListRef = ref()


    onMounted(() => {
        getMarks()
        getInfo()
    })

    // Api
    function getInfo() {
        data.value.loading = true
        getDeliveryOrders({ id: data.value.dataInfo.id }).then(res => {
            data.value.loading = false
            data.value.dataInfo = res.data
        })
    }

    function alterStatus(status) {
        let params = { id: data.value.dataInfo.id, status: status }
        alterDeliveryOrdersStatus(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }

    function onProcess() {
        let params = { ids: [data.value.dataInfo.id] }
        processDeliveryOrder(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }

    function close() {
        let params = {
            id: data.value.dataInfo.id,
            amount: Math.round(refundAmount.value * 100),
            toCredits: refundToCredits.value,
            toRefund: confirmToRefund.value,
            notes: refundNotes.value
        }
        closeDeliveryOrder(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }

    function refund() {
        let params = {
            id: data.value.dataInfo.id,
            amount: Math.round(refundAmount.value * 100),
            toCredits: refundToCredits.value,
            notes: refundNotes.value
        }
        refundDeliveryOrder(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }

    function removeNote(idx) {
        ElMessageBox.confirm(
            'Confirm to delete this notes?',
            'Confirm',
            {
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel',
                type: 'warning',
            }).then(() => {
                let params = {
                    id: data.value.dataInfo.id,
                    noteIdx: idx
                }
                removeOrderNote(params).then(res => {
                    if (res.data.errCode == 365) {
                        getInfo()
                    }
                })

            }).catch(() => { })
    }

    function addNote() {
        let params = {
            id: data.value.dataInfo.id,
            notes: newNote.value
        }
        addOrderNote(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()

            }
            newNote.value = null
            newNoteDialogVisible.value = false
        })
    }

    // Add note
    const newNote = ref(null)
    const newNoteDialogVisible = ref(false)

    function onAddNote() {
        newNoteDialogVisible.value = true
    }
    function onCancelAddNote() {
        newNoteDialogVisible.value = false
        newNote.value = null
    }

    // Marks
    function getMarks() {
        getAllOrderMarks().then(res => {
            data.value.marks = res.data
        })
    }

    function addMarks() {
        let params = {
            id: data.value.dataInfo.id,
            marks: data.value.dataInfo.marks
        }
        updateDeliveryOrder(params).then(res => {
            if (res.data.errCode == 365) {
                logListRef.value.reload();
            }
        })


    }

    // BaseInfoForm
    const baseInfoFormRef = ref()
    const baseInfoToModify = ref({
        name: null,
        company: null,
        phone: null,
        email: null,
        secondaryPhone: null,
        buzzerCode: null,
    })
    const baseInfoType = ref()
    const baseInfoDialogVisible = ref(false)

    function showBaseInfoDialog(type) {
        baseInfoType.value = type
        if (type == 'pickup') {
            baseInfoToModify.value.name = data.value.dataInfo.sender_name
            baseInfoToModify.value.company = data.value.dataInfo.sender_company
            baseInfoToModify.value.phone = data.value.dataInfo.sender_phone
            baseInfoToModify.value.email = data.value.dataInfo.sender_email
            baseInfoToModify.value.secondaryPhone = data.value.dataInfo.sender_secondary_phone
            baseInfoToModify.value.buzzerCode = data.value.dataInfo.sender_buzzer_code
        } else {
            baseInfoToModify.value.name = data.value.dataInfo.receiver_name
            baseInfoToModify.value.company = data.value.dataInfo.receiver_company
            baseInfoToModify.value.phone = data.value.dataInfo.receiver_phone
            baseInfoToModify.value.email = data.value.dataInfo.receiver_email
            baseInfoToModify.value.secondaryPhone = data.value.dataInfo.receiver_secondary_phone
            baseInfoToModify.value.buzzerCode = data.value.dataInfo.receiver_buzzer_code
        }
        baseInfoDialogVisible.value = true
    }

    function hideBaseInfoDialog() {
        baseInfoType.value = null
        baseInfoDialogVisible.value = false
        baseInfoToModify.value.name = null
        baseInfoToModify.value.company = null
        baseInfoToModify.value.phone = null
        baseInfoToModify.value.email = null
        baseInfoToModify.value.secondaryPhone = null
        baseInfoToModify.value.buzzerCode = null
    }

    function onUpdateInfo() {
        baseInfoFormRef.value.submit();
    }


    // Date & Time
    const dateTimeFormRef = ref()
    const dateTimeToModify = ref({
        date: null,
        time: null,
    })
    const dateTimeType = ref()
    const postalCodeToModify = ref()
    const dateTimeDialogVisible = ref(false)

    function showDateTimeDialog(type) {
        dateTimeType.value = type
        if (type == 'pickup') {
            postalCodeToModify.value = data.value.dataInfo.sender_postal_code
            dateTimeToModify.value.date = data.value.dataInfo.expected_pickup_date
            dateTimeToModify.value.time = data.value.dataInfo.extended_info.time_infos.pickup.id
        } else {
            postalCodeToModify.value = data.value.dataInfo.receiver_postal_code
            dateTimeToModify.value.date = data.value.dataInfo.expected_delivery_date
            dateTimeToModify.value.time = data.value.dataInfo.extended_info.time_infos.delivery.id
        }
        dateTimeDialogVisible.value = true
    }

    function hideDateTimeDialog() {
        dateTimeType.value = null
        dateTimeDialogVisible.value = false
        dateTimeToModify.value.date = null
        dateTimeToModify.value.time = null
    }

    function onUpdateDateTime() {
        dateTimeFormRef.value.submit();
    }
    // Address
    const addressFormRef = ref()
    const addressToModify = ref({
        address: null,
        city: null,
        province: null,
        postalCode: null,
        addrDb: null,
    })
    const addressType = ref()
    const addressDialogVisible = ref(false)

    function showAddressDialog(type) {
        addressType.value = type
        if (type == 'pickup') {
            addressToModify.value.address = data.value.dataInfo.sender_address
            addressToModify.value.city = data.value.dataInfo.sender_city
            addressToModify.value.province = data.value.dataInfo.sender_province
            addressToModify.value.postalCode = data.value.dataInfo.sender_postal_code
            addressToModify.value.addrDb = data.value.dataInfo.extended_info.address_infos.pickup?.Id
            addressToModify.value.unitNo = data.value.dataInfo.sender_unit_no
        } else {
            addressToModify.value.address = data.value.dataInfo.receiver_address
            addressToModify.value.city = data.value.dataInfo.receiver_city
            addressToModify.value.province = data.value.dataInfo.receiver_province
            addressToModify.value.postalCode = data.value.dataInfo.receiver_postal_code
            addressToModify.value.addrDb = data.value.dataInfo.extended_info.address_infos.delivery?.Id
            addressToModify.value.unitNo = data.value.dataInfo.receiver_unit_no

        }
        addressDialogVisible.value = true
    }

    function hideAddressDialog() {
        addressType.value = null
        addressDialogVisible.value = false
        addressToModify.value.date = null
        addressToModify.value.time = null
    }

    function onUpdateAddress() {
        addressFormRef.value.submit();
    }



    function onUpdateSuccess() {
        getInfo();
        hideBaseInfoDialog();
        hideDateTimeDialog();
        hideAddressDialog();
    }



    // Update Services
    const servicesDialog = ref({
        visible: false,
        title: null
    })

    const onUpdateServices = (val) => {
        servicesDialog.value.title = val
        servicesDialog.value.visible = true
    }

    const onServicesSuccess = () => {
        getInfo()
        logListRef.value.reload();
        servicesDialog.value.visible = false
    }

    // Close
    function onClose() {
        if (data.value.dataInfo.can_refund) {
            onShowRefundDialog(close)
        } else {
            close()
        }
    }

    // Refund
    const showRefundDialog = ref(false)
    const refundAmount = ref(0.0)
    const refundToCredits = ref(true)
    const refundNotes = ref('')

    const refundCallback = ref()
    const confirmToRefund = ref(false)

    function onShowRefundDialog(callback) {
        showRefundDialog.value = true
        refundAmount.value = data.value.dataInfo.total
        refundToCredits.value = true
        refundNotes.value = ''
        refundCallback.value = callback
    }

    function onHidRefundDialog() {
        showRefundDialog.value = false
        refundCallback.value = null
        confirmToRefund.value = false
    }

    function onConfirmRefund() {
        confirmToRefund.value = true
        refundCallback.value && refundCallback.value()
        showRefundDialog.value = false
    }

    // Update Delivery Option
    const quantityDialogVisible = ref(false)

    const quantity = ref()

    function onUpdateQuantity() {
        quantity.value = data.value.dataInfo.quantity
        quantityDialogVisible.value = true;
    }

    function onCancelUpdateQuantity() {
        quantityDialogVisible.value = false;
    }

    function onConfirmUpdateQuantity() {
        let params = { id: data.value.dataInfo.id, quantity: quantity.value }
        updateDeliveryOrder(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo();
                logListRef.value.reload();
                onCancelUpdateQuantity();
            }
        })
    }

    // Labels
    const shippingLabelDialogVisible = ref(false)
    const labelQuantity = ref(1)
    const isProtected = ref(true)

    function onDownloadShippingLabel() {
        shippingLabelDialogVisible.value = true
        labelQuantity.value = data.value.form.quantity
        isProtected.value = true
    }

    function onCancelDownloadShippingLabel() {
        shippingLabelDialogVisible.value = false
    }

    function onConfirmDownloadShippingLabel() {
        let params = {
            id: data.value.dataInfo.id,
            quantity: labelQuantity.value,
            isProtected: isProtected.value,
        }
        downloadShippingLabel(params).then(res => {
            if (res.data.errCode == 365) {
                window.open(res.data.url);
                onCancelDownloadShippingLabel();
            }

        })
    }

    function onAddPickupPhoto(params) {
        addPickupPhoto(props.id, params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }

    function onRemovePickupPhoto(index) {
        ElMessageBox.confirm(`确认删除这张照片吗？`, '确认信息').then(() => {
            onConfirmRemovePickupPhoto(index)
        }).catch(() => { })
    }

    function onConfirmRemovePickupPhoto(index) {
        let params = {
            id: props.id,
            photo: data.value.dataInfo.pickup_photos[index].id,
        }
        removePickupPhoto(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }



    function onAddDeliveryPhoto(params) {
        addDeliveryPhoto(props.id, params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }

    function onRemoveDeliveryPhoto(index) {
        ElMessageBox.confirm(`确认删除这张照片吗？`, '确认信息').then(() => {
            onConfirmRemoveDeliveryPhoto(index)
        }).catch(() => { })

    }

    function onConfirmRemoveDeliveryPhoto(index) {
        let params = {
            id: props.id,
            photo: data.value.dataInfo.delivery_photos[index].id,
        }
        removeDeliveryPhoto(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo()
            }
        })
    }

    const pickupPhotos = computed({
        get: function () {
            return data.value.dataInfo.pickup_photos?.map(e => e.url)
        },
        set: function (val) {
            emit('update:modelValue', val)
        }
    })

    const deliveryPhotos = computed({
        get: function () {
            return data.value.dataInfo.delivery_photos?.map(e => e.url)
        },
        set: function (val) {
            emit('update:modelValue', val)
        }
    })

    // SMS Drawer
    const smsDrawerVisible = ref(false)
    const smsPart = ref(null)
    // Email Drawer
    const emailDrawerVisible = ref(false)
    const emailPart = ref(null)

    function openUrl(url) {
        window.open(url, '_blank')
    }

    // Add Service Dialog
    const addServiceDialogVisible = ref(false)

    function onGetAddServiceUrl() {
        addServiceDialogVisible.value = true
    }



</script>

<template>
    <div v-loading="data.loading">
        <el-row>
            <el-col :span="16">
                <!-- Main -->
                <page-main>
                    <template #title>
                        <el-space>
                            <span style="font-weight: bolder; font-size: larger;">{{ data.dataInfo.no }}</span>
                            <el-tag :type="statusStyles[data.dataInfo.status]" round size="large">
                                {{ $t(`delivery.orders.selections.status.${data.dataInfo.status}`) }}
                            </el-tag>
                            <el-button text type="primary" @click="onDownloadShippingLabel">
                                {{ $t('delivery.orders.operations.printLabel') }}
                            </el-button>
                        </el-space>
                    </template>
                    <!-- Buttons -->
                    <template #extra>
                        <el-space>
                            <el-button type="danger" plain v-if="data.dataInfo.can_close" @click="onClose">
                                {{ $t('delivery.orders.operations.close') }}
                            </el-button>
                            <el-button type="warning" plain v-if="data.dataInfo.can_pend"
                                @click="alterStatus('pending')">
                                {{ $t('delivery.orders.operations.pend') }}
                            </el-button>
                            <el-button type="warning" plain v-if="data.dataInfo.can_queue"
                                @click="alterStatus('inQueue')">
                                {{ $t('delivery.orders.operations.queue') }}
                            </el-button>
                            <el-button type="warning" v-if="data.dataInfo.can_process" @click="onProcess">
                                {{ $t('delivery.orders.operations.process') }}
                            </el-button>
                            <el-button type="success" plain v-if="data.dataInfo.can_pick_up"
                                @click="alterStatus('pickedUp')">
                                {{ $t('delivery.orders.operations.pickup') }}
                            </el-button>
                            <el-button type="success" plain v-if="data.dataInfo.can_transport"
                                @click="alterStatus('transporting')">
                                {{ $t('delivery.orders.operations.transport') }}
                            </el-button>
                            <el-button type="success" v-if="data.dataInfo.can_delivery"
                                @click="alterStatus('delivered')">
                                {{ $t('delivery.orders.operations.deliver') }}
                            </el-button>
                            <el-button type="primary" v-if="data.dataInfo.can_complete"
                                @click="alterStatus('completed')">
                                {{ $t('delivery.orders.operations.complete') }}
                            </el-button>
                            <el-button type="danger" v-if="data.dataInfo.can_exception"
                                @click="alterStatus('exceptional')">
                                {{ $t('delivery.orders.operations.exception') }}
                            </el-button>
                        </el-space>
                    </template>
                    <el-row :gutter="20">
                        <!-- Sender -->
                        <el-col :span="12">
                            <el-card shadow="hover" class="sender-card" :header="$t('delivery.orders.fields.sender')">
                                <el-descriptions :column="1">
                                    <template #title>
                                        <el-space>
                                            <span>
                                                {{ data.dataInfo.sender_name }}
                                            </span>
                                            <template v-if="canEdit">
                                                <el-button text :icon="EditPen" type="primary" size="small"
                                                    @click="showBaseInfoDialog('pickup')" />
                                            </template>
                                        </el-space>
                                    </template>
                                    <el-descriptions-item :label="$t('user.fields.phoneNumber')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ phoneNumberFormatter(data.dataInfo.sender_phone) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.email')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.sender_email }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.secondaryPhone')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ phoneNumberFormatter(data.dataInfo.sender_secondary_phone) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.buzzerCode')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.sender_buzzer_code }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.address')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        <el-space>
                                            <span>
                                                {{ data.dataInfo.sender_address }}
                                            </span>
                                            <template v-if="canEdit">
                                                <el-button text :icon="EditPen" type="primary" size="small"
                                                    @click="showAddressDialog('pickup')" />
                                            </template>
                                        </el-space>
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.unitNo')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.sender_unit_no }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.postalCode')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.sender_postal_code }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.city')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.sender_city }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.province')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.sender_province }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.expectedPickupTime')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        <el-space>
                                            <span>
                                                {{ data.dataInfo.expected_pickup_start_at?.slice(0, -3) }} ~
                                                {{ data.dataInfo.expected_pickup_end_at }}
                                            </span>
                                            <template v-if="canEdit">
                                                <el-button text :icon="EditPen" type="primary" size="small"
                                                    @click="showDateTimeDialog('pickup')" />
                                            </template>
                                        </el-space>
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.pickedUpAt')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.picked_up_at }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.pickedUpBy')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.pickup_by }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.pickedUpBy')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.picked_up_by }}
                                    </el-descriptions-item>

                                </el-descriptions>
                            </el-card>
                        </el-col>
                        <!-- Receiver -->
                        <el-col :span="12">
                            <el-card shadow="hover" class="receiver-card"
                                :header="$t('delivery.orders.fields.receiver')">
                                <el-descriptions :column="1">
                                    <template #title>
                                        <el-space>
                                            <span>
                                                {{ data.dataInfo.receiver_name }}
                                            </span>
                                            <template v-if="canEdit">
                                                <el-button text :icon="EditPen" type="primary" size="small"
                                                    @click="showBaseInfoDialog('delivery')" />
                                            </template>
                                        </el-space>
                                    </template>
                                    <el-descriptions-item :label="$t('user.fields.phoneNumber')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ phoneNumberFormatter(data.dataInfo.receiver_phone) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.email')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.receiver_email }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.secondaryPhone')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ phoneNumberFormatter(data.dataInfo.receiver_secondary_phone) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.buzzerCode')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.receiver_buzzer_code }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.address')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        <el-space>
                                            <span>
                                                {{ data.dataInfo.receiver_address }}
                                            </span>
                                            <template v-if="canEdit">
                                                <el-button text :icon="EditPen" type="primary" size="small"
                                                    @click="showAddressDialog('delivery')" />
                                            </template>
                                        </el-space>
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.unitNo')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.receiver_unit_no }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.postalCode')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.receiver_postal_code }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.city')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.receiver_city }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.province')" class-name="bold-content"
                                        label-class-name="desc-label">
                                        {{ data.dataInfo.receiver_province }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.expectedDeliveryTime')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        <el-space>
                                            <span>
                                                {{ data.dataInfo.expected_delivery_start_at?.slice(0, -3) }} ~
                                                {{ data.dataInfo.expected_delivery_end_at }}
                                            </span>
                                            <template v-if="canEdit">
                                                <el-button text :icon="EditPen" type="primary" size="small"
                                                    @click="showDateTimeDialog('delivery')" />
                                            </template>
                                        </el-space>
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.deliveredAt')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.delivered_at }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.deliveredBy')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ data.dataInfo.delivered_by }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </el-card>
                        </el-col>
                    </el-row>
                    <!-- Notes -->
                    <el-card shadow="hover" class="notes-card">
                        <template #header>
                            <span>{{ $t('delivery.orders.fields.notes') }}</span>
                            <el-button circle type="success" :icon="Plus" size="small" plain @click="onAddNote" />
                        </template>
                        <el-row>
                            <el-col :span="12">
                                {{ data.dataInfo.notes }}
                            </el-col>
                            <el-col :span="12" class="admin-notes">
                                <div v-for="item in data.dataInfo.admin_notes">
                                    <p class="note-small">
                                        <span>{{ item.admin }} &ThickSpace;{{ item.created }}</span>
                                        <el-button circle :icon="Delete" type="danger" plain size="small"
                                            @click="removeNote(item.idx)" />
                                    </p>
                                    <p>{{ item.notes }}</p>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>
                    <el-card shadow="hover" class="marks-card" v-if="data.marks.length > 0">
                        <template #header>
                            <span>{{ $t('delivery.orders.fields.marks') }}</span>
                            <el-button circle type="success" :icon="Finished" size="small" plain @click="addMarks" />
                        </template>
                        <el-space>
                            <el-checkbox-group v-model="data.dataInfo.marks">
                                <el-checkbox v-for="m of data.marks" :value="m">
                                    <el-space>
                                        <span :style="{ 'color': m.color }">●</span>
                                        <el-text>{{ m.notes }}</el-text>
                                    </el-space>
                                </el-checkbox>
                            </el-checkbox-group>
                        </el-space>
                    </el-card>
                </page-main>
                <page-main title="Messages">
                    <el-space>
                        <el-button text @click="emailDrawerVisible = true; emailPart = 'sender'">
                            Email Logs
                        </el-button><el-button text @click="smsDrawerVisible = true; smsPart = 'sender'">
                            SMS Logs
                        </el-button>
                    </el-space>
                </page-main>
                <!-- Extended Infos -->
                <page-main>
                    <el-descriptions :title="$t('delivery.orders.sections.extendedInfo')" :column="1">
                        <el-descriptions-item :label="$t('delivery.orders.fields.quantity')" class-name="bold-content"
                            label-class-name="desc-label">
                            <el-space>
                                <span>{{ data.dataInfo.quantity }}</span>
                                <template v-if="canEdit">
                                    <el-button text :icon="EditPen" type="primary" size="small"
                                        @click="onUpdateQuantity" />
                                </template>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.packageType')"
                            class-name="bold-content" label-class-name="desc-label">
                            <el-space>
                                <span>{{ data.dataInfo.extended_info.package_type?.name }}</span>
                                <span>
                                    {{ currencyFormatter(_, _, data.dataInfo.extended_info.package_type?.fee) }}
                                </span>
                                <template v-if="canEdit">
                                    <el-button
                                        v-if="data.dataInfo.extended_info.package_type && Object.keys(data.dataInfo.extended_info.package_type).length"
                                        text :icon="EditPen" type="primary" size="small"
                                        @click="onUpdateServices('packageType')" />
                                    <el-button v-else text :icon="Plus" type="primary" size="small"
                                        @click="onUpdateServices('packageType')" />
                                </template>

                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.sizeWeight')" class-name="bold-content"
                            label-class-name="desc-label">
                            <el-space>
                                <span>{{ data.dataInfo.extended_info.size_weight?.name }}</span>
                                <span>{{ currencyFormatter(_, _, data.dataInfo.extended_info.size_weight?.fee) }}</span>
                                <template v-if="canEdit">
                                    <el-button
                                        v-if="data.dataInfo.extended_info.size_weight && Object.keys(data.dataInfo.extended_info.size_weight).length"
                                        text :icon="EditPen" type="primary" size="small"
                                        @click="onUpdateServices('sizeWeight')" />
                                    <el-button v-else text :icon="Plus" type="primary" size="small"
                                        @click="onUpdateServices('sizeWeight')" />
                                </template>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.addedServices')"
                            class-name="bold-content" label-class-name="desc-label">
                            <el-space v-for="item of data.dataInfo.extended_info.added_services">
                                <span>{{ item.name }}</span>
                                <span>{{ currencyFormatter(_, _, item.fee) }}</span>;
                            </el-space>
                            <template v-if="canEdit">
                                <el-button v-if="data.dataInfo.extended_info.added_services?.length" text
                                    :icon="EditPen" type="primary" size="small"
                                    @click="onUpdateServices('addedServices')" />
                                <el-button v-else text :icon="Plus" type="primary" size="small"
                                    @click="onUpdateServices('addedServices')" />
                                <el-button text :icon="Postcard" type="primary" size="small"
                                    @click="onGetAddServiceUrl" />
                            </template>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.addressInfo')"
                            class-name="bold-content" label-class-name="desc-label">
                            <el-space v-for="item, k, idx of data.dataInfo.extended_info.address_infos" :key="idx">
                                <code>{{ $t('delivery.orders.selections.extendedInfo.' + k) }}</code>
                                <span>{{ item.Id }}</span>
                                <span>{{ currencyFormatter(_, _, item.fee) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.pcInfo')" class-name="bold-content"
                            label-class-name="desc-label">
                            <el-space v-for="item, k, idx of data.dataInfo.extended_info.pc_infos">
                                <code>{{ $t('delivery.orders.selections.extendedInfo.' + k) }}</code>
                                <span>{{ item.code }}</span>
                                <span>{{ currencyFormatter(_, _, item.fee) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.holidayInfo')"
                            class-name="bold-content" label-class-name="desc-label">
                            <el-space v-for="item, k, idx of data.dataInfo.extended_info.holiday_infos">
                                <code>{{ $t('delivery.orders.selections.extendedInfo.' + k) }}</code>
                                <span>{{ item.name }}</span>
                                <span> {{ currencyFormatter(_, _, item.fee) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.weekdayInfo')"
                            class-name="bold-content" label-class-name="desc-label">
                            <el-space v-for="item, k, idx of data.dataInfo.extended_info.weekday_infos">
                                <code>{{ $t('delivery.orders.selections.extendedInfo.' + k) }}</code>
                                <span>{{ item.name }}</span>
                                <span>{{ currencyFormatter(_, _, item.fee) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.timeInfos')" class-name="bold-content"
                            label-class-name="desc-label">
                            <el-space v-for="item, k, idx of data.dataInfo.extended_info.time_infos">
                                <code>{{ $t('delivery.orders.selections.extendedInfo.' + k) }}</code>
                                <span>{{ item.name }}</span>
                                <span>{{ currencyFormatter(_, _, item.fee) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.zoneInfos')" class-name="bold-content"
                            label-class-name="desc-label">
                            <el-space v-for="item, k, idx of data.dataInfo.extended_info.zone_infos">
                                <code>{{ $t('delivery.orders.selections.extendedInfo.' + k) }}</code>
                                <span>{{ item.name }}</span>
                                <span>{{ currencyFormatter(_, _, item.fee) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.insurancePolicy')"
                            class-name="bold-content" label-class-name="desc-label">
                            <el-space>
                                <span>{{ data.dataInfo.extended_info.insurance_policy?.name }}</span>
                                <span>
                                    {{ currencyFormatter(_, _, data.dataInfo.extended_info.insurance_policy?.fee) }}
                                </span>
                                <template v-if="canEdit">
                                    <el-button
                                        v-if="data.dataInfo.extended_info.insurance_policy && Object.keys(data.dataInfo.extended_info.insurance_policy).length"
                                        text :icon="EditPen" type="primary" size="small"
                                        @click="onUpdateServices('insurance')" />
                                    <el-button v-else text :icon="Plus" type="primary" size="small"
                                        @click="onUpdateServices('insurance')" />
                                </template>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.promotionInfos')"
                            class-name="bold-content" label-class-name="desc-label">
                            <el-space v-for="item of data.dataInfo.extended_info.promotion_infos">
                                <span>{{ item.name }}</span>
                                <span>{{ currencyFormatter(_, _, item.amount) }}</span>;
                            </el-space>
                            <template v-if="canEdit">
                                <el-button v-if="data.dataInfo.extended_info.promotion_infos?.length" text
                                    :icon="EditPen" type="primary" size="small"
                                    @click="onUpdateServices('promotions')" />
                                <el-button v-else text :icon="Plus" type="primary" size="small"
                                    @click="onUpdateServices('promotions')" />
                            </template>
                        </el-descriptions-item>
                    </el-descriptions>
                </page-main>

                <!-- Photos -->
                <page-main title="POP">
                    <images-uploader v-model:url="pickupPhotos" :action="canEdit ? onAddPickupPhoto : null" name="image"
                        @on-success="handleSuccess2" :on-remove="canEdit ? onRemovePickupPhoto : null" />
                    <el-text v-if="!canEdit && (data.dataInfo.pickup_photos?.length ?? 0) == 0">No Photo</el-text>

                </page-main>
                <page-main title="POD">
                    <images-uploader v-model:url="deliveryPhotos" :action="canEdit ? onAddDeliveryPhoto : null"
                        name="image" @on-success="handleSuccess2" :on-remove="canEdit ? onRemoveDeliveryPhoto : null" />
                    <el-text v-if="!canEdit && (data.dataInfo.delivery_photos?.length ?? 0) == 0">No Photo</el-text>
                </page-main>
                <el-row>
                    <el-col :span="16">
                        <!-- Logs -->
                        <LogList ref="logListRef" :id="data.dataInfo.id" />
                    </el-col>

                    <el-col :span="8">
                        <!-- Sorting Logs -->
                        <page-main title="Sorting Logs">
                            <el-timeline v-if="data.dataInfo?.sorting_logs?.length">
                                <el-timeline-item v-for="(item, index) in data.dataInfo.sorting_logs" :key="index"
                                    :timestamp="item.created_at" type="warning" placement="top">
                                    <el-space direction="vertical" alignment="start" size="large" class="logs">
                                        <el-space>
                                            <el-tag type="info">{{ item.remark }}</el-tag>
                                            <el-tag type="warning">{{ item.index }}</el-tag>
                                        </el-space>
                                        <span class="operator">{{ item.operator }}</span>
                                    </el-space>
                                </el-timeline-item>
                            </el-timeline>
                            <el-empty v-else :description="$t('noData')" />
                        </page-main>

                        <!-- Exception Logs -->
                        <page-main title="Exception Logs">
                            <el-timeline v-if="data.dataInfo?.exception_logs?.length">
                                <el-timeline-item v-for="(item, index) in data.dataInfo.exception_logs" :key="index"
                                    :timestamp="item.created_at" type="danger" placement="top">
                                    <el-space direction="vertical" alignment="start" size="large" class="logs">
                                        <span class="operator">{{ item.employee }}</span>
                                        <span class="operator">{{ item.exception }}</span>
                                        <el-descriptions :column="1" v-if="item.tokens">
                                            <el-descriptions-item label-align="right" label-class-name="log-label"
                                                v-for="token in item.tokens">
                                                <el-space>
                                                    <el-tag size="small" type="info">{{ token.target }}</el-tag>
                                                    <el-button type="success" plain circle size="small" :icon="Link"
                                                        @click="openUrl(token.url)" />
                                                    <el-tooltip placement="top">
                                                        <template #content>
                                                            {{ $t('operations.copy') }}
                                                        </template>
                                                        <el-button @click.stop="copy(token.url)" size="small"
                                                            type="primary" plain circle :icon="DocumentCopy" />
                                                    </el-tooltip>
                                                </el-space>
                                            </el-descriptions-item>
                                        </el-descriptions>
                                    </el-space>
                                </el-timeline-item>
                            </el-timeline>
                            <el-empty v-else :description="$t('noData')" />
                        </page-main>


                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="8">
                <!-- User -->
                <page-main :title="$t('delivery.orders.sections.user')">
                    <el-descriptions :title="data.dataInfo.user_name" :column="1">
                        <el-descriptions-item :label="$t('user.fields.name')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.name }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.companyName')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.company_name }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.code')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.user_code }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.group')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.user_group?.name ?? '' }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.type')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ $t(`user.selection.userType.${data.dataInfo.user.user_type}`) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.phoneNumber')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ phoneNumberFormatter(data.dataInfo.user.phone_number) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.email')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.email }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.secondaryPhone')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ phoneNumberFormatter(data.dataInfo.user.sec_phone_number) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.extensionPhone')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.ext_phone_number }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.unitNo')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.unit_no }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.buzzerCode')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.buzzer_code }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.address')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.address }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.postalCode')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.postal_code }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.city')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.city }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.province')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.province }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.country')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.country }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.credits')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.user.credits) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.createdAt')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.created_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.updatedAt')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ data.dataInfo.user.updated_at }}
                        </el-descriptions-item>
                    </el-descriptions>
                </page-main>
                <!-- Fee Infos -->
                <page-main :title="$t('delivery.orders.sections.feeInfo')">
                    <template #extra>
                        <el-space>
                            <span>{{ $t('delivery.orders.fields.total') }}</span>
                            <span style="font-weight: bolder;">{{ currencyFormatter(_, _, data.dataInfo.total) }}</span>
                        </el-space>
                    </template>
                    <el-descriptions :column="1">
                        <el-descriptions-item :label="$t('delivery.orders.fields.deliveryFee')"
                            class-name="bold-content" label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.delivery_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.expressFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.express_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.typeFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.type_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.sizeFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.size_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.addedServicesFee')"
                            class-name="bold-content" label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.added_services_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.packagingFee')"
                            class-name="bold-content" label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.packaging_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.addressFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.address_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.pcFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.pc_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.holidayFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.holiday_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.weekdayFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.weekday_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.timeFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.time_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.zoneFee')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.zone_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.insuranceFee')"
                            class-name="bold-content" label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.fee_info?.insurance_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.promotionalDeduction')"
                            class-name="bold-content" label-class-name="desc-label">
                            - {{ currencyFormatter(_, _, data.dataInfo.fee_info?.promotional_deduction) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.tip')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.tip) }}
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-divider>
                        {{ $t('delivery.orders.fields.total') }}
                    </el-divider>
                    <el-descriptions :column="1">
                        <el-descriptions-item :label="$t('delivery.orders.fields.subtotal')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.subtotal) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.tax')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.tax) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.total')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.total) }}
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-divider border-style="dashed" />
                    <el-descriptions :column="1">
                        <el-descriptions-item :label="$t('delivery.orders.fields.tip')" class-name="bold-content"
                            label-class-name="desc-label">
                            {{ currencyFormatter(_, _, data.dataInfo.tip) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.refunded')" class-name="bold-content"
                            label-class-name="desc-label">
                            <el-space>
                                <span>{{ currencyFormatter(_, _, data.dataInfo.refunded_amount) }}</span>
                                <el-text type="info" size="small" v-if="data.dataInfo.pended_refund_amount > 0">
                                    ({{ currencyFormatter(_, _, data.dataInfo.pended_refund_amount) }} Pended or
                                    scheduled)
                                </el-text>
                            </el-space>
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-divider border-style="dashed" />
                    <el-descriptions :column="1">
                        <el-descriptions-item :label="$t('delivery.orders.fields.paid')" class-name="bold-content"
                            label-class-name="desc-label">
                            <el-space style="justify-content: space-between;">
                                <strong style="font-size: 1.2em;">{{ currencyFormatter(_, _, data.dataInfo.paid_amount)
                                }}</strong>
                                <el-button type="danger" size="small" v-if="data.dataInfo.can_refund"
                                    @click="onShowRefundDialog(refund)">
                                    {{ $t('delivery.orders.operations.refund') }}
                                </el-button>
                            </el-space>
                        </el-descriptions-item>
                    </el-descriptions>
                </page-main>
                <!-- Payment -->
                <page-main :title="$t('delivery.orders.sections.paymentInfo')">
                    <template #extra>
                        <el-space>
                            <span>{{ $t('delivery.orders.fields.total') }}</span>
                            <span style="font-weight: bolder;">
                                {{ currencyFormatter(null, null, data.dataInfo.payment_orders?.total) }}
                            </span>
                        </el-space>
                    </template>
                    <template
                        v-if="data.dataInfo.payment_orders.order_list && data.dataInfo.payment_orders.order_list.length">
                        <template v-for="(item, idx) of data.dataInfo.payment_orders.order_list" :key="idx">
                            <el-divider content-position="left">
                                {{ item.no }}
                            </el-divider>
                            <el-descriptions :column="1">
                                <el-descriptions-item :label="$t('finance.paymentOrder.fields.paidAt')"
                                    class-name="bold-content" label-class-name="desc-label">
                                    {{ item.paid_at }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('finance.paymentOrder.fields.paymentMethod')"
                                    class-name="bold-content" label-class-name="desc-label">
                                    {{ item.payment_method }}
                                </el-descriptions-item>
                            </el-descriptions>
                            <el-divider border-style="dashed" />
                            <el-descriptions :column="1">
                                <el-descriptions-item :label="$t('finance.paymentOrder.fields.amount')"
                                    class-name="bold-content" label-class-name="desc-label">
                                    {{ currencyFormatter(_, _, item.total) }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('finance.paymentOrder.fields.refunded')"
                                    class-name="bold-content" label-class-name="desc-label">
                                    <span :class="item.refunded ? 'red-desc' : ''">
                                        {{ currencyFormatter(_, _, item.refunded_amount) }}
                                    </span>
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('finance.paymentOrder.fields.status')"
                                    class-name="bold-content" label-class-name="desc-label">
                                    {{ $t('finance.paymentOrder.selections.status.' + item.status) }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('finance.paymentOrder.fields.invoice')"
                                    class-name="bold-content" label-class-name="desc-label">
                                    {{ data.dataInfo.invoice?.no }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </template>
                    </template>
                    <el-empty v-else />
                </page-main>
            </el-col>
        </el-row>
        <!-- BaseInfo Updater Dialog -->
        <el-dialog v-model="baseInfoDialogVisible" :draggable="true" destroy-on-close style="width: 600px;">
            <BaseInfoForm ref="baseInfoFormRef" :id="data.dataInfo.id" :data="baseInfoToModify" :part="baseInfoType"
                @success="onUpdateSuccess" />
            <template #footer>
                <span class="dialog-footer">
                    <span>
                        <el-button @click="hideBaseInfoDialog">
                            {{ $t('operations.cancel') }}
                        </el-button>
                        <el-button type="primary" @click="onUpdateInfo">
                            {{ $t('operations.update') }}
                        </el-button>
                    </span>
                </span>
            </template>
        </el-dialog>
        <!-- Date Time Updater Dialog -->
        <el-dialog v-model="dateTimeDialogVisible" :draggable="true" destroy-on-close style="width: 600px;">
            <DateTimeForm ref="dateTimeFormRef" :id="data.dataInfo.id" :data="dateTimeToModify" :part="dateTimeType"
                :postal-code="postalCodeToModify" @success="onUpdateSuccess" />
            <template #footer>
                <span class="dialog-footer">
                    <span>
                        <el-button @click="hideDateTimeDialog">
                            {{ $t('operations.cancel') }}
                        </el-button>
                        <el-button type="primary" @click="onUpdateDateTime">
                            {{ $t('operations.update') }}
                        </el-button>
                    </span>
                </span>
            </template>
        </el-dialog>
        <!-- Address Updater Dialog -->
        <el-dialog v-model="addressDialogVisible" :draggable="true" destroy-on-close style="width: 600px;">
            <AddressForm ref="addressFormRef" :id="data.dataInfo.id" :data="addressToModify" :part="addressType"
                @success="onUpdateSuccess" />
            <template #footer>
                <span class="dialog-footer">
                    <span>
                        <el-button @click="hideAddressDialog">
                            {{ $t('operations.cancel') }}
                        </el-button>
                        <el-button type="primary" @click="onUpdateAddress">
                            {{ $t('operations.update') }}
                        </el-button>
                    </span>
                </span>
            </template>
        </el-dialog>
        <!-- Services Update Dialog -->
        <ServicesDialog v-model="servicesDialog.visible" :type="servicesDialog.title" :order="data.dataInfo"
            @success="onServicesSuccess" />
        <!-- Add Service Dialog -->
        <AddServiceDialog v-model="addServiceDialogVisible" :id="data.dataInfo.id" @success="" />
        <!-- Quantity Dialog -->
        <el-dialog v-model="quantityDialogVisible" title="Update Quantity" align-center width="400">
            <el-input-number v-model="quantity" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelUpdateQuantity">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmUpdateQuantity">
                        {{ $t('operations.submit') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- Note Dialog -->
        <el-dialog v-model="newNoteDialogVisible" title="Add Note" align-center>
            <el-input v-model="newNote" type="textarea" maxlength="200" show-word-limit :rows="5" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelAddNote">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="addNote">
                        {{ $t('operations.submit') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- Refund Dialog -->
        <el-dialog v-model="showRefundDialog" title="Refund Payment" width="33%">
            <el-text type="danger" tag="b" v-if="data.dataInfo.pended_refund_amount > 0">
                Noted:
                <el-text tag="b" size="large" type="danger">
                    $ {{ currencyFormatter(_, _,
                        data.dataInfo.pended_refund_amount) }} CAD
                </el-text> of refund is being Pended or
                scheduled
            </el-text>
            <div style="height:20">&ThinSpace;</div>
            <el-form :model="form">
                <el-form-item :label="$t('fields.amount')" :label-width="formLabelWidth">
                    <el-input-number v-model="refundAmount"
                        :max="Number((data.dataInfo.available_amount / 100).toFixed(2))" :min="0" :step="0.1"
                        :precision="2" />
                </el-form-item>
                <el-form-item :label="$t('finance.paymentOrder.fields.refundToCredits')" :label-width="formLabelWidth">
                    <el-switch v-model="refundToCredits" />
                </el-form-item>
                <el-form-item :label="$t('fields.notes')">
                    <el-input type="textarea" rows="3" v-model="refundNotes" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onHidRefundDialog">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmRefund">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- Shipping Label Dialog -->
        <el-dialog v-model="shippingLabelDialogVisible" title="Download Label" width="500" :before-close="handleClose">
            <el-form :model="form" label-width="auto" style="max-width: 600px">
                <el-form-item label="Quantity">
                    <el-input-number v-model="labelQuantity" />
                </el-form-item>
                <el-form-item label="Hide Sender Info">
                    <el-switch v-model="isProtected" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelDownloadShippingLabel">
                        {{ $t('operations.cancel') }}
                    </el-button>
                    <el-button type="primary" @click="onConfirmDownloadShippingLabel">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- SMS Table Drawer  -->
        <el-drawer v-model="smsDrawerVisible" title="SMS Logs" direction="rtl" :size="800">
            <SMSTable :part="smsPart" :order-id="props.id" />
        </el-drawer>
        <!-- Email Table Drawer -->
        <el-drawer v-model="emailDrawerVisible" title="Email Logs" direction="rtl" :size="800">
            <EmailTable :part="emailPart" :order-id="props.id" />
        </el-drawer>
    </div>
</template>

<style lang="scss" scoped>
    :deep(.el-card__header) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-weight: bolder;
        color: white;
    }

    .sender-card {

        :deep(.el-card__header) {
            background-color: #eebe77 !important;
        }
    }

    .receiver-card {

        :deep(.el-card__header) {
            background-color: #b3e19d !important;
        }
    }

    .notes-card {
        margin-top: 20px;

        :deep(.el-card__header) {
            background-color: #606266 !important;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    .marks-card {
        margin-top: 20px;

        :deep(.el-card__header) {
            background-color: #fff !important;
            color: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    :deep(.el-descriptions__cell) {

        .bold-content {
            font-weight: bolder;
        }

        .desc-label {
            font-size: 0.9em;
            color: #676767 !important;
        }

        .red-desc {
            color: #f56c6c !important;
        }
    }

    .dialog-footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

    .admin-notes {
        border-left: 1px solid #ccc !important;
        padding-left: 20px;

        .note-small {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            font-size: small;
            color: #676767;
            margin-bottom: 0;
        }

        p {
            margin-top: 0;
        }
    }
</style>