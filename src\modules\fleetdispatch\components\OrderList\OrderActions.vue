// 使用API分配订单到路线（分组操作）
const assignOrderToRouteAPI = async () => {
    if (selectedOrderIds.value.length === 0 || !selectedRoute.value) {
        ElMessage.warning('请先选择订单和目标路线')
        return
    }
  
    try {
        isAssigning.value = true
        // 直接调用API分配订单
        console.log('分配订单：', selectedOrderIds.value, '到路线：', selectedRoute.value.id)
        const result = await orderStore.assignOrdersToRouteWithAPI(
            selectedOrderIds.value,
            selectedRoute.value.id
        )
        console.log('分配结果：', result)
    
        // 成功提示
        const count = selectedOrderIds.value.length
        ElMessage.success(`已成功分配${count}个订单到路线${formatRouteNumber(selectedRoute.value.name)}`)
    
        // 重置选择
        clearOrderSelection()
        
        // 确保所有组件都能立即显示最新状态
        orderStore.refreshOrderDisplay()
    } catch (error) {
        console.error('分配订单到路线失败:', error)
        ElMessage.error('分配订单到路线失败，请重试')
    } finally {
        isAssigning.value = false
    }
}

// 取消分配订单
const unassignOrders = async () => {
    if (selectedOrderIds.value.length === 0) {
        ElMessage.warning('请先选择要取消分配的订单')
        return
    }
  
    try {
        isUnassigning.value = true
        console.log('开始取消分配订单：', selectedOrderIds.value)
    
        // 使用API取消分配
        const result = await orderStore.unassignOrdersWithAPI(selectedOrderIds.value)
        console.log('取消分配结果：', result)
    
        // 重置选择
        clearOrderSelection()
    
        // 成功提示
        ElMessage.success(`已成功取消分配${selectedOrderIds.value.length}个订单`)
        
        // 确保所有组件都能立即显示最新状态
        orderStore.refreshOrderDisplay()
    } catch (error) {
        console.error('取消分配订单失败:', error)
        ElMessage.error('取消分配订单失败，请重试')
    } finally {
        isUnassigning.value = false
    }
} 