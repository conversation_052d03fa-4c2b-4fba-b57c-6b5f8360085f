<script setup name="SettingsDeliveryDestinationsList">
import { Delete } from '@element-plus/icons-vue'
import FormMode from './components/FormMode/index.vue'
import { getDeliveryDestinations, deleteDeliveryDestination, updateDeliveryDestinationPriority } from '@/api/modules/settings'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()


const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        row: { id: '' }
    },
    dataList: []
})



onMounted(() => {
    getDataList()
})


function getDataList() {
    data.value.loading = true
    getDeliveryDestinations().then(res => {
        data.value.loading = false
        data.value.dataList = res.data
    })
}

function onCreate() {
    data.value.formModeProps.row = { id: '' }
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.row = row
    data.value.formModeProps.visible = true
}

function onUpdatePriority(row) {
    let params = { id: row.id, priority: Number(row.priority) }
    updateDeliveryDestinationPriority(params).then((res) => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })

}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.en }), t('dialog.titles.confirmation')).then(() => {
        deleteDeliveryDestination({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

</script>

<template>
    <div>
        <page-header :title="$t('settings.deliveryDestinations.title')" />
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit">
                <el-table-column prop="priority" :label="$t('fields.priority')" align="center" width="80">
                    <template #default="scope">
                        <el-input v-model="scope.row.priority" :input-style="{ 'text-align': 'center' }"
                            :disabled="scope.row.is_default" @blur="onUpdatePriority(scope.row)"
                            @keyup.enter="onUpdatePriority(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="name" :label="$t('settings.deliveryDestinations.fields.name')" width="100"
                    align="center" />
                <el-table-column prop="en" :label="$t('settings.deliveryDestinations.fields.en')" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button :disabled="scope.row.is_default" type="danger" :icon="Delete" circle size="small"
                                @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
        <FormMode :row="data.formModeProps.row" v-model="data.formModeProps.visible" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-table {
    font-size: 0.8em;
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
