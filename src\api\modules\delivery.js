import { GET, POST, PUT, PAT, DEL, UPL, POSTFormData } from "../methods";

const path = 'delivery/'

const servicePath = path + 'services/'

export const getServicesByOrder = (params) => GET(servicePath + 'list/by-order/', params)
export const getServices = (params) => GET(servicePath + 'list/', params)
export const getAllServices = () => GET(servicePath + 'all/')

export const getPackageTypes = (params) => GET(servicePath + 'package-types/', params)
export const addPackageType = (params) => POST(servicePath + 'package-types/', params)
export const updatePackageType = (params) => PUT(servicePath + 'package-types/', params)
export const updatePackageTypeAvailabilityPriority = (params) => PAT(servicePath + 'package-types/', params)
export const deletePackageType = (params) => DEL(servicePath + 'package-types/', params)

export const batchAlterServiceAvailability = (params) => POST(servicePath + 'batch-alter-availability/', params)

export const getSizeWeights = (params) => GET(servicePath + 'size-weights/', params)
export const addSizeWeight = (params) => POST(servicePath + 'size-weights/', params)
export const updateSizeWeight = (params) => PUT(servicePath + 'size-weights/', params)
export const updateSizeWeightAvailabilityPriority = (params) => PAT(servicePath + 'size-weights/', params)
export const deleteSizeWeight = (params) => DEL(servicePath + 'size-weights/', params)

export const getAddedServices = (params) => GET(servicePath + 'added-services/', params)
export const addAddedService = (params) => POST(servicePath + 'added-services/', params)
export const updateAddedService = (params) => PUT(servicePath + 'added-services/', params)
export const updateAddedServiceAvailabilityPriority = (params) => PAT(servicePath + 'added-services/', params)
export const deleteAddedService = (params) => DEL(servicePath + 'added-services/', params)
export const uploadAddedServiceBannerImg = (params) => POSTFormData(servicePath + 'added-services/banner/', params)

export const getPackaging = (params) => GET(servicePath + 'packaging/', params)
export const addPackaging = (params) => POST(servicePath + 'packaging/', params)
export const updatePackaging = (params) => PUT(servicePath + 'packaging/', params)
export const updatePackagingAvailabilityPriority = (params) => PAT(servicePath + 'packaging/', params)
export const deletePackaging = (params) => DEL(servicePath + 'packaging/', params)


const timetablePath = path + 'timetables/'
const pickupTimePath = timetablePath + 'pickup-times/'
const deliveryTimePath = timetablePath + 'delivery-times/'
export const getPickupTimes = (params) => GET(pickupTimePath, params)
export const addPickupTime = (params) => POST(pickupTimePath, params)
export const updatePickupTime = (params) => PUT(pickupTimePath, params)
export const alterPickupTimeAvailability = (params) => PAT(pickupTimePath, params)
export const deletePickupTime = (params) => DEL(pickupTimePath, params)
export const getPickupTimeByPc = (params) => POST(pickupTimePath + 'get-by-pc/', params)

export const getDeliveryTimes = (params) => GET(deliveryTimePath, params)
export const addDeliveryTime = (params) => POST(deliveryTimePath, params)
export const updateDeliveryTime = (params) => PUT(deliveryTimePath, params)
export const alterDeliveryTimeAvailability = (params) => PAT(deliveryTimePath, params)
export const deleteDeliveryTime = (params) => DEL(deliveryTimePath, params)
export const getDeliveryTimeByPc = (params) => POST(deliveryTimePath + 'get-by-pc/', params)


export const batchAlterTimetableAvailability = (params) => POST(timetablePath + 'batch-alter-availability/', params)

export const getUserAddressBooks = (params) => GET(path + 'address-book/', params)

// Orders
const ordersPath = path + 'orders/'
export const getDeliveryOrders = params => GET(ordersPath, params)
export const getDeliveryOrderAddresses = id => GET(ordersPath + `addresses/${id}/`)
export const updateDeliveryOrder = params => PUT(ordersPath, params)

export const searchDeliveryOrderByPartialNum = num => GET(ordersPath + `search/${num}/`)

export const getEODDeliveryOrders = params => POST(ordersPath + 'eod-list/', params)
export const stableEODDeliveryOrders = params => POST(ordersPath + 'eod-list/stable/', params)
export const alterDeliveryOrdersStatus = params => PAT(ordersPath, params)

export const processDeliveryOrder = params => POST(ordersPath + 'process/', params)
export const process2DeliveryOrder = params => POST(ordersPath + 'process2/', params)
export const processPickupOrders = params => POST(ordersPath + 'process/pickups/', params)
export const processDeliveryOrders = params => POST(ordersPath + 'process/deliveries/', params)

export const closeDeliveryOrder = params => POST(ordersPath + 'close/', params)

export const refundDeliveryOrder = params => POST(ordersPath + 'refund/', params)

export const addOrderNote = params => POST(ordersPath + 'notes/add/', params)
export const removeOrderNote = params => POST(ordersPath + 'notes/remove/', params)

export const downloadShippingLabel = params => POST(ordersPath + 'shipping-label/', params)

export const addPickupPhoto = (id, params) => UPL(ordersPath + `photos/pickup/add/${id}/`, params)
export const removePickupPhoto = (params) => POST(ordersPath + `photos/pickup/remove/`, params)
export const addDeliveryPhoto = (id, params) => UPL(ordersPath + `photos/delivery/add/${id}/`, params)
export const removeDeliveryPhoto = (params) => POST(ordersPath + `photos/delivery/remove/`, params)

// Logs
export const getOrderLogs = params => GET(path + 'logs/', params)

// Fees
const feesPath = path + 'fees/'
export const getHolidayWeekdayFees = params => GET(feesPath + 'holiday-weekday/', params)


// Admin orders
const adminOrdersPath = path + 'admin-orders/'
export const getAdminOrders = params => GET(adminOrdersPath, params)
export const addAdminOrders = params => POST(adminOrdersPath, params)
export const generatePaymentOrder = params => GET(adminOrdersPath + 'generate-po/', params)


// User orders
const userOrdersPath = path + 'user-orders/'
export const getUserOrders = params => GET(userOrdersPath, params)
export const getUserOrder = id => GET(`${userOrdersPath}/${id}/`)
export const downloadUOShippingLabel = params => POST(ordersPath + 'shipping-label/uo/', params)
export const downloadUOAllShippingLabel = params => POST(ordersPath + 'shipping-label/uo/all/', params)



// Settings
const settingsPath = path + 'settings/'
export const getDeliverySettings = () => GET(settingsPath)
export const updateDeliverySettings = params => PUT(settingsPath, params)

export const getHolidayFees = () => GET(path + 'fees/holidays/')
export const addHolidayFee = params => POST(path + 'fees/holidays/', params)
export const updateHolidayFee = params => PUT(path + 'fees/holidays/', params)
export const updateHolidayFeeAvailability = params => PAT(path + 'fees/holidays/', params)
export const deleteHolidayFee = params => DEL(path + 'fees/holidays/', params)

export const getWeekDayFees = () => GET(path + 'fees/weekdays/')
export const addWeekDayFee = params => POST(path + 'fees/weekdays/', params)
export const updateWeekDayFee = params => PUT(path + 'fees/weekdays/', params)
export const updateWeekDayFeeAvailability = params => PAT(path + 'fees/weekdays/', params)
export const deleteWeekDayFee = params => DEL(path + 'fees/weekdays/', params)

// Distribution Centers
const dcPath = path + 'distribution-centers/'
export const getDistributionCenters = params => GET(dcPath, params)
export const addDistributionCenter = params => POST(dcPath, params)
export const updateDistributionCenter = params => PUT(dcPath, params)
export const updateDistributionCenterAvailabilityPriority = params => PAT(dcPath, params)
export const deleteDistributionCenter = params => DEL(dcPath, params)

// Utils

// Suppliers
const suppliersPath = path + 'suppliers/'
export const getDeliverySuppliers = params => GET(suppliersPath, params)
export const addDeliverySupplier = params => POST(suppliersPath, params)
export const updateDeliverySupplier = params => PUT(suppliersPath, params)
export const updateDeliverySupplierAvailabilityPriority = params => PAT(suppliersPath, params)
export const deleteDeliverySupplier = params => DEL(suppliersPath, params)
// Utils
export const getDestinations = () => GET(suppliersPath + 'utils/destinations/')
export const uploadDeliverySupplierLogo = params => UPL(suppliersPath + 'upload-logo/', params)

// Supplier Stores
const supplierStorePath = suppliersPath + 'stores/'
export const getDeliverySupplierStores = params => GET(supplierStorePath, params)
export const addDeliverySupplierStore = params => POST(supplierStorePath, params)
export const updateDeliverySupplierStore = params => PUT(supplierStorePath, params)
export const updateDeliverySupplierStoreAvailabilityPriority = params => PAT(supplierStorePath, params)
export const deleteDeliverySupplierStore = params => DEL(supplierStorePath, params)


// Utils
export const getPackageTypeList = () => GET(suppliersPath + 'utils/package-types/')
export const getSizeWeightList = () => GET(suppliersPath + 'utils/size-weights/')
export const getHandlers = () => GET(dcPath + 'utils/handlers/')
export const getZones = () => GET(dcPath + 'utils/zones/')
export const getPickupTimeList = () => GET(dcPath + 'utils/pickup-times/')
export const getDeliveryTimeList = () => GET(dcPath + 'utils/delivery-times/')
export const getHolidays = () => GET(dcPath + 'utils/holidays/')


// Service Packs
const servicePackItemPath = servicePath + 'pack-items/'
export const getServicePackItems = (params) => GET(servicePackItemPath, params);
export const getAllServicePackItems = () => GET(servicePackItemPath + 'all/');
export const updateServiePackItemPriority = (params) => PAT(servicePackItemPath, params);
export const updateServiePackItem = (params) => PUT(servicePackItemPath, params);
export const createServicePackItem = (params) => POST(servicePackItemPath, params);
export const deleteServicePackItem = (params) => DEL(servicePackItemPath, params);

const servicePackPath = servicePath + 'packs/'
export const getServicePacks = (params) => GET(servicePackPath, params);
export const getAllServicePacks = () => GET(servicePackPath + 'all/');
export const updateServiePackPriority = (params) => PAT(servicePackPath, params);
export const updateServiePack = (params) => PUT(servicePackPath, params);
export const createServicePack = (params) => POST(servicePackPath, params);
export const deleteServicePack = (params) => DEL(servicePackPath, params);


// Marks
const marksPath = ordersPath + 'marks/'
export const getAllOrderMarks = () => GET(marksPath + 'all/')
export const getOrderMarks = (params) => GET(marksPath, params)
export const addOrderMark = (params) => POST(marksPath, params)
export const updateOrderMark = (params) => PUT(marksPath, params)
export const deleteOrderMark = (params) => DEL(marksPath, params)


// Extended Orders
const extendedOrderPath = path + 'extended-orders/';
export const getExtendedOrders = (params) => GET(extendedOrderPath, params);
export const deleteExtendedOrder = (id) => DEL(extendedOrderPath + `${id}/`);
export const submitExtendedOrder = (id, params) => GET(extendedOrderPath + `submit/${id}/`, params);
export const closeExtendedOrder = (id) => GET(extendedOrderPath + `close/${id}/`);


// Add Service
const addServicePath = ordersPath + 'services/';
export const getSupplementServices = (id) => GET(addServicePath + `${id}/`);
export const addSupplementService = (id, params) => POST(addServicePath + `url/${id}/`, params)
