/*
* Author: <EMAIL>'
* Date: '2023-11-30 01:50:39'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/web_contents.js'
* File: 'web_contents.js'
* Version: '1.0.0'
*/

import { DEL, GET, PAT, PUT, POSTFormData, } from "../methods";

const path = 'public-contents/';

const feePackPath = path + 'fee-packs/'
export const getFeePacks = (params) => GET(feePackPath, params);
export const updateFeePack = (params) => POSTFormData(feePackPath, params)
export const updateFeePackPriority = (params) => PUT(feePackPath, params)
export const updateFeePackAvailability = (params) => PAT(feePackPath, params)
export const deleteFeePack = (params) => DEL(feePackPath, params)


const webContentPath = path + 'home-contents/'
export const getWebContents = () => GET(webContentPath);
export const updateWebContents = (params) => POSTFormData(webContentPath, params);
