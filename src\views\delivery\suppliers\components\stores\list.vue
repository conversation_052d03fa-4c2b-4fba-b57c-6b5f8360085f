<script setup name="DeliverySuppliersSupplierStoresList">
import { Delete } from '@element-plus/icons-vue'
import FormMode from './components/FormMode/index.vue'
import {
    getDeliverySupplierStores,
    updateDeliverySupplierStoreAvailabilityPriority,
    deleteDeliverySupplierStore
} from '@/api/modules/delivery'
import { phoneNumberFormatter } from '@/utils/formatter'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const router = useRouter()
// const route = useRoute()

const props = defineProps({
    supplierId: {
        type: String,
        default: ''
    },
    zones: {
        type: Array,
        default: []
    }

})


const data = ref({
    supplierId: props.supplierId,
    loading: false,
    formModeProps: {
        visible: false,
        id: '',
    },
    // 列表数据
    dataList: []
})

onMounted(() => {
    getDataList()
})


function getDataList() {
    data.value.loading = true
    let params = { supplier_id: data.value.supplierId }
    getDeliverySupplierStores(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data
    })
}


function onCreate() {
    data.value.formModeProps.id = null
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.visible = true
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deleteDeliverySupplierStore({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onUpdatePriority(row) {
    let params = {
        id: row.id,
        priority: row.priority
    }
    updateDeliverySupplierStoreAvailabilityPriority(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}


function onUpdateAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    updateDeliverySupplierStoreAvailabilityPriority(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>
<template>
    <div>
        <page-main :title="$t('delivery.supplierStores.title')">
            <template #extra>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </template>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit">
                <el-table-column prop="priority" :label="$t('fields.priority')" align="center" width="80">
                    <template #default="scope">
                        <el-input v-model="scope.row.priority" :input-style="{ 'text-align': 'center' }"
                            @blur="onUpdatePriority(scope.row)" @keyup.enter="onUpdatePriority(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="sku" :label="$t('fields.sku')" width="110" />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="contact" :label="$t('fields.contact')" width="160" align="center" />
                <el-table-column prop="phone_number" :label="$t('user.fields.phoneNumber')" width="160" align="center">
                    <template #default="scope">
                        {{ phoneNumberFormatter(scope.row.phone_number) }}
                    </template>
                </el-table-column>
                <el-table-column prop="email" :label="$t('user.fields.email')" width="160" align="center"
                    show-overflow-tooltip />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>

                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
        <FormMode :id="data.formModeProps.id" :zones="props.zones" :supplier-id="props.supplierId"
            v-model="data.formModeProps.visible" mode="dialog" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
