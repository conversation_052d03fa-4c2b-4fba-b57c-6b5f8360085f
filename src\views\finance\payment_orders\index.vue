
<script setup name="PaymentOrderIndex">
import { getPaymentGeneralStatistics, getPaymentOrderStatistics, getPaymentsStatistics } from '@/api/modules/payment'
import { currencyFormatter } from '@/utils/formatter'
import DonutPlot from './components/donut_plot.vue'
import OrderColumn from './components/order_columns.vue'
import PaymentColumn from './components/payment_column.vue'



const router = useRouter()

onMounted(() => {
    getStatistics()
})

const statistics = ref({
    total: {
        today_counts: 0,
        order_counts: 15117,
        total_sum: 38888938,
        sub_sum: 34367700,
        tax_sum: 4468438,
        tip_sum: 47900,
        refunded_sum: 1115239
    },
    month: {
        total: {
            order_counts: 315,
            total_sum: 1024545,
            refunded_sum: 11752
        },
        last: {
            order_counts: 716,
            total_sum: 1419054,
            refunded_sum: 6441
        },
        delta: {
            order_counts: -56.0,
            total_sum: -27.8,
            refunded_sum: 82.5
        }
    },
    cash: {
        total: {
            cc: 19780095,
            ap: 1836564,
            total: 21616659
        },
        month: {
            cc: 0,
            ap: 110513,
            total: 110513
        },
        last: {
            cc: 7910,
            ap: 244530,
            total: 252440
        },
        delta: {
            cc: -100.0,
            ap: -54.8,
            total: -56.2
        }
    }
})


function getStatistics() {
    getPaymentGeneralStatistics().then(res => {
        statistics.value = res.data
    })

}

function onUpdateTotal(data) {
    statistics.value = data
}

function goAnchor(id) {
    let anchor = document.getElementById(id)
    anchor.scrollIntoView()
}

function gotoList() {
    router.push('/users/List')
}
</script>

<template>
    <div>
        <page-header :title="$t('finance.paymentOrder.statistics.title')" />
        <page-main>
            <el-row :gutter="20">
                <el-col :span="6">
                    <ColorfulCard :header="$t('finance.paymentOrder.statistics.totalCounts')"
                        :num="statistics.total?.order_counts"
                        :tip="$t('finance.paymentOrder.statistics.todayCounts', { num: statistics.total?.today_counts })"
                        @click="gotoList" />
                </el-col>
                <el-col :span="6">
                    <ColorfulCard color-from="#fbaaa2" color-to="#fc5286"
                        :header="$t('finance.paymentOrder.statistics.monthOrders')"
                        :num="(statistics.month?.total.order_counts)"
                        :tip="$t('finance.paymentOrder.statistics.monthDelta', { num: statistics.month?.delta.order_counts })"
                        icon="ep:element-plus" @click="goAnchor('credit-stat')">
                    </ColorfulCard>
                </el-col>
                <el-col :span="6">
                    <ColorfulCard color-from="#ff763b" color-to="#ffc480"
                        :header="$t('finance.paymentOrder.statistics.monthIncoming')"
                        :num="currencyFormatter(_, _, statistics.month?.total.total_sum)"
                        :tip="$t('finance.paymentOrder.statistics.monthDelta', { num: statistics.month?.delta.total_sum })"
                        icon="ri:pages-line" @click="goAnchor('dau-stat')" />
                </el-col>
                <el-col :span="6">
                    <ColorfulCard color-from="#6a8eff" color-to="#0e4cfd"
                        :header="$t('finance.paymentOrder.statistics.cash')"
                        :num="currencyFormatter(_, _, statistics.cash?.total?.total)"
                        :tip="$t('finance.paymentOrder.statistics.monthCash', { num: statistics.cash ? currencyFormatter(_, _, statistics.cash.month.total) : 0 })"
                        icon="ep:link" @click="goAnchor('origin-stat')" />
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="6">
                    <DonutPlot k="total" @update-total="onUpdateTotal" />
                </el-col>
                <el-col :span="6">
                    <DonutPlot k="month" />
                </el-col>
                <el-col :span="6">
                    <DonutPlot k="cashMonth" />
                </el-col>
                <el-col :span="6">
                    <DonutPlot k="cash" />
                </el-col>
            </el-row>
        </page-main>
        <OrderColumn id="dau-stat" :title="$t('finance.paymentOrder.title')" :action="getPaymentOrderStatistics"
            :is-stack="true" />
        <PaymentColumn :title="$t('finance.paymentOrder.statistics.paymentStatistics')" :action="getPaymentsStatistics"
            :is-stack="true" />
    </div>
</template>

