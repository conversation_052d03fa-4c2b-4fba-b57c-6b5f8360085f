<!--
Author: <EMAIL>'
Date: '2023-06-03 16:38:35'
Project: 'FleetNowV3'
Path: 'src/views/messenger/cs_messages/components/TabContent/messages.vue'
File: 'messages.vue'
Version: '1.0.0'
-->

<script setup name="CSMessagesChatTab">
import useMessagesStore from '@/store/modules/messages'
import { nextTick } from 'vue'
import { Promotion, WarnTriangleFilled } from '@element-plus/icons-vue'
import { transferConversation, getOtherActiveAdministrators } from '@/api/modules/messenger'

const props = defineProps({
    currentUserPk: {
        type: String,
        default: '',
    },
})

const messageStore = useMessagesStore()

const emit = defineEmits(['messageSent', 'setCurrentUser'])

const currentUserPk = ref()

watch(() => messageStore.messages(currentUserPk.value).length, val => {
    if (val > 0) {
        nextTick(autoScroll)
    }
}, { immediate: true })

const data = ref({
    loading: false,
})

watch(() => messageStore.activeUsers.length, (val, oldVal) => {
    if (oldVal == 0 && val > 0) {
        currentUserPk.value = messageStore.activeUsers[0].id;
        emit('setCurrentUser', currentUserPk.value);
    } else if (val == 0) {
        currentUserPk.value = null
        emit('setCurrentUser', null);
    }
})

onMounted(() => {
    if (props.currentUserPk != '') {
        currentUserPk.value = props.currentUserPk;
        nextTick(autoScroll);
    }
    else if (messageStore.activeUsers.length) {
        currentUserPk.value = messageStore.activeUsers[0].id;
        emit('setCurrentUser', currentUserPk.value);
        nextTick(autoScroll);
    }

})

const currentToken = computed(() => {
    if (currentUserPk.value != null && messageStore.messages(currentUserPk.value).length) {
        if (messageStore.messages(currentUserPk.value)[0].user_id) { return messageStore.messages(currentUserPk.value)[0].from_token; }
    }
})



const text = ref('')

function onSend() {
    if (!currentToken.value) return;
    data.value.loading = true;
    messageStore.sendMessage(currentToken.value, text.value, 'csChat', currentUserPk.value).then(() => {
        text.value = ''
        emit('messageSent')
        nextTick(autoScroll)
        data.value.loading = false;
    })
}

function autoScroll() {
    const chatWindow = document.querySelector('.chat-box')
    chatWindow.scrollTop = chatWindow.scrollHeight
}

function onEndConversation() {
    messageStore.endConversation(currentUserPk.value);
}

const transferDialogVisible = ref(false)
const activeAdministrators = ref([])
const transferToTokens = ref([])

function onTransfer() {
    data.value.loading = true;
    // Get Active admin tokens (excluded self's)
    getOtherActiveAdministrators().then(res => {
        if (res.data?.length) {
            // If active tokens exist, show dialog to select tokens
            activeAdministrators.value = res.data
            transferDialogVisible.value = true
        }
        else {
            // Else show message: No other active admin
            ElMessageBox.alert('There\'s no other active administrators', 'Not able to transfer')
        }
        data.value.loading = false;
    })
}

function onConfirmTransfer() {
    data.value.loading = true;
    let params = {
        userId: currentUserPk.value,
        userToken: currentToken.value,
        adminTokens: transferToTokens.value
    }
    transferConversation(params).then(res => {
        if (res.data.errCode == 365) {
            onCancelTransfer();
        }
        data.value.loading = false;
    })

}
function onCancelTransfer() {
    transferDialogVisible.value = false;
    transferToTokens.value.length = 0;
    activeAdministrators.value.length = 0;
}

</script>
<template>
    <div>
        <el-tabs tab-position="left" v-if="messageStore.activeUsers.length" style="height: 100%" v-model="currentUserPk">
            <el-tab-pane v-for=" item in messageStore.activeUsers" :name="item.id">
                <template #label>
                    <el-tooltip effect="light">
                        <template #content>
                            <el-descriptions :column="1">
                                <template #title>
                                    <el-space>
                                        <el-avatar size="small">
                                            <img :src="item.avatar" v-if="item.avatar">
                                            <span v-else style="font-size: 0.7em;">
                                                {{
                                                    item.user_code
                                                    ? item.user_code
                                                    : item.name?.toUpperCase()[0]
                                                }}
                                            </span>
                                        </el-avatar>
                                        {{ item.name }}
                                    </el-space>
                                </template>
                                <el-descriptions-item :label="$t('user.fields.name')">
                                    {{ item.contact }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.email')">
                                    {{ item.email }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.phoneNumber')">
                                    {{ item.phone }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.group')">
                                    <ElTag v-if="item.user_group" size="small" type="success">
                                        {{ item.user_group.name }}
                                    </ElTag>
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.type')">
                                    <ElTag v-if="item.user_type === 'business'" size="small">
                                        B
                                    </ElTag>
                                    <ElTag v-else size="small" type="info">
                                        P
                                    </ElTag>
                                </el-descriptions-item>
                            </el-descriptions>
                        </template>
                        {{ item.name }}
                    </el-tooltip>
                </template>
                <div style="margin-right: 24px;">
                    <div class="chat-box">
                        <template v-if="messageStore.messages(item.id).length">
                            <template v-for="msg in messageStore.messages(item.id)">
                                <div class="message-item-title">
                                    <el-space>
                                        <el-text tag="b" :type="msg.admin_name ? 'warning' : 'success'">
                                            {{ msg.admin_name ? msg.admin_name : msg.user_name }}
                                        </el-text>
                                        <el-tooltip content="发送" v-if="msg.admin_name != null && msg.status == 'created'">
                                            <el-button size="small" text type="success" :icon="Promotion"
                                                @click="onSend(msg)" />
                                        </el-tooltip>
                                        <el-tooltip content="再次发送"
                                            v-else-if="msg.admin_name != null && msg.status == 'failed'">
                                            <el-button size="small" text type="warning" :icon="WarnTriangleFilled"
                                                @click="onSend(msg)" />
                                        </el-tooltip>
                                    </el-space>
                                    <el-text tag="sup" size="small">{{ msg.created_at }}</el-text>
                                </div>
                                <el-space>
                                    <el-tooltip content="新消息" v-if="!msg.admin_name && !msg.is_read">
                                        <span class="badge badge-dot"></span>
                                    </el-tooltip>
                                    <el-text tag="p">{{ msg.content }}</el-text>
                                </el-space>
                            </template>
                        </template>
                        <el-empty v-else />
                    </div>
                    <div class="message-text-area">
                        <el-input type="textarea" resize="none" :autosize="{ minRows: 5, maxRows: 5 }" v-model="text"
                            :disabled="data.loading" @keydown.enter.exact.prevent="onSend()" />
                        <div
                            style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; margin-top: 10px;">
                            <el-space>
                                <el-button type="danger" plain @click="onEndConversation"
                                    :loading="data.loading">End</el-button>
                                <el-button type="success" plain @click="onTransfer" :loading="data.loading"
                                    :disabled="!currentToken">Transfer</el-button>
                            </el-space>
                            <el-button type="primary" :disabled="text.length <= 0 || !currentToken" @click="onSend"
                                :loading="data.loading">
                                Send (Enter)
                            </el-button>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
        <el-empty v-else />
        <el-dialog v-model="transferDialogVisible" title="Choose Administrators to transfer" width="40%">
            <el-transfer v-model="transferToTokens" :data="activeAdministrators" :titles="['Active Admins', 'Transfer to']"
                :props="{
                    key: 'token',
                    label: 'name',
                }" style="width: 100%;">
                <template #default="{ option }">
                    <el-space>
                        <span class="avatar"><img :src="option.avatar"></span>
                        {{ option.name }}
                    </el-space>
                </template>
            </el-transfer>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelTransfer">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmTransfer" :disabled="!transferToTokens?.length">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template >

<style lang="scss">
.chat-box {
    font-size: 0.75em;
    height: calc(100vh - 582px);
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    padding: 20px;
    padding-top: 0;

    .name {
        font-weight: bolder;
    }

    .date {
        color: gray;
        font-size: 0.6em;
    }

    .admin-name {
        color: green;
    }

    .user-name {
        color: blue;
    }
}

.el-transfer-panel {
    width: 38%;
}




.message-text-area {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
}


.badge {
    background-color: var(--g-badge-bg);
    transition: background-color 0.3s, var(--el-transition-box-shadow);


    &-dot {
        right: 15px;
        border-radius: 50%;
        width: 6px;
        height: 6px;

        &::after {
            content: "";
            pointer-events: none;
            display: block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            animation: dot-twinkling 1s infinite ease-in-out;
            background-color: var(--g-badge-bg);
            transition: background-color 0.3s;
        }
    }

    @keyframes dot-twinkling {

        0% {
            opacity: 0.6;
            transform: scale(0.8);
        }

        100% {
            opacity: 0;
            transform: scale(2.4);
        }
    }
}

.avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    overflow: hidden;
    background-color: blue;

    /* commented for demo
  float: left;
  margin-left: 125px;
  margin-top: 20px;
  */

    /* for demo */
    display: inline-block;
    vertical-align: middle;

    img {
        width: 100%;
    }
}
</style>
