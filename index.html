<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="stylesheet" href="/browser_upgrade/index.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script type="module" src="/background_messages_storage.js"></script>
    <title>
        <%= title %>
    </title>
    <%- copyrightScript %>
</head>

<body>
    <div id="app">
        <div class="fantastic-admin-home">
            <div class="loading">
                <div class="square"></div>
                <div class="square"></div>
                <div class="square"></div>
                <div class="square"></div>
            </div>
            <div class="text">
                <%= title %>载入中…
            </div>
        </div>
        <!-- <div id="browser-upgrade">
            <div class="title">为了您的体验，推荐使用以下浏览器</div>
            <div class="browsers">
                <a href="https://www.microsoft.com/edge" target="_blank" class="browser">
                    <img class="browser-icon" src="/browser_upgrade/edge.png" />
                    <div class="browser-name">Mircosoft Edge</div>
                </a>
                <a href="https://www.google.cn/chrome/" target="_blank" class="browser">
                    <img class="browser-icon" src="/browser_upgrade/chrome.png" />
                    <div class="browser-name">Google Chrome</div>
                </a>
            </div>
        </div> -->
    </div>
    <script>
        // if(!!window.ActiveXObject || "ActiveXObject" in window) {
        //     document.getElementById('browser-upgrade').style.display = 'block'
        // } else {
        var Loading = document.querySelector('.loading')
        Loading.classList.add('animate')
        Loading.addEventListener('animationend', function () {
            setTimeout(function () {
                Loading.classList.remove('animate')
            }, 600)
            setTimeout(function () {
                Loading.classList.add('animate')
            }, 1000)
        })
        // }
    </script>
    <script type="module" src="/src/main.js"></script>
    <% if (debugTool=='eruda' ) { %>
        <script src="//unpkg.com/eruda/eruda.js"></script>
        <script>eruda.init();</script>
        <% } %>
            <% if (debugTool=='vconsole' ) { %>
                <script src="//unpkg.com/vconsole/dist/vconsole.min.js"></script>
                <script>new VConsole();</script>
                <% } %>
</body>

</html>
