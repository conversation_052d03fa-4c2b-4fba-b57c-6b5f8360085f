<script setup name="DeliveryOrderForm">
import { getDeliverySettings, addAdminOrders } from '@/api/modules/delivery';
import { getUserFee, getUserSimpleList } from '@/api/modules/users'
import { currencyFormatter } from '@/utils/formatter';
import { Delete } from '@element-plus/icons-vue'

import PackageVue from './order_item.vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const emit = defineEmits(['submitted', 'cancelled'])

const pkgRefs = ref([])
const data = ref({
    loading: false,
    deliveryFee: 0,
    taxRate: 13,
    user: undefined,
    dataList: [],
})

const activePkg = ref(0)

const pkgObj = {
    quantity: 1,
    notes: null,

    senderAddressId: null,
    sender_name: null,
    sender_company: null,
    sender_phone: null,
    sender_secondary_phone: null,
    sender_extension_no: null,
    sender_email: null,
    sender_unit_no: null,
    sender_buzzer_code: null,
    sender_address: null,
    sender_postal_code: null,
    sender_city: null,
    sender_province: null,

    receiverAddressId: null,
    receiver_name: null,
    receiver_company: null,
    receiver_phone: null,
    receiver_secondary_phone: null,
    receiver_extension_no: null,
    receiver_email: null,
    receiver_unit_no: null,
    receiver_buzzer_code: null,
    receiver_address: null,
    receiver_postal_code: null,
    receiver_city: null,
    receiver_province: null,
    tip: 0,
    subtotal: 0,
    is_express: false,

    adminNotes: [],
    expectedPickupTime: undefined,
    expectedDeliveryTime: undefined,
    packageType: undefined,
    sizeWeight: undefined,
    addedServices: [],
    packaging: [],
    promotions: [],
    insurancePolicy: undefined,

    user: null,
}

const userList = ref()

onMounted(() => {
    getSettings()
})

// Api
function getSettings() {
    getDeliverySettings().then(res => {
        if (!res.data.errCode) {
            data.value.deliveryFee = res.data.delivery_fee
            data.value.taxRate = res.data.tax_rate
        }
    })
}

function getUsers(query) {
    if (query && query.length > 3) {
        getUserSimpleList({ terms: query }).then(res => {
            userList.value = res.data
        })
    } else {
        userList.value = []
    }
}

watch(() => data.value.user?.id, val => {
    if (val) {
        getUserFee({ userId: val }).then(res => {
            if (!res.data.errCode) {
                if (res.data) {
                    data.value.deliveryFee = res.data
                } else {
                    getDeliverySettings()
                }
            }
        })

    }
})

// Operations
defineExpose({
    async submit(callback) {
        let ret = await validate()
        if (ret) {
            addAdminOrders(data.value.dataList).then(res => {
                if (res.data.errCode == 365) {
                    callback && callback()
                }
            })
        }
    }
})

const onSubmit = () => emit('submitted')
const onCancel = () => emit('cancelled')

const onAddPackage = async () => {
    if (data.value.dataList && data.value.dataList.length) {
        let ret = await validate()
        if (ret) {
            addPackage()
        }
    } else {
        addPackage()
    }
}

const addPackage = () => {
    data.value.dataList.push(JSON.parse(JSON.stringify(pkgObj)))
    pkgRefs.value.push(ref(0))
    activePkg.value = data.value.dataList.length - 1
    window.scrollTo(0, 0)
}
const removePackage = idx => {
    pkgRefs.value.splice(idx, 1)
    data.value.dataList.splice(idx, 1)
}
const validate = async () =>
    await pkgRefs.value.reduce((ret, pkg) => {
        let isValid = pkg.value[0].validate()
        ret = ret && isValid
        return ret
    }, true)


// Fee
const tipTotal = computed(() => {
    if (data.value.dataList.length) {
        return data.value.dataList.reduce(
            (total, pkg) => {
                total += pkg.tip
                return total
            }, 0)
    } else {
        return 0
    }
})

const subtotal = computed(() =>
    pkgRefs.value.reduce((total, p) => {
        total += p.value[0]?.subtotal || 0
        return total
    }, 0)
)

</script>

<template>
    <div v-loading=data.loading>
        <page-main>
            <el-card :header="$t('delivery.orders.fields.user')" shadow="hover">
                <el-select v-model="data.user" value-key="id" filterable remote reserve-keyword clearable
                    :remote-method="getUsers" placeholder="Input user name or company name to search">
                    <el-option v-for="item of userList" :value="item" :label="item.name">
                        {{ item.name }} {{ item.code ? (`(${item.code})`) : '' }}
                    </el-option>
                </el-select>
            </el-card>
        </page-main>
        <page-main v-if="data.dataList.length">
            <el-collapse v-model="activePkg">
                <el-collapse-item v-for="pkg, idx in data.dataList" :title="'Package ' + (idx + 1)" :name="idx">
                    <template #title>
                        <div class="package-title">
                            <span>
                                {{ $t('delivery.adminOrders.sections.package') }} &ThickSpace; {{ idx + 1}}
                                <el-button :icon="Delete" circle type="danger" plain size="small"
                                    @click="removePackage(idx)" />
                            </span>
                            <span v-if="pkgRefs[idx].value[0]?.isComplete === false" class="warning-package">{{
                                $t('delivery.adminOrders.messages.checkPackage')
                            }}</span>
                            <span>$&ThickSpace; {{ currencyFormatter(null, null, pkg.subtotal) }}</span>
                        </div>
                    </template>
                    <PackageVue :package="pkg" :delivery-fee="data.deliveryFee" :user-id="data.user?.id"
                        :ref="pkgRefs[idx]" />
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <fixed-action-bar>
            <el-row>
                <el-col :span="3">
                    <el-space>
                        <span>{{ $t('delivery.orders.fields.total') }} </span>
                        <span>$</span>
                        <span class="total-red">{{ currencyFormatter(null, null, subtotal + tipTotal) }}</span>
                        <span>cad</span>
                    </el-space>
                </el-col>
                <el-col :span="19">
                    <el-button size="large" type="success" @click="onAddPackage">
                        {{ $t('operations.add') }}
                    </el-button>
                    <el-button type="primary" size="large" @click="onSubmit" :disabled="!data.dataList.length">
                        {{ $t('operations.confirm') }}
                    </el-button>
                    <el-button size="large" @click="onCancel" :disabled="!data.dataList.length">
                        {{ $t('operations.cancel') }}
                    </el-button>
                </el-col>
            </el-row>
        </fixed-action-bar>
    </div>
</template>

<style lang="scss" scoped>
.total-red {
    color: red;
    font-weight: bolder;
}

.warning-package {
    color: red;
    font-weight: bolder;
}

.package-title {
    width: 90%;
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
}
</style>
