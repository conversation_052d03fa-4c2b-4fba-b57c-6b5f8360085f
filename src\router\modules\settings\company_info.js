/*
* Author: <EMAIL>'
* Date: '2024-03-13 19:19:47'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/settings/company_info.js'
* File: 'company_info.js'
* Version: '1.0.0'
*/



const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/company-info',
    component: Layout,
    redirect: '/settings/company-info/index',
    name: 'settingCompanyInfo',
    meta: {
        title: '公司信息',
        icon: 'mdi:company',
        auth: ['super'],
        i18n: 'route.system.companyInfo.title'
    },
    children: [
        {
            path: 'index',
            name: 'settingCompanyInfoIndex',
            component: () => import('@/views/settings/company_info/index.vue'),
            meta: {
                title: '列表',
                icon: 'bx:key',
                activeMenu: '/settings/company-info/index',
                i18n: 'route.system.companyInfo.title',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
