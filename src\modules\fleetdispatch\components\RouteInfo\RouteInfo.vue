<template>
    <div class="route-info">
        <div class="header">
            <!-- 隐藏标题，因为已经在FlexibleLayout中有了 -->
            <!-- <h3>路线信息</h3> -->
            <el-button
                :loading="loading"
                type="primary"
                circle
                @click="fetchRoutes"
            >
                <el-icon><Refresh /></el-icon>
            </el-button>
        </div>

        <el-collapse v-model="activeNames" accordion>
            <el-collapse-item
                v-for="route in routeStore.routes"
                :key="route.routeNumber"
                :name="route.routeNumber"
            >
                <template #title>
                    <div class="collapse-header">
                        <span>路线 {{ formatRouteNumber(route.name) }}</span>
                        <div class="driver-info">
                            <div class="color-dot" :style="{ backgroundColor: route.driver && driverStore.getDriverById(route.driver) ? driverStore.getDriverById(route.driver).color : '#999999' }" />
                            <span>{{ route.driver && driverStore.getDriverById(route.driver) ? driverStore.getDriverById(route.driver).name : '未分配司机' }}</span>
                        </div>
                    </div>
                </template>

                <!-- 订单列表 -->
                <el-scrollbar max-height="200px">
                    <div class="orders-list">
                        <div
                            v-for="(order, index) in orderStore.getOrdersByRouteNumber(route.id)"
                            :key="order.id"
                            class="order-item"
                        >
                            <div class="order-index" :style="{ backgroundColor: driverStore.getDriverById(route.driver).color }">
                                {{ index + 1 }}
                            </div>
                            <div class="order-content">
                                <h5>{{ order.name }}</h5>
                                <p>{{ order.address }}</p>
                                <p>订单号: {{ order.no }}</p>
                            </div>
                        </div>

                        <!-- 该路线没有订单时显示空状态 -->
                        <el-empty
                            v-if="!orderStore.getOrdersByRouteNumber(route.id).length"
                            description="暂无订单信息"
                        />
                    </div>
                </el-scrollbar>
            </el-collapse-item>

            <!-- 无数据时显示空状态 -->
            <el-empty v-if="!routeStore.routes.length" description="暂无路线信息" />
        </el-collapse>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue'
import { useRouteStore } from '@/stores/route'
import { useDriverStore } from '@/stores/driver'
import { useOrderStore } from '@/stores/order'
import { useTimeStore } from '@/stores/time'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { eventBus, EVENT_TYPES } from '@/utils/eventBus'

const timeStore = useTimeStore()
const routeStore = useRouteStore()
const driverStore = useDriverStore()
const orderStore = useOrderStore()
const activeNames = ref([])
const loading = ref(false)

// 格式化路线编号为5位数
const formatRouteNumber = num => {
    return String(num).padStart(5, '0')
}

// 获取路线信息
const fetchRoutes = async() => {
    loading.value = true
    try {
        // 确保先加载司机数据，传递 shift 参数
        console.log('获取司机数据，使用当前班次...')
        await driverStore.fetchDrivers({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift
        })
        console.log('司机数据获取完成，数量:', driverStore.drivers.length)

        // 然后加载路线数据
        if (timeStore.selectedDate && timeStore.selectedShift) {
            await routeStore.fetchRoutes({
                date: timeStore.selectedDate,
                shift: timeStore.selectedShift // 传递完整的 shift 对象
            })

            // 使用事件总线发送路线更新事件
            eventBus.emit(EVENT_TYPES.ROUTE_UPDATED, {
                routes: routeStore.routes
            })
        } else {
            console.warn('没有选择日期或班次，无法获取路线')
        }
    } catch (error) {
        console.error('获取路线信息失败:', error)
        ElMessage.error('获取路线信息失败')
    } finally {
        loading.value = false
    }
}

// 组件挂载时加载数据
onMounted(() => {
    fetchRoutes()

    // 订阅事件
    eventBus.on(EVENT_TYPES.DRIVER_SELECTED, handleDriverSelected)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
    eventBus.off(EVENT_TYPES.DRIVER_SELECTED)
})

// 处理司机选择事件
const handleDriverSelected = ({ driverId }) => {
    if (driverId) {
        // 查找司机的活动路线
        const driverRoute = routeStore.getDriverActiveRoute(driverId)
        if (driverRoute) {
            // 设置折叠面板为该路线
            activeNames.value = [driverRoute.routeNumber]
        }
    }
}

// 监听折叠面板的变化
watch(activeNames, async newVal => {
    if (newVal.length > 0) {
        const routeNumber = newVal[0]
        try {
            // 根据路线编号获取订单
            const orders = orderStore.getOrdersByRouteNumber(routeNumber)

            // 使用事件总线发送路线选择事件
            eventBus.emit(EVENT_TYPES.ROUTE_SELECTED, {
                routeNumber: routeNumber,
                orders: orders
            })
        } catch (error) {
            console.error('获取订单信息失败:', error)
            ElMessage.error('获取订单信息失败')
        }
    }
})

</script>

<style scoped>
.route-info {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eaeaea;
}

.header h3 {
    margin: 0;
    font-size: 16px;
}

.collapse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.route-count {
    color: #909399;
    font-size: 14px;
}

.routes-container {
    padding: 12px;
}

.route-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.route-item {
    transition: all 0.3s;
}

.route-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;
}

.route-number {
    font-weight: bold;
    color: #409eff;
}

.driver-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.orders-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 4px;
    background-color: #f8f9fa;
    transition: all 0.3s;
}

.order-item:hover {
    background-color: #f0f2f5;
}

.order-index {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    flex-shrink: 0;
}

.order-content {
    flex-grow: 1;
    min-width: 0;
}

.order-content h5 {
    margin: 0 0 4px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.order-content p {
    margin: 0;
    color: #666;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

:deep(.el-collapse-item__header) {
    font-size: 15px;
}

:deep(.el-collapse-item__content) {
    padding: 16px;
}

/* 移除不需要的样式 */

.route-item,
.route-item-header,
.route-list,
.route-count {
    display: none;
}
</style>