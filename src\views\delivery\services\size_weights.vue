<script setup>
import ListVue from './components/list.vue'
import { getSizeWeights, deleteSizeWeight, updateSizeWeightAvailabilityPriority, batchAlterServiceAvailability, addSizeWeight, updateSizeWeight } from '@/api/modules/delivery'

</script>
<template>
    <ListVue v-bind="$attrs" :get-data="getSizeWeights" :on-delete="deleteSizeWeight"
        :on-update-availability-priority="updateSizeWeightAvailabilityPriority"
        :on-batch-alter-availability="batchAlterServiceAvailability" :on-add="addSizeWeight" :on-update="updateSizeWeight"
        title="sizeWeight" module="sw" />
</template>
