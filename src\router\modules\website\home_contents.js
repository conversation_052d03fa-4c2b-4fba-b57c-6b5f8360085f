/*
* Author: <EMAIL>'
* Date: '2023-11-04 16:18:28'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/website/home_contents.js'
* File: 'home_contents.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/website/home-contents',
    component: Layout,
    redirect: '/website/home-contents/index',
    name: 'websiteHomeContents',
    meta: {
        title: '首页内容',
        icon: 'ri:article-fill',
        auth: ['super'],
        i18n: 'route.website.homeContents.title'
    },
    children: [
        {
            path: 'index',
            name: 'websiteHomeContentsIndex',
            component: () => import('@/views/website_management/home_contents/index.vue'),
            meta: {
                title: '首页内容',
                icon: 'quill:stack',
                i18n: 'route.website.homeContents.homeContents',
                activeMenu: '/website/home-contents/index',
                auth: ['super'],

            }
        },
        {
            path: 'fee-packs',
            name: 'websiteHomeContentsFeePacks',
            component: () => import('@/views/website_management/fee_packs/list.vue'),
            meta: {
                title: '运费包',
                icon: 'quill:stack',
                i18n: 'route.website.homeContents.feePacks',
                activeMenu: '/website/home-contents/fee-packs',
                auth: ['super'],

            }
        },
        {
            path: 'fee-pack',
            name: 'websiteHomeContentsFeePackDetail',
            component: () => import('@/views/website_management/fee_packs/detail.vue'),
            meta: {
                title: '运费包',
                icon: 'quill:stack',
                i18n: 'route.website.homeContents.feePacks',
                activeMenu: '/website/home-contents/fee-packs',
                sidebar: false,
                breadcrumbs: false,
                auth: ['super'],

            }
        },
    ]
}
