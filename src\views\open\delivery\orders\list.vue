<script setup name="OpenDeliveryOrderList">
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { orderNoFormatter } from '@/utils/formatter'
import { useClipboard } from '@vueuse/core'
import { CloseBold, DocumentCopy, DArrowRight, Upload, Download, Right, Select } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { orderStatusStyles } from '@/utils/constants'
import { getOpenDeliveryOrders } from '@/api/modules/open'
import OrderAddressDialog from '@/views/components/OrderAddressDialog/index.vue'
const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()
const { text, copy, copied, isSupported } = useClipboard()

const data = ref({
    loading: false,
    search: {
        name__icontains: null,
    },
    batch: {
        selectionDataList: []
    },
    dataList: []
})

watch(copied, (val) => {
    val && ElMessage.success(`${t('messages.copied')}: ${text.value}`)
})


// Filters
const searchBarCollapsed = ref(0)
function resetFilters() {
    data.value.search =
    {
        no__icontains: null,
        user__name__icontains: null,
        sender_name__icontains: null,
        receiver_name__icontains: null,
        total__gte: 0,
        total__lte: null,
        status__in: ['inQueue', 'processing'],
        created_at__lte: null,
        created_at__gte: null,
        updated_at__lte: null,
        updated_at__gte: null
    }
    currentChange()
}
const status = Object.keys(orderStatusStyles)
function onAllStatus(val) {
    data.value.search.status__in = val ? status : []
}

function onSearchStatusChanged(val) {
    const checkedStatusCount = val.length
    allStatus.value = checkedStatusCount === status.length

}


const allStatus = ref(false)
const partialStatus = computed(() => data.value.search.status__in?.length && data.value.search.status__in.length < status.length)


onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getOpenDeliveryOrders(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.order_list
        pagination.value.total = res.data.total
    })
}

function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onEdit(row) {
    router.push({
        name: 'deliveryOrderDetail',
        query: {
            id: row.id
        }
    })
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (row.status === 'closed') {
        return 'not-available-row'
    } else {
        return ''
    }
}

// Dialogs
const chosenRow = ref({})
const senderDialogVisible = ref(false)
const receiverDialogVisible = ref(false)

function showSender(row) {
    chosenRow.value = row
    senderDialogVisible.value = true
}
function showReceiver(row) {
    chosenRow.value = row
    receiverDialogVisible.value = true
}
</script>
<template>
    <div>
        <page-header :title="$t('delivery.openOrders.title')" />

        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange">
                <el-table-column type="index" align="center" fixed />
                <el-table-column prop="no" :label="$t('delivery.orders.fields.no')" width="140">
                    <template #default="scope">
                        <el-space>
                            <el-tooltip placement="top">
                                <template #content>
                                    {{ $t('operations.copy') }}
                                </template>
                                <el-button @click.stop="copy(scope.row.no)" size="small" type="primary" plain circle
                                    :icon="DocumentCopy" />
                            </el-tooltip>
                            <div class="order-number">
                                {{ orderNoFormatter(_, _, scope.row.no) }}
                            </div>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="sender_name" :label="$t('delivery.orders.fields.sender')" show-overflow-tooltip>
                    <template #default="scope">
                        <el-link type="warning" @click.stop="showSender(scope.row)">
                            {{ scope.row.sender_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="receiver_name" :label="$t('delivery.orders.fields.receiver')" show-overflow-tooltip>
                    <template #default="scope">
                        <el-link type="success" @click.stop="showReceiver(scope.row)">
                            {{ scope.row.receiver_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="status" :label="$t('delivery.orders.fields.status')" align="center" width="110">
                    <template #default="scope">
                        <el-tag :type="orderStatusStyles[scope.row.status]" round size="small">
                            {{ $t(`delivery.orders.selections.status.${scope.row.status}`) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="quantity" :label="$t('delivery.orders.fields.quantity')" align="center" width="80"
                    sortable="custom" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <!-- Sender -->
        <OrderAddressDialog v-model="senderDialogVisible" :address-info="chosenRow" address-type="sender" />
        <!-- Receiver -->
        <OrderAddressDialog v-model="receiverDialogVisible" :address-info="chosenRow" address-type="receiver" />
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
