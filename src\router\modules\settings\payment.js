/*
* Author: <EMAIL>'
* Date: '2023-04-29 20:03:48'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/payment/setting.js'
* File: 'setting.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/payment-setting',
    component: Layout,
    redirect: '/settings/payment-setting',
    name: 'paymentSetting',
    meta: {
        title: '支付',
        icon: 'material-symbols:display-settings-outline',
        auth: ['super'],
        i18n: 'route.system.paymentSettings.title'
    },
    children: [
        {
            path: 'settings',
            name: 'paymentSettingDetail',
            component: () => import('@/views/finance/payment_settings/detail.vue'),
            meta: {
                title: '设置',
                icon: 'mdi:payment-settings',
                i18n: 'route.system.paymentSettings.settings',
                activeMenu: '/settings/payment-setting//setting',
                auth: ['super']
            }
        },
        {
            path: 'suppliers',
            name: 'paymentSuppliers',
            component: () => import('@/views/finance/payment_suppliers/list.vue'),
            meta: {
                title: '支付渠道',
                icon: 'ri:secure-payment-line',
                i18n: 'route.system.paymentSettings.suppliers',
                activeMenu: '/settings/payment-setting//suppliers',
                auth: ['super']
            }
        },
        {
            path: 'cost-attributions',
            name: 'eodCostAttributions',
            component: () => import('@/views/finance/payment_settings/cost_attributions/list.vue'),
            meta: {
                title: '费用项目',
                icon: 'ri:secure-payment-line',
                i18n: 'route.system.paymentSettings.eodCostAttributions',
                activeMenu: '/settings/payment-setting/cost-attributions',
                auth: ['super']
            }
        },

    ]
}
