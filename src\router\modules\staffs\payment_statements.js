/*
* Author: <EMAIL>'
* Date: '2024-03-03 23:17:56'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/staffs/payment_statements.js'
* File: 'payment_statements.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/staffs/payment-statements',
    component: Layout,
    redirect: '/staffs/payment-statements/list',
    name: 'paymentStatements',
    meta: {
        title: '付款单管理',
        icon: 'material-symbols:punch-clock-outline',
        auth: ['super'],
        i18n: 'route.staffs.paymentStatements.title'
    },
    children: [
        {
            path: 'list',
            name: 'paymentStatementList',
            component: () => import('@/views/staffs/payment_statements/list.vue'),
            meta: {
                title: '付款单',
                icon: 'ep:odometer',
                activeMenu: '/staffs/payment-statements/list',
                i18n: 'route.staffs.paymentStatements.statements',
                auth: ['super']
            }
        },
        {
            path: 'detail',
            name: 'paymentStatementDetail',
            component: () => import('@/views/staffs/payment_statements/detail.vue'),
            meta: {
                title: '工单详情',
                activeMenu: '/staffs/payment-statements/list',
                sidebar: false,
                auth: ['super']
            }
        }
    ]
}
