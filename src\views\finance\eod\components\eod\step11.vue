<script setup>
import { addReport, getReports } from '@/api/modules/statistics';
import StepButtons from './step_buttons.vue'
import { currencyFormatter } from '@/utils/formatter';
const route = useRoute()

const props = defineProps({
    date: {
        type: String,
        default: ''
    },
    params: {
        type: Object,
        default: {
            delivery: null,
            salaries: [],
            costs: []
        }
    },
})

const report = ref({})

onMounted(() => {
    if (props.date != '') {
        createReport()
    } else {
        getData()
    }
})

async function createReport() {
    let params = {
        date: props.date,
        delivery_report: props.params.delivery,
        payment_report: props.params.payment,
        salary_report: props.params.salary,
        cost_report: props.params.cost,
    }
    let res = await addReport(params)
    if (res.data.errCode == 365 || res.data.errCode == null) {
        getReports({ id: res.data.id }).then(reportRes => {
            report.value = reportRes.data
        })
    }

}

function getData() {
    getReports({ id: route.query.id }).then(reportRes => {
        report.value = reportRes.data
    })
}

</script>
<template>
    <el-text size="large" tag="b">{{ report.date }}</el-text>
    <el-divider />
    <el-row :gutter="40">
        <el-col :span="8">
            <el-descriptions :title="$t('statistics.fields.income')" border :column="1">
                <el-descriptions-item :label="$t('statistics.delivery.fields.paidAmount')">
                    {{ currencyFormatter(_, _, report.paid_amount) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.delivery.fields.unpaidAmount')">
                    {{ currencyFormatter(_, _, report.unpaid_amount) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.delivery.fields.refundAmount')">
                    ({{ currencyFormatter(_, _, report.refund_amount) }})
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.fields.preTax')">
                    {{ currencyFormatter(_, _, report.income) }}
                </el-descriptions-item> <el-descriptions-item :label="$t('statistics.fields.hst')">
                    {{ currencyFormatter(_, _, report.hst) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.fields.total')">
                    {{ currencyFormatter(_, _, report.total_income) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.fields.count')" width="100">
                    {{ report.total_orders }}&ThinSpace; ({{ report.total_packages }})
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.delivery.fields.averagePU')">
                    {{ currencyFormatter(_, _, Math.round(report.income / report.total_packages)) }}
                </el-descriptions-item>

            </el-descriptions>
        </el-col>
        <el-col :span="8">
            <el-descriptions :title="$t('statistics.fields.payable')" border :column="1">
                <el-descriptions-item :label="$t('statistics.fields.costs')" :span="1">
                    {{ currencyFormatter(_, _, report.cost) }} ({{ $t('statistics.fields.preTax') }})
                    <el-text size="small" tag="i">
                        + {{ currencyFormatter(_, _, report.cost_hst) }} ({{ $t('statistics.fields.hst') }})
                    </el-text>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.fields.salaries')">
                    {{ currencyFormatter(_, _, report.salaries) }} ({{ $t('statistics.fields.preTax') }})
                    <el-text size="small" tag="i">
                        + {{ currencyFormatter(_, _, report.salary_hst) }} ({{ $t('statistics.fields.hst') }})
                    </el-text>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.salary.fields.tip')">
                    {{ currencyFormatter(_, _, report.tip) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.fields.hst')">
                    {{ currencyFormatter(_, _, report.hst_payable) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.fields.total')">
                    {{ currencyFormatter(_, _, report.total_payable) }}
                </el-descriptions-item>
            </el-descriptions>
        </el-col>
        <el-col :span="8">
            <el-descriptions :title="$t('statistics.fields.profit')" border :column="1">
                <el-descriptions-item :label="$t('statistics.fields.profit')">
                    {{ currencyFormatter(_, _, report.profit) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('statistics.fields.profitRate')">
                    {{ report.income ? ((report.profit / report.income) * 100).toFixed(2) + ' %' : '-' }}
                </el-descriptions-item>
            </el-descriptions>
        </el-col>
    </el-row>

    <StepButtons v-bind="$attrs" noNext />
</template>