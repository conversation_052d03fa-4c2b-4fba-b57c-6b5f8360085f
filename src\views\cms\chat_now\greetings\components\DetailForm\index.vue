<script setup>
import { updateChatGreeting, createChatGreeting } from '@/api/modules/chat_now'
const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    text: {
        type: String,
        default: ''
    },
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        text: props.text
    },
    rules: {
        text: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})


defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    createChatGreeting(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateChatGreeting(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('cms.chatNow.greetings.fields.text')" prop="text">
                <el-input v-model="data.form.text" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
