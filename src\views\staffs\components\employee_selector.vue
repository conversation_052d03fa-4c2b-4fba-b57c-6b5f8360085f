<script setup>
    import { getDrivers, getValidDrivers } from '@/api/modules/staffs'

    const props = defineProps({
        modelValue: {
            default: null
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        validOnly: {
            type: Boolean,
            default: true,
        },
        idOnly: {
            type: Boolean,
            default: true,
        }
    })

    const employees = ref([])

    onMounted(() => {
        getDriverList()
    })

    function getDriverList() {
        if (!props.validOnly) {
            getDrivers().then(res => {
                employees.value = res.data
            })

        } else {
            getValidDrivers().then(res => {
                employees.value = res.data
            })

        }
    }

    const emit = defineEmits(['update:modelValue'])

    let employee = computed({
        get: function () {
            return props.modelValue
        },
        set: function (val) {
            emit('update:modelValue', val)
        }
    })

</script>

<template>
    <el-select v-model="employee" placeholder="Select" :disabled="props.disabled" clearable filterable
        :value-key="props.idOnly ? 'value' : 'id'" style="width: 100%; min-width: 250px;">
        <el-option v-for="item in employees" :key="item.id" :label="`${item.firstname ?? ''} ${item.lastname}`"
            :value="props.idOnly ? item.id : item" />
    </el-select>

</template>
