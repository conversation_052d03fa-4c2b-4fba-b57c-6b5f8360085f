<script setup>
    import { getUserInvoice, voidUserInvoice } from '@/api/modules/payment';
    import { currencyFormatter, userNameFormatter } from '@/utils/formatter'
    import { useI18n } from 'vue-i18n';

    const { t } = useI18n()

    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })

    const data = ref({
        loading: false,
        form: {
            id: props.id,
            user: { user_code: null },
            orders: []
        },
    })

    onMounted(() => {
        getInfo()
    })

    function getInfo() {
        data.value.loading = true
        getUserInvoice(data.value.form.id).then(res => {
            data.value.loading = false
            data.value.form = res.data
        })
    }

    function sumOrders({ columns, data }) {
        let sums = []
        columns.forEach((column, index) => {
            if (index === 0) {
                sums[index] = t('fields.total')
                return
            } else if (index <= 5) {
                const values = data.map((item) => Number(item[column.property]))
                sums[index] = `${currencyFormatter(null, null, values.reduce((prev, curr) => {
                    const value = Number(curr)
                    if (!Number.isNaN(value)) {
                        return prev + curr
                    } else {
                        return prev
                    }
                }, 0))}`
            }
        })
        return sums
    }


    function onVoid() {
        ElMessageBox.confirm(t('dialog.messages.void', { name: data.value.form.no }), t('dialog.titles.confirmation')).then(() => {
            data.value.loading = true
            voidUserInvoice(data.value.form.id).then(res => {
                if (res.data.errCode == 365) {
                    data.value.loading = false
                    getInfo()
                }
            })
        }).catch(() => { })

    }
</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <template #title>
                <el-space>
                    <span>{{ data.form.no }}</span>
                    <el-tag type="danger" v-if="data.form.is_voided">Voided</el-tag>
                </el-space>
            </template>
            <template #extra>
                <el-space>
                    <span>{{ $t('fields.createdAt') }}</span>
                    <span>{{ data.form.created_at }}</span>
                    <el-button @click="onVoid" type="danger" v-if="!data.form.is_voided">Void</el-button>
                </el-space>
            </template>
            <el-descriptions column="1">
                <el-descriptions-item :label="$t('finance.invoice.fields.downloaded')">
                    {{ data.form.downloaded_count }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('fields.user')">
                    <el-space>
                        <el-tag v-if="data.form.user.user_code" size="small" type="info" effect="plain">
                            {{ data.form.user.user_code }}</el-tag>
                        <span>{{ userNameFormatter(data.form, null, null, null) }}</span>
                    </el-space>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('finance.invoice.fields.periodStart')">
                    {{ data.form.period_start }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('finance.invoice.fields.periodEnd')">
                    {{ data.form.period_end }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('fields.total')">
                    {{ currencyFormatter(null, null, data.form.total) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('fields.subtotal')">
                    {{ currencyFormatter(null, null, data.form.subtotal) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('fields.tax')">
                    {{ currencyFormatter(null, null, data.form.tax) }}
                </el-descriptions-item>
            </el-descriptions>
        </page-main>
        <page-main :title="$t('finance.paymentOrder.title')">
            <el-table ref="table" v-loading="data.loading" :data="data.form.orders" :summary-method="sumOrders"
                show-summary border stripe highlight-current-row @sort-change="sortChange">
                <el-table-column prop="no" :label="$t('finance.paymentOrder.fields.no')" width="200" />
                <el-table-column prop="total" :label="$t('finance.paymentOrder.fields.amount')"
                    :formatter="currencyFormatter" class-name="order-number" align="right" />
                <el-table-column prop="refunded" :label="$t('finance.paymentOrder.fields.refunded')"
                    :formatter="currencyFormatter" align="right">
                    <template #default="scope">
                        <span v-if="scope.row.refunded" class="refunded-column">
                            ({{ currencyFormatter(_, _, scope.row.refunded) }})
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" :label="$t('fields.status')" align="center" width="160">
                    <template #default="scope">
                        {{ $t(`finance.paymentOrder.selections.status.${scope.row.status}`) }}
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" width="160" :label="$t('fields.createdAt')" />
                <el-table-column prop="updated_at" width="160" :label="$t('fields.updatedAt')" />
            </el-table>
        </page-main>

    </div>
</template>

<style lang="scss">
    .el-table {
        font-size: 0.8em;
    }
</style>
