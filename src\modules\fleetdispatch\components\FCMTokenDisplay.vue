<template>
  <div class="token-display-container">
    <div class="token-header">
      <h3>Firebase消息推送令牌 (FCM Token)</h3>
      <el-button type="primary" size="small" @click="refreshToken" :loading="isLoading">
        刷新令牌
      </el-button>
      <el-button type="success" size="small" @click="copyToken" :disabled="!fcmToken">
        复制令牌
      </el-button>
      <el-button 
        :type="isMessageStopped ? 'danger' : 'warning'" 
        size="small" 
        @click="toggleMessageReceiving">
        {{ isMessageStopped ? '恢复接收消息' : '停止接收消息' }}
      </el-button>
    </div>
    <div class="token-content">
      <el-input
        v-model="fcmToken"
        type="textarea"
        :rows="3"
        placeholder="加载中..."
        readonly
      ></el-input>
      <div class="token-status" v-if="fcmToken">
        <el-tag type="success">令牌已获取</el-tag>
        <span class="token-time">获取时间: {{ tokenTime }}</span>
      </div>
      <div class="token-status" v-else-if="tokenError">
        <el-tag type="danger">令牌获取失败</el-tag>
        <span class="token-error">{{ tokenError }}</span>
      </div>
      <div class="token-status" v-else>
        <el-tag type="info">正在获取令牌...</el-tag>
      </div>
    </div>
    
    <div class="token-usage">
      <h4>如何使用推送令牌</h4>
      <p>使用以下JSON格式向Firebase Cloud Messaging发送消息:</p>
      <pre class="json-example">
{
  "message": {
    "token": "{{fcmToken || 'YOUR_FCM_TOKEN'}}",
    "notification": {
      "title": "新订单通知",
      "body": "您有一个新的订单等待处理"
    },
    "data": {
      "id": "order123",
      "type": "new_order",
      "created_at": "{{currentTime}}"
    }
  }
}</pre>
    </div>
    
    <div class="token-usage">
      <h4>调度系统通知格式</h4>
      <p>使用以下JSON格式发送调度系统通知:</p>
      <pre class="json-example">
{
  "validate_only": false,
  "message": {
    "name": "Driver's Position Updated",
    "data": {
      "title": "Driver's Position Updated",
      "body": "[{\"driver\": {\"id\": \"DRIVER-123\"}, \"positions\": [{\"timestamp\": {{Math.floor(Date.now()/1000)}}, \"latitude\": 43.714392, \"longitude\": -79.63071750000002, \"speed\": 0, \"accuracy\": 10.1}]}]",
      "analytics_label": "driver_position_updated",
      "catalog": "DISPATCHER_NOTIFICATION",
      "created_at": "{{currentTime}}",
      "action": "DRIVER_UPDATE"
    },
    "token": "{{fcmToken || 'YOUR_FCM_TOKEN'}}"
  }
}</pre>
    </div>
    
    <div class="testing-section">
      <h4>测试消息类型</h4>
      <div class="test-buttons">
        <el-button type="primary" size="small" @click="sendTestMessage('new_order')">
          测试新订单通知
        </el-button>
        <el-button type="success" size="small" @click="sendTestMessage('driver_location_update')">
          测试司机位置更新
        </el-button>
        <el-button type="warning" size="small" @click="sendTestMessage('order_status_change')">
          测试订单状态变更
        </el-button>
      </div>
      <div class="divider"></div>
      <h4>测试调度系统通知</h4>
      <div class="test-buttons">
        <el-button type="primary" size="small" @click="sendDispatcherNotification('DRIVER_UPDATE')">
          测试司机位置更新
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { getToken } from 'firebase/messaging'
import { useFirebaseMessaging } from '@/utils/composables/firebase_messaging'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'
import { firebaseMessagingService } from '../services/FirebaseMessagingService'
import { eventBus, EVENT_TYPES } from '../utils/eventBus'
import { useDriverStore } from '../stores/driver'

export default {
  name: 'FCMTokenDisplay',
  setup() {
    const fcmToken = ref('')
    const tokenError = ref('')
    const isLoading = ref(false)
    const tokenTimestamp = ref(null)
    const userStore = useUserStore()
    const driverStore = useDriverStore()
    
    // 消息接收状态
    const isMessageStopped = computed(() => userStore.stopReceivingMessages)

    const tokenTime = computed(() => {
      if (!tokenTimestamp.value) return '未知'
      const date = new Date(tokenTimestamp.value)
      return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
    })
    
    const currentTime = computed(() => {
      return new Date().toISOString()
    })

    // 使用FirebaseMessagingService服务获取令牌
    const fetchToken = async () => {
      isLoading.value = true
      tokenError.value = ''
      
      try {
        // 检查Firebase消息服务是否已初始化
        if (!firebaseMessagingService.initialized) {
          await firebaseMessagingService.initialize()
        }
        
        // 检查通知权限
        if (Notification.permission === 'denied') {
          tokenError.value = '通知权限已被拒绝，请在浏览器设置中启用通知'
          isLoading.value = false
          return
        }
        
        // 如果权限是默认状态，先请求权限
        if (Notification.permission === 'default') {
          try {
            const permission = await Notification.requestPermission()
            if (permission !== 'granted') {
              tokenError.value = '未获得通知权限，无法获取令牌'
              isLoading.value = false
              return
            }
          } catch (permError) {
            console.error('请求通知权限失败:', permError)
            tokenError.value = `请求通知权限失败: ${permError.message || '未知错误'}`
            isLoading.value = false
            return
          }
        }
        
        // 获取令牌
        try {
          const token = await firebaseMessagingService.getToken()
          
          if (token) {
            fcmToken.value = token
            tokenTimestamp.value = Date.now()
            console.log('获取FCM令牌成功:', token)
          } else {
            tokenError.value = '没有可用的令牌。请检查通知权限。'
            console.log('没有可用的令牌。请检查通知权限。')
          }
        } catch (tokenError) {
          console.error('获取FCM令牌失败:', tokenError)
          tokenError.value = `获取令牌时出错: ${tokenError.message || '未知错误'}`
        }
      } catch (error) {
        console.error('获取FCM令牌失败:', error)
        tokenError.value = `获取令牌时出错: ${error.message || '未知错误'}`
      } finally {
        isLoading.value = false
      }
    }

    const refreshToken = async () => {
      isLoading.value = true
      try {
        // 检查Firebase消息服务是否已初始化
        if (!firebaseMessagingService.initialized) {
          await firebaseMessagingService.initialize()
        }
        
        // 强制刷新令牌
        const token = await firebaseMessagingService.getToken(true)
        
        if (token) {
          fcmToken.value = token
          tokenTimestamp.value = Date.now()
          ElMessage({
            type: 'success',
            message: '令牌已成功强制刷新',
            duration: 2000
          })
        } else {
          tokenError.value = '刷新令牌失败，未获取到新令牌'
          ElMessage({
            type: 'error',
            message: '刷新令牌失败，未获取到新令牌',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('强制刷新令牌失败:', error)
        tokenError.value = `刷新令牌时出错: ${error.message || '未知错误'}`
        ElMessage({
          type: 'error',
          message: `刷新令牌失败: ${error.message || '未知错误'}`,
          duration: 2000
        })
      } finally {
        isLoading.value = false
      }
    }

    const copyToken = () => {
      if (!fcmToken.value) return
      
      navigator.clipboard.writeText(fcmToken.value)
        .then(() => {
          ElMessage({
            type: 'success',
            message: '令牌已复制到剪贴板',
            duration: 2000
          })
        })
        .catch(err => {
          console.error('复制失败:', err)
          ElMessage({
            type: 'error',
            message: '复制失败，请手动复制',
            duration: 2000
          })
        })
    }
    
    // 模拟发送测试消息
    const sendTestMessage = (type) => {
      // 构造测试消息
      const testMessage = {
        data: {
          id: `test_${Date.now()}`,
          type: type,
          created_at: new Date().toISOString()
        },
        notification: {
          title: '测试通知',
          body: `这是一条测试${type}消息`
        }
      }
      
      // 根据类型添加额外数据
      switch (type) {
        case 'new_order':
          testMessage.data.order_no = `ORD-${Math.floor(Math.random() * 10000)}`
          testMessage.notification.title = '新订单通知'
          testMessage.notification.body = `您有一个新的订单 #${testMessage.data.order_no}`
          break
        case 'driver_location_update':
          testMessage.data.driver_id = `D-${Math.floor(Math.random() * 100)}`
          testMessage.data.latitude = (Math.random() * 10 + 30).toFixed(6)
          testMessage.data.longitude = (Math.random() * 10 + 110).toFixed(6)
          testMessage.notification.title = '司机位置更新'
          testMessage.notification.body = `司机 ${testMessage.data.driver_id} 位置已更新`
          break
        case 'order_status_change':
          testMessage.data.order_no = `ORD-${Math.floor(Math.random() * 10000)}`
          testMessage.data.status = ['PENDING', 'ACCEPTED', 'PICKED_UP', 'DELIVERED'][Math.floor(Math.random() * 4)]
          testMessage.notification.title = '订单状态更新'
          testMessage.notification.body = `订单 #${testMessage.data.order_no} 状态变更为 ${testMessage.data.status}`
          break
      }
      
      // 调用Firebase消息服务的onReceiveMessage方法模拟收到消息
      firebaseMessagingService.onReceiveMessage(testMessage)
      
      ElMessage({
        type: 'success',
        message: `已发送测试${type}消息`,
        duration: 2000
      })
    }
    
    // 发送调度系统通知测试消息
    const sendDispatcherNotification = (action) => {
      // 获取所有司机
      const drivers = driverStore.allDrivers || []
      
      // 如果没有司机，创建一些虚拟的司机ID
      const driverIds = drivers.length > 0 
        ? drivers.slice(0, 3).map(driver => driver.id)
        : ['test-driver-1', 'test-driver-2', 'test-driver-3']
      
      // 创建一个测试位置 - 使用随机坐标或固定坐标
      const createTestPosition = (driverId) => {
        // 随机生成一个位置 (中国大陆区域)
        const lat = 30 + Math.random() * 10 // 大约30-40之间
        const lng = 110 + Math.random() * 15 // 大约110-125之间
        
        return {
          driver: { id: driverId },
          positions: [{
            timestamp: Math.floor(Date.now() / 1000),
            latitude: parseFloat(lat.toFixed(6)),
            longitude: parseFloat(lng.toFixed(6)),
            speed: Math.floor(Math.random() * 60),
            accuracy: parseFloat((Math.random() * 20).toFixed(1))
          }]
        }
      }
      
      // 根据操作类型创建不同的消息
      let testMessage
      
      switch (action) {
        case 'DRIVER_UPDATE':
          // 创建司机位置数据
          const driversPositions = driverIds.map(id => createTestPosition(id))
          
          // 构造测试消息
          testMessage = {
            data: {
              title: "Driver's Position Updated",
              body: JSON.stringify(driversPositions),
              analytics_label: "driver_position_updated",
              catalog: "DISPATCHER_NOTIFICATION",
              created_at: new Date().toISOString(),
              action: "DRIVER_UPDATE"
            }
          }
          break
          
        default:
          ElMessage({
            type: 'error',
            message: `未知的调度操作类型: ${action}`,
            duration: 2000
          })
          return
      }
      
      // 调用Firebase消息服务的onReceiveMessage方法模拟收到消息
      console.log('发送调度系统通知测试消息:', testMessage)
      firebaseMessagingService.onReceiveMessage(testMessage)
      
      ElMessage({
        type: 'success',
        message: `已发送测试调度系统通知: ${action}`,
        duration: 2000
      })
    }
    
    // 监听令牌更新事件
    const onTokenUpdated = (token) => {
      fcmToken.value = token
      tokenTimestamp.value = Date.now()
    }

    // 切换消息接收状态
    const toggleMessageReceiving = () => {
      const newStatus = userStore.toggleMessageReceiving()
      ElMessage({
        type: newStatus ? 'warning' : 'success',
        message: newStatus ? '已停止接收消息更新' : '已恢复接收消息更新',
        duration: 2000
      })
    }

    onMounted(() => {
      // 注册令牌更新事件监听
      eventBus.on(EVENT_TYPES.FIREBASE_TOKEN_UPDATED, onTokenUpdated)
      
      // 请求FCM权限并获取令牌
      if (Notification.permission !== 'granted') {
        userStore.requestFCMPermission()
          .then(() => fetchToken())
          .catch(error => {
            tokenError.value = `请求通知权限失败: ${error.message || '未知错误'}`
          })
      } else {
        // 先检查服务中是否已有令牌和是否过期
        if (firebaseMessagingService.currentToken && !userStore.isTokenExpired) {
          fcmToken.value = firebaseMessagingService.currentToken
          tokenTimestamp.value = Date.now()
        } else {
          // token不存在或已过期，重新获取
          if (userStore.isTokenExpired && firebaseMessagingService.currentToken) {
            console.log('FCM令牌已过期，正在重新获取...')
          }
          userStore.requestFCMPermission()
            .then(token => {
              if (token) {
                fcmToken.value = token
                tokenTimestamp.value = Date.now()
              } else {
                fetchToken() // 备用方案
              }
            })
            .catch(error => {
              console.error('通过store刷新令牌失败:', error)
              fetchToken() // 备用方案
            })
        }
      }
    })
    
    onUnmounted(() => {
      // 移除事件监听
      eventBus.off(EVENT_TYPES.FIREBASE_TOKEN_UPDATED, onTokenUpdated)
    })

    return {
      fcmToken,
      tokenError,
      isLoading,
      tokenTime,
      currentTime,
      refreshToken,
      copyToken,
      sendTestMessage,
      sendDispatcherNotification,
      isMessageStopped,
      toggleMessageReceiving
    }
  }
}
</script>

<style scoped>
.token-display-container {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.token-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.token-header h3 {
  margin: 0;
  margin-right: auto;
  font-size: 16px;
  color: #606266;
}

.token-content {
  margin-bottom: 15px;
}

.token-status {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.token-time {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}

.token-error {
  margin-left: 10px;
  font-size: 12px;
  color: #f56c6c;
}

.token-usage {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.token-usage h4 {
  font-size: 14px;
  color: #606266;
  margin-top: 0;
  margin-bottom: 10px;
}

.token-usage p {
  font-size: 12px;
  color: #606266;
  margin-bottom: 10px;
}

.json-example {
  background-color: #f5f7fa;
  border: 1px solid #e6e9f0;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  color: #476582;
  overflow-x: auto;
}

.testing-section {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.testing-section h4 {
  font-size: 14px;
  color: #606266;
  margin-top: 0;
  margin-bottom: 10px;
}

.divider {
  height: 1px;
  background-color: #eee;
  margin: 15px 0;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style> 