<script setup>
import { getEODDeliveryOrders, stableEODDeliveryOrders } from '@/api/modules/delivery'
import { userNameFormatter, orderNoFormatter } from '@/utils/formatter'
import statusStyles from '@/views/delivery/orders/status'
import StepButtons from './step_buttons.vue'


const props = defineProps({
    date: {
        type: String,
        default: ''
    }

})

const data = ref({
    loading: false,
    orderList: [],
})

const actionDisabled = computed(() => data.value.orderList.reduce(
    (ret, order) => ret && (order.disable_actions || order.to_status == null), true
))

onMounted(() => {
    getOrders()
})


function getOrders() {
    let params = { date: props.date }
    getEODDeliveryOrders(params).then(res => {
        data.value.orderList = res.data
    })
}

async function stableOrders() {
    let params = data.value.orderList.map(e => {
        return {
            id: e.id, to_status: e.to_status
        }
    })
    stableEODDeliveryOrders(params).then(res => {
        if (res.data.errCode == 365) getOrders()
    })
}
</script>
<template>
    <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.orderList" stripe highlight-current-row>
        <el-table-column type="index" />
        <el-table-column prop="no" :label="$t('fields.no')" width="100" :formatter="orderNoFormatter" />
        <el-table-column prop="user" :label="$t('fields.user')" :formatter="userNameFormatter" />
        <el-table-column prop="sender_address" :label="$t('delivery.orders.fields.sender')" />
        <el-table-column prop="receiver_address" :label="$t('delivery.orders.fields.receiver')" />
        <el-table-column prop="status" :label="$t('delivery.orders.fields.status')" align="center" width="110">
            <template #default="scope">
                <el-tag :type="statusStyles[scope.row.status]" round size="small">
                    {{ $t(`delivery.orders.selections.status.${scope.row.status}`) }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" />
        <el-table-column :label="$t('fields.operations')" width="300" align="center" fixed="right">
            <template #default="scope">
                <el-radio-group v-model="scope.row.to_status" size="small" :disabled="scope.row.disable_actions">
                    <el-radio label="closed">{{ $t('finance.eod.operations.close') }}</el-radio>
                    <el-radio label="completed">{{ $t('finance.eod.operations.complete') }}</el-radio>
                    <el-radio :label="null">{{ $t('finance.eod.operations.noAction') }}</el-radio>
                </el-radio-group>
            </template>
        </el-table-column>
    </el-table>
    <StepButtons v-bind="$attrs">
        <el-button type="primary" @click="stableOrders" :disabled="actionDisabled">{{
            $t('finance.eod.operations.completeOrders') }}</el-button>
    </StepButtons>
</template>
