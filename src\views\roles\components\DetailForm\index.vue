<script setup>
import { addRole, getAllModules, getAllPermissions, getRoles, updateRole } from '@/api/modules/permissions'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: '',
        desc: '',
        is_available: null,
        permissions: []
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const permissions = ref([])
const modules = ref([])

const groupedPermissions = computed(() => {
    let groups = []
    if (modules.value && permissions.value) {
        for (let i of modules.value) {
            let p = permissions.value.filter(p => p.module.id === i.id)
            if (p.length > 0) {
                i['permissions'] = p
                groups.push(i)
            }
        }
        return groups
    }
})

onMounted(() => {
    getModuleList()
    getPermissionList()

    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getRoles({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function getModuleList() {
    getAllModules().then(res => {
        modules.value = res.data
    })
}

function getPermissionList() {
    data.value.loading = true
    getAllPermissions().then(res => {
        permissions.value = res.data
        data.value.loading = false
    })

}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addRole(data.value.form).then(res => {
                        if (res.data.errCode === 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateRole(data.value.form).then(res => {
                        if (res.data.errCode === 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <page-main :title="$t('sections.baseInfo')">
        <el-row>
            <el-col :md="24" :lg="16">
                <div v-loading="data.loading">
                    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                        <el-form-item :label="$t('fields.name')" prop="name">
                            <el-input v-model="data.form.name" placeholder="请输入标题" :disabled="props.id != ''" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.desc')" prop="name">
                            <el-input v-model="data.form.desc" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.availability')">
                            <el-switch v-model="data.form.is_available" class="availability-switch" />
                        </el-form-item>
                    </el-form>
                </div>
            </el-col>
        </el-row>
    </page-main>
    <page-main :title="$t('administrator.fields.permissions')">
        <el-row>
            <el-col :span="24">
                <div v-loading="data.loading">
                    <el-space wrap>
                        <el-card v-for="(item, index) in groupedPermissions" :key="index" class="box-card">
                            <template #header>
                                <div class="card-header">
                                    <span>{{ $t(`modules.${item.name}`) }}</span>
                                    <el-button class="button" text>Operation button</el-button>
                                </div>
                            </template>
                            <el-checkbox-group v-model="data.form.permissions">
                                <el-space wrap :size="30">
                                    <div v-for="(i, idx) in item.permissions" :key="idx" class="text item">
                                        <el-checkbox :label="i.id">
                                            {{ $t(`operations.${i.operation.name}`) }}
                                        </el-checkbox>
                                    </div>
                                </el-space>
                            </el-checkbox-group>
                        </el-card>
                    </el-space>
                </div>
            </el-col>
        </el-row>
    </page-main>
</template>

<style lang="scss" scoped>
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.text {
    font-size: 14px;
    display: inline-block;
}

.item {
    margin-bottom: 18px;
}

.box-card {
    width: 480px;
}

.availability-switch {
    --el-switch-on-color: #13ce66;
    --el-switch-off-color: #ff4949;
}
</style>
