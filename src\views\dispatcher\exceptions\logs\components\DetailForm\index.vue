<script setup>
import api from '@/api'
import { getExceptionLogs, ackExceptionLog } from '@/api/modules/dispatcher'
import eventBus from '@/utils/eventBus'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        exception: {},
        order: {},
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getExceptionLogs({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        ackExceptionLog({ logs: [data.value.form.id] }).then((res) => {
            eventBus.emit('get-data-list');

        });

    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    api.post(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    api.post(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div>
        <el-row>
            <el-col :span="12">
                <div v-loading="data.loading">
                    <page-main>
                        <el-descriptions title="Detail" column="1">
                            <el-descriptions-item label="Exception">
                                {{ data.form.exception.name }}
                            </el-descriptions-item>
                            <el-descriptions-item :label="$t('staffs.fields.employee')">
                                {{ data.form.employee }}
                            </el-descriptions-item>
                            <el-descriptions-item :label="$t('fields.createdAt')">
                                {{ data.form.created_at }}
                            </el-descriptions-item>
                            <el-descriptions-item label="Order">
                                {{ data.form.order.no }}
                            </el-descriptions-item>
                            <el-descriptions-item label="Location">
                                {{ data.form.lat }}, {{ data.form.lng }}
                            </el-descriptions-item>
                        </el-descriptions>
                    </page-main>
                </div>
            </el-col>
            <el-col :span="12">
                <page-main title="Photos">
                    <el-space>
                        <el-image style=" height: 200px;" :src="p" fit="cover" :preview-src-list="[p]"
                            v-for="p of data.form.photos" hide-on-click-modal />
                        <!-- <el-text size="small" tag="b">
                                <code>{{ $t(`staffs.worksheet.selections.photoCategories.${p.category}`) }}</code>
                            </el-text> -->
                    </el-space>

                </page-main>
            </el-col>
        </el-row>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>