<script setup>
import { usePagination } from '@/utils/composables'
import { getUserLogs } from '@/api/modules/users';

const { pagination, getParams, onCurrentChange } = usePagination()

const props = defineProps({
    userId: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
    dataList: [],
})

const logStyles = {
    created: {
        type: 'success'
    },
    updated: {
        type: 'primary'
    },
    deleted: {
        type: 'danger',
        hollow: true
    },
}

defineExpose({
    getDataList,
})


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams({ userId: props.userId })
    getUserLogs(params).then(res => {
        pagination.value.total = res.data.total
        data.value.dataList = res.data.log_list
        data.value.loading = false
    })
}

function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

</script>
<template>
    <page-main :title="$t('user.sections.logs')">
        <el-timeline v-if="data.dataList.length">
            <el-timeline-item v-for="(item, index) in data.dataList" :key="index" :timestamp="item.created_at"
                :type="logStyles[item.operation]?.type" :hollow="logStyles[item.operation]?.hollow" placement="top">
                <el-space direction="vertical" alignment="start" size="large" class="logs">
                    <el-space>
                        <el-tag :type="logStyles[item.operation]?.type" size="small" round>
                            {{ $t(`user.log.operation.${item.operation}`) }}
                        </el-tag>
                        <span class="log-operator">{{ item.operator }}</span>
                    </el-space>
                    <el-descriptions :column="1" v-if="item.record">
                        <el-descriptions-item :label="$t('user.fields.status')" label-align="right"
                            label-class-name="log-label" class-name="log-content" v-if="item.record.status">
                            <el-space>
                                <span>{{ $t(`user.selection.status.${item.record.status.prev}`) }}</span>
                                <el-icon>
                                    <svg-icon name="ep:right" />
                                </el-icon>
                                <span>{{ $t(`user.selection.status.${item.record.status.curr}`) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.code')" label-align="right"
                            label-class-name="log-label" class-name="log-content" v-if="item.record.user_code">
                            <el-space>
                                <span>{{ item.record?.user_code.prev || 'Empty' }}</span>
                                <el-icon>
                                    <svg-icon name="ep:right" />
                                </el-icon>
                                <span>{{ item.record?.user_code.curr || 'Empty' }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.type')" label-align="right"
                            label-class-name="log-label" class-name="log-content" v-if="item.record.user_type">
                            <el-space>
                                <span>{{ $t(`user.selection.userType.${item.record.user_type.prev}`) }}</span>
                                <el-icon>
                                    <svg-icon name=" ep:right" />
                                </el-icon>
                                <span>{{ $t(`user.selection.userType.${item.record.user_type.curr}`) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('user.fields.group')" label-align="right"
                            label-class-name="log-label" class-name="log-content" v-if="item.record.user_group">
                            <el-space>
                                <span>{{ item.record.user_group.prev || 'Empty' }}</span>
                                <el-icon>
                                    <svg-icon name="ep:right" />
                                </el-icon>
                                <span>{{ item.record?.user_group.curr || 'Empty' }}</span>
                            </el-space>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-space>
            </el-timeline-item>
        </el-timeline>
        <el-empty v-else :description="$t('noData')" />
        <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
            :layout="pagination.layoutM" :hide-on-single-page="true" class="pagination" @current-change="currentChange" />
    </page-main>
</template>

