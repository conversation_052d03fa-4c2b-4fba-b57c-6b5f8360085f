<script setup name="DispatcherEliteExtraEliteExtraList">
import { Delete } from '@element-plus/icons-vue'
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { getEliteTemplates, updateEliteTemplateAvailability, deleteEliteTemplate } from '@/api/modules/dispatcher'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 列表数据
    dataList: []
})




onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams()
    getEliteTemplates(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data
        pagination.value.total = res.data.total
    })
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'dispatcherSupplierEliteTemplateDetail'
    })
}

function onEdit(row) {
    router.push({
        name: 'dispatcherSupplierEliteTemplateDetail',
        query: {
            id: row.id
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        let params = {
            id: row.id
        }
        deleteEliteTemplate(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}
function onUpdateAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    updateEliteTemplateAvailability(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header title="默认模块" />
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange">
                <el-table-column type="index" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="description" :label="$t('fields.desc')" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
