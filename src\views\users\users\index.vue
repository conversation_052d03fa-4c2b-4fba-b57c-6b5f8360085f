
<script setup name="UserIndex">
import { getUserCreationStatistics, getUserStatistics, getUserOriginStatistics, getDauStatistics, getUserCreditsStatistics } from '@/api/modules/users'

const router = useRouter()

onMounted(() => {
    getStatistics()
})

const statistics = ref({
    total: {
        total: 0,
        today: 0
    },
    month: {
        total: 0,
        delta: 0
    },
    dau: {
        avg: 0,
        delta: 0
    },
    cities: {
        total: 0,
        delta: 0
    },
    credits: {
        balance: 0,
        consumed: 0,
        returned: 0,
        promoted: 0
    }
})

function getStatistics() {
    getUserStatistics().then(res => {
        statistics.value = res.data
    })
}

function goAnchor(id) {
    let anchor = document.getElementById(id)
    anchor.scrollIntoView()
}

function gotoList() {
    router.push('/users/List')
}
</script>

<template>
    <div>
        <page-header :title="$t('user.userStatistics.title')" />
        <page-main>
            <el-row :gutter="20">
                <el-col :md="6">
                    <ColorfulCard :header="$t('user.userStatistics.fields.total')" :num="statistics.total.total"
                        :tip="$t('user.userStatistics.fields.todayDelta', { num: statistics.total.today })"
                        @click="gotoList" />
                </el-col>
                <el-col :md="6">
                    <ColorfulCard color-from="#fbaaa2" color-to="#fc5286"
                        :header="$t('user.userStatistics.fields.creditBalance')"
                        :num="(statistics.credits.balance / 100).toFixed(2)" icon="ep:element-plus"
                        @click="goAnchor('credit-stat')">
                        <template #tip>
                            <el-space>
                                <span>
                                    {{ $t('user.userStatistics.fields.consumed') }}&ThickSpace;
                                    {{ (statistics.credits.consumed / 100).toFixed(2) }}
                                </span>
                                <span>
                                    {{ $t('user.userStatistics.fields.returned') }}&ThickSpace;
                                    {{ (statistics.credits.returned / 100).toFixed(2) }}
                                </span>
                                <span>
                                    {{ $t('user.userStatistics.fields.promoted') }}&ThickSpace;
                                    {{ (statistics.credits.promoted / 100).toFixed(2) }}
                                </span>
                            </el-space>
                        </template>
                    </ColorfulCard>
                </el-col>
                <el-col :md="6">
                    <ColorfulCard color-from="#ff763b" color-to="#ffc480" :header="$t('user.userStatistics.fields.avgDau')"
                        :num="statistics.dau.avg"
                        :tip="$t('user.userStatistics.fields.todayDelta', { num: statistics.dau.delta })"
                        icon="ri:pages-line" @click="goAnchor('dau-stat')" />
                </el-col>
                <el-col :md="6">
                    <ColorfulCard color-from="#6a8eff" color-to="#0e4cfd" :header="$t('user.userStatistics.fields.cities')"
                        :num="statistics.cities.total"
                        :tip="$t('user.userStatistics.fields.lastMonthDelta', { num: statistics.cities.delta })"
                        icon="ep:link" @click="goAnchor('origin-stat')" />
                </el-col>
            </el-row>
        </page-main>
        <column-chart id="dau-stat" :title="$t('user.userStatistics.sections.creationStat')"
            :action="getUserCreationStatistics" />
        <column-chart id="credit-stat" :title="$t('user.userStatistics.sections.dauStat')" :action="getDauStatistics" />
        <muti-column-chart id="origin-stat" :title="$t('user.userStatistics.sections.creditStat')"
            :action="getUserCreditsStatistics" />
        <bar-chart :title="$t('user.userStatistics.sections.originStat')" :action="getUserOriginStatistics" />
    </div>
</template>

