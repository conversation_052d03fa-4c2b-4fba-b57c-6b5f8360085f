<script setup>
import { addPaymentReport, getPaymentReports } from '@/api/modules/statistics'
import { currencyFormatter } from '@/utils/formatter'
import StepButtons from './step_buttons.vue'
const route = useRoute()

const props = defineProps({
    date: {
        type: String,
        default: ''
    }

})

const emit = defineEmits(['setPaymentReport'])

const paymentReport = ref({})

onMounted(() => {
    if (props.date != '') {
        createDeliveryReport()
    } else {
        getData()
    }
})

async function createDeliveryReport() {
    let params = { date: props.date }
    let res = await addPaymentReport(params)
    if (res.data.errCode == null || res.data.errCode == 365) {
        emit('setPaymentReport', res.data.id)
        getPaymentReports({ id: res.data.id }).then(res => {
            paymentReport.value = res.data
        })
    }
}

function getData() {
    getPaymentReports({ id: route.query.id }).then(res => {
        paymentReport.value = res.data
    })
}
</script>
<template>
    <el-descriptions :title="$t('finance.eod.s7')" :column="3" border style="margin-bottom: 20px;">
        <el-descriptions-item :label="$t('fields.date')" label-align="left" align="center" :span="3">
            {{ paymentReport.date }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.fields.total')" label-align="left" align="center">
            {{ currencyFormatter(_, _, paymentReport.total) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.payment.fields.cashTotal')" label-align="left" align="center"
            label-class-name="bold" class-name="bold">
            {{ currencyFormatter(_, _, paymentReport.cash_total) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.payment.fields.creditTotal')" label-align="left" align="center"
            label-class-name="bold" class-name="bold">
            {{ currencyFormatter(_, _, paymentReport.credit_total) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('fields.count')" label-align="left" align="center">
            {{ paymentReport.count }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.payment.fields.cashCount')" label-align="left" align="center"
            label-class-name="bold" class-name="bold">
            {{ paymentReport.cash_count }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.payment.fields.creditCount')" label-align="left" align="center"
            label-class-name="bold" class-name="bold">
            {{ paymentReport.credit_count }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('finance.paymentOrder.fields.refunded')" label-align="left" align="center">
            {{ currencyFormatter(_, _, paymentReport.refund_total) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.payment.fields.cashRefund')" label-align="left" align="center">
            {{ currencyFormatter(_, _, paymentReport.cash_refund) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.payment.fields.creditRefund')" label-align="left" align="center">
            {{ currencyFormatter(_, _, paymentReport.credit_refund) }}
        </el-descriptions-item>
    </el-descriptions>
    <StepButtons v-bind="$attrs" />
</template>
<style scoped>
.bold {
    font-weight: bolder;
}
</style>
