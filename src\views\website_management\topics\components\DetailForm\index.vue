<script setup>
import { getTopics, addTopic, updateTopic, getAvailableTopicCategories } from '@/api/modules/topics'
import { languages } from '@/utils/constants'
const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    category: {
        type: String,
        default: ''
    },
})

const staticCategories = ref({
    'privacyPolicies': {
        categories: ['c17581b0eeec4d5e89f6ef2b9da0927d'],
        is_privacy_policies: true,
        is_terms_and_conditions: false,
        is_about_us: false,
        slug: 'privacy-policy',
        forbiddenUpdate: true,
    },
    'termConditions': {
        categories: ['00444fcbbfee4f90a0405c7751d62c4a'],
        is_privacy_policies: false,
        is_terms_and_conditions: true,
        is_about_us: false,
        slug: 'terms-and-conditions',
        forbiddenUpdate: true,

    },
    'aboutUs': {
        categories: ['d2192dd438f9477883035f064e884b3e'],
        is_privacy_policies: false,
        is_terms_and_conditions: false,
        is_about_us: true,
        slug: 'about-us',
        forbiddenUpdate: false,
    },

})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: '',
        priority: 10,
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const categories = ref([])

onMounted(() => {
    if (props.categoryId != '') {
        getCategories();
    }
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getCategories() {
    let params = {
        filters: {
            is_available: true,
        }
    }
    getAvailableTopicCategories(params).then(res => {
        categories.value = res.data

    })
}

function getInfo() {
    data.value.loading = true
    getTopics({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    if (props.category) {
                        data.value.form = { ...data.value.form, ...staticCategories.value[props.category] }
                    }
                    addTopic(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    if (props.category) {
                        delete data.value.form.categories
                    }
                    updateTopic(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('topics.fields.categories')" prop="categories" v-if="props.category == ''">
                <el-select v-model="data.form.categories" multiple placeholder="Select">
                    <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('fields.priority')" prop="priority" v-if="props.category == ''">
                <el-input-number v-model="data.form.priority" placeholder="请输入标题" :min="0" :step="10" />
            </el-form-item>
            <el-form-item :label="$t('topics.fields.title')" prop="title">
                <el-input v-model="data.form.title" placeholder="请输入标题"
                    :disabled="props.category != '' && staticCategories[props.category]?.forbiddenUpdate" />
            </el-form-item>
            <el-form-item :label="$t('topics.fields.slug')" prop="slug" v-if="props.category == ''">
                <el-input v-model="data.form.slug" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('topics.fields.author')" prop="author" v-if="props.category == ''">
                <el-input v-model="data.form.author" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.language')" prop="lang_codes">
                <el-select v-model="data.form.lang_code" placeholder="Select"
                    :disabled="props.category != '' && staticCategories[props.category]?.forbiddenUpdate">
                    <el-option v-for="item in languages" :key="item"
                        :label="$t(`messenger.selections.languageSelections.${item}`)" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('topics.fields.content')" prop="content">
                <editor v-model="data.form.content"
                    :disabled="props.category != '' && staticCategories[props.category]?.forbiddenUpdate" />
            </el-form-item>
            <el-form-item :label="$t('topics.fields.keywords')" prop="keywords">
                <el-input v-model="data.form.keywords" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')" prop="is_available">
                <el-switch v-model="data.form.is_available" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
