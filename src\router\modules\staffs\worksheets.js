/*
* Author: <EMAIL>'
* Date: '2024-03-03 20:11:39'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/staffs/worksheets.js'
* File: 'worksheets.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/staffs/worksheets',
    component: Layout,
    redirect: '/staffs/worksheets/delivery/list',
    name: 'worksheets',
    meta: {
        title: '工单管理',
        icon: 'material-symbols:punch-clock-outline',
        auth: ['super'],
        i18n: 'route.staffs.worksheets.title'
    },
    children: [
        {
            path: 'delivery/list',
            name: 'deliveryWorksheets',
            component: () => import('@/views/staffs/worksheets/list.vue'),
            meta: {
                title: '司机工单',
                icon: 'ep:odometer',
                activeMenu: '/staffs/worksheets/delivery/list',
                i18n: 'route.staffs.worksheets.drivers',
                auth: ['super']
            }
        },
        {
            path: 'delivery/detail',
            name: 'deliveryWorksheetDetail',
            component: () => import('@/views/staffs/worksheets/detail.vue'),
            meta: {
                title: '工单详情',
                activeMenu: '/staffs/worksheets/delivery/list',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
