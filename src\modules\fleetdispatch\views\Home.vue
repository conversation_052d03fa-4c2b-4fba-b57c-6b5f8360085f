<template>
    <div class="home">
        <FlexibleLayout>
            <template #driver-route-panel>
                <driver-route-panel />
            </template>

            <template #map-view>
                <map-view ref="mapViewRef" />
            </template>

            <template #order-list>
                <order-list />
            </template>
        </FlexibleLayout>

        <!-- 全局订单详情模态弹窗 -->
        <OrderDetailModal ref="orderDetailModal" />

        <!-- Firebase Token 显示 -->
        <div class="token-display-wrapper">
            <el-button type="primary" @click="showTokenDialog = true" size="small">
                显示FCM令牌
            </el-button>
        </div>

        <el-dialog
            v-model="showTokenDialog"
            title="Firebase 推送令牌"
            width="600px"
            destroy-on-close
        >
            <FCMTokenDisplay />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, provide } from 'vue'
import { storeToRefs } from 'pinia'
import { useDriverStore } from '../stores/driver'
import { useOrderStore } from '../stores/order'
import { useTimeStore } from '../stores/time'
import { useRouteStore } from '../stores/route'
import DriverRoutePanel from '../components/DriverRoutePanel.vue'
import {MapView} from '../components/MapView'
import OrderList from '../components/OrderList/OrderList.vue'
import FlexibleLayout from '../components/FlexibleLayout.vue'
import OrderDetailModal from '../components/OrderDetailModal.vue'
import FCMTokenDisplay from '../components/FCMTokenDisplay.vue'

const driverStore = useDriverStore()
const orderStore = useOrderStore()
const timeStore = useTimeStore()
const routeStore = useRouteStore()

// FCM令牌对话框显示控制
const showTokenDialog = ref(false)

const { selectedDriver } = storeToRefs(driverStore)

// 订单详情模态弹窗引用
const orderDetailModal = ref(null)

// 地图组件引用
const mapViewRef = ref(null)

// 提供全局订单详情模态弹窗方法给子组件
provide('orderDetailModal', {
    open: (orderId, orderNo) => {
        console.log('Home.vue: 收到打开订单详情请求', { orderId, orderNo })

        // 生成URL
        const baseUrl = window.location.origin
        const orderUrl = `${baseUrl}/delivery/orders/detail?id=${orderId}`

        // 输出日志
        console.log('Home.vue: 准备打开订单详情', { orderId, orderNo, orderUrl })

        // 打开模态框
        if (orderDetailModal.value) {
            console.log('Home.vue: 使用OrderDetailModal组件打开')
            orderDetailModal.value.open(orderId, orderNo, orderUrl)
        } else {
            console.error('Home.vue: orderDetailModal引用未找到，使用备选方法')

            // 备选方法：直接创建模态框
            const openInNewTab = false // 是否在新标签页打开

            if (openInNewTab) {
                // 在新标签页打开
                window.open(orderUrl, '_blank')
            } else {
                // 创建一个临时的模态框元素
                createTempModalInHome(orderId, orderNo, orderUrl)
            }
        }
    }
})

// 提供地图路线管理功能给子组件
provide('mapRouteManagement', {
    get drawRealisticRoute() {
        return mapViewRef.value?.drawRealisticRoute || null
    },
    get updateRouteSegments() {
        return mapViewRef.value?.updateRouteSegments || null
    },
    get handleRouteOrderChange() {
        return mapViewRef.value?.handleRouteOrderChange || null
    },
    get fetchRouteSegment() {
        return mapViewRef.value?.fetchRouteSegment || null
    },
    get rebuildAndUpdateRouteDisplay() {
        return mapViewRef.value?.rebuildAndUpdateRouteDisplay || null
    },
    get clearAllRoutes() {
        return mapViewRef.value?.clearAllRoutes || null
    },
    get hideAllRoutes() {
        return mapViewRef.value?.hideAllRoutes || null
    },
    get showRoute() {
        return mapViewRef.value?.showRoute || null
    },
    get redrawRoutes() {
        return mapViewRef.value?.redrawRoutes || null
    },
    get drawDirectRoute() {
        return mapViewRef.value?.drawDirectRoute || null
    },
    get drawRoutes() {
        return mapViewRef.value?.drawRoutes || null
    }
})

// 创建临时模态框（作为备选方案）
const createTempModalInHome = (orderId, orderNo, url) => {
    console.log('Home.vue: 创建临时模态框')

    // 检查是否已存在全局模态框，如果存在则先移除
    const existingModal = document.getElementById('home-order-modal')
    if (existingModal) {
        document.body.removeChild(existingModal)
    }

    // 创建模态框容器
    const modalContainer = document.createElement('div')
    modalContainer.id = 'home-order-modal'
    modalContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 99999;
        display: flex;
        justify-content: center;
        align-items: center;
    `

    // 创建模态框内容
    const modalContent = document.createElement('div')
    modalContent.style.cssText = `
        position: relative;
        width: 90vw;
        height: 90vh;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        animation: homeModalAppear 0.3s ease-out;
    `

    // 添加动画样式
    const styleElement = document.createElement('style')
    styleElement.textContent = `
        @keyframes homeModalAppear {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `
    document.head.appendChild(styleElement)

    // 创建模态框头部
    const modalHeader = document.createElement('div')
    modalHeader.style.cssText = `
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dfe4e8;
    `

    // 添加标题
    const modalTitle = document.createElement('h2')
    modalTitle.textContent = `订单详情: ${orderNo || '加载中...'}`
    modalTitle.style.cssText = `
        margin: 0;
        font-size: 18px;
        color: #333;
    `

    // 添加关闭按钮
    const closeButton = document.createElement('button')
    closeButton.innerHTML = '&times;'
    closeButton.style.cssText = `
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        transition: all 0.2s;
    `
    closeButton.onclick = () => {
        document.body.removeChild(modalContainer)
        document.head.removeChild(styleElement)
        document.body.style.overflow = 'auto'
    }

    // 创建内容区域
    const modalBody = document.createElement('div')
    modalBody.style.cssText = `
        flex: 1;
        overflow: hidden;
        position: relative;
    `

    // 创建iframe
    const iframe = document.createElement('iframe')
    iframe.src = url
    iframe.style.cssText = `
        width: 100%;
        height: 100%;
        border: none;
    `

    // 创建底部
    const modalFooter = document.createElement('div')
    modalFooter.style.cssText = `
        padding: 12px 20px;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid #dfe4e8;
    `

    // 添加关闭按钮
    const closeButtonLarge = document.createElement('button')
    closeButtonLarge.textContent = '关闭'
    closeButtonLarge.style.cssText = `
        padding: 8px 20px;
        background-color: #1a73e8;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
    `
    closeButtonLarge.onclick = () => {
        document.body.removeChild(modalContainer)
        document.head.removeChild(styleElement)
        document.body.style.overflow = 'auto'
    }

    // 组装模态框
    modalHeader.appendChild(modalTitle)
    modalHeader.appendChild(closeButton)
    modalBody.appendChild(iframe)
    modalFooter.appendChild(closeButtonLarge)

    modalContent.appendChild(modalHeader)
    modalContent.appendChild(modalBody)
    modalContent.appendChild(modalFooter)

    modalContainer.appendChild(modalContent)

    // 阻止背景滚动
    document.body.style.overflow = 'hidden'

    // 添加ESC键监听
    const handleEscKeyDown = event => {
        if (event.key === 'Escape') {
            document.body.removeChild(modalContainer)
            document.head.removeChild(styleElement)
            document.body.style.overflow = 'auto'
            document.removeEventListener('keydown', handleEscKeyDown)
        }
    }
    document.addEventListener('keydown', handleEscKeyDown)

    // 添加点击背景关闭功能
    modalContainer.addEventListener('click', event => {
        if (event.target === modalContainer) {
            document.body.removeChild(modalContainer)
            document.head.removeChild(styleElement)
            document.body.style.overflow = 'auto'
            document.removeEventListener('keydown', handleEscKeyDown)
        }
    })

    // 添加到文档中
    document.body.appendChild(modalContainer)

    console.log('Home.vue: 已创建临时订单详情模态框')
}

// 在父组件中统一初始化数据
onMounted(async() => {
    // 设置当前时间和班次
    timeStore.setCurrentShift()

    // 先获取班次数据
    await timeStore.fetchShifts()
    console.log('班次数据获取完成，当前班次:', timeStore.selectedShift ? timeStore.selectedShift.label : '未设置')

    // 加载司机数据，传递 shift 参数
    console.log('获取司机数据，使用当前班次...')
    await driverStore.fetchDrivers({
        date: timeStore.selectedDate,
        shift: timeStore.selectedShift
    })
    console.log('司机数据获取完成，数量:', driverStore.drivers.length)

    // 加载订单数据
    await orderStore.fetchOrders(timeStore.currentTimeRange)

    // 加载路线数据
    try {
        routeStore.fetchRoutes({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift // 传递完整的 shift 对象
        })
    } catch (error) {
        console.error('加载路线数据失败:', error)
    }

    // 在Home组件挂载后打印日志确认模态框引用
    console.log('Home.vue mounted, orderDetailModal ref:', orderDetailModal.value)
})
</script>

<style scoped>
.home {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.token-display-wrapper {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100;
}

/* 确保按钮悬浮显示在地图上 */
.token-display-wrapper .el-button {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
