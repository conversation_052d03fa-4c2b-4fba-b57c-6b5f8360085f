/*
 * Author: <EMAIL>'
 * Date: '2022-10-28 23:04:38'
 * Project: 'Admin-UI'
 * Path: 'src/http/request.js'
 * File: 'request.js'
 * Version: '1.0.0'
 */

import router from '@/router/index'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import axios from 'axios'

import { createI18n } from 'vue-i18n'
import { messages } from '@/locales/index'




const instance = axios.create({
    headers: { 'Content-Type': 'application/json;charset=UTF-8;' }
})

// Request interceptor
instance.interceptors.request.use(
    config => {
        return config
    },
    error => {
        return Promise.reject(error)
    },
    request => {
        const userStore = useUserStore()
        if (userStore.isLogin) {
            return request
        }
    }
)

// Response interceptor
instance.interceptors.response.use(
    response => {
        // 导出
        const headers = response.headers
        // console.log(headers['content-type'])  将打印的值，也将后台返回的相应头设置成相同的，我的就是'application/octet-stream;charset=UTF-8',然后返回response
        if (headers['content-type'] == 'application/pdf') {
            return response
        }

        const settingsStore = useSettingsStore()
        const i18n = createI18n({
            legacy: false,
            locale: settingsStore.app.defaultLang,
            flatJson: true,
            fallbackLocale: 'en',
            messages,
        })

        const { t } = i18n.global


        if (response.data && response.data.errCode) {
            let msg = response.data.msg
            switch (response.data.errCode) {
                case 365:
                    ElMessage.success(
                        t(
                            `response.messages.${msg.split('.')[2]}`,
                            { entity: t(`response.entities.${msg.split('.')[1]}`) }
                        )
                    )
                    break
                case 444:
                    ElMessage.warning(t(
                        `response.messages.${msg.split('.')[2]}`,
                        { entity: t(`response.entities.${msg.split('.')[1]}`) }
                    )
                    )
                    break
                case 445:
                    ElMessage.error(t(
                        `response.messages.${msg.split('.')[2]}`,
                        { entity: t(`response.entities.${msg.split('.')[1]}`) }
                    )
                    )
                    break
                case 499:
                    ElMessage.error(t(
                        `response.messages.${msg.split('.')[2]}`,
                        { entity: t(`response.entities.${msg.split('.')[1]}`) }
                    )
                    )
                    break
                case 366:
                    ElMessage(t(
                        `response.messages.${msg.split('.')[2]}`,
                        { entity: t(`response.entities.${msg.split('.')[1]}`) }
                    )
                    )
                    break
                default:
                    ElMessage(t(
                        `response.messages.${msg.split('.')[2]}`,
                        { entity: t(`response.entities.${msg.split('.')[1]}`) }
                    )
                    )
            }
        }
        return response
    },
    error => {
        switch (error.response.status) {
            case 401:
                break
            case 500:
                ElMessage('systemError')
                break
            default:
                alert(error.response.data)
        }
        return Promise.reject(error)
    }
)

export default instance
