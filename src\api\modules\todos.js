/*
* Author: <EMAIL>'
* Date: '2023-04-29 20:14:15'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/todos.js'
* File: 'todos.js'
* Version: '1.0.0'
*/

import { DEL, GET, PAT, POST, PUT } from "../methods";

const path = 'todos/'

export const getTodos = (params) => GET(path, params)
export const addTodo = (params) => POST(path, params)
export const updateTodo = (params) => PUT(path, params)
export const alterTodoStatus = (params) => PAT(path, params)
export const deleteTodos = (params) => DEL(path, params)

export const executeTodos = (params) => POST(path + 'execute/', params)

export const queryNewTodos = () => GET(path + 'query/new/')



