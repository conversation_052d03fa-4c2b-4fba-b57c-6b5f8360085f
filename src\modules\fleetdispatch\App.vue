<template>
    <div class="app">
        <Home />
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouteStore } from './stores/route'
import Home from './views/Home.vue'
import { authAPI } from './api'
import { useOrderStore } from './stores/order'
import { useMapStore } from './stores/map'
import { initializeFleetDispatch } from './utils/initialize'

const routeStore = useRouteStore()
const orderStore = useOrderStore()
const mapStore = useMapStore()

// 直接使用initialize.js中的初始化函数
onMounted(async() => {
    console.log('FleetDispatch App 已加载')
  
    // 初始化FleetDispatch模块
    await initializeFleetDispatch()
  
    console.log('App mounted, initializing data...')
  
    // 加载订单数据
    await orderStore.fetchOrders()
    console.log('Orders loaded:', {
        count: orderStore.orders?.length || 0
    })
  
    // 初始化地图点位
    if (orderStore.allOrders?.length > 0) {
        console.log('Initializing map points...')
        mapStore.initMapPoints(orderStore.allOrders)
        console.log('Map points initialized')
    }
})
</script>

<style>
.app {
    height: 100%;
    width: 100%;
    overflow: hidden;
}

/* 清除地图溢出样式 */

.map-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* 确保内容不超出面板 */

.panel-content {
    overflow: auto;
}
</style> 