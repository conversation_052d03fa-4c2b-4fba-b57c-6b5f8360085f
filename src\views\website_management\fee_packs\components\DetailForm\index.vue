<script setup>
import { getFeePacks, updateFeePack } from '@/api/modules/web_contents.js'
import { getAllServicePacks } from '@/api/modules/delivery'
import { Plus } from '@element-plus/icons-vue'
import ServicePackItemSelector from '@/views/components/ServicePackItems/selector.vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        items: [],
        packs: []
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const fee = ref(0)
const packs = ref([])
const imageUrl = ref(null)


onMounted(async () => {
    getPacks();
    console.log(imageUrl.value)
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getPacks() {
    getAllServicePacks().then(res => {
        packs.value = res.data
    })

}
const selectedItems = ref([])

function getInfo() {
    data.value.loading = true
    getFeePacks({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        fee.value = Number((res.data.fee / 100).toFixed(2))
        imageUrl.value = res.data.banner
        selectedItems.value = JSON.parse(JSON.stringify(res.data.items))
    })
}

defineExpose({
    submit(callback) {
        const _data = prepareData();
        formRef.value.validate(valid => {
            if (valid) {
                updateFeePack(_data).then((res) => {
                    if (res.data.errCode == 365) { callback && callback() }
                })
            }
        })
    }
})

const file = ref()

function prepareData() {
    const formData = new FormData();
    formData.append('id', data.value.form.id)
    formData.append('name', data.value.form.name)
    formData.append('fee', fee.value * 100)
    formData.append('is_vip', data.value.form.is_vip)
    formData.append('is_available', data.value.form.is_available)
    formData.append('priority', data.value.form.priority)
    formData.append('banner', file.value?.raw ?? '')
    formData.append('items', JSON.stringify(selectedItems.value))
    formData.append('packs', data.value.form.packs)
    return formData;
}

function onChange(_file, fileList) {
    file.value = _file
    imageUrl.value = URL.createObjectURL(_file.raw)
}
</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                <el-form-item :label="$t('fields.priority')" prop="priority">
                    <el-input-number v-model="data.form.priority"
                        :placeholder="$t('placeholder', { field: $t('fields.priority') })" />
                </el-form-item>
                <el-form-item :label="$t('fields.name')" prop="name">
                    <el-input v-model="data.form.name" placeholder="请输入标题" />
                </el-form-item>
                <el-form-item :label="$t('fields.fee')" prop="fee">
                    <el-input-number v-model="fee" placeholder="请输入标题" />
                </el-form-item>
                <el-form-item :label="$t('webContents.feePacks.fields.isVip')" prop="is_vip">
                    <el-switch v-model="data.form.is_vip" placeholder="请输入标题" />
                </el-form-item>
                <el-form-item :label="$t('webContents.feePacks.fields.banner')">
                    <el-upload class="uploader" :show-file-list="false" :auto-upload="false" :on-change="onChange">
                        <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                        <el-icon v-else style=" color: #8c939d; width: 178px; height: 30px; text-align: center;">
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>

                <el-form-item :label="$t('fields.isAvailable')" prop="is_available">
                    <el-switch v-model="data.form.is_available" placeholder="请输入标题" />
                </el-form-item>
                <el-form-item :label="$t('webContents.feePacks.fields.packs')">
                    <el-checkbox-group v-model="data.form.packs" v-if="packs.length > 0">
                        <div style="flex-direction: column;" v-for="item of packs">
                            <el-checkbox :label="item.id">
                                {{ item.names['en-us'] }}
                            </el-checkbox>
                        </div>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item :label="$t('webContents.feePacks.fields.items')">
                    <ServicePackItemSelector v-model="selectedItems" />
                </el-form-item>
            </el-form>
        </page-main>

    </div>
</template>

<style lang="scss">
.uploader {

    .el-upload {
        border: 1px dashed var(--el-border-color);
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: var(--el-transition-duration-fast);
    }

    .avatar {
        height: 30px;
        width: auto;
        display: block;
    }

    .el-upload:hover {
        border-color: var(--el-color-primary);
    }
}
</style>
