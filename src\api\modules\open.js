import { GET, POST, PAT, PUT, DEL } from "../methods";

const path = 'open/'

export const getOpenUser = params => GET(path + 'users/', params);
export const getOpenUserSimpleList = params => GET(path + 'users/simple-list/', params);
export const addOpenUser = params => POST(path + 'users/', params);
export const updateOpenUserSecret = params => PUT(path + 'users/', params);
export const alterOpenUserStatus = params => PAT(path + 'users/', params);
export const approveOpenUser = params => GET(path + 'users/approve/', params);
export const deleteOpenUserStatus = params => DEL(path + 'users/', params);

// Delivery
const deliveryPath = path + 'delivery/'
export const getOpenDeliveryOrders = params => GET(deliveryPath + 'orders/', params)
export const getUnbilledOpenDeliveryOrders = params => GET(deliveryPath + 'orders/unbilled/', params)

// Bill
const billPath = deliveryPath + 'bills/'
export const getOpenDeliveryBills = params => GET(billPath, params);
export const downloadOpenDeliveryBill = id => GET(billPath + `download/${id}/`);
export const sendOpenDeliveryBill = id => GET(billPath + `send/${id}/`);
export const addOpenDeliveryBill = params => POST(billPath, params);
export const updateOpenDeliveryBillPayment = params => PUT(billPath, params);
export const deleteOpenDeliveryBill = params => DEL(billPath, params);

// Utils
export const getPaymentMethods = () => GET(path + 'payment-methods/');
