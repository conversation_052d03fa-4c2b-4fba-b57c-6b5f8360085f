/*
* Author: <EMAIL>'
* Date: '2024-02-03 20:42:16'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/dispatcher/elite_extra.js'
* File: 'elite_extra.js'
* Version: '1.0.0'
*/
const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/elite-extra',
    component: Layout,
    redirect: '/dispatcher/elite-extra/logs',
    name: 'dispatcherEliteExtra',
    meta: {
        title: 'Elite Extra',
        icon: 'carbon:image-service',
        auth: ['super'],
        // i18n: 'route.dispatcher.suppliers.title'
    },
    children: [
        {
            path: 'drivers',
            name: 'dispatcherSupplierEliteDrivers',
            component: () => import('@/views/dispatcher/elite_extra/drivers.vue'),
            meta: {
                title: 'Drivers',
                icon: 'tabler:message-2-up',
                activeMenu: '/dispatcher/elite-extra/drivers',
                i18n: 'route.dispatcher.eliteExtra.drivers',
                auth: ['super']
            }
        },
        {
            path: 'templates',
            name: 'dispatcherSupplierEliteTemplateList',
            component: () => import('@/views/dispatcher/elite_extra/list.vue'),
            meta: {
                title: 'Elite Templates',
                icon: 'tabler:message-2-up',
                activeMenu: '/dispatcher/elite-extra/templates',
                i18n: 'route.dispatcher.suppliers.eliteTemplates',
                auth: ['super']
            }
        },
        {
            path: 'elite-extra/templates/detail',
            name: 'dispatcherSupplierEliteTemplateDetail',
            component: () => import('@/views/dispatcher/elite_extra/detail.vue'),
            meta: {
                title: 'Elite Template',
                icon: 'tabler:message-2-up',
                activeMenu: '/dispatcher/elite-extra/templates',
                i18n: 'route.dispatcher.suppliers.eliteTemplates',
                breadcrumb: false,
                sidebar: false,
                auth: ['super']
            }
        },
        {
            path: 'logs',
            name: 'eliteExtraLogs',
            component: () => import('@/views/dispatcher/logs/list.vue'),
            meta: {
                title: '地址列表',
                icon: 'icon-park-outline:upload-logs',
                activeMenu: '/dispatcher/elite-extra/logs',
                i18n: 'route.dispatcher.logs.title',
                auth: ['super']
            }
        },

    ]
}
