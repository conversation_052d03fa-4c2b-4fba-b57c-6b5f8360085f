{"addresses": {"title": "地址库", "fields": {}}, "zones": {"title": "区域管理"}, "postalCodes": {"title": "邮政编码", "fields": {"pc1": "PC 1"}}, "suppliers": {"title": "供应商", "fields": {"key": "Key", "pin": "<PERSON>n", "baseUrl": "Base Url"}}, "elite": {"template": "Elite 模版", "templates": "Elite 模版", "availableVars": "可用变量", "vars": {"AddedServiceCode": "增值服务代码", "AdminNotes": "管理员备注", "DeliveryDay": "送货日期(日)", "DeliveryHour": "送货时间（时）", "DeliveryMonth": "送货日期(月)", "DeliveryOption": "送货选项", "DeliveryTime": "送货时间", "Packaging": "包装物", "PackagingFee": "包装费", "PickupDay": "取货日期(日)", "PickupHour": "取货时间（时）", "PickupMonth": "取货日期(月)", "PickupTime": "取货时间", "ReceiverAddress2": "收件人地址2 (Unit: xxx, Buzzer: yyy", "ReceiverCompany": "收件人公司名", "ReceiverName": "收货人", "ReceiverPhone": "收货人电话", "ReceiverPhoneExtension": "收件人分机号", "ReceiverSecondaryPhone": "收件人备用电话", "SenderAddress2": "寄件人地址2 (Unit: xxx, Buzzer: yyy", "SenderCompany": "寄件人公司名", "SenderName": "送货人", "SenderPhone": "送货人电话", "SenderSecondaryPhone": "寄件人备用电话", "TipFee": "小费", "UserCode": "用户代码", "UserCompanyName": "用户公司名", "UserLanguage": "用户语言", "UserVip": "用户VIP"}}, "logs": {"title": "日志", "fields": {"doc": "文档", "exceptions": "错误", "orderNo": "订单 #", "tracking": "跟踪码", "userCompanyName": "用户公司", "userName": "用户姓名"}, "selections": {"status": {"failed": "失败", "success": "成功"}}}, "orders": {"title": "订单列表", "fields": {"dispatchedAt": "调度时间", "pickedUpBy": "取货司机", "pickupStopNo": "取货停站 #", "pickedUpAt": "取货时间", "deliveredBy": "送货司机", "deliveryStopNo": "送货停站 #", "deliveredAt": "送达时间", "isSorted": "已分拣", "address": "地址", "contact": "联系人", "travelTime": "行程时间", "distance": "行程距离", "completedAt": "完成时间", "eta": "预计到达时间", "stop": "停站", "timeWindow": "时间窗口", "phoneNumber": "电话", "location": "位置名称", "serviceTime": "服务时间", "unitNo": "单元号", "buzzerCode": "门铃码", "relatedOrders": "相关订单", "uploadedAt": "最近上传时间", "uploadAt": "计划上传时间", "duration": "用时"}, "operations": {"remove": "移除"}}, "sort": {"title": "订单分拣"}, "exceptions": {"fields": {"notifyUser": "通知用户", "notifyMethod": "通知方式", "messageCategory": "消息类型", "needAck": "需要确认"}}, "routes": {"fields": {"startAt": "开始时间", "completedAt": "完成时间", "driver": "司机", "pickups": "取货单", "deliveries": "送货单", "name": "路线"}}, "serviceOrders": {"selections": {"status": {"CANCELED": "已取消", "CREATED": "已创建", "PENDING": "已挂起", "SCHEDULED": "已计划", "FAILED": "失败", "COMPLETED": "已完成"}}}}