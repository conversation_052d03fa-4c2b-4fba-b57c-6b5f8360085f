export class BackupService {
    constructor() {
        this.backupFileName = 'exampleorders_backup.json'
    }

    /**
   * 将订单列表转换为备份格式并下载为JSON文件
   * @param {Array} orderList - 原始订单列表
   * @returns {Promise<boolean>} - 是否成功保存
   */
    async saveOrdersToBackup(orderList) {
        try {
            // 转换订单列表为备份格式
            const backupOrders = this.convertOrdersToBackupFormat(orderList)
      
            // 准备备份数据
            const backupData = { orders: backupOrders }
      
            // 将数据保存到localStorage以便后续使用
            localStorage.setItem(this.backupFileName, JSON.stringify(backupData))
      
            // 下载文件
            this.downloadJsonFile(backupData, this.backupFileName)
      
            console.log(`成功将${backupOrders.length}个订单备份并下载为文件`)
            return true
        } catch (error) {
            console.error('将订单写入备份文件时出错:', error)
            return false
        }
    }

    /**
   * 将JSON数据下载为文件
   * @param {Object} data - 要下载的JSON数据
   * @param {string} filename - 文件名
   */
    downloadJsonFile(data, filename) {
    // 将数据转换为JSON字符串，并保持缩进格式
        const jsonString = JSON.stringify(data, null, 2)
    
        // 创建一个Blob对象
        const blob = new Blob([jsonString], { type: 'application/json' })
    
        // 创建一个下载链接
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
    
        // 添加到DOM树中并触发点击（然后移除）
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
    
        // 释放URL对象
        URL.revokeObjectURL(url)
    }

    /**
   * 将原始订单列表转换为备份格式
   * @param {Array} orderList - 原始订单列表
   * @returns {Array} - 备份格式的订单列表
   */
    convertOrdersToBackupFormat(orderList) {
        if (!Array.isArray(orderList) || orderList.length === 0) {
            return []
        }

        return orderList.map(order => {
            // 确定订单类型 (P: 取件, D: 送件)
            const orderType = order.type === 'PICKUP' ? 'P' : 'D'
      
            // 获取收件人信息 (counterpart)
            const receiver = order.counterpart || {}
      
            // 始终使用receiver的信息来填充主要字段
            const postal_code = receiver.postal_code || ''
            const address = receiver.address || ''
            const city = receiver.city || ''
            const name = receiver.name || ''
            const company = receiver.company || ''
            const phone = receiver.phone || ''
      
            // 保存原始订单信息（用于sender字段）
            const original_postal_code = order.postal_code || ''
            const original_address = order.address || ''
            const original_city = order.city || ''
            const original_name = order.name || ''
            const original_company = order.company || ''
            const original_phone = order.phone || ''
      
            // 生成唯一的外部客户ID
            const externalCustomerId = `EC-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`
      
            // 获取经纬度
            const latitude = order.lng_lat && order.lng_lat.length >= 2 ? order.lng_lat[0] : 0
            const longitude = order.lng_lat && order.lng_lat.length >= 2 ? order.lng_lat[1] : 0
      
            // 获取当前日期作为默认日期，格式为YYYY-MM-DD
            const today = new Date().toISOString().split('T')[0]
      
            // 构造备份格式的订单对象，始终使用receiver的信息作为主要地址字段
            return {
                'type': orderType,
                'order_channel_code': 'SMALL',
                'self_pickup': 0,
                'customer_id': null,
                'customer_code': null,
                'postcode': postal_code,
                'address_1': address,
                'address_2': '',
                'combine_address': 'false',
                'buzz_code': '',
                'ref': order.no || '',
                'city': city,
                'province': 'ON',
                'country': 'Canada',
                'name': name,
                'company_name': company,
                'email': '<EMAIL>',
                'telephone': phone,
                'need_pick_up': orderType === 'P' ? 1 : 0,
                'packages': 1,
                'packagesDetail': [
                    {
                        'ref': order.no || '',
                        'weight': 1000,
                        'length': 300,
                        'width': 200,
                        'height': 100,
                        'dimension_unit': 1,
                        'weight_unit': 1,
                        'package_value': 200,
                        'insurance_value': 200,
                        'external_tracking_number': ''
                    }
                ],
                'allow_dropoff': 1,
                'shipping_from': '',
                'sender_name': original_name,
                'sender_address_1': original_address,
                'sender_address_2': '',
                'sender_address_type': 1,
                'sender_postcode': original_postal_code,
                'sender_city': original_city,
                'sender_province': 'ON',
                'sender_country': 'Canada',
                'sender_company_name': original_company,
                'sender_email': '<EMAIL>',
                'sender_telephone': original_phone,
                'sender_buzz_code': '',
                'pickup_instruction': '',
                'delivery_instruction': '',
                'note': '',
                'signature_option': 1,
                'insurance_option': 1,
                'insurance_value': 200,
                'scheduled_date': order.scheduled_date || `${today}T00:00:00.000Z`,
                'time_window_start': order.time_window_from ? `${order.scheduled_date || today}T${order.time_window_from}.000Z` : `${today}T15:00:00.000Z`,
                'time_window_end': order.time_window_to ? `${order.scheduled_date || today}T${order.time_window_to}.000Z` : `${today}T16:45:00.000Z`,
                'pickup_date': order.scheduled_date || `${today}T00:00:00.000Z`,
                'pickup_time_window_start': order.time_window_from ? `${order.scheduled_date || today}T${order.time_window_from}.000Z` : `${today}T15:00:00.000Z`,
                'pickup_time_window_end': order.time_window_to ? `${order.scheduled_date || today}T${order.time_window_to}.000Z` : `${today}T16:45:00.000Z`,
                'service_time': 1,
                'pickup_service_time': 1,
                'notification_language': 'en',
                'skills': '',
                'latitude': latitude,
                'longitude': longitude,
                'order_batch': today.replace(/-/g, '-'),
                'auto_deduplication': 1,
                'external_customer': {
                    'id': externalCustomerId,
                    'name': name || 'Fleetnow',
                    'email': '<EMAIL>',
                    'system_code': 'es1'
                },
                'a_scan_at': Math.floor(Date.now() / 1000),
                'sorting_code': 0,
                'travel_time': order.travel_time || 0,
                'distance': order.distance || 0,
                'receiver_name': name,
                'receiver_company_name': company,
                'receiver_telephone': phone,
                'receiver_address_1': address,
                'receiver_address_2': '',
                'receiver_city': city,
                'receiver_province': 'ON',
                'receiver_postcode': postal_code,
                'receiver_country': 'Canada'
            }
        })
    }

    /**
   * 清空备份数据并重新初始化
   * @returns {Promise<boolean>} - 是否成功初始化
   */
    async initializeBackupFile() {
        try {
            const emptyBackup = { orders: [] }
            localStorage.setItem(this.backupFileName, JSON.stringify(emptyBackup))
            console.log('备份数据已初始化')
            return true
        } catch (error) {
            console.error('初始化备份数据时出错:', error)
            return false
        }
    }
} 