import { ref, computed } from 'vue';
import { useDriverStore } from '../../../stores/driver';
import maplibregl from 'maplibre-gl';
import { eventBus, EVENT_TYPES } from '../../../utils/eventBus';

export function useDriverHomeMarkers(map, moveSymbolLayersToTop) {
    const driverStore = useDriverStore();
    
    // 司机家庭地址标记管理
    const showDriverHomes = ref(true);
    const activeDriverHomePopup = ref(null);
    
    // 添加司机家庭地址图层引用
    const driverHomesLayer = ref(null);
    
    // 创建房子图标
    const createHouseIcon = (color) => {
        if (!map.value) return;
        
        const iconId = `house-marker-${color.replace('#', '')}`;
        
        // 检查图标是否已存在
        if (map.value.hasImage(iconId)) return iconId;
        
        // 创建一个Canvas元素来绘制房子图标
        const size = 32;
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        if (!ctx) return null;
        
        // 清除之前的内容
        ctx.clearRect(0, 0, size, size);
        
        // 绘制房子图标
        ctx.save();
        
        // 绘制房子轮廓
        ctx.fillStyle = color;
        
        // 房子主体
        ctx.beginPath();
        ctx.moveTo(16, 4);  // 屋顶顶点
        ctx.lineTo(4, 14);  // 左下角
        ctx.lineTo(28, 14); // 右下角
        ctx.closePath();
        ctx.fill();
        
        // 房子底部
        ctx.fillRect(6, 14, 20, 12);
        
        // 门
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(13, 18, 6, 8);
        
        // 窗户
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(8, 16, 4, 4);
        ctx.fillRect(20, 16, 4, 4);
        
        // 烟囱
        ctx.fillStyle = color;
        ctx.fillRect(22, 6, 3, 4);
        
        ctx.restore();
        
        // 将画布转换为图像并添加到地图
        map.value.addImage(iconId, { 
            width: size, 
            height: size, 
            data: ctx.getImageData(0, 0, size, size).data 
        });
        
        console.log(`已创建房子图标: ${iconId}`);
        return iconId;
    };
    
    // 添加默认房子图标
    const addDefaultHouseIcon = () => {
        if (!map.value) return;
        
        // 创建默认灰色房子图标
        createHouseIcon('#9E9E9E');
    };
    
    // 添加司机家庭地址图层
    const addDriverHomesLayer = () => {
        if (!map.value) return;
        
        try {
            // 添加默认房子图标
            addDefaultHouseIcon();
            
            // 如果数据源不存在，创建数据源
            if (!map.value.getSource('driver-homes-source')) {
                map.value.addSource('driver-homes-source', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: []
                    }
                });
                console.log('已创建司机家庭地址数据源');
            }
            
            // 如果图层不存在，添加图层
            if (!map.value.getLayer('driver-homes-layer')) {
                map.value.addLayer({
                    id: 'driver-homes-layer',
                    type: 'symbol',
                    source: 'driver-homes-source',
                    layout: {
                        // 使用小房子图标，颜色和司机颜色一致
                        'icon-image': [
                            'case',
                            ['has', 'driver_color'], ['get', 'icon_id'], // 使用带颜色的房子图标
                            'house-marker-9E9E9E' // 使用默认灰色房子图标
                        ],
                        'icon-size': 0.8,
                        'icon-allow-overlap': true,
                        'icon-ignore-placement': true,
                        // 添加司机名称标签
                        'text-field': ['get', 'name'],
                        'text-size': 12,
                        'text-offset': [0, 1.5], // 在图标下方显示文本
                        'text-anchor': 'top',
                        'text-allow-overlap': false,
                        'text-ignore-placement': false
                    },
                    paint: {
                        // 文本样式
                        'text-color': '#333',
                        'text-halo-color': '#fff',
                        'text-halo-width': 1
                    }
                });
                
                // 设置图层引用
                driverHomesLayer.value = 'driver-homes-layer';
                
                // 添加事件监听
                map.value.on('click', 'driver-homes-layer', onDriverHomeMarkerClicked);
                map.value.on('mouseenter', 'driver-homes-layer', () => {
                    map.value.getCanvas().style.cursor = 'pointer';
                });
                map.value.on('mouseleave', 'driver-homes-layer', () => {
                    map.value.getCanvas().style.cursor = '';
                });
                
                // 确保订单图层显示在司机家庭地址图层之上
                if (typeof moveSymbolLayersToTop === 'function') {
                    moveSymbolLayersToTop();
                }
                console.log('已添加司机家庭地址图层');
            } else {
                // 图层已存在，设置引用
                driverHomesLayer.value = 'driver-homes-layer';
            }
        } catch (error) {
            console.error('添加司机家庭地址图层时出错:', error);
        }
    };
    
    // 处理司机家庭地址标记点击事件
    const onDriverHomeMarkerClicked = (e) => {
        if (!e.features || e.features.length === 0) return;
        
        // 获取司机ID
        const driverId = e.features[0].properties.id;
        
        // 获取司机对象
        const driver = driverStore.getDriverById(driverId);
        
        if (!driver) {
            console.warn('无法找到ID为', driverId, '的司机');
            return;
        }
        
        // 在store中选择司机
        driverStore.selectDriver(driver);
        
        // 触发事件通知其他组件
        eventBus.emit(EVENT_TYPES.DRIVER_SELECTED, driverId);
    };
    
    // 更新司机家庭地址标记
    const updateDriverHomeMarkers = () => {
        if (!map.value || !showDriverHomes.value) return;
        
        // 获取所有司机
        const drivers = driverStore.drivers;
        
        // 过滤出有家庭地址坐标的司机
        const driversWithHomeAddress = drivers.filter(driver => 
            driver.address_lng_lat && 
            Array.isArray(driver.address_lng_lat) && 
            driver.address_lng_lat.length === 2
        );
        
        console.log(`找到 ${driversWithHomeAddress.length} 个有家庭地址的司机`);
        
        // 确保司机家庭地址图层存在
        const hasDriverHomesLayer = driverHomesLayer && driverHomesLayer.value;
        if (!hasDriverHomesLayer || !map.value.getLayer(driverHomesLayer.value)) {
            console.log('添加司机家庭地址图层');
            addDriverHomesLayer();
        }
        
        // 创建GeoJSON特征
        const features = driversWithHomeAddress.map(driver => {
            // 获取坐标
            const [lat, lng] = driver.address_lng_lat;
            
            // 获取司机颜色，如果没有则使用默认颜色
            const color = driver.color || '#9E9E9E';
            
            // 创建带颜色的房子图标
            const iconId = createHouseIcon(color);
            
            // 创建要素
            return {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lng, lat]
                },
                properties: {
                    id: driver.id,
                    name: driver.name || `司机 ${driver.id}`,
                    driver_color: color,
                    icon_id: iconId
                }
            };
        });
        
        // 更新数据源
        try {
            const source = map.value.getSource('driver-homes-source');
            if (source) {
                source.setData({
                    type: 'FeatureCollection',
                    features: features
                });
                console.log(`司机家庭地址数据源更新成功，包含 ${features.length} 个地址`);
            } else {
                console.warn('司机家庭地址数据源不存在，无法更新');
                console.log('尝试重新添加司机家庭地址图层');
                addDriverHomesLayer();
                
                // 重试一次更新数据源
                setTimeout(() => {
                    const retrySource = map.value.getSource('driver-homes-source');
                    if (retrySource) {
                        retrySource.setData({
                            type: 'FeatureCollection',
                            features: features
                        });
                        console.log('重试更新司机家庭地址数据源成功');
                    } else {
                        console.error('重试更新司机家庭地址数据源失败');
                    }
                }, 500);
            }
        } catch (error) {
            console.error('更新司机家庭地址数据源时出错:', error);
        }
    };
    
    // 切换司机家庭地址显示
    const toggleDriverHomes = () => {
        showDriverHomes.value = !showDriverHomes.value;
        
        if (map.value && map.value.getLayer('driver-homes-layer')) {
            const visibility = showDriverHomes.value ? 'visible' : 'none';
            map.value.setLayoutProperty('driver-homes-layer', 'visibility', visibility);
            console.log(`司机家庭地址图层可见性设置为: ${visibility}`);
        }
        
        if (showDriverHomes.value) {
            updateDriverHomeMarkers();
        }
    };
    
    return {
        showDriverHomes,
        addDriverHomesLayer,
        updateDriverHomeMarkers,
        toggleDriverHomes
    };
}
