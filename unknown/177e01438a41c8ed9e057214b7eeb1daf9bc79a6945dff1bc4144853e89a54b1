<template>
    <div class="marker-popup" :class="{ 'loading': !isReady }">
        <div v-if="isReady">
            <!-- 取件订单 -->
            <div v-if="pickupOrders.length > 0">
                <div class="order-type-title">取件订单 ({{ pickupOrders.length }})</div>
                <div v-for="order in pickupOrders" :key="order.id" class="order-item" :class="{ 'selected': isOrderSelected(order.id) }">
                    <div class="order-header">
                        <a 
                            href="javascript:void(0)" 
                            class="order-no order-link"
                            :title="'点击查看订单详情: ' + (order.no || '--')"
                            @click="openOrderDetail(order.id, order.no)"
                        >
                            <span class="link-icon">🔍</span>
                            {{ order.no || '--' }}
                        </a>
                        <span v-if="order.driver_id" class="order-stop-no">{{ order.stop_no || '' }}</span>
                    </div>
                    <div class="order-driver">
                        <template v-if="order.driver_id">
                            <span class="driver-label">司机:</span>
                            <span class="driver-name" :style="{ color: getDriverColor(order.driver_id) }">{{ getDriverName(order.driver_id) }}</span>
                        </template>
                        <template v-else>
                            <span class="driver-label">状态:</span>
                            <span class="driver-unassigned">未分配</span>
                        </template>
                    </div>
                    <div v-if="order.address" class="order-address">
                        {{ order.address || '--' }}
                    </div>
                    <div class="order-contact">客户: {{ order.name || '--' }}</div>
                    <div v-if="order.counterpart?.name" class="order-contact">收件: {{ order.counterpart?.name }}</div>
                    <div v-if="order.status" class="order-status">
                        <span class="status-label">状态:</span>
                        <span class="status-value">{{ getStatusText(order.status) }}</span>
                    </div>
                </div>
            </div>
            
            <!-- 派送订单 -->
            <div v-if="deliveryOrders.length > 0">
                <div class="order-type-title">派送订单 ({{ deliveryOrders.length }})</div>
                <div v-for="order in deliveryOrders" :key="order.id" class="order-item" :class="{ 'selected': isOrderSelected(order.id) }">
                    <div class="order-header">
                        <a 
                            href="javascript:void(0)" 
                            class="order-no order-link"
                            :title="'点击查看订单详情: ' + (order.no || '--')"
                            @click="openOrderDetail(order.id, order.no)"
                        >
                            <span class="link-icon">🔍</span>
                            {{ order.no || '--' }}
                        </a>
                        <span v-if="order.driver_id" class="order-stop-no">{{ order.stop_no || '' }}</span>
                    </div>
                    <div class="order-driver">
                        <template v-if="order.driver_id">
                            <span class="driver-label">司机:</span>
                            <span class="driver-name" :style="{ color: getDriverColor(order.driver_id) }">{{ getDriverName(order.driver_id) }}</span>
                        </template>
                        <template v-else>
                            <span class="driver-label">状态:</span>
                            <span class="driver-unassigned">未分配</span>
                        </template>
                    </div>
                    <div v-if="order.address" class="order-address">
                        {{ order.address || '--' }}
                    </div>
                    <div class="order-contact">客户: {{ order.name || '--' }}</div>
                    <div v-if="order.counterpart?.name" class="order-contact">发件: {{ order.counterpart?.name }}</div>
                    <div v-if="order.status" class="order-status">
                        <span class="status-label">状态:</span>
                        <span class="status-value">{{ getStatusText(order.status) }}</span>
                    </div>
                </div>
            </div>

            <!-- 无订单信息时显示 -->
            <div v-if="allOrders.length === 0" class="no-orders">
                该位置暂无订单信息
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, inject, computed } from 'vue'
import { useOrderStore } from '../../stores/order'
import { useDriverStore } from '../../stores/driver'
import { useRouteStore } from '../../stores/route'
import { ElMessage } from 'element-plus'

const props = defineProps({
    point: {
        type: Object,
        required: true
    }
})

const isReady = ref(false)
const orderStore = useOrderStore()
const driverStore = useDriverStore()
const routeStore = useRouteStore()

// 开发模式标识，用于显示调试信息
const isDevMode = process.env.NODE_ENV === 'development'

// 获取所有订单并按类型分类
const allOrders = computed(() => {
    // 检查point.orders是否存在
    if (!props.point || !props.point.orders) {
        console.error('MarkerPopup: point.orders不存在', props.point)
        return []
    }
    
    // 收集所有订单到一个数组
    let orders = []
    
    // 从point.orders对象中获取所有订单
    Object.keys(props.point.orders).forEach(key => {
        if (Array.isArray(props.point.orders[key])) {
            orders = orders.concat(props.point.orders[key])
        }
    })
    
    // 调试日志
    if (isDevMode && orders.length > 0) {
        console.log('MarkerPopup所有订单:', orders)
    }
    
    return orders
})

// 取件订单
const pickupOrders = computed(() => {
    return allOrders.value.filter(order => {
        return order.type && order.type.toUpperCase() === 'PICKUP'
    })
})

// 派送订单
const deliveryOrders = computed(() => {
    return allOrders.value.filter(order => {
        return !order.type || order.type.toUpperCase() !== 'PICKUP'
    })
})

// 尝试找到注入的orderDetailModal
const orderDetailModal = inject('orderDetailModal', null)

onMounted(() => {
    // 输出调试信息
    console.log('MarkerPopup mounted, orderDetailModal:', orderDetailModal)
    
    // 使用 nextTick 确保 DOM 已更新
    setTimeout(() => {
        isReady.value = true
        
        // 调试日志
        if (isDevMode) {
            console.log('MarkerPopup分类结果 - 取件订单:', pickupOrders.value)
            console.log('MarkerPopup分类结果 - 派送订单:', deliveryOrders.value)
        }
    }, 100)
})

// 添加订单状态文本转换函数
const getStatusText = status => {
    if (!status) return '未知'
    
    const statusMap = {
        'processing': '处理中',
        'sorting': '分拣中',
        'pickedUp': '已取件',
        'transporting': '运输中',
        'delivered': '已送达'
    }
    
    return statusMap[status] || status
}

// 打开订单详情模态弹窗
const openOrderDetail = (orderId, orderNo) => {
    // 阻止事件冒泡
    event.stopPropagation()
    
    // 记录日志
    console.log('尝试打开订单详情:', orderId, orderNo)
    
    // 使用全局订单详情模态弹窗
    if (orderDetailModal) {
        console.log('找到orderDetailModal，调用open方法')
        orderDetailModal.open(orderId, orderNo || '未知订单号')
    } else {
        console.log('全局订单详情模态弹窗未找到，使用备选方法')
        // 如果没有找到注入的orderDetailModal，使用备选方案打开订单详情
        openOrderDetailFallback(orderId, orderNo)
    }
}

// 备选方案打开订单详情
const openOrderDetailFallback = (orderId, orderNo) => {
    // 生成URL
    const baseUrl = window.location.origin
    const orderUrl = `${baseUrl}/delivery/orders/detail?id=${orderId}`

    // 创建临时模态框
    createTempModal(orderId, orderNo, orderUrl)
}

// 创建临时模态框
const createTempModal = (orderId, orderNo, url) => {
    console.log('创建临时订单详情模态框')
    
    // 检查是否已存在临时模态框，如果存在则先移除
    const existingModal = document.getElementById('temp-order-modal')
    if (existingModal) {
        document.body.removeChild(existingModal)
    }
    
    // 创建模态框容器
    const modalContainer = document.createElement('div')
    modalContainer.id = 'temp-order-modal'
    modalContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 99999;
        display: flex;
        justify-content: center;
        align-items: center;
    `
    
    // 创建模态框内容
    const modalContent = document.createElement('div')
    modalContent.style.cssText = `
        position: relative;
        width: 90vw;
        height: 90vh;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        animation: tempModalAppear 0.3s ease-out;
    `
    
    // 添加动画样式
    const styleElement = document.createElement('style')
    styleElement.textContent = `
        @keyframes tempModalAppear {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `
    document.head.appendChild(styleElement)
    
    // 创建模态框头部
    const modalHeader = document.createElement('div')
    modalHeader.style.cssText = `
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dfe4e8;
    `
    
    // 添加标题
    const modalTitle = document.createElement('h2')
    modalTitle.textContent = `订单详情: ${orderNo || '加载中...'}`
    modalTitle.style.cssText = `
        margin: 0;
        font-size: 18px;
        color: #333;
    `
    
    // 添加关闭按钮
    const closeButton = document.createElement('button')
    closeButton.innerHTML = '&times;'
    closeButton.style.cssText = `
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        transition: all 0.2s;
    `
    closeButton.onclick = () => {
        document.body.removeChild(modalContainer)
        document.head.removeChild(styleElement)
        document.body.style.overflow = 'auto'
    }
    
    // 创建内容区域
    const modalBody = document.createElement('div')
    modalBody.style.cssText = `
        flex: 1;
        overflow: hidden;
        position: relative;
    `
    
    // 创建iframe
    const iframe = document.createElement('iframe')
    iframe.src = url
    iframe.style.cssText = `
        width: 100%;
        height: 100%;
        border: none;
    `
    
    // 创建底部
    const modalFooter = document.createElement('div')
    modalFooter.style.cssText = `
        padding: 12px 20px;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid #dfe4e8;
    `
    
    // 添加关闭按钮
    const closeButtonLarge = document.createElement('button')
    closeButtonLarge.textContent = '关闭'
    closeButtonLarge.style.cssText = `
        padding: 8px 20px;
        background-color: #1a73e8;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
    `
    closeButtonLarge.onclick = () => {
        document.body.removeChild(modalContainer)
        document.head.removeChild(styleElement)
        document.body.style.overflow = 'auto'
    }
    
    // 组装模态框
    modalHeader.appendChild(modalTitle)
    modalHeader.appendChild(closeButton)
    modalBody.appendChild(iframe)
    modalFooter.appendChild(closeButtonLarge)
    
    modalContent.appendChild(modalHeader)
    modalContent.appendChild(modalBody)
    modalContent.appendChild(modalFooter)
    
    modalContainer.appendChild(modalContent)
    
    // 添加到body
    document.body.appendChild(modalContainer)
    
    // 阻止背景滚动
    document.body.style.overflow = 'hidden'
    
    // 添加ESC键监听
    const handleEscKeyDown = event => {
        if (event.key === 'Escape') {
            document.body.removeChild(modalContainer)
            document.head.removeChild(styleElement)
            document.body.style.overflow = 'auto'
            document.removeEventListener('keydown', handleEscKeyDown)
        }
    }
    document.addEventListener('keydown', handleEscKeyDown)
    
    // 添加点击背景关闭功能
    modalContainer.addEventListener('click', (e) => {
        if (e.target === modalContainer) {
            document.body.removeChild(modalContainer)
            document.head.removeChild(styleElement)
            document.body.style.overflow = 'auto'
            document.removeEventListener('keydown', handleEscKeyDown)
        }
    })
}

// 添加检查订单是否被选中的方法
const isOrderSelected = orderId => {
    return orderStore.selectedOrderIds.has(orderId)
}

// 获取司机姓名
const getDriverName = driverId => {
    const driver = driverStore.getDriverById(driverId)
    return driver ? driver.name : '未知司机'
}

// 获取司机颜色
const getDriverColor = driverId => {
    const driver = driverStore.getDriverById(driverId)
    return driver ? driver.color : '#9E9E9E'
}

// 获取路线名称
const getRouteName = routeId => {
    const routes = routeStore.routes || []
    const route = routes.find(r => r.id === routeId)
    return route ? formatRouteNumber(route.name) : '未知路线'
}

// 格式化路线编号
const formatRouteNumber = num => {
    return String(num || '').padStart(5, '0')
}
</script>

<style scoped>
.marker-popup {
    padding: 8px;
    min-width: 180px;
    max-width: 250px;
    max-height: 300px; /* 增大高度以容纳更多内容 */
    background-color: white;
    z-index: 2000;
    opacity: 1 !important;
    visibility: visible !important;
    overflow-y: auto;
}

.marker-popup.loading {
    opacity: 0 !important;
    visibility: hidden !important;
}

.order-type-title {
    font-weight: bold;
    margin: 4px 0;
    color: #1a73e8;
    font-size: 13px;
    border-bottom: 1px solid #eee;
    padding-bottom: 2px;
}

.order-item {
    margin-bottom: 8px;
    padding: 5px;
    border-radius: 4px;
    background-color: #f5f5f5;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
    font-size: 12px;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
}

.order-no {
    font-weight: bold;
    color: #333;
}

.order-link {
    text-decoration: none;
    cursor: pointer;
    color: #1a73e8;
    transition: color 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 3px;
    padding: 2px 0;
}

.link-icon {
    font-size: 10px;
    display: inline-block;
    line-height: 1;
}

.order-link::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: 0;
    left: 0;
    background-color: #1a73e8;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.order-link:hover {
    color: #0d47a1;
}

.order-link:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.order-stop-no {
    background-color: #1a73e8;
    color: white;
    border-radius: 3px;
    padding: 1px 4px;
    font-size: 10px;
    font-weight: bold;
}

/* 司机信息样式 */
.order-driver {
    display: flex;
    align-items: center;
    margin: 4px 0;
    font-size: 12px;
    position: relative;
    padding-left: 16px; /* 为图标留出空间 */
}

.order-driver::before {
    content: "🧑‍✈️"; /* 司机emoji图标 */
    position: absolute;
    left: 0;
    font-size: 10px;
}

.driver-label {
    margin-right: 4px;
    color: #555;
}

.driver-name {
    font-weight: 500;
}

.driver-unassigned {
    color: #9E9E9E;
    font-style: italic;
    background-color: #f0f0f0;
    padding: 1px 4px;
    border-radius: 3px;
}

/* 修改：将 .order-route 改为 .order-address 并更新图标 */
.order-address {
    margin: 4px 0;
    font-size: 12px;
    color: #555;
    position: relative;
    padding-left: 16px; /* 为图标留出空间 */
    white-space: normal; /* 允许地址换行 */
    word-break: break-word; /* 允许在单词内部换行 */
}

.order-address::before {
    content: "📍"; /* 地址emoji图标 */
    position: absolute;
    left: 0;
    top: 1px; /* 微调图标位置 */
    font-size: 10px;
}

.order-contact {
    font-size: 12px;
    color: #555;
    margin: 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 添加选中状态的样式 */
.order-item.selected {
    background-color: #e8f0fe;
    border-left: 3px solid #1a73e8;
}

/* 添加订单状态样式 */
.order-status {
    font-size: 12px;
    margin: 4px 0;
    display: flex;
    align-items: center;
}

.status-label {
    margin-right: 4px;
    color: #555;
}

.status-value {
    font-weight: 500;
    padding: 0 4px;
    border-radius: 3px;
    background-color: #f0f0f0;
}

/* 调试信息样式 */
.debug-info {
    margin-bottom: 8px;
    padding: 5px;
    background-color: #333;
    color: #fff;
    font-size: 10px;
    border-radius: 3px;
}

/* 无订单样式 */
.no-orders {
    padding: 10px;
    text-align: center;
    color: #999;
    font-style: italic;
    font-size: 12px;
}

/* 滚动条样式 */
.marker-popup::-webkit-scrollbar {
    width: 4px;
}

.marker-popup::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.marker-popup::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}

.marker-popup::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}
</style>

<style>
/* 全局弹出框样式 */

.maplibregl-popup {
    z-index: 2000 !important;
    transition: opacity 0.2s ease-out !important;
}

.maplibregl-popup-content {
    background: white !important;
    padding: 12px !important;
    border-radius: 8px !important;
    box-shadow: 0 3px 14px rgb(0 0 0 / 20%) !important;
    overflow-y: auto !important;
}

/* 美化滚动条样式 */

.maplibregl-popup-content::-webkit-scrollbar {
    width: 6px;
}

.maplibregl-popup-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.maplibregl-popup-content::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.maplibregl-popup-content::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 箭头样式 */

.maplibregl-popup-anchor-top .maplibregl-popup-tip {
    border-bottom-color: white !important;
}

.maplibregl-popup-anchor-bottom .maplibregl-popup-tip {
    border-top-color: white !important;
}

.maplibregl-popup-anchor-left .maplibregl-popup-tip {
    border-right-color: white !important;
}

.maplibregl-popup-anchor-right .maplibregl-popup-tip {
    border-left-color: white !important;
}

/* 添加到全局样式中 */

.hidden-popup {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    transition: opacity 0.2s ease-out, visibility 0.2s ease-out !important;
}

/* 添加显示动画类 */

.maplibregl-popup:not(.hidden-popup) {
    opacity: 1 !important;
    visibility: visible !important;
    transition: opacity 0.2s ease-out, visibility 0s !important;
}
</style> 