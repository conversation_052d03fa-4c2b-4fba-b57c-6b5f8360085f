import { ref, onMounted, onUnmounted } from 'vue';

export function usePerformanceMonitor() {
    // 性能监控数据
    const performanceStats = ref({
        fps: 0,
        markerCount: 0,
        renderTime: 0,
        processingTime: 0,
        totalPoints: 0,
        visibleCount: 0
    });
    
    // FPS计算相关变量
    let frameCount = 0;
    let lastFpsUpdateTime = 0;
    let fpsUpdateInterval = 1000; // 每秒更新一次FPS
    let animationFrameId = null;
    
    // 开始性能监控
    const startPerformanceMonitoring = () => {
        lastFpsUpdateTime = performance.now();
        frameCount = 0;
        
        const updateFps = () => {
            frameCount++;
            
            const now = performance.now();
            const elapsed = now - lastFpsUpdateTime;
            
            if (elapsed >= fpsUpdateInterval) {
                // 计算FPS
                performanceStats.value.fps = Math.round((frameCount * 1000) / elapsed);
                
                // 重置计数器
                frameCount = 0;
                lastFpsUpdateTime = now;
            }
            
            // 继续监控
            animationFrameId = requestAnimationFrame(updateFps);
        };
        
        // 开始监控
        animationFrameId = requestAnimationFrame(updateFps);
    };
    
    // 停止性能监控
    const stopPerformanceMonitoring = () => {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }
    };
    
    // 更新性能统计信息
    const updatePerformanceStats = (stats) => {
        if (!stats) return;
        
        // 更新渲染时间
        if (stats.renderTime !== undefined) {
            performanceStats.value.renderTime = stats.renderTime;
        }
        
        // 更新标记数量
        if (stats.markerCount !== undefined) {
            performanceStats.value.markerCount = stats.markerCount;
        }
        
        // 更新处理时间
        if (stats.processingTime !== undefined) {
            performanceStats.value.processingTime = stats.processingTime;
        }
        
        // 更新点位总数
        if (stats.totalPoints !== undefined) {
            performanceStats.value.totalPoints = stats.totalPoints;
        }
        
        // 更新可见点位数
        if (stats.visibleCount !== undefined) {
            performanceStats.value.visibleCount = stats.visibleCount;
        }
    };
    
    // 组件挂载时自动开始监控
    onMounted(() => {
        startPerformanceMonitoring();
    });
    
    // 组件卸载时停止监控
    onUnmounted(() => {
        stopPerformanceMonitoring();
    });
    
    return {
        performanceStats,
        updatePerformanceStats
    };
} 