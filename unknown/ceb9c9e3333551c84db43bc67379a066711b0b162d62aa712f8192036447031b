// src/modules/fleetdispatch/components/MapView/composables/useMapInitialization.js
import { ref } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { MAPTILER_API_KEY } from '../../../config/keys'; // 确保路径正确
import { useMapStore } from '../../../stores/map'; // 确保路径正确

export function useMapInitialization(mapContainerRef) {
    const map = ref(null);
    const isMapLoaded = ref(false);
    const mapStore = useMapStore();

    // 初始化地图
    const initializeMap = (onLoadCallback) => {
        console.log('开始初始化地图')
      
        map.value = new maplibregl.Map({
            container: mapContainerRef.value,
            style: `https://api.maptiler.com/maps/streets/style.json?key=${MAPTILER_API_KEY}`,
            center: [-79.346574, 43.818219], // 多伦多北约克的坐标
            zoom: 11,
            attributionControl: false,
            maxZoom: 22,
            minZoom: 3,
            dragRotate: false,
            pitchWithRotate: false,
            touchZoomRotate: true,
            trackResize: true,
            boxZoom: false, // 禁用默认的框选功能
            logoPosition: 'bottom-right',
            failIfMajorPerformanceCaveat: true,
            preserveDrawingBuffer: false,
            refreshExpiredTiles: false,
            fadeDuration: 0
        })

        // 监听样式加载完成事件
        map.value.on('style.load', () => {
            console.log('地图样式加载完成')
        
            // 检查并确保图层可见性
            if (map.value.getLayer('unassigned-orders-layer') && 
                map.value.getLayer('assigned-orders-layer') && 
                map.value.getLayer('selected-orders-layer')) {
          
                map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
                map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
                map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')
          
                // 确保标记图层在最上层
                moveSymbolLayersToTop()
            }
        })

        map.value.on('load', () => {
            console.log('地图加载完成')
            addMapControls()
            isMapLoaded.value = true
            mapStore.setMapReady(true)
            
            // 添加标记图层应该在地图加载完成后，即使没有提供回调
            moveSymbolLayersToTop()
            
            // 调用 MapView.vue 传入的回调函数
            if (onLoadCallback && typeof onLoadCallback === 'function') {
                onLoadCallback()
            }
        
            // 添加地图渲染完成后的标记可见性检查
            setTimeout(() => {
                if (map.value) {
                    // 确保所有图层可见
                    if (map.value.getLayer('unassigned-orders-layer')) {
                        map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
                    }
                    if (map.value.getLayer('assigned-orders-layer')) {
                        map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
                    }
                    if (map.value.getLayer('selected-orders-layer')) {
                        map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')
                    }
                }
            }, 500)
        })
        
        // 添加错误处理
        map.value.on('error', (e) => {
            console.error('MapLibre GL JS Error:', e)
        })
    }

    // 添加地图控件
    const addMapControls = () => {
        if (!map.value) return
        
        // 添加全屏控件
        map.value.addControl(new maplibregl.FullscreenControl(), 'top-right')
        
        // 添加缩放控件
        map.value.addControl(new maplibregl.NavigationControl(), 'top-right')
        
        // 添加定位控件
        map.value.addControl(
            new maplibregl.GeolocateControl({
                positionOptions: {
                    enableHighAccuracy: true
                },
                trackUserLocation: true
            }),
            'top-right'
        )
    }
    
    // 确保标记图层在最上层显示
    const moveSymbolLayersToTop = () => {
        if (!map.value) return
        
        try {
            // 获取所有图层
            const layers = map.value.getStyle().layers
            if (!layers) return
            
            // 找出所有需要移动到顶部的图层ID
            const symbolLayerIds = []
            for (const layer of layers) {
                if (layer.type === 'symbol' || 
                    layer.id === 'unassigned-orders-layer' ||
                    layer.id === 'assigned-orders-layer' ||
                    layer.id === 'selected-orders-layer') {
                    symbolLayerIds.push(layer.id)
                }
            }
            
            // 将这些图层移到最上层
            for (const layerId of symbolLayerIds) {
                map.value.moveLayer(layerId)
            }
            
            console.log('已将标记图层移至顶部')
        } catch (error) {
            console.error('移动图层到顶部时出错:', error)
        }
    }

    // 销毁地图
    const removeMap = () => {
        if (map.value) {
            console.log('移除地图实例')
            map.value.remove()
            map.value = null
            isMapLoaded.value = false
            mapStore.setMapReady(false)
        }
    }

    return {
        map,
        isMapLoaded,
        initializeMap,
        removeMap,
        moveSymbolLayersToTop
    }
}