import { ref, watch, onMounted } from 'vue';
import { useOrderStore } from '../../../stores/order';
import { useGeocodingService } from '../../../services/GeocodingService';
import { eventBus, EVENT_TYPES } from '../../../utils/eventBus';
import { debounce } from 'lodash-es';

/**
 * 地理编码集成组合式函数
 * @param {Object} options 配置选项
 * @param {Ref} options.map 地图实例
 * @param {Function} options.updateMarkers 更新标记的函数
 * @returns {Object} 地理编码相关的方法和状态
 */
export function useGeocoding({ map, updateMarkers }) {
    const orderStore = useOrderStore();
    const geocodingService = useGeocodingService();
    
    // 状态
    const isProcessingCoordinates = ref(false);
    const processingProgress = ref(0);
    const processingStats = ref({
        total: 0,
        processed: 0,
        successful: 0,
        failed: 0,
        cached: 0
    });
    
    // 跟踪已经处理过的订单ID，避免重复处理
    const processedOrderIds = new Set();
    
    // 使用防抖处理的更新标记函数，减少频繁更新
    const debouncedUpdateMarkers = debounce((orders) => {
        if (updateMarkers && typeof updateMarkers === 'function') {
            console.log(`[防抖] 更新 ${orders.length} 个标记到地图`);
            updateMarkers(orders);
        }
    }, 300); // 300ms防抖时间
    
    /**
     * 处理没有坐标的订单
     * @param {Array} orders 订单列表
     * @returns {Promise<Array>} 有坐标的订单
     */
    const processOrdersWithoutCoordinates = async (orders) => {
        if (!Array.isArray(orders) || orders.length === 0) return [];
        
        // 过滤掉已处理过的订单和已有坐标的订单
        const ordersNeedingCoords = orders.filter(order => 
            order && order.id && 
            !processedOrderIds.has(order.id) && 
            (!order.lng_lat || !Array.isArray(order.lng_lat) || order.lng_lat.length !== 2)
        );
        
        if (ordersNeedingCoords.length === 0) {
            console.log('没有新的订单需要地理编码');
            return [];
        }
        
        // 记录这些订单ID为已处理，避免重复请求
        ordersNeedingCoords.forEach(order => {
            if (order && order.id) {
                processedOrderIds.add(order.id);
            }
        });
        
        console.log(`发现 ${ordersNeedingCoords.length} 个新订单缺少坐标，开始地理编码...`);
        
        // 防止同时多次处理
        if (isProcessingCoordinates.value) {
            console.log('已有地理编码处理正在进行，等待其完成...');
            return [];
        }
        
        // 更新状态
        isProcessingCoordinates.value = true;
        processingStats.value = {
            total: ordersNeedingCoords.length,
            processed: 0,
            successful: 0,
            failed: 0,
            cached: 0
        };
        processingProgress.value = 0;
        
        try {
            // 调用地理编码服务处理订单
            const results = await geocodingService.batchGeocodeOrders(ordersNeedingCoords);
            
            if (results.length > 0) {
                console.log(`地理编码完成，成功获取 ${results.length} 个坐标`);
                
                // 过滤出有效的坐标结果
                const validResults = results.filter(result => 
                    result && result.id && result.coordinates && 
                    Array.isArray(result.coordinates) && 
                    result.coordinates.length === 2
                );
                
                // 更新处理统计
                processingStats.value.successful = validResults.length;
                processingStats.value.failed = ordersNeedingCoords.length - validResults.length;
                processingStats.value.cached = validResults.filter(r => r.cached).length;
                
                if (validResults.length > 0) {
                    // 获取OrderService实例
                    const orderService = orderStore.orderService;
                    
                    // 批量更新订单坐标，一次性更新所有坐标，避免频繁触发事件
                    if (orderService && typeof orderService.batchUpdateOrderCoordinates === 'function') {
                        orderService.batchUpdateOrderCoordinates(validResults);
                        console.log(`已批量更新 ${validResults.length} 个订单的坐标`);
                    } else {
                        console.error('OrderService中缺少batchUpdateOrderCoordinates方法');
                    }
                    
                    // 返回更新后的订单结果
                    return validResults;
                }
            }
            
            return [];
        } catch (error) {
            console.error('地理编码处理失败:', error);
            return [];
        } finally {
            isProcessingCoordinates.value = false;
            processingProgress.value = 100;
        }
    };
    
    /**
     * 更新地图显示
     * 先显示有坐标的订单，然后处理没有坐标的订单
     */
    const updateMapWithValidCoordinates = async (orders) => {
        if (!Array.isArray(orders) || orders.length === 0) return;
        
        // 首先筛选出有有效坐标的订单
        const ordersWithCoords = orders.filter(order => 
            order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2
        );
        
        // 更新地图显示有坐标的订单
        if (ordersWithCoords.length > 0) {
            console.log(`先更新 ${ordersWithCoords.length}/${orders.length} 个有坐标的订单到地图`);
            debouncedUpdateMarkers(ordersWithCoords);
        }
        
        // 如果有订单缺少坐标，尝试获取坐标
        if (ordersWithCoords.length < orders.length) {
            // 在后台处理地理编码
            const updatedResults = await processOrdersWithoutCoordinates(orders);
            
            if (updatedResults.length > 0) {
                console.log(`地理编码完成后，更新所有订单的地图显示`);
                
                // 合并更新后的订单坐标
                const allOrdersToUpdate = [...ordersWithCoords];
                
                // 查找并更新新获取坐标的订单
                updatedResults.forEach(result => {
                    const orderIndex = orders.findIndex(o => o.id === result.id);
                    if (orderIndex >= 0) {
                        // 已经有坐标的订单
                        allOrdersToUpdate.push(orders[orderIndex]);
                    }
                });
                
                // 一次性更新所有订单标记，使用防抖减少频繁更新
                debouncedUpdateMarkers(allOrdersToUpdate);
            }
        }
    };
    
    // 监听订单更新事件，使用防抖减少频繁更新
    const handleOrderCoordinatesUpdated = debounce(({ orderId, coordinates }) => {
        // 避免触发太多次地图更新，只在关键时刻更新
        if (updateMarkers && typeof updateMarkers === 'function' && map.value) {
            // 找到特定订单并更新
            const orderToUpdate = orderStore.allOrders.find(o => o.id === orderId);
            if (orderToUpdate) {
                const ordersToUpdate = orderStore.allOrders.filter(o => 
                    o.lng_lat && Array.isArray(o.lng_lat) && o.lng_lat.length === 2
                );
                console.log(`[防抖] 坐标更新事件，更新 ${ordersToUpdate.length} 个订单标记`);
                debouncedUpdateMarkers(ordersToUpdate);
            }
        }
    }, 500); // 500ms防抖时间
    
    // 监听批量订单坐标更新事件
    const handleOrdersCoordinatesUpdated = debounce((data) => {
        if (data && data.isCoordinatesUpdate && 
            updateMarkers && typeof updateMarkers === 'function' && 
            map.value) {
            // 获取所有有坐标的订单
            const ordersToUpdate = orderStore.allOrders.filter(o => 
                o.lng_lat && Array.isArray(o.lng_lat) && o.lng_lat.length === 2
            );
            console.log(`[防抖] 批量坐标更新事件，更新 ${ordersToUpdate.length} 个订单标记`);
            debouncedUpdateMarkers(ordersToUpdate);
        }
    }, 500); // 500ms防抖时间
    
    // 在组件挂载时设置事件监听
    onMounted(() => {
        // 监听订单坐标更新事件
        eventBus.on(EVENT_TYPES.ORDER_COORDINATES_UPDATED, handleOrderCoordinatesUpdated);
        
        // 监听订单更新事件
        eventBus.on(EVENT_TYPES.ORDERS_UPDATED, handleOrdersCoordinatesUpdated);
    });
    
    return {
        isProcessingCoordinates,
        processingProgress,
        processingStats,
        processOrdersWithoutCoordinates,
        updateMapWithValidCoordinates,
        geocodingCacheSize: geocodingService.cacheSize
    };
} 