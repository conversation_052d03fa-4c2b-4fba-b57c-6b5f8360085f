<script setup>
import api from '@/api'
import { addEmailSettings, getEmailSettings, updateEmailSettings } from '@/api/modules/messenger'
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        host: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        port: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        user: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        password: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getEmailSettings({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addEmailSettings(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateEmailSettings(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.email.fields.host')" prop="host">
                <el-input v-model="data.form.host" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.email.fields.port')" prop="port">
                <el-input v-model="data.form.port" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.email.fields.useTLS')" prop="tls">
                <el-switch v-model="data.form.use_tls" />
            </el-form-item>
            <el-form-item :label="$t('settings.email.fields.user')" prop="user">
                <el-input v-model="data.form.user" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.email.fields.password')" prop="password">
                <el-input v-model="data.form.password" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('settings.email.fields.fromEmail')" prop="from_email">
                <el-input v-model="data.form.from_email" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
