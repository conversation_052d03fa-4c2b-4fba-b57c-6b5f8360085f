<script setup>
    import UserSelector from '@/views/components/UserSelector/index.vue'
    import { usePagination } from '@/utils/composables'
    import {
        getOpenDeliveryBills,
        getUnbilledOpenDeliveryOrders,
        addOpenDeliveryBill,
        getOpenUserSimpleList,
        getOpenDeliveryOrders,
        sendOpenDeliveryBill,
        getPaymentMethods,
        updateOpenDeliveryBillPayment,
        deleteOpenDeliveryBill,
        downloadOpenDeliveryBill
    } from '@/api/modules/open'
    import { currencyFormatter, orderNoFormatter } from '@/utils/formatter'
    import { orderStatusStyles } from '@/utils/constants'
    import OrderAddressDialog from '@/views/components/OrderAddressDialog/index.vue'
    import eventBus from '@/utils/eventBus'

    const router = useRouter()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            title: ''
        },
        rules: {
            user: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ]
        },
        orders: [],
        selectionOrders: []


    })

    const newBill = computed(() => data.value.form.id === '');
    const selectedUser = ref()
    const selectedDateRange = ref()
    const totalToBill = computed(() => data.value.selectionOrders.reduce((total, curr) => {

        return total + curr.total

    }, 0))

    onMounted(async () => {
        if (!newBill.value) {
            await getInfo();
            getOrders();
        }
    })

    async function getInfo() {
        data.value.loading = true
        getOpenDeliveryBills({ id: data.value.form.id }).then(res => {
            data.value.loading = false;
            data.value.form = res.data;
        })
    }
    function getOrders() {
        let search = { bill_id: data.value.form.id }
        let params = getParams(
            {
                filters: JSON.stringify(search)
            }
        )
        getOpenDeliveryOrders(params).then(res => {
            data.value.orders = res.data.order_list
            pagination.value.total = res.data.total
        })
    }

    const shortcuts = [
        {
            text: 'Last week',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                return [start, end]
            },
        },
        {
            text: 'Last month',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                return [start, end]
            },
        },
        {
            text: 'Last 3 months',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                return [start, end]
            },
        },]

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getOrders())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getOrders())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getOrders())
    }

    // Dialogs
    const chosenRow = ref({})
    const senderDialogVisible = ref(false)
    const receiverDialogVisible = ref(false)

    function showSender(row) {
        chosenRow.value = row
        senderDialogVisible.value = true
    }
    function showReceiver(row) {
        chosenRow.value = row
        receiverDialogVisible.value = true
    }


    function getUnBilledOrders() {
        if (!selectedUser.value) return;
        let params = {
            user_id: selectedUser.value?.id,
            created_range: JSON.stringify(selectedDateRange.value),
        }
        getUnbilledOpenDeliveryOrders(params).then(res => {
            data.value.orders = res.data;
        })
    }

    const dueDate = ref()
    const showWholeNo = ref(true)
    const showCustomerId = ref(false)


    function addBill() {
        if (data.value.selectionOrders.length == 0) return;
        if (!dueDate.value) return;
        let params = {
            userId: selectedUser.value?.id,
            ids: data.value.selectionOrders.map(e => e.id),
            dueDate: dueDate.value,
            showWholeNo: showWholeNo.value,
            showCustomerId: showCustomerId.value
        }
        addOpenDeliveryBill(params).then(res => {
            if (res.data.errCode == 365) {
                data.value.form.id = res.data.id;
                getInfo();
            }
        })
    }

    function onSend() {
        data.value.loading = true
        sendOpenDeliveryBill(data.value.form.id).then(res => {
            data.value.loading = false
        })
    }

    function onDel() {
        ElMessageBox.confirm(`确认删除「${data.value.form.no}」吗？`, '确认信息').then(() => {
            console.log('OOK')
            deleteOpenDeliveryBill({ id: data.value.form.id }).then((res) => {
                if (res.data.errCode == 365) {
                    eventBus.emit('get-data-list');
                    router.push({ name: 'billList' })
                }
            })
        }).catch(() => { })
    }

    function onDownload() {
        downloadOpenDeliveryBill(data.value.form.id).then(res => {
            if (res.data.errCode == 365) {
                window.open(res.data.url);
            }
        })
    }


    const notes = ref('')
    const selectedPaymentMethod = ref(null)
    const paidAmount = ref(0.0)
    const payDialogVisible = ref(false)
    const paidAt = ref(new Date())

    const paymentMethods = ref([])

    function showPayDialog() {
        getPaymentMethods().then(res => {
            paymentMethods.value = res.data;
            notes.value = data.value.form.notes;
            paidAmount.value = Number(((data.value.form.total - data.value.form.paid_amount) / 100).toFixed(2))
        })
        payDialogVisible.value = true;
    }
    function onConfirmPay() {
        let params = {
            id: data.value.form.id,
            payMethod: selectedPaymentMethod.value,
            paidAt: paidAt.value,
            paidAmount: Math.round(paidAmount.value * 100),
            notes: notes.value
        }
        updateOpenDeliveryBillPayment(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo();
            }
        })
        payDialogVisible.value = false;
    }
    function onCancelPay() {
        payDialogVisible.value = false;
    }

</script>

<template>
    <div v-loading="data.loading">
        <el-row>
            <el-col :lg="8" v-if="newBill">
                <page-main>
                    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                        <el-form-item prop="user" label="选择用户">
                            <UserSelector v-model="selectedUser" :get-data="getOpenUserSimpleList" />
                        </el-form-item>
                        <el-form-item prop="date" label="日期">
                            <el-date-picker v-model="selectedDateRange" type="daterange" unlink-panels
                                range-separator="To" start-placeholder="Start date" end-placeholder="End date"
                                :shortcuts="shortcuts" value-format="YYYY-MM-DD" :size="size" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="getUnBilledOrders">
                                {{ $t('operations.query') }}</el-button>
                        </el-form-item>
                    </el-form>
                </page-main>
                <page-main title="Bills selected">
                    <el-descriptions column="2">
                        <el-descriptions-item label="Total to bill" span="2">
                            {{ currencyFormatter(_, _, totalToBill) }}
                        </el-descriptions-item>
                        <el-descriptions-item label="Count to bill" span="2">
                            {{ data.selectionOrders.length }}
                        </el-descriptions-item>
                        <el-descriptions-item label="完整订单号">
                            <el-switch v-model="showWholeNo" />
                        </el-descriptions-item>
                        <el-descriptions-item label="完整自定编号">
                            <el-switch v-model="showCustomerId" />
                        </el-descriptions-item>

                        <el-descriptions-item label="Due Date" span="2">
                            <el-date-picker v-model="dueDate" type="date" placeholder="Pick a day" :size="size"
                                value-format="YYYY-MM-DD" />
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-button type="primary" @click="addBill">{{ $t('operations.add') }}</el-button>
                </page-main>
            </el-col>
            <el-col :lg="8" v-else>
                <page-main title="Bill Detail">
                    <template #extra>
                        <el-button type="danger" text v-if="!data.form.is_paid" @click="onDel">{{
                            $t('operations.delete')
                        }}</el-button>
                    </template>
                    <el-descriptions column="1">
                        <el-descriptions-item :label="$t('fields.no')">
                            {{ data.form.no }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('fields.createdAt')">
                            {{ data.form.created_at }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.invoice.fields.period')">
                            {{ data.form.period_start }} ~ {{ data.form.period_end }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('fields.subtotal')">
                            {{ currencyFormatter(_, _, data.form.subtotal) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('fields.tax')">
                            {{ currencyFormatter(_, _, data.form.tax) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('fields.total')">
                            {{ currencyFormatter(_, _, data.form.total) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.bill.fields.dueDate')">
                            {{ data.form.due_date }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.invoice.fields.paidAmount')">
                            {{ currencyFormatter(_, _, data.form.paid_amount) }}
                        </el-descriptions-item>

                        <template v-if="data.form.is_paid">
                            <el-descriptions-item :label="$t('finance.bill.fields.paidAt')">
                                {{ data.form.paid_at }}
                            </el-descriptions-item>
                            <el-descriptions-item :label="$t('finance.bill.fields.paymentMethod')">
                                {{ data.form.pay_method }}
                            </el-descriptions-item>
                            <el-descriptions-item :label="$t('fields.notes')">
                                {{ data.form.notes }}
                            </el-descriptions-item>
                        </template>
                    </el-descriptions>
                    <el-button type="primary" v-if="!data.form.is_fully_paid" @click="showPayDialog">{{
                        $t('operations.pay')
                    }}</el-button>
                    <el-button type="success" @click="onSend">{{ $t('operations.send') }}</el-button>
                    <el-button type="primary" plain @click="onDownload">{{ $t('operations.download') }}</el-button>
                </page-main>
            </el-col>
            <el-col :lg="16">
                <page-main>
                    <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.orders" stripe
                        highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                        @row-dblclick="onEdit" @sort-change="sortChange"
                        @selection-change="data.selectionOrders = $event">
                        <el-table-column v-if="newBill" type="selection" align="center" fixed />
                        <el-table-column v-else type="index" align="center" fixed />
                        <el-table-column prop="no" :label="$t('delivery.orders.fields.no')" width="140">

                            <template #default="scope">
                                <el-space>
                                    <div class="order-number">
                                        {{ orderNoFormatter(_, _, scope.row.no) }}
                                    </div>
                                </el-space>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sender_name" :label="$t('delivery.orders.fields.sender')" width="120"
                            show-overflow-tooltip>

                            <template #default="scope">
                                <el-link type="warning" @click.stop="showSender(scope.row)">
                                    {{ scope.row.sender_name }}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="receiver_name" :label="$t('delivery.orders.fields.receiver')"
                            show-overflow-tooltip>

                            <template #default="scope">
                                <el-link type="success" @click.stop="showReceiver(scope.row)">
                                    {{ scope.row.receiver_name }}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="status" :label="$t('delivery.orders.fields.status')" align="center"
                            width="110">

                            <template #default="scope">
                                <el-tag :type="orderStatusStyles[scope.row.status]" round size="small">
                                    {{ $t(`delivery.orders.selections.status.${scope.row.status}`) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="total" :label="$t('delivery.orders.fields.total')" align="right"
                            width="80" :sortable="newBill ? null : 'custom'" :formatter="currencyFormatter" />
                        <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160"
                            :sortable="newBill ? null : 'custom'" align="right" />
                        <!-- <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" /> -->
                    </el-table>
                    <el-pagination :current-page="pagination.page" :total="pagination.total"
                        :page-size="pagination.size" :page-sizes="pagination.sizes" :layout="pagination.layout"
                        :hide-on-single-page="false" class="pagination" background @size-change="sizeChange"
                        @current-change="currentChange" v-if="!newBill" />
                </page-main>
            </el-col>
        </el-row>
        <!-- Sender -->
        <OrderAddressDialog v-model="senderDialogVisible" :address-info="chosenRow" address-type="sender" />
        <!-- Receiver -->
        <OrderAddressDialog v-model="receiverDialogVisible" :address-info="chosenRow" address-type="receiver" />
        <!-- Pay -->
        <el-dialog v-model="payDialogVisible" :title="`Pay ${data.form.no}`" width="500">
            <el-form :model="form">
                <el-form-item :label="$t('finance.bill.fields.paidAt')" :label-width="100">
                    <el-date-picker v-model="paidAt" type="datetime" placeholder="Pick a day" :size="size"
                        value-format="YYYY-MM-DD HH:mm:ss" />
                </el-form-item>
                <el-form-item :label="$t('finance.invoice.fields.paidAmount')" :label-width="100">
                    <el-input-number v-model="paidAmount" :min="0.0"
                        :max="(data.form.total - data.form.paid_amount) / 100" :precision="2" :step="0.1" />
                </el-form-item>
                <el-form-item :label="$t('finance.bill.fields.paymentMethod')" label-width="100">
                    <el-select v-model="selectedPaymentMethod" placeholder="Please select a zone">
                        <el-option v-for="m of paymentMethods" :value="m.key" :key="m" :label="m.value" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('fields.notes')" :label-width="100">
                    <el-input v-model="notes" placeholder="Please input notes" maxlength="80" show-word-limit
                        type="textarea" />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelPay">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmPay">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
    // scss</style>
