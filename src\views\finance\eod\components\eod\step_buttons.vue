<script setup>
const props = defineProps({
    step: {
        type: Number,
        default: 0
    },
    noNext: {
        type: <PERSON>olean,
        default: false
    },
    noPrevious: {
        type: <PERSON>olean,
        default: false
    }
})

const emit = defineEmits(['setStep'])

function toNextStep() {
    emit('setStep', props.step + 1)
}
function toPreviousStep() {
    emit('setStep', props.step - 1)
}
</script>
<template>
    <div style="display: flex; flex-direction: row; justify-content: space-between; margin-top: 30px;"
        v-if="props.step > 0">
        <el-button v-if="!noPrevious" type="primary" plain @click="toPreviousStep">
            {{ $t('statistics.operations.previous') }}
        </el-button>
        <div v-else></div>
        <div>
            <slot />
        </div>
        <el-button v-if="!noNext" type="primary" plain @click="toNextStep">
            {{ $t('statistics.operations.next') }}
        </el-button>
        <div v-else></div>
    </div>
</template>