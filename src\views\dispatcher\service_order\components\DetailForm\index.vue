<script setup>
    import { getServiceOrder, createServiceOrders, updateServiceOrder } from '@/api/modules/dispatcher'
    import { searchDeliveryOrderByPartialNum } from '@/api/modules/delivery'
    import EmployeeSelector from '@/views/staffs/components/employee_selector.vue'
    import WorkShiftSelector from '@/views/dispatcher/work_shifts/components/shift_selector.vue'
    import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'

    import OrderStatusStyles from '../../status'

    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            address: {},
            shift: null,
            driver: null
        },
        rules: {
            location_name: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            phone_number: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
        }
    })


    const shortcuts = [
        {
            text: 'Today',
            value: new Date(),
        },
        {
            text: 'Yesterday',
            value: () => {
                const date = new Date()
                date.setTime(date.getTime() - 3600 * 1000 * 24)
                return date
            },
        },
        {
            text: 'A week ago',
            value: () => {
                const date = new Date()
                date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                return date
            },
        },
    ]

    const disabledDate = (time) => {
        return time.getTime() < Date.now() - 3600 * 1000 * 24
    }

    onMounted(() => {
        if (data.value.form.id != '') {
            getInfo()
        }
    })

    function getInfo() {
        data.value.loading = true
        getServiceOrder(data.value.form.id).then(res => {
            data.value.loading = false
            data.value.form = res.data
            deliveryOrderList.value = res.data.related_orders
        })
    }

    defineExpose({
        submit(callback) {
            let params = JSON.parse(JSON.stringify(data.value.form))
            params.address_db_id = data.value.form.address.id
            params.related_order_ids = data.value.form.related_orders?.map(e => e.id)

            if (data.value.form.id == '') {
                formRef.value.validate(valid => {
                    if (valid) {
                        createServiceOrders(params).then((res) => {
                            if (res.data.errCode == 365) {
                                callback && callback()
                            }
                        })
                    }
                })
            } else {
                formRef.value.validate(valid => {
                    if (valid) {
                        updateServiceOrder(data.value.form.id, params).then((res) => {
                            if (res.data.errCode == 365) {
                                callback && callback()
                            }
                        })
                    }
                })
            }
        }
    })


    function onClearAddress() {
        data.value.form.address.address = null
        data.value.form.address.city = null
        data.value.form.address.province = null
        data.value.form.address.postal_code = null
        data.value.form.address.id = null
    }

    function onRetrieveAddress(val) {
        data.value.form.address.address = val.Line1
        data.value.form.address.city = val.City
        data.value.form.address.province = val.ProvinceName
        data.value.form.address.postal_code = val.PostalCode
        data.value.form.address.id = val.Id
    }

    const deliveryOrderList = ref([])


    function getDeliveryOrders(num) {
        if ((num?.length ?? 0) >= 6)
            searchDeliveryOrderByPartialNum(num).then(res => {
                deliveryOrderList.value = res.data;
            })
    }


</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <template v-if="data.form.id.length > 0">
                <el-form-item :label="$t('fields.no')" prop="no">
                    <el-text tag="b">{{ data.form.no }}</el-text>
                </el-form-item>
                <el-form-item :label="$t('dispatcher.orders.fields.eta')" prop="no">
                    {{ data.form.eta }}
                </el-form-item>
                <el-form-item :label="$t('dispatcher.orders.fields.distance')" prop="no">
                    {{ data.form.distance }}
                </el-form-item>
                <el-form-item :label="$t('dispatcher.orders.fields.serviceTime')" prop="no">
                    {{ data.form.duration }}
                </el-form-item>
                <el-form-item :label="$t('fields.status')" prop="status">
                    <el-tag :type="OrderStatusStyles[data.form.status]" round>
                        {{ $t(`dispatcher.serviceOrders.selections.status.${data.form.status}`) }}
                    </el-tag>
                </el-form-item>
                <el-form-item :label="$t('dispatcher.orders.fields.uploadedAt')" prop="uploaded_at">
                    {{ data.form.uploaded_at }}
                </el-form-item>
                <el-form-item :label="$t('fields.updatedAt')" prop="updated_at">
                    {{ data.form.updated_at }}
                </el-form-item>
                <el-form-item :label="$t('fields.createdAt')" prop="created_at">
                    {{ data.form.created_at }}
                </el-form-item>
                <el-divider />
            </template>
            <el-form-item :label="$t('dispatcher.orders.fields.location')" prop="location_name">
                <el-input v-model="data.form.location_name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.address')">
                <AddressSelect ref="addressSelectRef" :id="data.form.address.id" :address="data.form.address.address"
                    :city="data.form.address.city" :province="data.form.address.province"
                    :postal-code="data.form.address.postal_code" :on-clear="onClearAddress"
                    @success="onRetrieveAddress" />
            </el-form-item>

            <el-form-item :label="$t('dispatcher.orders.fields.unitNo')" prop="unit_no">
                <el-input v-model="data.form.unit_no" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('dispatcher.orders.fields.buzzerCode')" prop="buzzer_code">
                <el-input v-model="data.form.buzzer_code" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('dispatcher.orders.fields.phoneNumber')" prop="phone_number">
                <el-input v-model="data.form.phone_number" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.quantity')" prop="quantity">
                <el-input-number v-model="data.form.quantity" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('dispatcher.orders.fields.relatedOrders')" prop="related_orders">
                <el-select v-model="data.form.related_orders" multiple filterable remote reserve-keyword
                    placeholder="Please enter at least 6 numbers" :remote-method="getDeliveryOrders" :loading="loading"
                    style="min-width: 240px">
                    <el-option v-for="item in deliveryOrderList" :key="item" :label="item.no" :value="item" />
                </el-select>
            </el-form-item>
            <el-divider />
            <el-form-item :label="$t('fields.date')" prop="date">
                <el-date-picker v-model="data.form.date" type="date" placeholder="Pick a day" value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate" :shortcuts="shortcuts" :size="size" />
            </el-form-item>
            <el-form-item :label="$t('dispatcher.orders.fields.timeWindow')" prop="time_window_from">
                <el-space>
                    <el-time-select v-model="data.form.time_window_from" style="width: 240px" start="08:30" step="00:15"
                        end="23:30" placeholder="Select time" value-format="HH:MM" />

                    ~
                    <el-time-select v-model="data.form.time_window_to" style="width: 240px" start="08:30" step="00:15"
                        end="23:30" placeholder="Select time" value-format="HH:MM" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('delivery.worksheets.fields.driver')" prop="driver">
                <EmployeeSelector v-model="data.form.driver" />
            </el-form-item>
            <el-form-item :label="$t('staffs.worksheet.fields.shift')" prop="shift">
                <WorkShiftSelector v-model="data.form.shift" />
            </el-form-item>
            <el-form-item :label="$t('dispatcher.orders.fields.uploadAt')" prop="upload_at">
                <el-date-picker v-model="data.form.upload_at" type="datetime" placeholder="Select date and time" />
            </el-form-item>
            <el-form-item :label="$t('fields.notes')" prop="notes">
                <el-input v-model="data.form.notes" placeholder="请输入标题" type="textarea" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
    // scss
</style>
