const Layout = () => import('@/layout/index.vue')

export default {
    path: '/users/users',
    component: Layout,
    redirect: '/users/users/index',
    name: 'users',
    meta: {
        title: '网站用户',
        icon: 'gis:globe-users',
        i18n: 'route.users.title',

    },
    children: [
        {
            path: 'index',
            name: 'userIndex',
            component: () => import('@/views/users/users/index.vue'),
            meta: {
                title: '用户统计',
                icon: 'akar-icons:statistic-up',
                i18n: 'route.users.statics',
                activeMenu: '/users/users/index',
            }
        },
        {
            path: 'list',
            name: 'usersList',
            component: () => import('@/views/users/users/list.vue'),
            meta: {
                title: '用户列表',
                icon: 'ph:users-duotone',
                i18n: 'route.users.users',
                activeMenu: '/users/users/list',
                cache: ['userDetail']
            }
        },
        {
            path: 'detail',
            name: 'userDetail',
            component: () => import('@/views/users/users/detail.vue'),
            meta: {
                title: '用户详情',
                i18n: 'user.pages.detail',
                activeMenu: '/users/users/list',
                sidebar: false,
            }
        },
        {
            path: 'groupList',
            name: 'userGroups',
            component: () => import('@/views/users/user_groups/list.vue'),
            meta: {
                title: '分组管理',
                icon: 'fa6-solid:users-between-lines',
                i18n: 'route.users.groups',
                activeMenu: '/users/users/groupList',
            },
        }
    ]
}
