/*
* Author: <EMAIL>'
* Date: '2024-06-06 21:43:20'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/tracking.js'
* File: 'tracking.js'
* Version: '1.0.0'
*/
import { DEL, GET, POST, PUT } from "../methods";

const path = 'cms/tracking/'

export const getTrackingStatus = (params) => GET(path + 'status/', params)
export const addTrackingStatus = (params) => POST(path + 'status/', params)
export const updateTrackingStatus = (params) => PUT(path + 'status/', params)
export const deleteTrackingStatus = (params) => DEL(path + 'status/', params)
