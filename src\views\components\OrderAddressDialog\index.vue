<script setup>
import { phoneNumberFormatter } from '@/utils/formatter'

const props = defineProps({
    addressInfo: {
        type: Object,
        default: {},
    },
    addressType: {
        type: String,
        default: 'sender'
    },
    modelValue: {
        type: Boolean,
        default: false
    },
})
const emit = defineEmits(['update:modelValue'])

let myVisible = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})

</script>
<template>
    <el-dialog v-model="myVisible" :class="props.addressType == 'sender' ? 'sender-dialog' : 'receiver-dialog'"
        :title="$t(`delivery.orders.dialogs.${props.addressType == 'sender' ? 'senderDetail' : 'receiverDetail'}`)"
        width="30%">
        <el-descriptions :title="props.addressType == 'sender' ? addressInfo.sender_name : addressInfo.receiver_name"
            :column="1">
            <el-descriptions-item :label="$t('user.fields.name')" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_name : addressInfo.receiver_name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.phoneNumber`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ phoneNumberFormatter(props.addressType == 'sender' ? addressInfo.sender_phone
                    : addressInfo.receiver_phone) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.email`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_email : addressInfo.receiver_email }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.secondaryPhone`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ phoneNumberFormatter(props.addressType == 'sender' ? addressInfo.sender_secondary_phone
                    : addressInfo.receiver_secondary_phone) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.unitNo`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_unit_no : addressInfo.receiver_unit_no }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.buzzerCode`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_buzzer_code : addressInfo.receiver_buzzer_code }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.address`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_address : addressInfo.receiver_address }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.postalCode`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_postal_code : addressInfo.receiver_postal_code }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.city`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_city : addressInfo.receiver_city }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t(`user.fields.province`)" class-name="bold-content"
                label-class-name="desc-label">
                {{ props.addressType == 'sender' ? addressInfo.sender_province : addressInfo.receiver_province }}
            </el-descriptions-item>
        </el-descriptions>
    </el-dialog>
</template>
<style lang="scss">
.bold-content {
    font-weight: bolder;
}

.desc-label {
    font-size: 0.9em;
    color: #676767 !important;
}

.sender-dialog {

    header.el-dialog__header {
        background-color: #eebe77 !important;
        margin-right: 0;
        padding-right: 16px;

        .el-dialog__title,
        .el-dialog__close {
            color: white !important;
        }
    }
}

.receiver-dialog {

    header.el-dialog__header {
        background-color: #b3e19d !important;
        margin-right: 0;
        padding-right: 16px;

        .el-dialog__title,
        .el-dialog__close {
            color: white !important;
        }
    }
}
</style>