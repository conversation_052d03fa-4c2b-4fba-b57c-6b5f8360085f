<route>
{
    name: 'personalEditPassword',
    meta: {
        title: "修改密码"
    }
}
</route>

<script setup name="PersonalEditPassword">
import useUserStore from '@/store/modules/user';

const route = useRoute()
const router = useRouter()

const userStore = useUserStore()

const validatePassword = (rule, value, callback) => {
    if (value === form.value.password) {
        callback(new Error('samePassword'))
    } else {
        callback()
    }
}
const validatePassword2 = (rule, value, callback) => {
    if (value !== form.value.newPassword) {
        callback(new Error('notSameNewPassword'))
    } else {
        callback()
    }
}

const formRef = ref()
const form = ref({
    password: '',
    newPassword: '',
    newPassword2: ''
})

const rules = ref({
    password: [
        { required: true, message: '请输入原密码', trigger: 'blur' }
    ],
    newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, max: 18, trigger: 'blur', message: '密码长度为6到18位' },
        { validator: validatePassword }

    ],
    newPassword2: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { validator: validatePassword2 }
    ]
})

function onSubmit() {
    formRef.value.validate(valid => {
        if (valid) {
            userStore.editPassword(form.value).then(() => {
                window.history.go(-1)
            })
        }
    })
}
</script>

<template>
    <div>
        <page-header title="修改密码" content="定期修改密码可以提高帐号安全性噢~" />
        <page-main>
            <el-row>
                <el-col :md=" 24 " :lg=" 12 ">
                    <el-form ref="formRef" :model=" form " :rules=" rules " label-width="120px">
                        <el-form-item label="原密码" prop="password">
                            <el-input v-model=" form.password " type="password" placeholder="请输入原密码" />
                        </el-form-item>
                        <el-form-item label="新密码" prop="newPassword">
                            <el-input v-model=" form.newPassword " type="password" placeholder="请输入原密码" />
                        </el-form-item>
                        <el-form-item label="确认新密码" prop="newPassword2">
                            <el-input v-model=" form.newPassword2 " type="password" placeholder="请输入原密码" />
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
        </page-main>
        <fixed-action-bar>
            <el-button type="primary" size="large" @click=" onSubmit ">提交</el-button>
        </fixed-action-bar>
    </div>
</template>
