import { queryNewTodos } from "@/api/modules/todos"
import useUserStore from './user'
import autoQuery from './switch'

const useNotificationStore = defineStore(
    // 唯一ID
    'notification',
    {
        state: () => ({
            timer: null,
            interval: 5000,
            // 消息
            message: 0,
            // 待办
            todo: {
                Refund: 0
            }
        }),
        getters: {
            // 未读通知总数
            total() {
                return this.message + this.todo
            }
        },
        actions: {
            // 初始化，获取所有通知的未读数量
            init() {
                this.getUnreadMessage()
                this.startQuery()
            },

            startQuery() {
                this.stopQuery();
                const userStore = useUserStore()
                if (autoQuery && userStore.isLogin && (userStore.isSuper || userStore.permissions.includes('todo'))) {
                    this.timer = setInterval(() => {
                        this.getUnreadTodo();
                    }, this.interval);
                }
            },

            stopQuery() {
                if (this.timer) clearInterval(this.timer)
            },


            // 获取未读消息数
            getUnreadMessage() {
                // 为方便演示，这里直接写死的未读数
                this.message = 9
            },
            // 获取未读待办数
            getUnreadTodo() {
                queryNewTodos().then(res => {
                    this.todo = res.data
                })
            }
        }
    }
)

export default useNotificationStore
