import useSettingsStore from '@/store/modules/settings'
import storage from '@/utils/storage'

import { createI18n } from 'vue-i18n'

import en from './lang/en'
import zhCN from './lang/zh-cn'

import elementLocaleZhCN from 'element-plus/dist/locale/zh-cn.mjs'
import elementLocaleZhTW from 'element-plus/dist/locale/zh-tw.mjs'
import elementLocaleEn from 'element-plus/dist/locale/en.mjs'
import zhTW from './lang/zh-tw/index'

const messages = {
    en: en,
    'zh-cn': zhCN,
    // 'zh-tw': zhTW,
}
const settingsStore = useSettingsStore()

function useI18n(app) {
    // 如果没设置默认语言，则根据当前浏览器语言设置默认语言
    if (!settingsStore.app.defaultLang) {
        let lang = storage.local.get('fleetnowLang')
        if (lang) {
            settingsStore.setDefaultLang(lang)
        } else {
            lang = (navigator.language || navigator.browserLanguage).toLowerCase()
        }
        if (lang == 'zh') lang = 'zh-cn';
        for (var key in messages) {
            lang.includes(key) && settingsStore.setDefaultLang(key)
        }
    }
    const i18n = createI18n({
        legacy: false,
        locale: settingsStore.app.defaultLang,
        flatJson: true,
        fallbackLocale: 'en',
        messages,
        globalInjection: true
    })

    app.use(i18n)
}



function getElementLocales() {
    let locales = {}
    for (const key in messages) {
        locales[key] = {}
        switch (key) {
            case 'zh-cn':
                Object.assign(locales[key], elementLocaleZhCN, { 'labelName': '中文(简体)' })
                break
            case 'zh-tw':
                Object.assign(locales[key], elementLocaleZhTW, { 'labelName': '中文(繁體)' })
                break
            case 'en':
                Object.assign(locales[key], elementLocaleEn, { 'labelName': 'English' })
                break
        }
    }
    return locales
}

export {
    useI18n,
    getElementLocales,
    messages,
}
