<script setup>

    import {
        getSupplementServices,
        addSupplementService
    } from '@/api/modules/delivery';
    import { useI18n } from 'vue-i18n';
    import { DocumentCopy, View } from '@element-plus/icons-vue'
    import { useClipboard } from '@vueuse/core'

    const { text, copy, copied, isSupported } = useClipboard()

    const { t } = useI18n()

    const props = defineProps({
        modelValue: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            default: null
        },
    })

    const emit = defineEmits(['update:modelValue', 'success'])
    const selected = ref(null)
    const part = ref('SENDER')
    const options = ref([])


    const dialogVisible = computed({
        get: () => { return props.modelValue },
        set: (val) => {
            emit('update:modelValue', val)
            return val
        }
    })

    const url = ref(null)

    // Api
    function onGetUrl() {
        if (!selected.value || !part.value) return;

        let param = {
            serviceId: selected.value.id,
            part: part.value,
        }
        addSupplementService(props.id, param).then((res) => {
            if (!res.data.errCode) {
                url.value = res.data.url

            }

        })
    }

    const getOptions = () => {
        getSupplementServices(props.id).then((res) => {
            if (!res.data.errCode) {
                options.value = res.data
                if (res.data.length > 0) {
                    selected.value = res.data[0]
                }
            }
        })
    }



    // Methods

    const clearData = () => {
        options.value = []
        selected.value = 'SENDER'
        url.value = null
    }


    function openUrl(url) {
        window.open(url, '_blank')
    }


    watch(copied, (val) => {
        val && ElMessage.success(`${t('messages.copied')}：${text.value}`)
    })


</script>
<template>
    <el-dialog v-model="dialogVisible" :title="$t('delivery.orders.fields.addedServices')" width="30%" align-center
        destroy-on-close @open="getOptions" @close="clearData">
        <div style="display: flex; flex-direction: column;  align-items:start">
            <el-select v-model="selected" class="m-2"
                :placeholder="$t('selectPlaceHolder', { field: $t('delivery.orders.fields.addedServices') })"
                size="large" prop="service" value-key="id" no-data-text="No available service">
                <el-option v-for="item in options" :key="item.id" :label="item.names['en-us']" :value="item" />
            </el-select>
            <el-radio-group v-model="part">
                <el-radio value="SENDER" size="large">Sender</el-radio>
                <el-radio value="RECEIVER" size="large">Receiver</el-radio>
            </el-radio-group>
            <el-button text @click="onGetUrl" type="primary" size="large">Get Url</el-button>
        </div>
        <el-divider />
        <el-space v-if="url">
            <el-text>{{ url }}</el-text>
            <el-tooltip :content="$t('operations.copy')" placement="top-start">
                <span>
                    <el-button type="primary" plain circle size="small" :icon="DocumentCopy" @click="copy(url)" />
                </span>
            </el-tooltip>

            <el-tooltip :content="$t('operations.open')" placement="top-start">
                <span>
                    <el-button type="success" plain circle size="small" :icon="View" @click="openUrl(url)" />
                </span>
            </el-tooltip>
        </el-space>

        <el-text v-else>No Url</el-text>
        <el-divider />

    </el-dialog>
</template>
<style lang="scss" scoped>
    .margin-top {
        margin-top: 20px;
    }

    :deep(.el-descriptions__label) {
        width: 90px;
    }

    .packaging-quantity {
        width: 100%;
        min-width: 120px;
    }
</style>