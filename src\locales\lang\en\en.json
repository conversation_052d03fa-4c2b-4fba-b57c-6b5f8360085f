{
    // Settings
    "setting": {
        "title": "Settings",
        "fields": {},
        "operations": {}
    },
    // Zones
    "zone": {
        "title": "Zones",
        "fields": {},
        "operations": {}
    },
    "postalCode": {
        "title": "Postal Codes",
        "fields": {},
        "operations": {}
    },
    "address": {
        "title": "Addresses",
        "fields": {},
        "operations": {}
    },
    // Services
    "service": {
        "title": "Services",
        "fields": {},
        "operations": {}
    },
    "packageType": {
        "title": "Package Types",
        "fields": {},
        "operations": {}
    },
    "size": {
        "title": "Size & Weight",
        "fields": {},
        "operations": {}
    },
    "mode": {
        "title": "Service Modes",
        "fields": {},
        "operations": {}
    },
    "packing": {
        "title": "Packing Materials",
        "fields": {},
        "operations": {}
    },
    // Promotions
    "promotion": {
        "title": "Promotions",
        "fields": {},
        "operations": {}
    },
    "enoughOrder": {
        "title": "Enough Orders",
        "fields": {},
        "operations": {}
    },
    "code": {
        "title": "Codes",
        "fields": {},
        "operations": {}
    },
    "groupBuy": {
        "title": "Group Buy",
        "fields": {},
        "operations": {}
    },
    "newCustomer": {
        "title": "Welcome Gifts",
        "fields": {},
        "operations": {}
    },
    "frequentCustomer": {
        "title": "Frequent Customers",
        "fields": {},
        "operations": {}
    },
    "lostCustomer": {
        "title": "Lost Customers",
        "fields": {},
        "operations": {}
    },
    "holiday": {
        "title": "Holiday Gifts",
        "fields": {},
        "operations": {}
    },
    "free": {
        "title": "Free Bill",
        "fields": {},
        "operations": {}
    },
    "promoStatics": {
        "title": "Promo Statics",
        "fields": {},
        "operations": {}
    },
    "promoLog": {
        "title": "Promo Logs",
        "fields": {},
        "operations": {}
    },
    "tips": {
        "title": "Tips",
        "fields": {},
        "operations": {}
    },
    "incoming": {
        "title": "Incomings",
        "fields": {},
        "operations": {}
    },
    "bill": {
        "title": "Bills",
        "fields": {},
        "operations": {}
    },
    "openBill": {
        "title": "openBills",
        "fields": {},
        "operations": {}
    },
    // Logistic Orders
    "order": {
        "title": "Orders",
        "fields": {},
        "operations": {}
    },
    "openOrder": {
        "title": "Open Orders",
        "fields": {},
        "operations": {}
    },
    "orderStatics": {
        "title": "Order Statics",
        "fields": {},
        "operations": {}
    },
    // Mall
    "mall": {
        "title": "Mall",
        "fields": {},
        "operations": {}
    },
    "shopCategory": {
        "title": "Shop Categories",
        "fields": {},
        "operations": {}
    },
    "store": {
        "title": "Stores",
        "fields": {},
        "operations": {}
    },
    "category": {
        "title": "Categories",
        "fields": {},
        "operations": {}
    },
    "product": {
        "title": "Products",
        "fields": {},
        "operations": {}
    },
    "mallOrder": {
        "title": "Orders",
        "fields": {},
        "operations": {}
    },
    // User Credits
    "credit": {
        "title": "User Credits",
        "fields": {},
        "operations": {}
    },
    "creditLog": {
        "title": "Credit Log",
        "fields": {},
        "operations": {}
    },
    "directCredit": {
        "title": "Direct Credits",
        "fields": {},
        "operations": {}
    },
    "creditStatics": {
        "title": "Credit Statics",
        "fields": {},
        "operations": {}
    }
}
