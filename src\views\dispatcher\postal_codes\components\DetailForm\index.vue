<script setup>
import api from '@/api'
import { addUpdatePostalCode } from '@/api/modules/dispatcher';
const props = defineProps({
    row: {
        type: Object,
        default: { id: '', fee: 0 }
    },
    zones: {
        type: Array,
        default: []
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: props.row,
    fee: props.row.fee / 100,
    rules: {
        code: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        city: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        province: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.fee = Math.round(data.value.fee * 100)
        params.zone = data.value.form.zone?.id
        formRef.value.validate(valid => {
            if (valid) {
                addUpdatePostalCode(params).then((res) => {
                    if (res.data.errCode == 365) { callback && callback() }
                })
            }
        })
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('user.fields.postalCode')" prop="code">
                <el-input v-model="data.form.code" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.city')" prop="city">
                <el-input v-model="data.form.city" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.province')" prop="province">
                <el-input v-model="data.form.province" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('delivery.services.fields.zone')">
                <el-select v-model="data.form.zone" value-key="id">
                    <el-option v-for="item in props.zones" :key="item.id" :value="item" :label="item.name">
                        <el-space>
                            <span style="float: left;">{{ item.name }}</span>
                            <span style="float: left; color: var(--el-text-color-secondary); font-size: 13px;">{{
                                item.description }}</span>
                        </el-space>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item :label="$t('fields.fee')" prop="fee">
                <el-input-number v-model="data.fee" placeholder="请输入标题" :min="0.0" :step="0.1" :precision="2" />
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')" prop="priority">
                <el-switch v-model="data.form.is_available" />
            </el-form-item>

        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
