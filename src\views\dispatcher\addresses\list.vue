<script setup name="DispatcherAddressList">
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import FormMode from './components/FormMode/index.vue'
import { getAddresses, updateAddressFee } from '@/api/modules/dispatcher'
import { currencyFormatter } from '@/utils/formatter'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        Line1__icontains: null,
        PostalCode__icontains: null,
        City__icontains: null,
        ProvinceName__icontains: null,
        fee__lte: null,
        fee__gte: 0,
    },
    formModeProps: {
        visible: false,
        row: { Id: '' }
    },

    // 批量操作
    batch: {
        selectionDataList: []
    },
    fee: 0,
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)

const showBatchDialog = ref(false)


// Filters
function resetFilters() {
    data.value.search =
    {
        Line1__icontains: null,
        PostalCode__icontains: null,
        City__icontains: null,
        ProvinceName__icontains: null,
        fee__lte: null,
        fee__gte: 0,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getAddresses(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.address_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onEdit(row) {
    data.value.formModeProps.row = row
    data.value.formModeProps.visible = true
}

function _showBatchDialog() {
    showBatchDialog.value = true
}

function _hideBatchDialog() {
    showBatchDialog.value = false
    data.value.fee = 0.0
}

function onBatchEdit() {
    data.value.loading = true

    var params = {
        id: data.value.batch.selectionDataList.map(x => x.Id),
        fee: data.value.fee * 100
    }
    updateAddressFee(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
            _hideBatchDialog()
        }
        data.value.loading = false
    })
}

</script>

<template>
    <div>
        <page-header :title="$t('dispatcher.addresses.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.address')">
                                        <el-input v-model="data.search.Line1__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.address') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="3">
                                    <el-form-item :label="$t('delivery.addressBook.fields.postalCode')">
                                        <el-input v-model="data.search.PostalCode__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.postalCode') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.city')">
                                        <el-input v-model="data.search.City__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.city') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.province')">
                                        <el-input v-model="data.search.ProvinceName__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.city') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col>
                                    <el-form-item :label="$t('fields.fee')">
                                        <el-space>
                                            <el-input-number v-model="data.search.fee__gte" controls-position="right"
                                                :min="0" :step="1" :precision="2" :max="data.search.fee__lte ?? 100"
                                                :placeholder="$t('placeholder', { field: $t('fields.feeMin') }) + ', ' + $t('fuzzySupported')"
                                                clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                            ~
                                            <el-input-number v-model="data.search.fee__lte" controls-position="right"
                                                :min="data.search.fee__gte" :step="1" :precision="2"
                                                :placeholder="$t('placeholder', { field: $t('fields.feeMax') }) + ', ' + $t('fuzzySupported')"
                                                clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button size="default" type="warning" @click="_showBatchDialog">
                        {{ $t('operations.batch', { op: $t('operations.edit') }) }}
                    </el-button>
                </batch-action-bar>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column type="index" width="50" />
                <el-table-column prop="Line1" :label="$t('user.fields.address')" />
                <el-table-column prop="PostalCode" :label="$t('user.fields.postalCode')" sortable="custom" width="150"
                    align="center" />
                <el-table-column prop="City" :label="$t('user.fields.city')" sortable="custom" width="150" align="center" />
                <el-table-column prop="ProvinceName" :label="$t('user.fields.province')" sortable="custom" width="150"
                    align="center" />
                <el-table-column prop="fee" :label="$t('fields.fee')" sortable="custom" :formatter="currencyFormatter"
                    width="100" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :row="data.formModeProps.row" v-model="data.formModeProps.visible" @success="getDataList" />
        <el-dialog v-model="showBatchDialog" center
            :title="$t('dialog.titles.batchEditItems', { count: data.batch.selectionDataList.length })" width="30%">
            <el-row justify="end">
                <el-col>
                    <el-form :model="form">
                        <el-form-item :label="$t('fields.fee')">
                            <el-input-number v-model="data.fee" autocomplete="off" :min="0.0" :step="0.1" :precision="2" />
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="_hideBatchDialog">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onBatchEdit">{{ $t('operations.confirm') }}</el-button>
                </span>
            </template>
        </el-dialog>

    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
