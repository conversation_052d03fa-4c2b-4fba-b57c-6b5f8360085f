import { ref, computed, createApp, h } from 'vue';
import { useDriverStore } from '../../../stores/driver';
import DriverMarker from '../components/DriverMarker.vue';
import DriverMarkerPopup from '../components/DriverMarkerPopup.vue';
import maplibregl from 'maplibre-gl';
import { eventBus, EVENT_TYPES } from '../../../utils/eventBus';

export function useDriverMarkers(map) {
    const driverStore = useDriverStore();
    
    // 司机标记管理
    const driverMarkers = ref(new Map());
    const showDrivers = ref(true);
    const activeDriverPopup = ref(null);
    const autoZoom = ref(false);
    
    // 添加司机图层引用
    const driversLayer = ref(null);
    
    // 添加司机图层
    const addDriversLayer = () => {
        if (!map.value) return;
      
        try {
            // 如果来源不存在，添加
            if (!map.value.getSource('drivers-source')) {
                map.value.addSource('drivers-source', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: []
                    }
                });
                console.log('已添加司机数据源');
            }
          
            // 如果图层不存在，添加图层
            if (!map.value.getLayer('drivers-layer')) {
                map.value.addLayer({
                    id: 'drivers-layer',
                    type: 'symbol',
                    source: 'drivers-source',
                    layout: {
                        // 使用小车图标，颜色和司机颜色一致
                        'icon-image': [
                            'case',
                            ['has', 'driver_color'], ['concat', 'car-marker-', ['get', 'driver_color']], // 使用带颜色的小车图标
                            'car-marker-default' // 使用默认小车图标
                        ],
                        'icon-size': 0.6,
                        'icon-allow-overlap': true,
                        'icon-ignore-placement': true,
                        'icon-rotate': ['get', 'bearing'], // 使用方向信息旋转图标
                        'icon-rotation-alignment': 'map',
                        // 添加司机名称标签
                        'text-field': ['get', 'name'],
                        'text-size': 12,
                        'text-offset': [0, 1.5], // 在图标下方显示文本
                        'text-anchor': 'top',
                        'text-allow-overlap': false,
                        'text-ignore-placement': false
                    },
                    paint: {
                        // 文本样式
                        'text-color': '#333',
                        'text-halo-color': '#fff',
                        'text-halo-width': 1
                    }
                });
                
                // 设置图层引用
                driversLayer.value = 'drivers-layer';
                
                // 添加事件监听
                map.value.on('click', 'drivers-layer', onDriverMarkerClicked);
                map.value.on('mouseenter', 'drivers-layer', () => {
                    map.value.getCanvas().style.cursor = 'pointer';
                });
                map.value.on('mouseleave', 'drivers-layer', () => {
                    map.value.getCanvas().style.cursor = '';
                });
                
                // 确保订单图层显示在司机图层之上
                if (typeof moveSymbolLayersToTop === 'function') {
                    moveSymbolLayersToTop();
                }
                console.log('已添加司机图层');
            } else {
                // 图层已存在，设置引用
                driversLayer.value = 'drivers-layer';
            }
        } catch (error) {
            console.error('添加司机图层时出错:', error);
        }
    };
    
    // 更新司机位置方法
    const updateDriversPositions = (driversWithLocation, autoZoomParam = false) => {
        console.log('开始执行updateDriversPositions函数');
      
        // 始终确保显示开关是打开的
        if (!showDrivers.value) {
            console.warn('司机位置显示已关闭，强制开启显示');
            showDrivers.value = true;
        }
      
        if (!map.value) {
            console.error('地图未加载，无法更新司机位置');
            return;
        }
      
        try {
            // 验证输入数据
            if (!Array.isArray(driversWithLocation)) {
                console.warn('更新司机位置失败: 输入数据不是数组');
                return;
            }
            
            console.log(`准备更新${driversWithLocation.length}个司机位置`);
            
            // 确保司机图层存在
            addDriversLayer();
            
            // 更新GeoJSON数据
            const features = driversWithLocation.map(driverLocation => {
                // 验证位置数据
                if (!driverLocation || !driverLocation.id || 
                    typeof driverLocation.latitude !== 'number' || 
                    typeof driverLocation.longitude !== 'number') {
                    console.warn('司机位置数据无效:', driverLocation);
                    return null;
                }
                
                // 获取司机信息
                const driver = driverStore.getDriverById(driverLocation.id);
                
                // 计算行驶方向（如果有前一个位置和当前速度）
                let bearing = 0;
                if (driverLocation.speed && driverLocation.speed > 0 && driver && driver.previousLocation) {
                    // 计算方向
                    const prevLat = driver.previousLocation.latitude;
                    const prevLng = driver.previousLocation.longitude;
                    const currLat = driverLocation.latitude;
                    const currLng = driverLocation.longitude;
                    
                    // 简单计算方向角度
                    if (prevLat !== currLat || prevLng !== currLng) {
                        const deltaLng = currLng - prevLng;
                        const deltaLat = currLat - prevLat;
                        bearing = (Math.atan2(deltaLng, deltaLat) * 180 / Math.PI) + 180;
                    }
                }
                
                // 保存当前位置作为下次更新的前一个位置
                if (driver) {
                    driver.previousLocation = {
                        latitude: driverLocation.latitude,
                        longitude: driverLocation.longitude
                    };
                }
                
                // 创建要素
                return {
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: [driverLocation.longitude, driverLocation.latitude]
                    },
                    properties: {
                        id: driverLocation.id,
                        name: driver?.name || `司机 ${driverLocation.id}`,
                        speed: driverLocation.speed,
                        accuracy: driverLocation.accuracy,
                        timestamp: driverLocation.timestamp,
                        route_id: driver?.route_id,
                        route_number: driver?.route_number,
                        driver_color: driver?.color,
                        bearing: bearing,
                        isOnline: true
                    }
                };
            }).filter(feature => feature !== null); // 过滤掉无效数据
            
            console.log(`过滤后有${features.length}个有效司机位置数据`);
            
            // 确保至少有一个有效的司机位置
            if (features.length === 0) {
                console.warn('没有有效的司机位置数据可更新');
                return;
            }
            
            // 更新数据源
            try {
                const source = map.value.getSource('drivers-source');
                if (source) {
                    source.setData({
                        type: 'FeatureCollection',
                        features: features
                    });
                    console.log(`司机位置数据源更新成功，包含${features.length}个司机位置`);
                } else {
                    console.warn('司机数据源不存在，无法更新位置');
                    console.log('尝试重新添加司机图层');
                    addDriversLayer();
                    
                    // 重试一次更新数据源
                    setTimeout(() => {
                        const retrySource = map.value.getSource('drivers-source');
                        if (retrySource) {
                            retrySource.setData({
                                type: 'FeatureCollection',
                                features: features
                            });
                            console.log('重试更新司机位置数据源成功');
                        } else {
                            console.error('重试更新司机位置数据源失败');
                        }
                    }, 500);
                    return;
                }
            } catch (sourceError) {
                console.error('更新司机数据源时发生错误:', sourceError);
                return;
            }
            
            console.log(`地图上成功更新了${features.length}个司机位置`);
            
            // 确保订单图层显示在司机图层之上
            if (typeof moveSymbolLayersToTop === 'function') {
                moveSymbolLayersToTop();
            }
            
            // 尝试调整视图以显示所有司机
            if (autoZoomParam && features.length > 0) {
                try {
                    fitMapToDrivers(features);
                } catch (zoomError) {
                    console.error('调整地图视图以显示所有司机时发生错误:', zoomError);
                }
            }
        } catch (error) {
            console.error('更新司机位置失败:', error);
        }
    };
    
    // 添加调整视图以显示所有司机的辅助函数
    const fitMapToDrivers = (driverFeatures) => {
        if (!map.value || !driverFeatures || driverFeatures.length === 0) return;
        
        try {
            // 提取司机坐标
            const bounds = new maplibregl.LngLatBounds();
            
            driverFeatures.forEach(feature => {
                if (feature.geometry && feature.geometry.coordinates) {
                    bounds.extend(feature.geometry.coordinates);
                }
            });
            
            // 调整地图视图
            map.value.fitBounds(bounds, {
                padding: 100,
                maxZoom: 16,
                duration: 1000
            });
            
            console.log('地图视图已调整以显示所有司机');
        } catch (error) {
            console.error('调整地图视图失败:', error);
        }
    };
    
    // 处理司机标记点击事件
    const onDriverMarkerClicked = (e) => {
        if (!e.features || e.features.length === 0) return;
        
        // 获取司机ID
        const driverId = e.features[0].properties.id;
        
        // 获取司机对象
        const driver = driverStore.getDriverById(driverId);
        
        if (!driver) {
            console.warn('无法找到ID为', driverId, '的司机');
            return;
        }
        
        // 在store中选择司机，使用正确的selectDriver方法
        driverStore.selectDriver(driver);
        
        // 触发事件通知其他组件
        eventBus.emit(EVENT_TYPES.DRIVER_SELECTED, driverId);
        
        // 弹窗由鼠标悬停处理，点击时不处理弹窗
    };
    
    // 处理司机位置更新
    const updateDriverMarkers = () => {
        if (!map.value || !showDrivers.value) return;
        
        const drivers = driverStore.drivers;
        
        try {
            // 记录已更新的司机ID
            const updatedDriverIds = new Set();
            
            // 更新或添加司机标记
            drivers.forEach(driver => {
                if (!driver.latitude || !driver.longitude) return;
                
                const position = [parseFloat(driver.longitude), parseFloat(driver.latitude)];
                updatedDriverIds.add(driver.id);
                
                // 如果已存在该司机标记，更新位置
                if (driverMarkers.value.has(driver.id)) {
                    const marker = driverMarkers.value.get(driver.id);
                    marker.setLngLat(position);
                } else {
                    // 创建新的司机标记
                    createDriverMarker(driver, position);
                }
            });
            
            // 移除不再存在的司机标记
            const driversToRemove = [];
            driverMarkers.value.forEach((marker, driverId) => {
                if (!updatedDriverIds.has(driverId)) {
                    driversToRemove.push(driverId);
                }
            });
            
            driversToRemove.forEach(driverId => {
                const marker = driverMarkers.value.get(driverId);
                if (marker) {
                    marker.remove();
                    driverMarkers.value.delete(driverId);
                }
            });
        } catch (error) {
            console.error('更新司机标记时出错:', error);
        }
    };
    
    // 创建司机标记
    const createDriverMarker = (driver, position) => {
        if (!map.value || !driver || !position) return;
        
        try {
            // 创建自定义标记元素
            const markerApp = createApp({
                render() {
                    return h(DriverMarker, {
                        driver: driver,
                        isSelected: driverStore.selectedDriver?.id === driver.id,
                        showDriverInfo: false,
                        onClick: () => handleDriverMarkerClick(driver)
                    });
                }
            });
            
            const markerElement = document.createElement('div');
            markerApp.mount(markerElement);
            
            // 创建maplibre标记
            const marker = new maplibregl.Marker({
                element: markerElement,
                anchor: 'center'
            })
                .setLngLat(position)
                .addTo(map.value);
                
            // 添加事件处理
            markerElement.addEventListener('mouseenter', () => handleDriverMouseEnter(marker, driver));
            markerElement.addEventListener('mouseleave', handleDriverMouseLeave);
            
            // 保存标记
            driverMarkers.value.set(driver.id, marker);
            
            // 将司机信息关联到标记
            marker._driver = driver;
        } catch (error) {
            console.error('创建司机标记时出错:', error);
        }
    };
    
    // 切换司机标记显示
    const toggleDrivers = () => {
        if (!map.value) return;
        
        if (showDrivers.value) {
            // 检查地图层
            const hasDriversLayer = driversLayer && driversLayer.value && map.value.getLayer(driversLayer.value);
            if (hasDriversLayer) {
                map.value.setLayoutProperty(driversLayer.value, 'visibility', 'visible');
                
                // 立即更新一次司机位置
                const driversWithLocation = driverStore.driversWithLocation;
                if (driversWithLocation.length > 0) {
                    updateDriversPositions(driversWithLocation);
                }
            } else {
                // 如果图层不存在，尝试添加
                addDriversLayer();
                
                // 然后使用标记方式更新
                updateDriverMarkers();
            }
        } else {
            // 隐藏所有司机标记
            const hasDriversLayer = driversLayer && driversLayer.value && map.value.getLayer(driversLayer.value);
            if (hasDriversLayer) {
                map.value.setLayoutProperty(driversLayer.value, 'visibility', 'none');
            }
            
            // 移除所有标记
            driverMarkers.value.forEach(marker => marker.remove());
            driverMarkers.value.clear();
        }
    };
    
    // 监听司机位置更新事件
    const onDriverPositionsUpdated = (driversPositions) => {
        console.log('收到司机位置更新事件', driversPositions);
        
        // 数据检查 - 添加详细日志
        if (!driversPositions) {
            console.error('司机位置数据为空');
            return;
        }
        
        if (!Array.isArray(driversPositions)) {
            console.error('司机位置数据不是数组格式:', typeof driversPositions, driversPositions);
            
            // 尝试转换为数组
            try {
                if (typeof driversPositions === 'string') {
                    driversPositions = JSON.parse(driversPositions);
                    console.log('已将字符串转换为对象:', driversPositions);
                }
                
                if (!Array.isArray(driversPositions)) {
                    driversPositions = [driversPositions];
                    console.log('已将非数组对象转换为数组');
                }
            } catch (error) {
                console.error('转换司机位置数据失败:', error);
                return;
            }
        }
        
        console.log(`收到${driversPositions.length}个司机位置数据`);
        console.log('司机位置数据示例:', driversPositions[0]);
        
        if (!map.value) {
            console.error('地图未初始化，无法更新司机位置');
            return;
        }
        
        // 确保显示开关打开
        if (!showDrivers.value) {
            console.log('司机位置显示已关闭，打开显示');
            showDrivers.value = true;
        }
        
        // 过滤出有效的位置数据
        const validPositions = driversPositions.filter(pos => 
            pos && 
            pos.id && 
            typeof pos.latitude === 'number' && 
            typeof pos.longitude === 'number' &&
            pos.latitude !== 0 && 
            pos.longitude !== 0
        );
        
        console.log(`过滤后有${validPositions.length}个有效司机位置数据`);
        
        if (validPositions.length === 0) {
            console.warn('没有有效的司机位置数据');
            return;
        }
        
        // 打印有效位置数据的示例
        console.log('有效司机位置数据示例:', validPositions[0]);
        
        // 确保司机图层存在
        const hasDriversLayer = driversLayer && driversLayer.value;
        if (!hasDriversLayer || !map.value.getLayer(driversLayer.value)) {
            console.log('添加司机图层');
            addDriversLayer();
        }
        
        // 标记当前时间
        console.log(`处理${validPositions.length}个有效司机位置数据, 更新时间:`, new Date().toLocaleTimeString());
        
        // 更新司机位置
        updateDriversPositions(validPositions, autoZoom.value);
    };
    
    // 处理司机标记点击
    const handleDriverMarkerClick = (driver) => {
        // 选择司机 - 使用正确的方法selectDriver而不是selectDriverById
        driverStore.selectDriver(driver);
        
        // 触发事件
        eventBus.emit(EVENT_TYPES.DRIVER_SELECTED, driver.id);
    };
    
    // 处理司机标记鼠标进入
    const handleDriverMouseEnter = (marker, driver) => {
        try {
            // 如果已有弹窗，先移除
            if (activeDriverPopup.value) {
                activeDriverPopup.value.remove();
                activeDriverPopup.value = null;
            }
            
            // 创建弹窗元素
            const popupElement = document.createElement('div');
            popupElement.className = 'driver-popup-container';
            
            // 创建弹窗内容
            const popupApp = createApp({
                render() {
                    return h(DriverMarkerPopup, {
                        driver: driver
                    });
                }
            });
            
            // 挂载弹窗应用
            popupApp.mount(popupElement);
            
            // 创建Maplibre弹窗
            const popup = new maplibregl.Popup({
                closeButton: false,
                closeOnClick: false,
                offset: 20,
                className: 'driver-mini-popup',
                maxWidth: '150px'
            })
                .setLngLat(marker.getLngLat())
                .setDOMContent(popupElement)
                .addTo(map.value);
            
            // 保存弹窗引用
            activeDriverPopup.value = popup;
        } catch (error) {
            console.error('创建司机弹窗时出错:', error);
        }
    };
    
    // 处理司机标记鼠标离开
    const handleDriverMouseLeave = () => {
        // 鼠标移出时关闭弹窗
        if (activeDriverPopup.value) {
            activeDriverPopup.value.remove();
            activeDriverPopup.value = null;
        }
    };
    
    return {
        driverMarkers,
        showDrivers,
        activeDriverPopup,
        driversLayer,
        updateDriverMarkers,
        toggleDrivers,
        updateDriversPositions,
        onDriverPositionsUpdated,
        addDriversLayer,
        fitMapToDrivers,
        autoZoom
    };
} 