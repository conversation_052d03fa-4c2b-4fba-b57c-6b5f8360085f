<template>
    <div class="map-container">
        <div id="map" ref="mapContainer" style="width: 100%; height: 100%;" />
    
        <div class="route-type-control">
            <label>
                <input v-model="routeType" type="radio" value="direct" @change="redrawRoutes">
                直线路线
            </label>
            <label>
                <input v-model="routeType" type="radio" value="realistic" @change="redrawRoutes">
                真实道路
            </label>
        </div>
    
        <!-- 添加交通图层控制 -->
        <div class="traffic-control">
            <label>
                <input v-model="showTraffic" type="checkbox" @change="toggleTraffic">
                实时交通
            </label>
        </div>
    
        <!-- 添加自动缩放控制 -->
        <div class="zoom-control">
            <label>
                <input v-model="autoZoom" type="checkbox">
                自动缩放
            </label>
        </div>
    
        <!-- 添加矩形选择框 -->
        <div v-show="isDrawingSelectionBox" ref="selectionBox" class="selection-box" />
    
        <!-- 添加选择提示 -->
        <div v-show="showSelectionTooltip" class="selection-tooltip">
            {{ selectionTooltipContent }}
        </div>
    
        <div v-if="performanceStats" class="performance-stats">
            <div>FPS: {{ performanceStats.fps.toFixed(1) }}</div>
            <div>Markers: {{ performanceStats.markerCount }}</div>
            <div>Render Time: {{ performanceStats.renderTime.toFixed(1) }}ms</div>
            <div>Processing Time: {{ performanceStats.processingTime?.toFixed(1) || 0 }}ms</div>
            <div>Total Points: {{ performanceStats.totalPoints }}</div>
            <div>Visible Points: {{ performanceStats.visibleCount }}</div>
        </div>

        <!-- 添加司机位置显示控制 -->
        <div class="driver-control">
            <label>
                <input v-model="showDrivers" type="checkbox" @change="toggleDrivers">
                显示司机位置
            </label>
        </div>
        
        <!-- 添加地图图例 -->
        <div v-if="showDrivers" class="map-legend">
            <div class="legend-title">图例</div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #1E88E5;"></div>
                <div>司机位置</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4CAF50; border: 2px solid white;"></div>
                <div>在线司机</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #E91E63;"></div>
                <div>未分配订单</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4CAF50;"></div>
                <div>已分配订单</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, onUnmounted, createApp, h } from 'vue'
import { useMapStore } from '../../stores/map'
import { useOrderStore } from '../../stores/order'
import { useDriverStore } from '../../stores/driver'
import { useRouteStore } from '../../stores/route'
import { useTimeStore } from '../../stores/time'
import maplibregl from 'maplibre-gl'
import 'maplibre-gl/dist/maplibre-gl.css'
import { MAPTILER_API_KEY } from '../../config/keys'
import { eventBus, EVENT_TYPES } from '../../utils/eventBus'
import Marker from './Marker.vue'
import MarkerPopup from './MarkerPopup.vue'
import DriverMarker from './components/DriverMarker.vue'
import DriverMarkerPopup from './components/DriverMarkerPopup.vue'
import { debounce } from 'lodash'

const mapStore = useMapStore()
const orderStore = useOrderStore()
const driverStore = useDriverStore()
const routeStore = useRouteStore()
const timeStore = useTimeStore()

const map = ref(null)
const mapContainer = ref(null)
const isMapLoaded = ref(false)
const isHandlingListEvent = ref(false)
const isHandlingMapEvent = ref(false)
const isUpdatingMarkers = ref(false)
const isVisible = ref(true)
const popupCache = ref({})
const maxCachedPopups = 20
const lastClickTime = ref(0)
const clickDebounceTime = 300
const lastPopupTime = ref(0)
const popupDebounceTime = 500
const markers = ref(new Map())
const isUpdatingRoutes = ref(false)
const routeType = ref('direct') // 'direct' 或 'realistic'
// 添加交通图层控制
const showTraffic = ref(true) // 默认开启
const autoZoom = ref(false) // 默认关闭自动缩放
// 添加弹窗控制变量
const activePopup = ref(null)
const popupHoverTimer = ref(null)
const POPUP_HOVER_DELAY = 400 // 悬停400ms后显示弹窗

// 性能监控
const performanceStats = ref({
    fps: 0,
    markerCount: 0,
    renderTime: 0,
    processingTime: 0,
    totalPoints: 0,
    visibleCount: 0
})

// 添加订单过滤键
const ordersFilterKey = ref(Date.now().toString())

// 保存选中的订单列表
const selectedOrders = ref(new Set())

// 计算当前显示的订单
const currentOrders = computed(() => {
    console.log('计算当前订单:', {
        filteredOrders: window.filteredOrdersFromList,
        selectedRoute: routeStore.selectedRoute,
        selectedDriver: driverStore.selectedDriver,
        selectedOrder: orderStore.selectedOrder,
        selectedOrders: orderStore.selectedOrders?.length,
        allOrders: orderStore.allOrders?.length,
        showAllOrders: window.showAllOrders
    })
  
    // 如果有过滤的订单列表，优先使用
    if (window.filteredOrdersFromList && Array.isArray(window.filteredOrdersFromList)) {
        return window.filteredOrdersFromList
    }
  
    // 根据不同条件过滤订单
    if (routeStore.selectedRoute) {
        return orderStore.getOrdersByRouteNumber(routeStore.selectedRoute.id)
    } else if (driverStore.selectedDriver) {
        return orderStore.getDriverOrders(driverStore.selectedDriver.id)
    }
    // 当有选中的订单时，仍然保持与订单列表的过滤结果一致
    // 如果没有过滤结果，则根据showAllOrders决定显示所有订单还是只显示未分配订单
    else {
        // 先检查是否有之前的过滤结果
        if (window.lastFilteredOrdersFromList && Array.isArray(window.lastFilteredOrdersFromList)) {
            return window.lastFilteredOrdersFromList
        }
    
        // 检查全局显示模式标志
        if (window.showAllOrders === false) {
            // 仅显示未分配订单
            return orderStore.orders
        }
    
        // 显示所有订单
        return orderStore.allOrders
    }
})

// 路线图层ID前缀
const ROUTE_LAYER_ID_PREFIX = 'route-layer-'
const ROUTE_SOURCE_ID_PREFIX = 'route-source-'

// 添加TomTom API密钥
const TOMTOM_API_KEY = '********************************'
// 添加Mapbox API密钥
const MAPBOX_API_KEY = 'pk.eyJ1Ijoic2FzaGltaXRoZXB1ZyIsImEiOiJjbThxbnlzMTgwbjU4MmpxNDMzcTVrN3JqIn0.HKec-0aGPHdLlEtX7bn2jg'

// 添加全局路线请求队列和控制变量
const routeRequestQueue = ref([])
const isProcessingRouteQueue = ref(false)
const maxConcurrentRequests = 6 // 同时发送的最大请求数
const activeRequests = ref(0)
const lastRequestTime = ref(0)
const REQUEST_INTERVAL = 100 // 请求间隔，避免同时发送大量请求

// 添加矩形选择相关变量
const selectionBox = ref(null)
const isDrawingSelectionBox = ref(false)
const selectionStart = ref({ x: 0, y: 0 })
const selectionCurrent = ref({ x: 0, y: 0 })
const isCtrlPressed = ref(false)
const isShiftPressed = ref(false)
const selectionTooltip = ref(null)
const selectionTooltipContent = ref('按住Ctrl键并拖动鼠标可批量选择订单')
const showSelectionTooltip = ref(false)
const tooltipHideTimer = ref(null)

// 初始化地图
const initializeMap = () => {
    console.log('开始初始化地图')
  
    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: `https://api.maptiler.com/maps/streets/style.json?key=${MAPTILER_API_KEY}`,
        center: [-79.3832, 43.8532],
        zoom: 8,
        attributionControl: false,
        maxZoom: 22,
        minZoom: 3,
        dragRotate: false,
        pitchWithRotate: false,
        touchZoomRotate: true,
        trackResize: true,
        boxZoom: false, // 禁用默认的框选功能
        logoPosition: 'bottom-right',
        failIfMajorPerformanceCaveat: true,
        preserveDrawingBuffer: false,
        refreshExpiredTiles: false,
        fadeDuration: 0
    })

    // 监听样式加载完成事件
    map.value.on('style.load', () => {
        console.log('地图样式加载完成')
    
        // 检查并确保图层可见性
        if (map.value.getLayer('unassigned-orders-layer') && 
            map.value.getLayer('assigned-orders-layer') && 
            map.value.getLayer('selected-orders-layer')) {
      
            map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
            map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
            map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')
      
            // 刷新订单数据，确保在样式重新加载后标记仍然显示
            if (currentOrders.value.length > 0) {
                updateOrdersData(currentOrders.value)
            }
      
            // 确保标记图层在最上层
            moveSymbolLayersToTop()
        }
    })

    map.value.on('load', () => {
        console.log('地图加载完成')
        addMapControls()
        bindMapMarkerEventHandlers()
        isMapLoaded.value = true
        mapStore.setMapReady(true)
    
        // 添加交通图层
        addTrafficLayer()
    
        // 添加标记图标
        addMarkerImages()
    
        // 添加GeoJSON数据源
        addOrdersDataSource()
    
        // 如果有订单数据，创建标记
        const orders = currentOrders.value.filter(o => o && (o.lng_lat || o.location))
        if (orders.length > 0) {
            updateOrdersData(orders)
            fitMapBounds(orders)
            // 绘制路线
            drawRoutes(orders)
        }
    
        // 添加地图渲染完成后的标记可见性检查
        setTimeout(() => {
            if (map.value) {
                // 确保所有图层可见
                if (map.value.getLayer('unassigned-orders-layer')) {
                    map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
                }
                if (map.value.getLayer('assigned-orders-layer')) {
                    map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
                }
                if (map.value.getLayer('selected-orders-layer')) {
                    map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')
                }
        
                // 更新标记大小
                updateMarkerSizeForZoom()
            }
        }, 500)
    })
}

// 添加地图控件
const addMapControls = () => {
    if (!map.value) return
    
    // 添加全屏控件
    map.value.addControl(new maplibregl.FullscreenControl(), 'top-right')
    
    // 添加缩放控件
    map.value.addControl(new maplibregl.NavigationControl(), 'top-right')
    
    // 添加定位控件
    map.value.addControl(
      new maplibregl.GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true
      }),
      'top-right'
    )
    
    // 添加司机位置图层
    addDriversLayer()
    
    // 如果有司机位置数据，则立即显示
    const driversWithLocation = driverStore.driversWithLocation
    if (driversWithLocation.length > 0) {
      updateDriversPositions(driversWithLocation)
    }
}

// 创建并显示标记
const createAndDisplayMarkers = orders => {
    if (isUpdatingMarkers.value || !map.value || !isMapLoaded.value) {
        console.log('标记更新被跳过: 当前正在更新或地图未就绪')
        return
    }
  
    isUpdatingMarkers.value = true
    const startTime = performance.now()
  
    try {
        console.log('开始创建标记，订单数量:', orders.length)
        // 打印详细日志，帮助调试
        console.log('地图显示状态:', { 
            showAllOrders: window.showAllOrders,
            hasFiltered: window.filteredOrdersFromList ? window.filteredOrdersFromList.length : 0,
            currentOrdersLength: currentOrders.value.length,
            ordersToDisplayLength: orders.length
        })
    
        clearMarkers()
        // 清除路线
        clearRoutes()
    
        const validOrders = orders.filter(order => order && (order.lng_lat || order.location))
        console.log('有效订单数量:', validOrders.length)
    
        if (validOrders.length > 0) {
            validOrders.forEach(order => {
                const coords = order.lng_lat || order.location
                if (!coords || !Array.isArray(coords) || coords.length !== 2) {
                    console.log('无效坐标:', order.id, coords)
                    return
                }
        
                // 注意：我们的坐标是 [lat, lng] 格式，而 MapLibre 需要 [lng, lat] 格式
                const [lat, lng] = coords
                if (isNaN(lat) || isNaN(lng)) {
                    console.log('坐标包含无效值:', order.id, coords)
                    return
                }
        
                // 创建标记
                const marker = new maplibregl.Marker({
                    draggable: false, // 禁用拖拽功能
                    scale: 1.0, // 缩小标记
                    element: createCustomMarkerElement(order)
                })
                    .setLngLat([lng, lat])
                    .addTo(map.value)
        
                // 添加点击事件
                marker.on('click', e => {
                    // 阻止事件冒泡，防止地图的点击事件被触发
                    e.preventDefault()
                    e.stopPropagation()
                    // 注意：标记点击事件现在由内部的Marker组件处理
                })
        
                // 存储标记引用
                markers.value.set(order.id, marker)
            })
      
            // 绘制路线
            drawRoutes(validOrders)
      
            // 调整地图视野以适应所有标记
            fitMapBounds(validOrders)
        }
    
        performanceStats.value.renderTime = performance.now() - startTime
        performanceStats.value.markerCount = validOrders.length
        console.log('标记创建完成，总数:', performanceStats.value.markerCount)
    } catch (error) {
        console.error('创建标记时出错:', error)
    } finally {
        isUpdatingMarkers.value = false
    }
}

// 获取标记颜色
const getMarkerColor = order => {
    // 如果订单已分配给司机，显示司机的颜色
    if (order.driver_id) {
        // 从driverStore获取司机颜色
        const driver = driverStore.getDriverById(order.driver_id)
        return driver?.color || '#FF9800' // 如果找不到司机，使用默认橙色
    }
  
    // 未分配的订单显示灰色
    return '#9E9E9E'
}

// 创建自定义标记元素
const createCustomMarkerElement = order => {
    // 使用Vue组件创建DOM
    const app = document.createElement('div')
  
    // 检查订单是否已分配
    const isAssigned = !!order.driver_id
  
    // 将stop_no转换为字符串类型，防止类型错误
    const stopText = isAssigned ? (order.stop_no ? String(order.stop_no) : '') : ''
  
    // 检查是否为选中的订单 - 使用orderStore中的数据确保最新状态
    const isSelected = orderStore.selectedOrderIds.has(order.id)
  
    // 打印调试信息
    console.log('创建标记元素状态:', {
        id: order.id,
        isSelected: isSelected,
        originalSelected: order.isSelected,
        inStore: orderStore.selectedOrderIds.has(order.id)
    })
  
    const orderData = {
        id: order.id,
        text: stopText, // 确保text始终是字符串
        markerColor: getMarkerColor(order),
        isAssigned: isAssigned,
        isSelected: isSelected // 使用从orderStore获取的选中状态
    }
  
    // 保存订单数据到DOM元素，便于后续更新
    app._orderData = orderData
  
    // 挂载自定义Marker组件并监听点击事件
    const instance = createApp({
        render() {
            return h(Marker, {
                ...orderData,
                onClickMarker: data => {
                    console.log('Marker点击事件 - 检测Ctrl键:', data.id)
          
                    // 使用防抖，避免重复点击
                    const now = Date.now()
                    if (now - lastClickTime.value < clickDebounceTime) {
                        console.log('点击过于频繁，忽略此次点击')
                        return
                    }
                    lastClickTime.value = now
          
                    // 检查是否按下了Ctrl键（多选模式）
                    // 注意：通过window.event获取当前点击事件
                    const isMultiSelect = window.event && (window.event.ctrlKey || window.event.metaKey)
                    console.log('是否按下Ctrl键:', isMultiSelect)
          
                    if (isMultiSelect) {
                        // Ctrl键多选模式：切换当前订单选择状态
                        orderStore.toggleOrderSelection(data.id)
                    } else {
                        // 单选模式：先清除所有选择，再选中当前订单
                        // 如果当前订单已经选中，则清除选择
                        const isCurrentlySelected = orderStore.selectedOrderIds.has(data.id)
            
                        if (isCurrentlySelected) {
                            // 如果已经选中，则清除选择
                            orderStore.clearSelection()
                        } else {
                            // 先清除所有选择，再选中当前订单
                            orderStore.clearSelection()
                            orderStore.toggleOrderSelection(data.id)
                        }
                    }
          
                    // 强制更新当前marker
                    setTimeout(() => {
                        updateMarkerSelection(data.id, orderStore.selectedOrderIds.has(data.id))
                    }, 50)
                }
            })
        }
    })
  
    // 添加鼠标悬停事件处理
    app.addEventListener('mouseenter', () => handleMarkerMouseEnter(order))
    app.addEventListener('mouseleave', handleMarkerMouseLeave)
  
    instance.mount(app)
  
    // 返回包含Vue组件的DOM元素
    return app
}

// 处理标记鼠标悬停事件
const handleMarkerMouseEnter = order => {
    // 清除任何之前的定时器
    if (popupHoverTimer.value) {
        clearTimeout(popupHoverTimer.value)
    }
  
    // 设置新的定时器，在POPUP_HOVER_DELAY毫秒后显示弹窗
    popupHoverTimer.value = setTimeout(() => {
        showOrderPopup(order)
    }, POPUP_HOVER_DELAY)
}

// 处理标记鼠标离开事件
const handleMarkerMouseLeave = () => {
    // 清除定时器
    if (popupHoverTimer.value) {
        clearTimeout(popupHoverTimer.value)
        popupHoverTimer.value = null
    }
  
    // 设置一个短延时，允许鼠标移动到弹窗上
    setTimeout(() => {
        // 检查鼠标是否已移动到弹窗上
        if (activePopup.value) {
            const popupElement = document.querySelector('.maplibregl-popup')
            if (popupElement && !popupElement.matches(':hover')) {
                hideOrderPopup()
            }
        }
    }, 100)
}

// 显示订单弹窗
const showOrderPopup = order => {
    // 如果已有弹窗，先关闭
    hideOrderPopup()
  
    // 检查订单数据
    if (!order || !map.value) return
  
    // 获取坐标
    const coords = order.lng_lat || order.location
    if (!coords || !Array.isArray(coords) || coords.length !== 2) return
  
    // 获取该点位所有订单
    const orders = getOrdersAtLocation(coords)
  
    if (orders.length === 0) return
  
    // 创建弹窗数据对象
    const popupData = {
        orders: {
            pickup: orders.filter(o => o.type === 'pickup'),
            delivery: orders.filter(o => o.type !== 'pickup')
        }
    }
  
    // 创建弹窗元素
    const popupElement = document.createElement('div')
    popupElement.className = 'marker-popup-container'
  
    // 使用Vue组件渲染弹窗内容
    const popupApp = createApp({
        render() {
            return h(MarkerPopup, {
                point: popupData
            })
        }
    })
  
    // 挂载组件
    popupApp.mount(popupElement)
  
    // 计算最佳弹窗位置
    const anchor = calculateBestAnchor([coords[1], coords[0]])
  
    // 创建MapLibre弹窗
    activePopup.value = new maplibregl.Popup({
        closeButton: false,
        closeOnClick: false,
        maxWidth: '250px',
        offset: getPopupOffset(anchor),
        className: 'marker-hover-popup',
        anchor: anchor,
        trackPointer: false
    })
        .setLngLat([coords[1], coords[0]])
        .setDOMContent(popupElement)
        .addTo(map.value)
  
    // 检查弹窗是否在可视范围内
    setTimeout(() => {
        ensurePopupInView()
    }, 10)
  
    // 添加鼠标事件到弹窗
    const popupDomElement = document.querySelector('.marker-hover-popup')
    if (popupDomElement) {
        popupDomElement.addEventListener('mouseenter', () => {
            // 鼠标进入弹窗时保持显示
            if (popupHoverTimer.value) {
                clearTimeout(popupHoverTimer.value)
                popupHoverTimer.value = null
            }
        })
    
        popupDomElement.addEventListener('mouseleave', () => {
            // 鼠标离开弹窗时关闭
            hideOrderPopup()
        })
    }
}

// 计算弹窗最佳锚点位置
const calculateBestAnchor = lngLat => {
    if (!map.value) return 'top'  // 默认在标记下方
  
    // 获取地图容器尺寸
    const mapContainer = map.value.getContainer()
    const mapHeight = mapContainer.offsetHeight
  
    // 获取点在地图上的像素坐标
    const pointPixel = map.value.project(lngLat)
  
    // 估算弹窗高度（通常在150px左右）
    const estimatedPopupHeight = 180
  
    // 计算上下方可用空间
    const spaceAbove = pointPixel.y
    const spaceBelow = mapHeight - pointPixel.y
  
    // 首先确定垂直方向位置
    let anchor
    if (spaceBelow >= estimatedPopupHeight) {
        // 如果下方空间足够，优先放在下方（使用top锚点）
        anchor = 'top'
    } else if (spaceAbove >= estimatedPopupHeight) {
        // 如果上方空间足够，放在上方（使用bottom锚点）
        anchor = 'bottom'
    } else {
        // 两边都不够，选择空间较大的方向
        anchor = spaceBelow > spaceAbove ? 'top' : 'bottom'
    }
  
    return anchor
}

// 根据不同锚点返回不同的偏移量
const getPopupOffset = anchor => {
    switch (anchor) {
        case 'bottom':  // 弹窗在上方
            return [0, 10] 
        case 'top':     // 弹窗在下方
        default:
            return [0, -10]
    }
}

// 确保弹窗在可视范围内
const ensurePopupInView = () => {
    if (!activePopup.value || !map.value) return
  
    const popupElement = document.querySelector('.marker-hover-popup')
    if (!popupElement) return
  
    // 获取地图容器
    const mapContainer = map.value.getContainer()
    const mapRect = mapContainer.getBoundingClientRect()
  
    // 获取弹窗位置
    const popupRect = popupElement.getBoundingClientRect()
  
    // 计算弹窗是否超出视图
    const isOutOfTop = popupRect.top < mapRect.top
    const isOutOfBottom = popupRect.bottom > mapRect.bottom
    const isOutOfLeft = popupRect.left < mapRect.left
    const isOutOfRight = popupRect.right > mapRect.right
  
    if (isOutOfTop || isOutOfBottom || isOutOfLeft || isOutOfRight) {
        console.log('弹窗超出可视范围，调整位置')
    
        // 获取弹窗绑定的坐标
        const lngLat = activePopup.value.getLngLat()
    
        // 保存原始DOM内容
        const originalContent = popupElement.innerHTML
    
        // 移除现有弹窗
        activePopup.value.remove()
    
        // 重新创建弹窗，使用不同的锚点
        let anchor
    
        // 优先处理垂直方向
        if (isOutOfTop) {
            // 如果弹窗超出顶部，改为显示在标记下方（使用top锚点）
            anchor = 'top'
        } else if (isOutOfBottom) {
            // 如果弹窗超出底部，改为显示在标记上方（使用bottom锚点）
            anchor = 'bottom'
        } else {
            // 保持当前垂直锚点
            anchor = activePopup.value.options.anchor?.split('-')[0] || 'top'
        }
    
        // 处理水平方向
        if (isOutOfLeft) {
            // 左侧空间不足，向右偏移
            anchor = anchor + '-left'
        } else if (isOutOfRight) {
            // 右侧空间不足，向左偏移
            anchor = anchor + '-right'
        }
    
        console.log('调整后的锚点:', anchor)
    
        // 创建新的内容元素
        const newContentElement = document.createElement('div')
        newContentElement.innerHTML = originalContent
    
        // 重新创建弹窗
        activePopup.value = new maplibregl.Popup({
            closeButton: false,
            closeOnClick: false,
            maxWidth: '250px',
            offset: getPopupOffset(anchor),
            className: 'marker-hover-popup',
            anchor: anchor
        })
            .setLngLat(lngLat)
            .setDOMContent(newContentElement)
            .addTo(map.value)
    
        // 添加鼠标事件到弹窗
        const newPopupElement = document.querySelector('.marker-hover-popup')
        if (newPopupElement) {
            newPopupElement.addEventListener('mouseenter', () => {
                if (popupHoverTimer.value) {
                    clearTimeout(popupHoverTimer.value)
                    popupHoverTimer.value = null
                }
            })
      
            newPopupElement.addEventListener('mouseleave', () => {
                hideOrderPopup()
            })
        }
    
        // 如果弹窗依然超出视图，可以尝试平移地图
        setTimeout(() => {
            const newPopupRect = document.querySelector('.marker-hover-popup')?.getBoundingClientRect()
            if (!newPopupRect) return
      
            const newIsOutOfView = 
                newPopupRect.top < mapRect.top ||
                newPopupRect.bottom > mapRect.bottom ||
                newPopupRect.left < mapRect.left ||
                newPopupRect.right > mapRect.right
      
            if (newIsOutOfView) {
                // 计算需要平移的距离
                let dx = 0
                let dy = 0
        
                if (newPopupRect.top < mapRect.top) {
                    dy = mapRect.top - newPopupRect.top + 10
                } else if (newPopupRect.bottom > mapRect.bottom) {
                    dy = mapRect.bottom - newPopupRect.bottom - 10
                }
        
                if (newPopupRect.left < mapRect.left) {
                    dx = mapRect.left - newPopupRect.left + 10
                } else if (newPopupRect.right > mapRect.right) {
                    dx = mapRect.right - newPopupRect.right - 10
                }
        
                // 平移地图
                if (dx !== 0 || dy !== 0) {
                    map.value.panBy([dx, dy], { duration: 300 })
                }
            }
        }, 50)
    }
}

// 隐藏订单弹窗
const hideOrderPopup = () => {
    if (activePopup.value) {
        activePopup.value.remove()
        activePopup.value = null
    }
}

// 获取指定位置的所有订单
const getOrdersAtLocation = location => {
    if (!location || !Array.isArray(location)) return []
  
    // 查找具有相同或非常接近坐标的所有订单
    return currentOrders.value.filter(order => {
        const orderCoords = order.lng_lat || order.location
        if (!orderCoords || !Array.isArray(orderCoords)) return false
    
        // 检查坐标是否非常接近（允许微小差异）
        const latDiff = Math.abs(orderCoords[0] - location[0])
        const lngDiff = Math.abs(orderCoords[1] - location[1])
    
        // 如果坐标差异小于阈值，认为是同一位置
        return latDiff < 0.0001 && lngDiff < 0.0001
    })
}

// 更新标记元素而不重新创建
const updateMarkerElement = (markerElement, order) => {
    if (!markerElement || !markerElement._orderData) return

    // 检查订单是否已分配
    const isAssigned = !!order.driver_id
  
    // 将stop_no转换为字符串类型，防止类型错误
    const stopText = isAssigned ? (order.stop_no ? String(order.stop_no) : '') : ''
  
    // 获取之前的数据
    const prevData = markerElement._orderData
  
    // 创建新的数据 - 确保使用正确的isSelected值
    const isSelected = orderStore.selectedOrderIds.has(order.id)
  
    const newData = {
        id: order.id,
        text: stopText, // 确保text始终是字符串
        markerColor: getMarkerColor(order),
        isAssigned: isAssigned,
        isSelected: isSelected // 始终使用最新的选中状态
    }
  
    // 检查是否有变化
    const hasChanged = 
        prevData.text !== newData.text ||
        prevData.markerColor !== newData.markerColor ||
        prevData.isAssigned !== newData.isAssigned ||
        prevData.isSelected !== newData.isSelected
  
    // 如果有变化，才更新DOM
    if (hasChanged) {
        console.log('标记数据发生变化，更新元素:', order.id, '选中状态:', newData.isSelected)
    
        // 直接修改DOM元素显示选中状态，而不是完全替换元素
        // 这避免了MapLibre的标记元素被替换导致的问题
        try {
            // 保存新数据到元素
            markerElement._orderData = newData
      
            // 找到marker内部的自定义元素
            const customMarker = markerElement.querySelector('.custom-marker')
            if (customMarker) {
                // 添加或移除选中类
                if (newData.isSelected) {
                    customMarker.classList.add('selected')
                } else {
                    customMarker.classList.remove('selected')
                }
        
                return true
            } else {
                // 如果找不到自定义元素，则完全替换DOM
                const newElement = createCustomMarkerElement(order)
                const parent = markerElement.parentNode
        
                if (parent) {
                    parent.replaceChild(newElement, markerElement)
                    return true
                }
            }
        } catch (error) {
            console.error('直接修改DOM失败，回退到替换整个元素:', error)
            // 如果直接修改DOM失败，回退到之前的方式：完全替换元素
            const newElement = createCustomMarkerElement(order)
            const parent = markerElement.parentNode
      
            if (parent) {
                parent.replaceChild(newElement, markerElement)
                return true
            }
        }
    }
  
    return false
}

// 更新标记选择状态 - 优化版本
const updateMarkerSelection = (orderId, isSelected) => {
    const marker = markers.value?.get(orderId)
    if (marker) {
        // 从当前显示的订单中查找订单
        const order = currentOrders.value.find(o => o.id === orderId)
        if (order) {
            // 获取当前标记元素
            const markerElement = marker.getElement()

            console.log('正在更新标记选择状态:', orderId, isSelected)
      
            // 更新标记元素
            const updated = updateMarkerElement(markerElement, {
                ...order,
                isSelected: isSelected
            })
      
            // 如果没有成功更新DOM，强制重新创建标记
            if (!updated) {
                console.log('DOM更新失败，重新创建标记:', orderId)
        
                // 移除旧标记
                marker.remove()
        
                // 创建新标记
                const coords = order.lng_lat || order.location
                if (coords && Array.isArray(coords) && coords.length === 2) {
                    const [lat, lng] = coords
          
                    const newMarker = new maplibregl.Marker({
                        draggable: false, // 禁用拖拽功能
                        scale: 1.0,
                        element: createCustomMarkerElement({
                            ...order,
                            isSelected: isSelected
                        })
                    })
                        .setLngLat([lng, lat])
                        .addTo(map.value)
          
                    // 更新标记引用
                    markers.value.set(orderId, newMarker)
                }
            }
      
            // 更新选中订单集合
            if (isSelected) {
                selectedOrders.value.add(orderId)
            } else {
                selectedOrders.value.delete(orderId)
            }
        }
    } else {
        console.warn('找不到要更新的标记:', orderId)
    }
}

// 添加CSS样式到文档
const addMarkerStyles = () => {
    // 检查样式是否已存在
    if (document.getElementById('dynamic-marker-styles')) return
  
    // 创建样式元素
    const style = document.createElement('style')
    style.id = 'dynamic-marker-styles'
    style.textContent = `
    .custom-marker {
      position: relative;
      cursor: pointer;
    }
    
    .custom-marker.selected {
      transform: scale(1.1);
      z-index: 10 !important;
    }
  `
  
    // 添加到文档头部
    document.head.appendChild(style)
}

// 从 store 更新所有标记的选择状态 - 优化版本
const updateMarkersSelectionsFromStore = () => {
    if (!markers.value) return
  
    // 只处理真正需要更新的标记
    let updatedCount = 0
  
    // 获取当前选中的订单ID集合
    const selectedOrderIds = orderStore.selectedOrderIds
  
    // 1. 先清除所有不再选中的标记
    selectedOrders.value.forEach(orderId => {
        if (!selectedOrderIds.has(orderId)) {
            // 取消选中该标记
            updateMarkerSelection(orderId, false)
            updatedCount++
        }
    })
  
    // 2. 再设置新选中的标记
    selectedOrderIds.forEach(orderId => {
        if (!selectedOrders.value.has(orderId)) {
            // 选中该标记
            updateMarkerSelection(orderId, true)
            updatedCount++
        }
    })
  
    console.log(`标记选择状态更新完成，共更新 ${updatedCount} 个标记`)
}

// 处理标记拖拽结束
const handleMarkerDragEnd = (orderId, newLngLat) => {
    // 从当前显示的订单中查找订单
    const order = currentOrders.value.find(o => o.id === orderId)
    if (order) {
        order.lng_lat = [newLngLat.lng, newLngLat.lat]
        // 触发事件通知其他组件
        eventBus.emit(EVENT_TYPES.ORDER_LOCATION_UPDATED, {
            orderId,
            newLocation: order.lng_lat
        })
    }
}

// 清除标记
const clearMarkers = () => {
    if (!map.value) return
  
    try {
        if (markers.value) {
            markers.value.forEach(marker => marker.remove())
            markers.value.clear()
        }
    } catch (error) {
        console.error('清除标记时出错:', error)
    }
}

// 绑定地图标记事件处理
const bindMapMarkerEventHandlers = () => {
    if (!map.value || !mapContainer.value) return
  
    console.log('绑定地图事件处理器')
  
    // 绑定地图点击事件
    map.value.on('click', handleMapClick)
  
    // 绑定鼠标事件以支持矩形选择
    mapContainer.value.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  
    // 绑定键盘事件
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)
  
    // 添加鼠标离开地图容器的事件处理
    mapContainer.value.addEventListener('mouseleave', handleMapMouseLeave)
  
    console.log('地图事件处理器绑定完成')
}

// 处理键盘按下事件
const handleKeyDown = e => {
    if (e.key === 'Control' || e.key === 'Meta') {
        isCtrlPressed.value = true
        selectionTooltipContent.value = '拖动鼠标框选区域可选择订单，重复框选相同区域会反选订单'
        showSelectionTooltip.value = true
    } else if (e.key === 'Shift') {
        isShiftPressed.value = true
        selectionTooltipContent.value = '按住Shift键可执行其他操作'
        showSelectionTooltip.value = true
    }
}

// 处理键盘释放事件
const handleKeyUp = e => {
    if (e.key === 'Control' || e.key === 'Meta') {
        isCtrlPressed.value = false
        selectionTooltipContent.value = '按住Ctrl键并拖动鼠标可批量选择或反选订单'
        showSelectionTooltip.value = true
        setTimeout(() => {
            showSelectionTooltip.value = false
        }, 2000)
    } else if (e.key === 'Shift') {
        isShiftPressed.value = false
    }
}

// 处理鼠标按下事件
const handleMouseDown = e => {
    // 只有当按住Ctrl键时才启动矩形选择
    if (isCtrlPressed.value) {
        e.preventDefault()
        e.stopPropagation()
    
        // 记录起始点
        const mapContainer = map.value.getCanvasContainer()
        const rect = mapContainer.getBoundingClientRect()
        selectionStart.value = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        }
        selectionCurrent.value = { ...selectionStart.value }
    
        // 显示选择框
        isDrawingSelectionBox.value = true
        updateSelectionBox()
    
        // 隐藏提示
        showSelectionTooltip.value = false
    
        // 添加鼠标移动和抬起事件
        document.addEventListener('mousemove', handleMouseMove)
        document.addEventListener('mouseup', handleMouseUp)
    }
}

// 处理鼠标移动 - 更新选择框
const handleMouseMove = e => {
    if (!isDrawingSelectionBox.value) return
  
    const mapContainer = map.value.getCanvasContainer()
    const rect = mapContainer.getBoundingClientRect()
    selectionCurrent.value = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
    }
  
    updateSelectionBox()
}

// 处理鼠标抬起 - 完成选择
const handleMouseUp = e => {
    if (!isDrawingSelectionBox.value) return
  
    // 阻止事件冒泡，防止触发地图的点击事件
    e.preventDefault()
    e.stopPropagation()
  
    // 移除事件监听
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  
    // 获取选择区域内的订单
    selectOrdersInBox()
  
    // 隐藏选择框
    isDrawingSelectionBox.value = false
  
    // 设置一个标志，防止接下来的点击事件清除选择
    lastClickTime.value = Date.now()
}

// 更新选择框位置和大小
const updateSelectionBox = () => {
    if (!selectionBox.value) return
  
    const left = Math.min(selectionStart.value.x, selectionCurrent.value.x)
    const top = Math.min(selectionStart.value.y, selectionCurrent.value.y)
    const width = Math.abs(selectionStart.value.x - selectionCurrent.value.x)
    const height = Math.abs(selectionStart.value.y - selectionCurrent.value.y)
  
    selectionBox.value.style.left = `${left}px`
    selectionBox.value.style.top = `${top}px`
    selectionBox.value.style.width = `${width}px`
    selectionBox.value.style.height = `${height}px`
  
    // 如果选择框太小，则忽略
    if (width < 5 || height < 5) {
        return
    }
}

// 选择框内的订单
const selectOrdersInBox = () => {
    if (!map.value || !isMapLoaded.value) return
  
    // 获取选择框大小，如果太小，则可能是意外点击，不执行选择
    const width = Math.abs(selectionStart.value.x - selectionCurrent.value.x)
    const height = Math.abs(selectionStart.value.y - selectionCurrent.value.y)
    if (width < 5 || height < 5) {
        return
    }
  
    // 计算选择框的地理范围
    const bounds = new maplibregl.LngLatBounds()
  
    // 添加各个角落的点
    bounds.extend(map.value.unproject([
        Math.min(selectionStart.value.x, selectionCurrent.value.x),
        Math.min(selectionStart.value.y, selectionCurrent.value.y)
    ]))
  
    bounds.extend(map.value.unproject([
        Math.max(selectionStart.value.x, selectionCurrent.value.x),
        Math.min(selectionStart.value.y, selectionCurrent.value.y)
    ]))
  
    bounds.extend(map.value.unproject([
        Math.min(selectionStart.value.x, selectionCurrent.value.x),
        Math.max(selectionStart.value.y, selectionCurrent.value.y)
    ]))
  
    bounds.extend(map.value.unproject([
        Math.max(selectionStart.value.x, selectionCurrent.value.x),
        Math.max(selectionStart.value.y, selectionCurrent.value.y)
    ]))
  
    console.log('框选区域经纬度范围:', bounds.toString())
  
    // 找出边界框内的所有订单
    const ordersInBox = currentOrders.value.filter(order => {
        const coords = order.lng_lat || order.location
        if (!coords || !Array.isArray(coords) || coords.length !== 2) return false
    
        // 注意：我们的坐标是 [lat, lng] 格式，而 MapLibre 需要 [lng, lat] 格式
        const [lat, lng] = coords
        const isInBounds = bounds.contains([lng, lat])
    
        if (isInBounds) {
            console.log(`订单 ${order.id} 在框选范围内, 坐标: [${lng}, ${lat}]`)
        }
    
        return isInBounds
    })
  
    console.log('选择框内的订单数量:', ordersInBox.length, '当前订单总数:', currentOrders.value.length)
  
    // 如果有订单，则处理选择
    if (ordersInBox.length > 0) {
        console.log('框选区域内的订单IDs:', ordersInBox.map(o => o.id))
    
        // 获取当前已选中的订单ID集合
        const currentSelectedIds = new Set(orderStore.selectedOrderIds)
        const newSelectedIds = new Set(currentSelectedIds) // 创建副本用于修改
    
        // 实现Windows风格的交互：
        // 1. 如果订单之前未选中，则选中它
        // 2. 如果订单之前已选中，则取消选中它
        ordersInBox.forEach(order => {
            if (currentSelectedIds.has(order.id)) {
                // 如果订单之前已选中，从新集合中移除（取消选中）
                newSelectedIds.delete(order.id)
                console.log(`取消选中已选订单: ${order.id}`)
            } else {
                // 如果订单之前未选中，添加到新集合（选中）
                newSelectedIds.add(order.id)
                console.log(`选中新订单: ${order.id}`)
            }
        })
    
        // 将新的选择集合应用到store
        const orderIdsArray = Array.from(newSelectedIds)
        console.log('更新后的所有选中订单IDs:', orderIdsArray, '共', orderIdsArray.length, '个')
    
        // 计算新选中和取消选中的数量
        const newlySelected = ordersInBox.filter(o => !currentSelectedIds.has(o.id)).length
        const newlyUnselected = ordersInBox.filter(o => currentSelectedIds.has(o.id)).length
    
        // 防止事件处理中断选择状态
        isHandlingListEvent.value = true
    
        try {
            // 使用selectOrders方法确保订单的isSelected属性被正确更新
            orderStore.selectOrders(orderIdsArray)
      
            // 触发批量选择事件，通知其他组件（如订单列表）更新
            eventBus.emit(EVENT_TYPES.ORDERS_BATCH_SELECTED, {
                orderIds: orderIdsArray,
                source: 'map'
            })
      
            // 强制更新标记选中状态
            setTimeout(() => {
                updateMarkersSelectionsFromStore()
            }, 50)
      
            // 显示选择成功提示
            let tooltipMessage = ''
            if (newlySelected > 0 && newlyUnselected > 0) {
                tooltipMessage = `选中 ${newlySelected} 个订单，取消选中 ${newlyUnselected} 个订单，总计选中 ${orderIdsArray.length} 个`
            } else if (newlySelected > 0) {
                tooltipMessage = `选中 ${newlySelected} 个新订单，总计选中 ${orderIdsArray.length} 个订单`
            } else if (newlyUnselected > 0) {
                tooltipMessage = `取消选中 ${newlyUnselected} 个订单，总计选中 ${orderIdsArray.length} 个订单`
            }
      
            selectionTooltipContent.value = tooltipMessage
            showSelectionTooltip.value = true
            if (tooltipHideTimer.value) {
                clearTimeout(tooltipHideTimer.value)
            }
            tooltipHideTimer.value = setTimeout(() => {
                showSelectionTooltip.value = false
            }, 2000)
        } finally {
            // 确保事件处理标志最终被重置
            setTimeout(() => {
                isHandlingListEvent.value = false
            }, 200)
        }
    }
}

// 调整地图视野
const fitMapBounds = orders => {
    // 如果自动缩放没有开启，直接返回
    if (!autoZoom.value) return
  
    if (!orders || orders.length === 0 || !map.value) return
  
    const coordinates = orders.reduce((acc, order) => {
        const coords = order.lng_lat || order.location
        if (coords && Array.isArray(coords) && coords.length === 2) {
            // 注意：我们的坐标是 [lat, lng] 格式，而 MapLibre 需要 [lng, lat] 格式
            const [lat, lng] = coords
            acc.push([lng, lat])
        }
        return acc
    }, [])
  
    if (coordinates.length === 0) return
  
    const bounds = coordinates.reduce((bounds, coord) => {
        return bounds.extend(coord)
    }, new maplibregl.LngLatBounds(coordinates[0], coordinates[0]))
  
    map.value.fitBounds(bounds, {
        padding: 50,
        maxZoom: 13
    })
}

// 事件处理 
// 这三个函数在后面有完整定义，此处注释掉避免重复定义
/*
const handleOrderSelectionChanged = (data) => {
  if (isHandlingMapEvent.value) return;
  
  try {
    isHandlingMapEvent.value = true;
    updateOrdersData(currentOrders.value);
  } finally {
    setTimeout(() => {
      isHandlingMapEvent.value = false;
    }, 100);
  }
};

const handleBatchOrderSelectionChanged = (data) => {
  if (isHandlingMapEvent.value) return;
  
  try {
    isHandlingMapEvent.value = true;
    updateOrdersData(currentOrders.value);
  } finally {
    setTimeout(() => {
      isHandlingMapEvent.value = false;
    }, 100);
  }
};

const handleOrdersSelectionCleared = () => {
  if (isHandlingMapEvent.value) return;
  
  try {
    isHandlingMapEvent.value = true;
    updateOrdersData(currentOrders.value);
  } finally {
    setTimeout(() => {
      isHandlingMapEvent.value = false;
    }, 100);
  }
};
*/

// 重新绘制路线（当路线类型变化时）
const redrawRoutes = () => {
    if (!map.value || !isMapLoaded.value) return
  
    // 重置路线请求队列
    routeRequestQueue.value = []
    isProcessingRouteQueue.value = false
    activeRequests.value = 0
  
    // 获取当前订单
    const validOrders = currentOrders.value.filter(o => o && (o.lng_lat || o.location))
  
    // 清除现有路线
    clearRoutes()
  
    // 重新绘制
    if (validOrders.length > 0) {
        drawRoutes(validOrders)
    }
}

// 绘制路线
const drawRoutes = orders => {
    if (!map.value || !isMapLoaded.value || isUpdatingRoutes.value) return
  
    isUpdatingRoutes.value = true
  
    try {
        console.log('开始绘制路线')
    
        // 按司机分组订单
        const ordersByDriver = {}
        orders.forEach(order => {
            if (order.driver_id && (order.lng_lat || order.location)) {
                if (!ordersByDriver[order.driver_id]) {
                    ordersByDriver[order.driver_id] = []
                }
                ordersByDriver[order.driver_id].push(order)
            }
        })
    
        // 为每个司机绘制路线
        Object.keys(ordersByDriver).forEach(driverId => {
            const driverOrders = ordersByDriver[driverId]
      
            // 按停靠点编号排序
            driverOrders.sort((a, b) => {
                const stopA = parseInt(a.stop_no) || 0
                const stopB = parseInt(b.stop_no) || 0
                return stopA - stopB
            })
      
            // 无论用户选择什么模式，先使用直线路线
            // 这样可以确保用户始终看到一些路线
            drawDirectRoute(driverId, driverOrders)
      
            // 如果用户选择了真实路线模式，添加到队列进行处理
            if (routeType.value === 'realistic') {
                // 使用优先级将请求加入队列
                // 分配一个优先级，较少点的路线优先处理（因为响应更快）
                const priority = 1000 - driverOrders.length // 点越少，优先级越高
        
                routeRequestQueue.value.push({
                    driverId,
                    driverOrders,
                    priority
                })
        
                // 如果队列处理器没有运行，启动它
                if (!isProcessingRouteQueue.value) {
                    processRouteQueue()
                }
            }
        })
    
        console.log('路线绘制任务已加入队列，直线路线绘制完成')
    
        // 确保标记图层在路线图层之上
        moveSymbolLayersToTop()
    } catch (error) {
        console.error('绘制路线时出错:', error)
    } finally {
        isUpdatingRoutes.value = false
    }
}

// 处理路线请求队列
const processRouteQueue = async() => {
    if (isProcessingRouteQueue.value) return
    isProcessingRouteQueue.value = true
  
    console.log(`开始处理路线队列，总共 ${routeRequestQueue.value.length} 条路线待处理`)
  
    try {
        // 按优先级排序队列
        routeRequestQueue.value.sort((a, b) => b.priority - a.priority)
    
        while (routeRequestQueue.value.length > 0) {
            // 检查是否达到最大并发请求数
            if (activeRequests.value >= maxConcurrentRequests) {
                // 等待一些请求完成
                await new Promise(resolve => setTimeout(resolve, 500))
                continue
            }
      
            // 处理下一个请求
            const request = routeRequestQueue.value.shift()
      
            // 增加活动请求计数
            activeRequests.value++
      
            // 使用Promise处理，确保无论成功或失败都减少活动请求计数
            drawRealisticRoute(request.driverId, request.driverOrders)
                .finally(() => {
                    // 完成后减少活动请求计数
                    activeRequests.value--
                })
      
            // 允许一些时间给事件循环处理其他任务
            // 添加一个小的延迟，给事件循环留出时间处理其他任务
            await new Promise(resolve => setTimeout(resolve, REQUEST_INTERVAL / 2)); // 使用请求间隔的一半作为延迟
        }
    } catch (error) {
        console.error('处理路线请求队列时出错:', error)
    } finally {
        console.log('路线队列处理完成')
        isProcessingRouteQueue.value = false
    }
}

// 绘制直线连接路线
const drawDirectRoute = (driverId, driverOrders) => {
    if (!map.value) return
  
    // 提取坐标
    const coordinates = driverOrders.map(order => {
        const coords = order.lng_lat || order.location
        // 注意：我们的坐标是 [lat, lng] 格式，而 MapLibre 需要 [lng, lat] 格式
        return [coords[1], coords[0]]
    })
  
    if (coordinates.length < 2) return // 至少需要两个点来绘制线
  
    // 获取路线颜色
    const driver = driverStore.getDriverById(driverId)
    const routeColor = driver?.color || '#FF9800'
  
    // 创建源和图层ID
    const sourceId = `${ROUTE_SOURCE_ID_PREFIX}${driverId}`
    const layerId = `${ROUTE_LAYER_ID_PREFIX}${driverId}`
  
    // 添加源
    if (map.value.getSource(sourceId)) {
        // 更新现有源
        const source = map.value.getSource(sourceId)
        source.setData({
            type: 'Feature',
            properties: {},
            geometry: {
                type: 'LineString',
                coordinates: coordinates
            }
        })
    } else {
        // 创建新源
        map.value.addSource(sourceId, {
            type: 'geojson',
            data: {
                type: 'Feature',
                properties: {},
                geometry: {
                    type: 'LineString',
                    coordinates: coordinates
                }
            }
        })
    
        // 添加图层
        map.value.addLayer({
            id: layerId,
            type: 'line',
            source: sourceId,
            layout: {
                'line-join': 'round',
                'line-cap': 'round'
            },
            paint: {
                'line-color': routeColor,
                'line-width': 3,
                'line-opacity': 0.8
            }
        })
    }
}

// 绘制真实道路路线
const drawRealisticRoute = async(driverId, driverOrders) => {
    // Capture the context ID when this specific request starts
    const requestContextId = routeRequestContextId.value;

    // Initial check (optional but good practice)
    if (requestContextId !== routeRequestContextId.value) {
        console.log(`绘制真实路线 ${driverId} (Ctx ${requestContextId}) 立即中止: 上下文已过时`);
        return null;
    }

    if (!map.value || driverOrders.length < 2) return null

    const driver = driverStore.getDriverById(driverId)
    const routeColor = driver?.color || '#FF9800'

    // 创建源和图层ID
    const sourceId = `${ROUTE_SOURCE_ID_PREFIX}${driverId}`
    const layerId = `${ROUTE_LAYER_ID_PREFIX}${driverId}`

    try {
        // 准备所有路段的坐标数组
        let allSegments = []

        console.log(`开始绘制司机 ${driverId} 的真实路线 (上下文 ${requestContextId})`);

        // 初始化地图数据源，确保从开始就能更新路线
        // 使用第一个点初始化路线，后续动态更新
        const firstPoint = driverOrders[0].lng_lat || driverOrders[0].location
        if (firstPoint && Array.isArray(firstPoint) && firstPoint.length === 2) {
            allSegments.push([firstPoint[1], firstPoint[0]])

            // 创建或更新数据源
            if (map.value.getSource(sourceId)) {
                // 更新现有源
                map.value.getSource(sourceId).setData({
                    type: 'Feature',
                    properties: {},
                    geometry: {
                        type: 'LineString',
                        coordinates: allSegments
                    }
                })
            } else {
                // 创建新源
                map.value.addSource(sourceId, {
                    type: 'geojson',
                    data: {
                        type: 'Feature',
                        properties: {},
                        geometry: {
                            type: 'LineString',
                            coordinates: allSegments
                        }
                    }
                })

                // 添加图层
                map.value.addLayer({
                    id: layerId,
                    type: 'line',
                    source: sourceId,
                    layout: {
                        'line-join': 'round',
                        'line-cap': 'round'
                    },
                    paint: {
                        'line-color': routeColor,
                        'line-width': 3,
                        'line-opacity': 0.8
                    }
                })
            }
        }

        // Modify updateRouteOnMap to check context
        // 动态更新函数 - 用于在处理过程中实时更新地图上的路线
        const updateRouteOnMap = () => {
             // Check global context before updating map
             if (requestContextId !== routeRequestContextId.value) {
                  console.log(`更新地图路线 ${driverId} (Ctx ${requestContextId}) 中止: 上下文已过时 (当前 ${routeRequestContextId.value})`);
                  return;
             }
            if (map.value && map.value.getSource(sourceId) && allSegments.length > 1) {
                map.value.getSource(sourceId).setData({
                    type: 'Feature',
                    properties: {},
                    geometry: {
                        type: 'LineString',
                        coordinates: allSegments
                    }
                })
            }
        }

        // Modify processRouteBetweenPoints to check context
        // 处理一对点之间的路线的函数
        const processRouteBetweenPoints = async(startPoint, endPoint, index, totalPairs) => {
             // Check context at the beginning
             if (requestContextId !== routeRequestContextId.value) {
                 console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 中止: 上下文已过时`);
                 return null;
             }

            const startCoords = startPoint.lng_lat || startPoint.location
            const endCoords = endPoint.lng_lat || endPoint.location

            if (!startCoords || !endCoords) return null // Return null if coords invalid

            // 检查起点和终点是否相同或距离太近
            const isSamePoint =
                Math.abs(startCoords[0] - endCoords[0]) < 0.0001 &&
                Math.abs(startCoords[1] - endCoords[1]) < 0.0001

            if (isSamePoint) {
                  if (requestContextId !== routeRequestContextId.value) return null;
                 return [[startCoords[1], startCoords[0]]];
            }

            // 计算两点之间的距离
            const distance = calculateDistance(
                startCoords[0], startCoords[1],
                endCoords[0], endCoords[1]
            )

            // 如果距离过短(<100米)，直接使用直线连接，避免无效的API调用
            if (distance < 0.1) { // 距离小于100米
                console.log(`路段 ${index + 1}/${totalPairs}: 距离过短(${(distance * 1000).toFixed(0)}m)，使用直线连接`)
                  if (requestContextId !== routeRequestContextId.value) return null;
                 return [ [startCoords[1], startCoords[0]], [endCoords[1], endCoords[0]] ];
            }

            // API请求限流 - 确保请求间隔足够长
            const now = Date.now()
            const timeSinceLastRequest = now - lastRequestTime.value
            if (timeSinceLastRequest < REQUEST_INTERVAL) {
                await new Promise(resolve =>
                    setTimeout(resolve, REQUEST_INTERVAL - timeSinceLastRequest)
                )
            }
            lastRequestTime.value = Date.now()

            // 使用Mapbox Directions API
            try {
                // 检查坐标有效性
                 if (!isValidCoordinate(startCoords) || !isValidCoordinate(endCoords)) {
                     console.warn('无效的坐标', startCoords, endCoords)
                      if (requestContextId !== routeRequestContextId.value) return null;
                      return [ [startCoords[1], startCoords[0]], [endCoords[1], endCoords[0]] ];
                 }

                // 注意：Mapbox API 使用 [经度,纬度] 格式
                const startLng = startCoords[1]  // 经度
                const startLat = startCoords[0]  // 纬度
                const endLng = endCoords[1]      // 经度
                const endLat = endCoords[0]      // 纬度

                // 构建Mapbox Directions API请求URL
                // 格式: /directions/v5/{profile}/{coordinates}
                const profile = 'mapbox/driving' // 驾车模式
                const coordinates = `${startLng},${startLat};${endLng},${endLat}`
                // 简化请求参数，只获取必要的数据
                const url = `https://api.mapbox.com/directions/v5/${profile}/${coordinates}?geometries=geojson&access_token=${MAPBOX_API_KEY}&overview=full&steps=false`

                console.log(`请求Mapbox路线数据 ${index + 1}/${totalPairs}: 从 [${startLat.toFixed(6)},${startLng.toFixed(6)}] 到 [${endLat.toFixed(6)},${endLng.toFixed(6)}] (Ctx ${requestContextId})`)

                // 添加超时时间和重试逻辑
                let retries = 0
                const maxRetries = 2
                let success = false
                let data

                while (retries <= maxRetries && !success) {
                      // Check context before each retry
                      if (requestContextId !== routeRequestContextId.value) {
                          console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 重试中止: 上下文已过时`);
                          return null;
                      }
                    try {
                        // 使用较长的超时时间
                        const controller = new AbortController()
                        const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

                        const response = await fetch(url, {
                            method: 'GET',
                            signal: controller.signal,
                            headers: {
                                'Accept': 'application/json'
                            }
                        })

                        clearTimeout(timeoutId)

                        if (!response.ok) {
                            const errStatus = response.status
                            let errText = await response.text()

                            // 如果是429（请求过多），等待更长时间后重试
                            if (errStatus === 429 && retries < maxRetries) {
                                console.log(`请求限制(429)，等待重试... (${retries + 1}/${maxRetries + 1})`)
                                // 指数退避策略
                                await new Promise(resolve => setTimeout(resolve, 2000 * Math.pow(2, retries)))
                                retries++
                                continue
                            }

                            throw new Error(`Mapbox API请求失败: ${errStatus} ${errText}`)
                        }

                        data = await response.json()
                        success = true
                    } catch (e) {
                        if (e.name === 'AbortError') {
                            console.log('请求超时，正在重试...')
                        } else if (retries < maxRetries) {
                            console.log(`请求失败，重试中... (${retries + 1}/${maxRetries + 1})`, e)
                        } else {
                            throw e // Throw error after max retries
                        }
                        retries++

                        // 重试前等待
                        await new Promise(resolve => setTimeout(resolve, 1000 * retries))
                    }
                }

                 // Check context after fetch/retry loop
                 if (requestContextId !== routeRequestContextId.value) {
                      console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 结果丢弃: 上下文已过时`);
                      return null;
                 }

                 if (!success || !data) {
                      // Return fallback (no need to check context again here as it was checked just before)
                      console.log('无法获取Mapbox路线数据，使用直线连接')
                      return [ [startCoords[1], startCoords[0]], [endCoords[1], endCoords[0]] ];
                 }

                // 从Mapbox API响应提取路线坐标
                if (data.routes && data.routes.length > 0) {
                    const route = data.routes[0]

                    // 打印路线信息用于调试
                    console.log(`路线距离: ${(route.distance / 1000).toFixed(2)}公里，预计时间: ${(route.duration / 60).toFixed(2)}分钟`)

                    // Mapbox直接返回GeoJSON格式的geometry
                    if (route.geometry && route.geometry.coordinates) {
                        const coordinates = route.geometry.coordinates
                        const pointCount = coordinates.length

                        // 筛选有效坐标
                        const validCoordinates = coordinates.filter(coord =>
                            Array.isArray(coord) && coord.length >= 2 &&
                            !isNaN(coord[0]) && !isNaN(coord[1])
                        )

                        console.log(`路段 ${index + 1} 获取到 ${validCoordinates.length} 个有效点`)

                         // Return validCoordinates (no need to check context again)
                         return validCoordinates;
                    } else {
                         throw new Error('Mapbox响应中缺少路线几何数据'); // Or handle API error message
                    }
                } else {
                     throw new Error('未找到路线数据'); // Or handle API error message
                }
            } catch (error) {
                console.error('获取Mapbox路线失败:', error)
                  // Check context before returning fallback on error
                  if (requestContextId !== routeRequestContextId.value) {
                       console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 回退中止: 上下文已过时`);
                      return null;
                  }
                 // Return fallback line
                 console.log('回退到直线连接')
                 return [ [startCoords[1], startCoords[0]], [endCoords[1], endCoords[0]] ];
            }
        }; // end processRouteBetweenPoints

        // 创建路线点对数组
        const pointPairs = []
        for (let i = 0; i < driverOrders.length - 1; i++) {
            pointPairs.push({
                start: driverOrders[i],
                end: driverOrders[i + 1],
                index: i
            })
        }

        console.log(`司机 ${driverId} 路线点对总数: ${pointPairs.length} (Ctx ${requestContextId})`)

        // 将点对分组，每组最多4个点对，避免并发请求过多
        const batchSize = 4 // 由于全局队列控制，每批次处理4个即可
        const batches = []
        for (let i = 0; i < pointPairs.length; i += batchSize) {
            batches.push(pointPairs.slice(i, i + batchSize))
        }

        console.log(`司机 ${driverId} 路线分为 ${batches.length} 个批次处理 (Ctx ${requestContextId})`)

        // 跟踪进度
        let processedBatches = 0
        const totalBatches = batches.length

        // 按批次并行处理点对
        for (const batch of batches) {
             // Check context before processing a batch
             if (requestContextId !== routeRequestContextId.value) {
                 console.log(`处理批次 ${driverId} (Ctx ${requestContextId}) 中止: 上下文已过时`);
                 break; // Stop processing further batches
             }
            processedBatches++
            console.log(`处理司机 ${driverId} 批次 ${processedBatches}/${totalBatches}，包含 ${batch.length} 对点 (Ctx ${requestContextId})`)

            // 并行处理当前批次的所有点对
            const batchResults = await Promise.all(batch.map(pair =>
                processRouteBetweenPoints(pair.start, pair.end, pair.index, pointPairs.length)
            ));

             // Process results (check if segmentCoords is null)
             for (const segmentCoords of batchResults) {
                  if (segmentCoords) { // Only process if not null (i.e., context was valid)
                      if (allSegments.length > 0 && segmentCoords.length > 1) {
                          // If not the first segment, skip the first point (it should match the last point of the previous segment)
                          // Check for potential empty first segment
                          if (allSegments.length === 1 && allSegments[0].length === 2 && segmentCoords.length > 0) {
                                // Special case: if allSegments only has the start point, don't slice
                                allSegments.push(...segmentCoords);
                          } else {
                                allSegments.push(...segmentCoords.slice(1));
                          }
                      } else if (segmentCoords.length > 0) { // Handle the very first segment
                          allSegments.push(...segmentCoords);
                      }
                  }
             }
             // Update map (checks context internally)
             updateRouteOnMap();

             console.log(`司机 ${driverId} 进度: ${processedBatches}/${totalBatches} 批次完成 (${Math.round(processedBatches / totalBatches * 100)}%) (Ctx ${requestContextId})`);
        }

        // Final check before returning
        if (requestContextId !== routeRequestContextId.value) {
            console.log(`司机 ${driverId} (Ctx ${requestContextId}) 真实路线绘制完成，但上下文已过时`);
            return null;
        }

        // 确保至少有最后一个点
        const lastOrder = driverOrders[driverOrders.length - 1]
        const lastCoords = lastOrder.lng_lat || lastOrder.location
        if (lastCoords && allSegments.length > 0 &&
            (allSegments[allSegments.length - 1][0] !== lastCoords[1] ||
                allSegments[allSegments.length - 1][1] !== lastCoords[0])) {
            // Ensure last point is added only if context is still valid
             if (requestContextId === routeRequestContextId.value) { // Check context
                  allSegments.push([lastCoords[1], lastCoords[0]]);
             }
        }

        // 最终更新，确保显示完整路线 (checks context internally)
        updateRouteOnMap();

        console.log(`司机 ${driverId} 的真实路线绘制完成 (上下文 ${requestContextId})，总计 ${allSegments.length} 个点`);
        return allSegments; // 返回生成的路线点，便于调试

    } catch (error) {
        console.error('绘制真实路线时出错:', error);
        // Draw direct route fallback (only if context is still valid)
        if (requestContextId === routeRequestContextId.value) {
             drawDirectRoute(driverId, driverOrders);
        }
        return null;
    }
}; // end drawRealisticRoute

// 计算两点之间的距离（公里）
const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLon = (lon2 - lon1) * Math.PI / 180
    const a = 
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLon / 2) * Math.sin(dLon / 2) 
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)) 
    const distance = R * c
    return distance // 公里
}

// 检查坐标是否有效
const isValidCoordinate = coord => {
    if (!coord || !Array.isArray(coord) || coord.length !== 2) return false
  
    const [lat, lng] = coord
  
    // 检查纬度是否在有效范围内 (-90 到 90)
    if (lat < -90 || lat > 90) return false
  
    // 检查经度是否在有效范围内 (-180 到 180)
    if (lng < -180 || lng > 180) return false
  
    // 检查坐标是否为数字
    if (isNaN(lat) || isNaN(lng)) return false
  
    return true
}

// 清除路线
const clearRoutes = () => {
    if (!map.value) return
  
    try {
        // 查找所有路线图层并移除
        const layers = map.value.getStyle().layers
        if (layers) {
            layers.forEach(layer => {
                if (layer.id.startsWith(ROUTE_LAYER_ID_PREFIX)) {
                    map.value.removeLayer(layer.id)
                }
            })
        }
    
        // 查找所有路线源并移除
        const sources = map.value.getStyle().sources
        if (sources) {
            Object.keys(sources).forEach(sourceId => {
                if (sourceId.startsWith(ROUTE_SOURCE_ID_PREFIX)) {
                    map.value.removeSource(sourceId)
                }
            })
        }
    } catch (error) {
        console.error('清除路线时出错:', error)
    }
}

// 监听orderStore的各种变化，确保地图及时更新
watch(() => orderStore.orders.length, newLength => {
    // 如果没有选中司机且不显示所有订单时，订单列表变化后需要更新地图
    if (!driverStore.selectedDriver && window.showAllOrders === false) {
        console.log('未分配订单数量变化:', newLength)
        requestAnimationFrame(() => {
            updateOrdersData(currentOrders.value)
        })
    }
})

// 直接监听过滤订单变化，增加立即性和敏感度
watch(() => window.filteredOrdersFromList, newOrders => {
    if (newOrders && map.value && isMapLoaded.value) {
        console.log('监测到过滤订单变化，立即更新地图显示，订单数量:', newOrders.length)
        // 使用立即执行的方式处理
        requestAnimationFrame(() => {
            updateOrdersData(newOrders)
        })
    }
}, { deep: true, immediate: true })

// 直接监听当前显示订单的变化，确保地图及时更新
watch(() => currentOrders.value, newOrders => {
    if (map.value && isMapLoaded.value) {
        console.log('当前显示订单变化，立即更新地图显示，订单数量:', newOrders.length)
        // 使用立即执行的方式处理
        requestAnimationFrame(() => {
            updateOrdersData(newOrders)
        })
    }
}, { deep: false, immediate: true })

// 监听showAllOrders变化，确保模式切换时地图即时更新
watch(() => window.showAllOrders, newValue => {
    console.log('显示模式变化，更新地图显示:', newValue ? '显示所有订单' : '仅显示未分配订单')
  
    // 强制更新过滤键
    ordersFilterKey.value = Date.now().toString()
  
    if (map.value && isMapLoaded.value) {
        // 使用 requestAnimationFrame 确保在下一帧渲染
        requestAnimationFrame(() => {
            // 根据新的显示模式获取正确的订单列表
            const ordersToDisplay = newValue ? orderStore.allOrders : orderStore.orders
      
            // 确保有订单数据
            if (ordersToDisplay && ordersToDisplay.length > 0) {
                console.log('显示模式变化，更新订单显示，数量:', ordersToDisplay.length)
                updateOrdersData(ordersToDisplay)
        
                // 调整地图视图以显示所有订单
                const validOrders = ordersToDisplay.filter(order => order && (order.lng_lat || order.location))
                if (validOrders.length > 0) {
                    fitMapBounds(validOrders)
                }
            } else {
                console.log('显示模式变化，但没有可显示的订单')
                clearOrdersData()
            }
        })
    }
}, { immediate: true })

// 添加订单选中状态变化的监听器
watch(() => orderStore.selectedOrderIds, selectedIds => {
    console.log('订单选中状态变化:', Array.from(selectedIds))
  
    // 防止无限循环
    if (isHandlingListEvent.value) return
  
    try {
        isHandlingListEvent.value = true
        // 更新地图上标记的选择状态
        updateOrdersData(currentOrders.value)
    } finally {
        setTimeout(() => {
            isHandlingListEvent.value = false
        }, 100)
    }
}, { deep: true })

// 生命周期钩子
onMounted(() => {
    // 初始化地图
    initializeMap()
  
    // 添加标记样式
    addMarkerStyles()
  
    // 首先监听订单过滤事件，确保优先处理
    eventBus.on(EVENT_TYPES.ORDERS_FILTERED, handleOrdersFiltered)
  
    // 监听显示模式变化事件
    eventBus.on(EVENT_TYPES.DISPLAY_MODE_CHANGED, data => {
        console.log('地图收到显示模式变化事件:', data)
        window.showAllOrders = data.showAllOrders
    
        // 模式变化时立即更新地图
        if (map.value && isMapLoaded.value) {
            requestAnimationFrame(() => {
                updateOrdersData(currentOrders.value)
            })
        }
    })
  
    // 然后监听其他事件
    eventBus.on(EVENT_TYPES.ORDER_SELECTED, handleOrderSelectionChanged)
    eventBus.on(EVENT_TYPES.ORDERS_BATCH_SELECTED, handleBatchOrderSelectionChanged)
    eventBus.on(EVENT_TYPES.ORDERS_SELECTION_CLEARED, handleOrdersSelectionCleared)
    
    // 监听专门的订单取消分配事件
    eventBus.on(EVENT_TYPES.ORDERS_UNASSIGNED, data => {
        console.log('地图收到订单取消分配事件:', data)
    
        if (data && data.orders && Array.isArray(data.orders)) {
            // 获取已取消分配的订单ID列表
            const unassignedOrderIds = data.orders.map(order => order.id)
      
            // 更新地图数据以反映取消分配状态
            updateOrdersData(currentOrders.value)
      
            // 如果当前正在显示受影响的路线，重新绘制该路线
            if (routeType.value === 'route' && routeStore.selectedRoute) {
                const selectedRouteId = routeStore.selectedRoute.id
                if (data.affectedRouteIds && data.affectedRouteIds.includes(selectedRouteId)) {
                    console.log('当前显示的路线受到影响，重新绘制路线')
                    // 短暂延迟确保数据已更新
                    setTimeout(() => {
                        drawSelectedRouteLines(true)
                    }, 50)
                }
            }
      
            // 显示视觉提示（可选）
            showUnassignNotification(data.orders)
        }
    })
    
    // 监听专门的订单分配事件
    eventBus.on(EVENT_TYPES.ORDERS_ASSIGNED, data => {
        console.log('地图收到订单分配事件:', data)
    
        if (data && data.orders && Array.isArray(data.orders)) {
            // 获取已分配的订单ID列表
            const assignedOrderIds = data.orders.map(order => order.id)
      
            // 更新地图数据以反映分配状态
            updateOrdersData(currentOrders.value)
      
            // 如果当前正在显示相关路线，重新绘制该路线
            if (routeType.value === 'route' && routeStore.selectedRoute) {
                const selectedRouteId = routeStore.selectedRoute.id
                if (data.routeId === selectedRouteId) {
                    console.log('当前显示的路线收到新订单，重新绘制路线')
                    // 短暂延迟确保数据已更新
                    setTimeout(() => {
                        drawSelectedRouteLines(true)
                    }, 50)
                }
            }
      
            // 显示视觉提示
            showAssignNotification(data.orders, data.routeName || `Route-${data.routeId}`)
        }
    })
    
    // 监听订单更新事件（包括停靠点序号的更新）
    eventBus.on(EVENT_TYPES.ORDERS_UPDATED, data => {
        console.log('地图收到订单更新事件:', data)
    
        // 如果有更新的订单数据，立即刷新地图上的标记
        if (data && data.orders && Array.isArray(data.orders)) {
            // 处理取消分配的情况
            if (data.isUnassign) {
                console.log('处理取消分配事件，刷新地图视图')
        
                // 重置地图视图状态
                if (data.allOrders) {
                    // 全局刷新 - 重新获取当前显示的订单数据
                    const currentDisplayedOrders = filterOrdersBasedOnUIState()
          
                    // 强制刷新地图数据
                    setTimeout(() => {
                        updateOrdersData(currentDisplayedOrders)
                        // 如果当前显示路线，需要重绘路线
                        if (routeType.value === 'route' && routeStore.selectedRoute) {
                            drawSelectedRouteLines(true)
                        }
                    }, 100)
          
                    return
                }
            }
      
            // 更新当前路线的订单
            if (routeStore.selectedRoute && routeStore.selectedRoute.id === data.routeId) {
                // 立即重新绘制当前路线
                const routeOrders = data.orders.map(order => ({
                    ...order,
                    route_id: data.routeId
                }))
        
                // 立即执行，不使用requestAnimationFrame
                try {
                    // 立即更新标记显示
                    updateOrdersData(routeOrders)
          
                    // 立即重新绘制路线
                    if (routeType.value === 'route') {
                        drawSelectedRouteLines(true)
                    }
          
                    console.log('地图已立即更新显示新的订单顺序')
                } catch (error) {
                    console.error('立即更新地图显示失败，降级使用requestAnimationFrame:', error)
                    // 降级使用requestAnimationFrame
                    requestAnimationFrame(() => {
                        updateOrdersData(routeOrders)
                        if (routeType.value === 'route') {
                            drawSelectedRouteLines(true)
                        }
                    })
                }
            } else if (currentOrders.value.some(order => data.orders.some(o => o.id === order.id))) {
                // 当前显示的订单与更新的订单有交集，立即刷新
                try {
                    // 刷新缓存数据
                    ordersFilterKey.value = Date.now().toString()
          
                    // 延迟一点执行，确保数据已更新
                    setTimeout(() => {
                        // 如果是取消分配操作，可能需要重新获取当前订单列表
                        if (data.isUnassign) {
                            const updatedOrders = filterOrdersBasedOnUIState()
                            updateOrdersData(updatedOrders)
                        } else {
                            // 正常刷新当前显示
                            updateOrdersData(currentOrders.value)
                        }
                        console.log('地图已立即更新显示相关订单')
                    }, 50)
                } catch (error) {
                    console.error('立即更新相关订单显示失败，降级使用requestAnimationFrame:', error)
                    // 降级使用requestAnimationFrame
                    requestAnimationFrame(() => {
                        updateOrdersData(currentOrders.value)
                    })
                }
            }
        }
    })
    
    // 清理弹窗
    document.addEventListener('click', e => {
        if (!e.target.closest('.marker-hover-popup') && !e.target.closest('.custom-marker')) {
            hideOrderPopup()
        }
    })

    // 启动FPS监控
    let frameCount = 0
    let lastTime = performance.now()
  
    const updateFPS = () => {
        frameCount++
        const now = performance.now()
        const delta = now - lastTime
    
        if (delta >= 1000) {
            performanceStats.value.fps = (frameCount * 1000) / delta
            frameCount = 0
            lastTime = now
        }
    
        requestAnimationFrame(updateFPS)
    }
  
    updateFPS()
  
    // 确保初始时将选中状态同步到地图
    if (orderStore.selectedOrderIds.size > 0) {
        updateOrdersData(currentOrders.value)
    }

    // 确保解绑旧的监听器，防止重复绑定
    eventBus.off(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)
    
    // 添加监听器
    eventBus.on(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)
    console.log('已注册司机位置更新事件监听器', EVENT_TYPES.DRIVER_POSITIONS_UPDATED)

    // 添加周期性检查，但降低频率，避免过多更新
    const forceUpdateInterval = setInterval(() => {
        // 只在页面前台可见时执行，且至少2分钟才执行一次
        if (document.visibilityState === 'visible' && 
            driverStore.allDriverPositions && 
            driverStore.allDriverPositions.length > 0) {
            
            // 检查是否足够时间已经过去（防止过于频繁更新）
            const now = Date.now();
            const lastUpdateTime = window.__lastDriverPositionUpdate || 0;
            const timeSinceLastUpdate = now - lastUpdateTime;
            
            // 如果距离上次更新时间不到2分钟，则跳过
            if (timeSinceLastUpdate < 120000) {
                return;
            }
            
            console.log('周期性检查司机位置更新 (距上次更新:', Math.round(timeSinceLastUpdate/1000), '秒)');
            window.__lastDriverPositionUpdate = now;
            updateDriversPositions(driverStore.allDriverPositions);
        }
    }, 60000); // 每分钟检查一次更新条件，但实际更新间隔至少2分钟

    // 保存定时器引用以便清理
    window.driverPositionCheckTimer = forceUpdateInterval;

    // 清除旧的监听器
    eventBus.off(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated);
    
    // 添加监听器
    eventBus.on(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)
    console.log('已注册司机位置更新事件监听器', EVENT_TYPES.DRIVER_POSITIONS_UPDATED)

    // 直接请求一次司机位置，确保地图初始化后显示
    if (driverStore.allDriverPositions && driverStore.allDriverPositions.length > 0) {
        console.log('地图初始化后请求初始司机位置');
        updateDriversPositions(driverStore.allDriverPositions);
        // 记录初始更新时间
        window.__lastDriverPositionUpdate = Date.now();
    }

    // 在onMounted钩子中添加加载小车图标的代码
    onMounted(() => {
      // ... 保留原有代码

      // 加载小车图标
      const carColors = ['default', 'red', 'blue', 'green', 'yellow', 'purple', 'orange', 'cyan', 'magenta', 'black', 'white'];
      
      carColors.forEach(color => {
        // 根据不同颜色加载对应的小车图标
        const img = new Image();
        img.onload = () => {
          // 图片加载完成后，创建图标
          if (map.value) {
            map.value.addImage(`car-marker-${color}`, img);
            console.log(`已加载小车图标: car-marker-${color}`);
          }
        };
        // 设置图片源，可以使用不同颜色的小车图片
        img.src = `/images/car-icons/car-${color}.png`;
        
        // 备用方案：如果找不到对应颜色的图片，使用SVG创建
        img.onerror = () => {
          console.log(`找不到小车图片: car-${color}.png, 使用SVG创建`);
          
          // 创建一个SVG小车图标
          const size = 24;
          const canvas = document.createElement('canvas');
          canvas.width = size;
          canvas.height = size;
          const ctx = canvas.getContext('2d');
          
          // 基于颜色选择填充颜色
          let fillColor;
          switch(color) {
            case 'red': fillColor = '#ff5252'; break;
            case 'blue': fillColor = '#4285f4'; break;
            case 'green': fillColor = '#0f9d58'; break;
            case 'yellow': fillColor = '#ffeb3b'; break;
            case 'purple': fillColor = '#673ab7'; break;
            case 'orange': fillColor = '#ff9800'; break;
            case 'cyan': fillColor = '#00bcd4'; break;
            case 'magenta': fillColor = '#e91e63'; break;
            case 'black': fillColor = '#212121'; break;
            case 'white': fillColor = '#f5f5f5'; break;
            default: fillColor = '#757575'; break;
          }
          
          // 绘制小车图标
          ctx.fillStyle = fillColor;
          
          // 车身
          ctx.beginPath();
          ctx.roundRect(5, 8, 14, 10, 2);
          ctx.fill();
          
          // 车顶
          ctx.beginPath();
          ctx.roundRect(8, 4, 8, 6, 2);
          ctx.fill();
          
          // 车轮
          ctx.fillStyle = '#333';
          ctx.beginPath();
          ctx.arc(7, 18, 2, 0, Math.PI * 2);
          ctx.arc(17, 18, 2, 0, Math.PI * 2);
          ctx.fill();
          
          if (map.value) {
            map.value.addImage(`car-marker-${color}`, canvas);
            console.log(`已创建SVG小车图标: car-marker-${color}`);
          }
        };
      });
      
      // 添加默认小车图标(如果没有特定颜色)
      const defaultCar = new Image();
      defaultCar.onload = () => {
        if (map.value) {
          map.value.addImage('car-marker-default', defaultCar);
          console.log('已加载默认小车图标');
        }
      };
      defaultCar.src = '/images/car-icons/car-default.png';
      defaultCar.onerror = () => {
        // 如果默认图标找不到，创建一个灰色的SVG小车
        const size = 24;
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        // 绘制灰色小车
        ctx.fillStyle = '#757575';
        
        // 车身
        ctx.beginPath();
        ctx.roundRect(5, 8, 14, 10, 2);
        ctx.fill();
        
        // 车顶
        ctx.beginPath();
        ctx.roundRect(8, 4, 8, 6, 2);
        ctx.fill();
        
        // 车轮
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(7, 18, 2, 0, Math.PI * 2);
        ctx.arc(17, 18, 2, 0, Math.PI * 2);
        ctx.fill();
        
        if (map.value) {
          map.value.addImage('car-marker-default', canvas);
          console.log('已创建默认SVG小车图标');
        }
      };
    });
})

// 更新处理订单过滤事件
const handleOrdersFiltered = data => {
    if (!data) {
        console.warn('收到空的订单过滤事件数据')
        return
    }
  
    console.log('收到订单过滤事件:', data)
  
    const ordersData = data.orders || data.filteredOrders
    if (!Array.isArray(ordersData)) {
        console.warn('订单过滤事件中缺少有效的订单数组')
        return
    }
  
    // 保存过滤订单结果
    window.filteredOrdersFromList = ordersData
    // 同时保存一个副本以便在其他情况下使用
    window.lastFilteredOrdersFromList = ordersData
    // 保存显示模式
    window.showAllOrders = data.filterCriteria?.showAllOrders
  
    // 打印详细信息便于调试
    console.log('订单过滤事件详情:', {
        filteredCount: ordersData.length,
        filterCriteria: data.filterCriteria,
        showAllOrders: window.showAllOrders
    })
  
    // 强制更新过滤键
    ordersFilterKey.value = Date.now().toString()
  
    // 立即更新地图显示
    if (map.value && isMapLoaded.value) {
        // 使用 requestAnimationFrame 确保在下一帧渲染
        requestAnimationFrame(() => {
            // 根据当前显示模式获取正确的订单列表
            const ordersToDisplay = window.showAllOrders ? orderStore.allOrders : orderStore.orders
      
            // 确保有订单数据
            if (ordersToDisplay && ordersToDisplay.length > 0) {
                console.log('过滤事件触发，更新订单显示，数量:', ordersToDisplay.length)
                updateOrdersData(ordersToDisplay)
        
                // 调整地图视图以显示所有订单
                const validOrders = ordersToDisplay.filter(order => order && (order.lng_lat || order.location))
                if (validOrders.length > 0) {
                    fitMapBounds(validOrders)
                }
            } else {
                console.log('过滤事件触发，但没有可显示的订单')
                clearOrdersData()
            }
        })
    }
}

// 清除订单数据
const clearOrdersData = () => {
    if (!map.value || !map.value.getSource('orders-source')) return
  
    // 首先清理自定义图标
    clearCustomIcons()
  
    // 设置空的GeoJSON数据
    map.value.getSource('orders-source').setData({
        type: 'FeatureCollection',
        features: []
    })
}

// 处理订单选择事件
const handleOrderSelectionChanged = data => {
    if (isHandlingMapEvent.value) return
  
    try {
        isHandlingMapEvent.value = true
        updateOrdersData(currentOrders.value)
    } finally {
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 100)
    }
}

// 处理批量订单选择事件
const handleBatchOrderSelectionChanged = data => {
    if (isHandlingMapEvent.value) return
  
    try {
        isHandlingMapEvent.value = true
        updateOrdersData(currentOrders.value)
    } finally {
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 100)
    }
}

// 处理订单选择清除事件
const handleOrdersSelectionCleared = () => {
    if (isHandlingMapEvent.value) return
  
    try {
        isHandlingMapEvent.value = true
        updateOrdersData(currentOrders.value)
    } finally {
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 100)
    }
}

// 组件卸载时清理事件监听器
onUnmounted(() => {
    // 清理自定义图标
    clearCustomIcons()
  
    // 移除地图实例
    if (map.value) {
        map.value.remove()
        map.value = null
    }
  
    // 解除事件总线监听
    eventBus.off(EVENT_TYPES.ORDER_SELECTED)
    eventBus.off(EVENT_TYPES.ORDERS_BATCH_SELECTED)
    eventBus.off(EVENT_TYPES.ORDERS_SELECTION_CLEARED)
    eventBus.off(EVENT_TYPES.ORDERS_FILTERED)
    eventBus.off(EVENT_TYPES.DISPLAY_MODE_CHANGED)
    eventBus.off(EVENT_TYPES.ORDERS_UPDATED)
    eventBus.off(EVENT_TYPES.ORDERS_UNASSIGNED)
  
    // 移除全局DOM事件监听
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.removeEventListener('keydown', handleKeyDown)
    document.removeEventListener('keyup', handleKeyUp)
    document.removeEventListener('click', e => {
        if (!e.target.closest('.marker-hover-popup') && !e.target.closest('.custom-marker')) {
            hideOrderPopup()
        }
    })
  
    // 清除所有定时器
    if (popupHoverTimer.value) {
        clearTimeout(popupHoverTimer.value)
    }
  
    if (tooltipHideTimer.value) {
        clearTimeout(tooltipHideTimer.value)
    }
  
    // 移除弹窗
    hideOrderPopup()
  
    // 清除全局变量
    window.filteredOrdersFromList = null

    // 移除司机位置更新事件监听
    console.log('正在移除司机位置更新事件监听器')
    eventBus.off(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)
    
    // 清理定时器
    if (window.driverPositionCheckTimer) {
        clearInterval(window.driverPositionCheckTimer);
        delete window.driverPositionCheckTimer;
    }
})

// 添加交通图层
const addTrafficLayer = () => {
    if (!map.value) return
  
    // 检查图层是否已存在
    if (map.value.getSource('trafficSource')) {
        return
    }
  
    // 添加TomTom交通图层
    try {
        // 添加交通数据源
        map.value.addSource('trafficSource', {
            type: 'raster',
            tiles: [
                `https://api.tomtom.com/traffic/map/4/tile/flow/relative/{z}/{x}/{y}.png?key=${TOMTOM_API_KEY}&style=s3`
            ],
            tileSize: 256,
            attribution: '© TomTom'
        })
    
        // 添加交通流量图层
        map.value.addLayer({
            id: 'traffic-flow',
            type: 'raster',
            source: 'trafficSource',
            layout: {
                visibility: showTraffic.value ? 'visible' : 'none' // 根据控制状态设置初始可见性
            },
            paint: {
                'raster-opacity': 0.75
            }
        })
    
        console.log('TomTom交通图层添加完成')
    } catch (error) {
        console.error('添加TomTom交通图层失败:', error)
    }
}

// 切换交通图层显示/隐藏
const toggleTraffic = () => {
    if (!map.value || !isMapLoaded.value) return
  
    const visibility = showTraffic.value ? 'visible' : 'none'
  
    // 检查图层是否存在
    if (map.value.getLayer('traffic-flow')) {
        map.value.setLayoutProperty('traffic-flow', 'visibility', visibility)
        console.log(`TomTom交通图层已${showTraffic.value ? '显示' : '隐藏'}`)
    } else if (showTraffic.value) {
        // 如果图层不存在但需要显示，则添加图层
        addTrafficLayer()
    }
}

// 添加新方法，用于更新标记状态
const updateMarkersStatus = (orderIds, status) => {
    if (!markers.value || !orderIds || orderIds.length === 0) return
  
    console.log(`更新 ${orderIds.length} 个订单标记状态为 ${status}`)
  
    try {
        orderIds.forEach(orderId => {
            const marker = markers.value.get(orderId)
            if (marker) {
                // 移除所有状态类
                marker.getElement().classList.remove('assigned', 'unassigned', 'selected')
        
                // 添加新状态类
                if (status) {
                    marker.getElement().classList.add(status)
                }
        
                // 如果订单在当前选中列表中，保持选中状态
                if (orderStore.selectedOrderIds.has(orderId)) {
                    marker.getElement().classList.add('selected')
                }
            }
        })
    
        console.log(`标记状态更新完成 (${status})`)
    } catch (error) {
        console.error('更新标记状态时出错:', error)
    }
}

// 显示订单取消分配的通知
const showUnassignNotification = (orders) => {
    if (!orders || orders.length === 0) return
  
    // 在地图上可以添加一个临时的提示，几秒后自动消失
    const container = document.createElement('div')
    container.className = 'unassign-notification'
    container.innerHTML = `
    <div class="notification-content">
      <div class="notification-title">订单已取消分配</div>
      <div class="notification-count">${orders.length}个订单已移至未分配列表</div>
    </div>
  `
  
    // 添加样式
    container.style.position = 'absolute'
    container.style.top = '20px'
    container.style.right = '20px'
    container.style.backgroundColor = 'rgba(255, 153, 0, 0.9)'
    container.style.color = 'white'
    container.style.padding = '10px 15px'
    container.style.borderRadius = '4px'
    container.style.zIndex = '1000'
    container.style.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
    container.style.transition = 'opacity 0.5s ease-in-out'
  
    // 添加到地图容器
    if (mapContainer.value) {
        mapContainer.value.appendChild(container)
    
        // 3秒后淡出
        setTimeout(() => {
            container.style.opacity = '0'
      
            // 淡出完成后移除元素
            setTimeout(() => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container)
                }
            }, 500)
        }, 3000)
    }
}

// 显示订单分配成功的通知
const showAssignNotification = (orders, routeName) => {
    if (!orders || orders.length === 0) return
  
    // 在地图上可以添加一个临时的提示，几秒后自动消失
    const container = document.createElement('div')
    container.className = 'assign-notification'
    container.innerHTML = `
    <div class="notification-content">
      <div class="notification-title">订单已分配</div>
      <div class="notification-count">${orders.length}个订单已分配至路线 ${routeName}</div>
    </div>
  `
  
    // 添加样式
    container.style.position = 'absolute'
    container.style.top = '20px'
    container.style.right = '20px'
    container.style.backgroundColor = 'rgba(0, 153, 76, 0.9)'
    container.style.color = 'white'
    container.style.padding = '10px 15px'
    container.style.borderRadius = '4px'
    container.style.zIndex = '1000'
    container.style.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
    container.style.transition = 'opacity 0.5s ease-in-out'
  
    // 添加到地图容器
    if (mapContainer.value) {
        mapContainer.value.appendChild(container)
    
        // 3秒后淡出
        setTimeout(() => {
            container.style.opacity = '0'
      
            // 淡出完成后移除元素
            setTimeout(() => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container)
                }
            }, 500)
        }, 3000)
    }
}

// 添加标记图标到地图
const addMarkerImages = () => {
    if (!map.value) return
  
    // 首先清理旧的自定义图标
    clearCustomIcons()
  
    // 创建默认的基础图标
    createSVGMarkerImage('unassigned-marker', '#9E9E9E', false, false, '', null, null)
    createSVGMarkerImage('assigned-marker', '#3B82F6', true, false, '', null, null)
    createSVGMarkerImage('selected-unassigned-marker', '#9E9E9E', false, true, '', null, null)
    createSVGMarkerImage('selected-assigned-marker', '#3B82F6', true, true, '', null, null)
  
    // 将基本图标添加到集合
    createdIcons.value.add('unassigned-marker')
    createdIcons.value.add('assigned-marker')
    createdIcons.value.add('selected-unassigned-marker')
    createdIcons.value.add('selected-assigned-marker')
  
    console.log('已创建所有状态的基础标记图标')
}

// 创建与Marker.vue组件样式一致的SVG标记图标
const createSVGMarkerImage = (id, color, isAssigned, isSelected, stopNumber, orderStatus, orderType) => {
    if (!map.value) return
  
    // 创建一个Canvas元素来绘制SVG
    const canvas = document.createElement('canvas')
  
    // 设置正确的Canvas尺寸 - 增加选中状态时的额外空间
    const markerWidth = 32 // 增大标记宽度
    const markerHeight = 48 // 增大标记高度
    const padding = isSelected ? 16 : 0 // 选中状态需要额外空间显示选择括号
  
    const width = markerWidth
    const height = markerHeight + padding
    canvas.width = width
    canvas.height = height
  
    const ctx = canvas.getContext('2d')
    if (!ctx) return
  
    // 清除之前的内容
    ctx.clearRect(0, 0, width, height)
  
    // 缩放系数
    const scale = markerWidth / 27 // 基于原来27px宽度的缩放系数
  
    // 计算垂直偏移量，确保图标在正确的位置
    const verticalOffset = isSelected ? padding / 2 : 0
  
    // 如果是选中状态，绘制顶部选择括号
    if (isSelected) {
        ctx.fillStyle = '#FF3B5C'
    
        // 顶部横条
        const bracketWidth = 30 * scale
        ctx.fillRect((width - bracketWidth) / 2, 0, bracketWidth, 4)
    
        // 左侧竖条
        ctx.fillRect((width - bracketWidth) / 2, 0, 4, 8)
    
        // 右侧竖条
        ctx.fillRect(width - (width - bracketWidth) / 2 - 4, 0, 4, 8)
    }
  
    // 首先绘制阴影椭圆
    ctx.save()
    ctx.translate(3 * scale, (29 + verticalOffset) * scale)
  
    const shadowOpacity = 0.04
    ctx.fillStyle = `rgba(0, 0, 0, ${shadowOpacity})`
  
    // 绘制多个椭圆形成渐变阴影效果
    const drawEllipse = (cx, cy, rx, ry) => {
        ctx.beginPath()
        ctx.ellipse(cx * scale, cy, rx * scale, ry * scale, 0, 0, Math.PI * 2)
        ctx.fill()
    }
  
    // 从Marker.vue复制椭圆阴影
    const cx = 10.5
    const cy = 5.8
    drawEllipse(cx, cy, 10.5, 5.25)
    drawEllipse(cx, cy, 10.5, 5.25)
    drawEllipse(cx, cy, 9.5, 4.77)
    drawEllipse(cx, cy, 8.5, 4.29)
    drawEllipse(cx, cy, 7.5, 3.81)
    drawEllipse(cx, cy, 6.5, 3.34)
    drawEllipse(cx, cy, 5.5, 2.86)
    drawEllipse(cx, cy, 4.5, 2.38)
  
    ctx.restore()
  
    // 检查是否为特殊状态：已取件(pickedUp)的取货订单或已送达(delivered)的任何订单
    const isSpecialStatus = isAssigned && ((orderStatus === 'pickedUp' && orderType === 'PICKUP') || orderStatus === 'delivered')
  
    // 绘制主标记形状
    if (isSpecialStatus) {
        // 特殊状态：白色填充
        ctx.fillStyle = '#FFFFFF'
    } else {
        // 普通状态：使用原始颜色
        ctx.fillStyle = color
    }
    
    ctx.beginPath()
  
    // 使用与Marker.vue相同的路径数据绘制标记，但应用缩放
    ctx.save()
    ctx.scale(scale, scale)
    ctx.translate(0, verticalOffset / scale)
  
    const path = new Path2D('M27,13.5 C27,19.074644 20.250001,27.000002 14.75,34.500002 C14.016665,35.500004 12.983335,35.500004 12.25,34.500002 C6.7499993,27.000002 0,19.222562 0,13.5 C0,6.0441559 6.0441559,0 13.5,0 C20.955844,0 27,6.0441559 27,13.5 Z')
    ctx.fill(path)
  
    // 为特殊状态绘制原始颜色的边框
    if (isSpecialStatus) {
        ctx.strokeStyle = color
        ctx.lineWidth = 1.5
        ctx.stroke(path)
    }
  
    // 绘制标记外边框
    ctx.fillStyle = 'rgba(0, 0, 0, 0.25)'
    const outlinePath = new Path2D('M13.5,0 C6.0441559,0 0,6.0441559 0,13.5 C0,19.222562 6.7499993,27 12.25,34.5 C13,35.522727 14.016664,35.500004 14.75,34.5 C20.250001,27 27,19.074644 27,13.5 C27,6.0441559 20.955844,0 13.5,0 Z M13.5,1 C20.415404,1 26,6.584596 26,13.5 C26,15.898657 24.495584,19.181431 22.220703,22.738281 C19.945823,26.295132 16.705119,30.142167 13.943359,33.908203 C13.743445,34.180814 13.612715,34.322738 13.5,34.441406 C13.387285,34.322738 13.256555,34.180814 13.056641,33.908203 C10.284481,30.127985 7.4148684,26.314159 5.015625,22.773438 C2.6163816,19.232715 1,15.953538 1,13.5 C1,6.584596 6.584596,1 13.5,1 Z')
    ctx.fill(outlinePath)
    ctx.restore()
  
    // 添加未分配标记的白色圆圈
    if (!isAssigned) {
        ctx.save()
    
        // 移动到正确位置
        ctx.translate(8 * scale, (8 + verticalOffset) * scale)
    
        // 绘制黑色背景圆
        ctx.fillStyle = 'rgba(0, 0, 0, 0.25)'
        ctx.beginPath()
        ctx.arc(5.5 * scale, 5.5 * scale, 5.5 * scale, 0, Math.PI * 2)
        ctx.fill()
    
        // 绘制白色前景圆
        ctx.fillStyle = '#FFFFFF'
        ctx.beginPath()
        ctx.arc(5.5 * scale, 5.5 * scale, 5.5 * scale, 0, Math.PI * 2)
        ctx.fill()
    
        ctx.restore()
    }
  
    // 添加P角标 - 为所有取货订单添加小角标
    const isPickupType = orderType === 'PICKUP';
    if (isPickupType && !isSpecialStatus) {
        ctx.save();
        
        // 右上角小圆形位置
        const tagX = width - 8 * scale;
        const tagY = 8 * scale + verticalOffset;
        const tagRadius = 5 * scale;
        
        // 绘制黑色圆形背景
        ctx.fillStyle = '#333333';
        ctx.beginPath();
        ctx.arc(tagX, tagY, tagRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制白色P文字
        ctx.fillStyle = '#FFFFFF';
        ctx.font = `bold ${6 * scale}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('P', tagX, tagY);
        
        ctx.restore();
    }
  
    // 只有在stopNumber不为空时绘制文字
    if (stopNumber && stopNumber.trim() !== '') {
        if (!isAssigned) {
            // 未分配标记文字 - 改为白色文字
            ctx.fillStyle = '#FFFFFF'
            ctx.font = `bold ${11 * scale}px Arial`
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            // 调整位置：居中放置在标记中心位置
            ctx.fillText(stopNumber, width / 2, 13.5 * scale + verticalOffset)
        } else if (isSpecialStatus) {
            // 特殊状态（已取件/已送达）- 黑色文字
            ctx.fillStyle = '#333333'
            ctx.font = `bold ${12 * scale}px Arial`
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            // 已分配标记文字稍微靠上一点
            ctx.fillText(stopNumber, width / 2, 12 * scale + verticalOffset)
        } else {
            // 普通已分配标记文字 - 白色文字
            ctx.fillStyle = '#FFFFFF'
            ctx.font = `bold ${12 * scale}px Arial`
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            // 已分配标记文字稍微靠上一点
            ctx.fillText(stopNumber, width / 2, 12 * scale + verticalOffset)
        }
    }
  
    // 如果是选中状态，绘制底部选择括号
    if (isSelected) {
        ctx.fillStyle = '#FF3B5C'
    
        // 底部横条
        const bracketWidth = 30 * scale
        ctx.fillRect((width - bracketWidth) / 2, height - 4, bracketWidth, 4)
    
        // 左侧竖条
        ctx.fillRect((width - bracketWidth) / 2, height - 8, 4, 8)
    
        // 右侧竖条
        ctx.fillRect(width - (width - bracketWidth) / 2 - 4, height - 8, 4, 8)
    }
  
    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, width, height)
  
    try {
        // 添加到地图
        map.value.addImage(id, { 
            width: width, 
            height: height, 
            data: new Uint8Array(imageData.data.buffer),
            pixelRatio: 1
        })
    
        console.log(`已创建标记图标: ${id}，大小: ${width}x${height}`)
    } catch (error) {
        console.error(`创建标记图标失败: ${id}`, error)
        console.log(`图像数据大小: ${imageData.data.length}字节`)
    
        // 如果发生错误，尝试简化版本
        try {
            // 清除Canvas并重新绘制简单版本
            ctx.clearRect(0, 0, width, height)
            ctx.fillStyle = color
      
            // 简单圆形标记
            ctx.beginPath()
            ctx.arc(width / 2, height / 2, 12, 0, Math.PI * 2)
            ctx.fill()
      
            if (isSelected) {
                ctx.strokeStyle = '#FF3B5C'
                ctx.lineWidth = 3
                ctx.stroke()
            }
      
            // 获取简化图像数据
            const simpleImageData = ctx.getImageData(0, 0, width, height)
      
            // 尝试添加简化版本的图标
            map.value.addImage(id, { 
                width: width, 
                height: height, 
                data: new Uint8Array(simpleImageData.data.buffer),
                pixelRatio: 1
            })
      
            console.log(`使用简化版本创建标记图标: ${id}`)
        } catch (fallbackError) {
            console.error(`创建标记图标完全失败: ${id}`, fallbackError)
        }
    }
}

// 添加订单数据变化时的图标大小调整功能
const updateMarkerSizeForZoom = () => {
    if (!map.value) return
  
    // 获取当前缩放级别
    const zoom = map.value.getZoom()
  
    // 根据缩放级别调整图标大小
    let iconSize = 1.0 // 默认基础大小
  
    // 缩放级别小于8时，逐渐增大图标
    if (zoom < 8) {
        // 缩放值越小，图标越大，确保在低缩放级别可见
        iconSize = 1.0 * (1 + (8 - zoom) * 0.1)
        // 限制最大尺寸
        iconSize = Math.min(iconSize, 1.5)
    }
    // 在高缩放级别，图标保持大小或略微变大，但不要变小
    else if (zoom > 14) {
        // 在高缩放级别，图标可以略微变大，但有限制
        iconSize = 1.0 + (zoom - 14) * 0.02
        // 限制最大尺寸
        iconSize = Math.min(iconSize, 1.2)
    }
  
    // 更新各图层的图标大小
    map.value.setLayoutProperty('unassigned-orders-layer', 'icon-size', iconSize)
    map.value.setLayoutProperty('assigned-orders-layer', 'icon-size', iconSize)
    // 选中标记始终稍大一些
    map.value.setLayoutProperty('selected-orders-layer', 'icon-size', iconSize * 1.1)
}

// 添加订单GeoJSON数据源
const addOrdersDataSource = () => {
    if (!map.value) return
  
    // 创建空的GeoJSON数据源
    map.value.addSource('orders-source', {
        type: 'geojson',
        data: {
            type: 'FeatureCollection',
            features: []
        },
        cluster: false
    })
  
    // 添加未分配订单图层
    map.value.addLayer({
        id: 'unassigned-orders-layer',
        type: 'symbol',
        source: 'orders-source',
        filter: ['==', ['get', 'status'], 'unassigned'],
        minzoom: 0,  // 确保在所有缩放级别都可见
        maxzoom: 22, // 设置最大缩放级别
        layout: {
            'icon-image': ['get', 'iconImage'],
            'icon-size': 1.0, // 增大默认图标尺寸
            'icon-allow-overlap': true,
            'icon-ignore-placement': true, // 确保图标不会因为重叠而被隐藏
            'symbol-sort-key': 1, // 让未分配的订单优先级低一点
            'visibility': 'visible' // 始终可见
        }
    })
  
    // 添加已分配订单图层 - 使用数据驱动样式
    map.value.addLayer({
        id: 'assigned-orders-layer',
        type: 'symbol',
        source: 'orders-source',
        filter: ['==', ['get', 'status'], 'assigned'],
        minzoom: 0,  // 确保在所有缩放级别都可见
        maxzoom: 22, // 设置最大缩放级别
        layout: {
            'icon-image': ['get', 'iconImage'],
            'icon-size': 1.0, // 增大默认图标尺寸
            'icon-allow-overlap': true,
            'icon-ignore-placement': true, // 确保图标不会因为重叠而被隐藏
            'symbol-sort-key': 2, // 让已分配订单优先级中等
            'visibility': 'visible' // 始终可见
        }
    })
  
    // 添加选中订单图层
    map.value.addLayer({
        id: 'selected-orders-layer',
        type: 'symbol',
        source: 'orders-source',
        filter: ['==', ['get', 'selected'], true],
        minzoom: 0,  // 确保在所有缩放级别都可见
        maxzoom: 22, // 设置最大缩放级别
        layout: {
            'icon-image': ['get', 'iconImage'],
            'icon-size': 1.1, // 增大默认图标尺寸，选中的标记稍大
            'icon-allow-overlap': true,
            'icon-ignore-placement': true, // 确保图标不会因为重叠而被隐藏
            'symbol-sort-key': 3, // 让选中订单优先级最高
            'visibility': 'visible' // 始终可见
        }
    })
  
    // 添加点击事件处理
    map.value.on('click', 'unassigned-orders-layer', handleOrderLayerClick)
    map.value.on('click', 'assigned-orders-layer', handleOrderLayerClick)
    map.value.on('click', 'selected-orders-layer', handleOrderLayerClick)
  
    // 添加鼠标悬停事件
    map.value.on('mouseenter', 'unassigned-orders-layer', () => {
        map.value.getCanvas().style.cursor = 'pointer'
    })
    map.value.on('mouseleave', 'unassigned-orders-layer', () => {
        map.value.getCanvas().style.cursor = ''
    })
  
    map.value.on('mouseenter', 'assigned-orders-layer', () => {
        map.value.getCanvas().style.cursor = 'pointer'
    })
    map.value.on('mouseleave', 'assigned-orders-layer', () => {
        map.value.getCanvas().style.cursor = ''
    })
  
    map.value.on('mouseenter', 'selected-orders-layer', () => {
        map.value.getCanvas().style.cursor = 'pointer'
    })
    map.value.on('mouseleave', 'selected-orders-layer', () => {
        map.value.getCanvas().style.cursor = ''
    })
  
    // 添加悬停事件处理
    map.value.on('mousemove', e => {
        handleOrderHover(e)
    })
  
    // 监听地图缩放事件，确保标记保持可见
    map.value.on('zoom', () => {
        // 确保标记在所有缩放级别都可见
        map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
        map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
        map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')
    
        // 根据缩放级别动态调整图标大小
        updateMarkerSizeForZoom()
    })
  
    // 初始化设置图标大小
    updateMarkerSizeForZoom()
  
    // 确保标记图层在最顶部
    moveSymbolLayersToTop()
}

// 点击订单图层处理函数
const handleOrderLayerClick = e => {
    if (!e.features || e.features.length === 0) return
  
    // 阻止事件冒泡
    e.originalEvent.preventDefault()
    e.originalEvent.stopPropagation()
  
    // 获取点击的订单
    const feature = e.features[0]
    const orderId = feature.properties.id
    console.log('点击订单:', orderId)
  
    // 防止事件处理中断
    if (isHandlingMapEvent.value) return
  
    try {
        isHandlingMapEvent.value = true
    
        // 检查是否按下了Ctrl键（多选模式）
        const isMultiSelect = e.originalEvent.ctrlKey || e.originalEvent.metaKey
    
        if (isMultiSelect) {
            // Ctrl键多选模式：切换当前订单选择状态
            orderStore.toggleOrderSelection(orderId)
      
            // 发送选择事件，通知其他组件
            const isSelected = orderStore.selectedOrderIds.has(orderId)
            eventBus.emit(EVENT_TYPES.ORDER_SELECTED, {
                orderId,
                isSelected,
                source: 'map'
            })
        } else {
            // 单选模式：先检查当前是否已选中
            const isCurrentlySelected = orderStore.selectedOrderIds.has(orderId)
      
            if (isCurrentlySelected) {
                // 如果已经选中，则清除选择
                orderStore.clearSelection()
        
                // 发送清除选择事件
                eventBus.emit(EVENT_TYPES.ORDERS_SELECTION_CLEARED, {
                    source: 'map'
                })
            } else {
                // 先清除所有选择，再选中当前订单
                orderStore.clearSelection()
                orderStore.toggleOrderSelection(orderId)
        
                // 发送选择事件
                eventBus.emit(EVENT_TYPES.ORDER_SELECTED, {
                    orderId,
                    isSelected: true,
                    source: 'map'
                })
            }
        }
    
        // 更新数据源，反映选择状态变化
        updateOrdersData(currentOrders.value)
    } finally {
        // 延时重置处理标志
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 100)
    }
}

// 悬停订单处理
const handleOrderHover = e => {
    // 清除之前的定时器
    if (popupHoverTimer.value) {
        clearTimeout(popupHoverTimer.value)
        popupHoverTimer.value = null
    }
  
    // 检查鼠标是否在订单上
    const features = map.value.queryRenderedFeatures(e.point, {
        layers: ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer']
    })
  
    if (features.length > 0) {
        // 设置鼠标样式
        map.value.getCanvas().style.cursor = 'pointer'
    
        // 获取订单信息
        const feature = features[0]
        const orderId = feature.properties.id
    
        // 延迟显示弹窗
        popupHoverTimer.value = setTimeout(() => {
            // 找到对应的订单对象
            const order = currentOrders.value.find(o => o.id === orderId)
            if (order) {
                console.log('显示订单信息弹窗:', orderId)
                showOrderPopup(order)
            }
        }, POPUP_HOVER_DELAY)
    } else {
        // 设置默认鼠标样式
        map.value.getCanvas().style.cursor = ''
    
        // 检查鼠标是否在弹窗上
        const popupElement = document.querySelector('.maplibregl-popup')
        if (popupElement && popupElement.matches(':hover')) {
            // 鼠标在弹窗上，不关闭弹窗
            return
        }
    
        // 如果不在订单上，并且不在弹窗上，则延迟关闭弹窗
        setTimeout(() => {
            if (activePopup.value) {
                const popupElement = document.querySelector('.maplibregl-popup')
                if (popupElement && !popupElement.matches(':hover')) {
                    hideOrderPopup()
                }
            }
        }, 100)
    }
}

// 处理鼠标离开地图容器
const handleMapMouseLeave = () => {
    // 清除定时器
    if (popupHoverTimer.value) {
        clearTimeout(popupHoverTimer.value)
        popupHoverTimer.value = null
    }
  
    // 鼠标离开地图时，检查是否需要关闭弹窗
    setTimeout(() => {
        if (activePopup.value) {
            const popupElement = document.querySelector('.maplibregl-popup')
            if (popupElement && !popupElement.matches(':hover')) {
                hideOrderPopup()
            }
        }
    }, 200)
}

// 更新图层中订单的数据
const updateOrdersData = orders => {
    routeRequestContextId.value++; // Increment context ID
    if (!map.value || !map.value.getSource('orders-source')) return
  
    console.log('更新订单数据，数量:', orders.length)
    // 过滤有效订单
    const validOrders = orders.filter(order => order && (order.lng_lat || order.location))
  
    // 创建GeoJSON特征集合
    const features = validOrders.map(order => {
        // 获取坐标
        const coords = order.lng_lat || order.location
        if (!coords || !Array.isArray(coords) || coords.length !== 2) {
            return null
        }
    
        // 注意：我们的坐标是 [lat, lng] 格式，而 GeoJSON 需要 [lng, lat] 格式
        const [lat, lng] = coords
        if (isNaN(lat) || isNaN(lng)) {
            return null
        }
    
        // 检查订单是否已分配
        const isAssigned = !!order.driver_id
    
        // 获取停靠点编号
        let stopNo = ''
        if (isAssigned) {
            // 优先使用stop_no属性
            if (order.stop_no) {
                stopNo = String(order.stop_no)
            } 
            // 备选：使用pickup_stop_no属性
            else if (order.pickup_stop_no) {
                stopNo = String(order.pickup_stop_no)
            }
            // 如果两个都没有但有订单号，显示订单号最后一位
            else if (order.no) {
                const orderNo = String(order.no)
                stopNo = orderNo.slice(-1)
            }
        } else {
            // 未分配订单显示订单号的最后一位或索引
            if (order.no) {
                const orderNo = String(order.no)
                stopNo = orderNo.slice(-1)
            } else {
                // 最后的备选是使用1
                stopNo = '1'
            }
        }
    
        // 获取司机颜色
        const driverColor = isAssigned ? getMarkerColor(order) : '#9E9E9E'
    
        // 检查是否为选中的订单
        const isSelected = orderStore.selectedOrderIds.has(order.id)
    
        // 为每个订单创建特定的图标ID，包含停靠点编号
        let iconId
    
        // 构建基于订单ID和停靠点编号的唯一图标ID
        const uniqIconSuffix = `${order.id}-${stopNo || '0'}`
    
        if (isSelected) {
            // 选中状态的图标ID
            iconId = isAssigned 
                ? `selected-assigned-${uniqIconSuffix}` 
                : `selected-unassigned-${uniqIconSuffix}`
        } else {
            // 非选中状态的图标ID
            iconId = isAssigned 
                ? `assigned-${uniqIconSuffix}` 
                : `unassigned-${uniqIconSuffix}`
        }
    
        // 如果该图标不存在，则创建它
        if (!map.value.hasImage(iconId)) {
            // 创建图标
            createSVGMarkerImage(
                iconId, 
                isAssigned ? driverColor : '#9E9E9E', 
                isAssigned, 
                isSelected,
                stopNo,
                order.status,  // 添加订单状态参数
                order.type && order.type.toUpperCase()  // 添加订单类型参数
            )
      
            // 将图标ID添加到集合
            createdIcons.value.add(iconId)
        }
    
        // 创建GeoJSON特征对象
        return {
            type: 'Feature',
            geometry: {
                type: 'Point',
                coordinates: [lng, lat]
            },
            properties: {
                id: order.id,
                status: isAssigned ? 'assigned' : 'unassigned',
                stopNo: stopNo,
                driverId: order.driver_id || null,
                routeId: order.route_id || null,
                selected: isSelected,
                iconImage: iconId,  // 使用为每个订单创建的唯一图标ID
                color: driverColor,
                zIndexOffset: isSelected ? 1000 : (isAssigned ? 500 : 0) // 确保选中的订单显示在最上层
            }
        }
    }).filter(feature => feature !== null)
  
    // 更新数据源
    map.value.getSource('orders-source').setData({
        type: 'FeatureCollection',
        features: features
    })
  
    // 确保图层可见性和大小设置
    if (map.value.getLayer('unassigned-orders-layer') && 
        map.value.getLayer('assigned-orders-layer') && 
        map.value.getLayer('selected-orders-layer')) {
    
        // 确保图层可见
        map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
        map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
        map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')
    
        // 根据当前缩放级别更新图标大小
        updateMarkerSizeForZoom()
    }
  
    // 更新性能统计
    performanceStats.value.markerCount = features.length
    console.log('Canvas标记更新完成，总数:', features.length)
  
    // 确保标记图层在最顶部
    moveSymbolLayersToTop()
}

// 处理地图点击事件
const handleMapClick = e => {
    // 如果事件已被阻止或正在绘制选择框，不处理
    if (e.defaultPrevented || isDrawingSelectionBox.value) return
  
    // 矩形选择完成后的短时间内不清除选择（防止误触）
    const now = Date.now()
    if (now - lastClickTime.value < clickDebounceTime) {
        console.log('点击太快，忽略清除选择')
        return
    }
    lastClickTime.value = now
  
    // 检查是否按住了Ctrl键，如果按住则不清除选择
    if (isCtrlPressed.value) {
        console.log('按住Ctrl键点击，不清除选择')
        return
    }
  
    // 检查点击位置是否有标记
    const features = map.value.queryRenderedFeatures(e.point, {
        layers: ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer']
    })
  
    // 如果点击位置有标记，不清除选择（让标记的点击事件处理）
    if (features.length > 0) {
        return
    }
  
    console.log('地图空白处点击 - 清除订单选择')
  
    // 防止处理中断
    if (isHandlingMapEvent.value) return
  
    try {
        isHandlingMapEvent.value = true
    
        // 直接使用orderStore清除选择
        orderStore.clearSelection()
    
        // 发送清除选择事件
        eventBus.emit(EVENT_TYPES.ORDERS_SELECTION_CLEARED, {
            source: 'map'
        })
    
        // 更新地图数据反映选择状态变化
        updateOrdersData(currentOrders.value)
    } finally {
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 100)
    }
}

// 创建的图标集合，用于跟踪和删除
const createdIcons = ref(new Set())

// 清除自定义图标
const clearCustomIcons = () => {
    if (!map.value) return
  
    // 保留基本图标
    const basicIcons = [
        'unassigned-marker', 
        'assigned-marker', 
        'selected-unassigned-marker', 
        'selected-assigned-marker'
    ]
  
    // 删除所有非基本图标
    createdIcons.value.forEach(iconId => {
        if (!basicIcons.includes(iconId) && map.value.hasImage(iconId)) {
            try {
                map.value.removeImage(iconId)
            } catch (error) {
                console.error(`删除图标失败: ${iconId}`, error)
            }
        }
    })
  
    // 清空图标集合
    createdIcons.value.clear()
  
    // 重新添加基本图标到集合
    basicIcons.forEach(iconId => {
        createdIcons.value.add(iconId)
    })
}

// 确保标记图层显示在所有其他图层之上
const moveSymbolLayersToTop = () => {
    if (!map.value) return
  
    try {
        // 首先移动司机位置图层（如果存在）
        if (map.value.getLayer('drivers-layer')) {
            map.value.moveLayer('drivers-layer')
        }

        // 然后移动订单图层，这样订单图层将在司机图层之上
        const symbolLayerIds = [
            'unassigned-orders-layer',
            'assigned-orders-layer', 
            'selected-orders-layer'
        ]
    
        // 检查每个图层是否存在，并移动到最顶层（这会使订单图层在司机图层之上）
        symbolLayerIds.forEach(layerId => {
            if (map.value.getLayer(layerId)) {
                // 移动图层到最顶层
                map.value.moveLayer(layerId)
            }
        })
    
        console.log('已将所有标记图层移至最顶层')
    } catch (error) {
        console.error('移动标记图层时出错:', error)
    }
}

// 在声明ref变量部分添加司机位置相关变量
const driverMarkers = ref(new Map())
const selectedDriverId = ref(null)
const activeDriverPopup = ref(null)
const showDrivers = ref(true) // 是否显示司机位置

// 添加司机位置图层方法
const addDriversLayer = () => {
  if (!isMapLoaded.value || !map.value) return
  
  // 添加司机位置数据源
  if (!map.value.getSource('drivers-source')) {
    map.value.addSource('drivers-source', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: []
      }
    })
  }
  
  // 如果没有司机图标，则添加
  if (!map.value.hasImage('driver-marker')) {
    // 创建默认司机标记图标 (小车形状)
    const driverIconSize = 32
    const canvas = document.createElement('canvas')
    canvas.width = driverIconSize
    canvas.height = driverIconSize
    const ctx = canvas.getContext('2d')
    
    // 清空画布
    ctx.clearRect(0, 0, driverIconSize, driverIconSize)
    
    // 绘制小车形状
    const drawCarIcon = (color) => {
      // 设置默认颜色（如果没有传入）
      const carColor = color || '#4285F4'
      
      // 移动到中心点
      ctx.translate(driverIconSize / 2, driverIconSize / 2)
      
      // 车身
      ctx.beginPath()
      ctx.rect(-10, -5, 20, 10)
      ctx.fillStyle = carColor
      ctx.fill()
      ctx.strokeStyle = 'white'
      ctx.lineWidth = 1
      ctx.stroke()
      
      // 车顶
      ctx.beginPath()
      ctx.rect(-6, -10, 12, 5)
      ctx.fillStyle = carColor
      ctx.fill()
      ctx.strokeStyle = 'white'
      ctx.lineWidth = 1
      ctx.stroke()
      
      // 车轮
      ctx.beginPath()
      ctx.arc(-6, 5, 3, 0, 2 * Math.PI)
      ctx.fillStyle = '#333'
      ctx.fill()
      ctx.strokeStyle = 'white'
      ctx.lineWidth = 1
      ctx.stroke()
      
      ctx.beginPath()
      ctx.arc(6, 5, 3, 0, 2 * Math.PI)
      ctx.fillStyle = '#333'
      ctx.fill()
      ctx.strokeStyle = 'white'
      ctx.lineWidth = 1
      ctx.stroke()
      
      // 添加前灯
      ctx.beginPath()
      ctx.arc(10, -1, 2, 0, 2 * Math.PI)
      ctx.fillStyle = 'yellow'
      ctx.fill()
      
      // 重置变换
      ctx.setTransform(1, 0, 0, 1, 0, 0)
    }
    
    // 默认蓝色小车
    drawCarIcon('#4285F4')
    
    // 添加图标到地图
    map.value.addImage('driver-marker', { 
      width: driverIconSize, 
      height: driverIconSize, 
      data: new Uint8Array(ctx.getImageData(0, 0, driverIconSize, driverIconSize).data.buffer) 
    })
    
    // 为每个司机创建特定颜色的图标
    const addDriverSpecificIcons = () => {
      const drivers = driverStore.drivers || []
      drivers.forEach(driver => {
        if (driver.id && driver.color) {
          // 清空画布
          ctx.clearRect(0, 0, driverIconSize, driverIconSize)
          
          // 使用司机颜色绘制小车
          drawCarIcon(driver.color)
          
          // 添加到地图
          const iconId = `driver-marker-${driver.id}`
          map.value.addImage(iconId, { 
            width: driverIconSize, 
            height: driverIconSize, 
            data: new Uint8Array(ctx.getImageData(0, 0, driverIconSize, driverIconSize).data.buffer) 
          })
        }
      })
    }
    
    // 添加司机特定颜色图标
    addDriverSpecificIcons()
  }
  
  // 添加司机位置图层
  if (!map.value.getLayer('drivers-layer')) {
    map.value.addLayer({
      id: 'drivers-layer',
      type: 'symbol',
      source: 'drivers-source',
      layout: {
        // 使用动态图标表达式选择司机特定图标
        'icon-image': [
          'case',
          ['has', 'driver_color'], ['concat', 'driver-marker-', ['get', 'id']], // 使用司机特定图标
          'driver-marker' // 使用默认图标
        ],
        'icon-size': 0.8,
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-rotate': ['get', 'bearing'], // 使用方向信息旋转图标
        'icon-rotation-alignment': 'map'
      }
    })
    
    // 添加事件监听
    map.value.on('click', 'drivers-layer', onDriverMarkerClicked)
    map.value.on('mouseenter', 'drivers-layer', () => {
      map.value.getCanvas().style.cursor = 'pointer'
    })
    map.value.on('mouseleave', 'drivers-layer', () => {
      map.value.getCanvas().style.cursor = ''
    })
    
    // 确保订单图层显示在司机图层之上
    moveSymbolLayersToTop()
  }
}

// 更新司机位置方法
const updateDriversPositions = (driversWithLocation) => {
  console.log('开始执行updateDriversPositions函数');
  
  // 始终确保显示开关是打开的
  if (!showDrivers.value) {
    console.warn('司机位置显示已关闭，强制开启显示');
    showDrivers.value = true;
  }
  
  if (!isMapLoaded.value || !map.value) {
    console.error('地图未加载，无法更新司机位置');
    return;
  }
  
  try {
    // 验证输入数据
    if (!Array.isArray(driversWithLocation)) {
      console.warn('更新司机位置失败: 输入数据不是数组');
      return;
    }
    
    console.log(`准备更新${driversWithLocation.length}个司机位置`);
    
    // 确保司机图层存在
    addDriversLayer();
    
    // 更新GeoJSON数据
    const features = driversWithLocation.map(driverLocation => {
      // 验证位置数据
      if (!driverLocation || !driverLocation.id || 
          typeof driverLocation.latitude !== 'number' || 
          typeof driverLocation.longitude !== 'number') {
        console.warn('司机位置数据无效:', driverLocation);
        return null;
      }
      
      // 获取司机信息
      const driver = driverStore.getDriverById(driverLocation.id);
      
      // 计算行驶方向（如果有前一个位置和当前速度）
      let bearing = 0;
      if (driverLocation.speed && driverLocation.speed > 0 && driver && driver.previousLocation) {
        // 计算方向
        const prevLat = driver.previousLocation.latitude;
        const prevLng = driver.previousLocation.longitude;
        const currLat = driverLocation.latitude;
        const currLng = driverLocation.longitude;
        
        // 简单计算方向角度
        if (prevLat !== currLat || prevLng !== currLng) {
          const deltaLng = currLng - prevLng;
          const deltaLat = currLat - prevLat;
          bearing = (Math.atan2(deltaLng, deltaLat) * 180 / Math.PI) + 180;
        }
      }
      
      // 保存当前位置作为下次更新的前一个位置
      if (driver) {
        driver.previousLocation = {
          latitude: driverLocation.latitude,
          longitude: driverLocation.longitude
        };
      }
      
      // 创建要素
      return {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [driverLocation.longitude, driverLocation.latitude]
        },
        properties: {
          id: driverLocation.id,
          name: driver?.name || `司机 ${driverLocation.id}`,
          speed: driverLocation.speed,
          accuracy: driverLocation.accuracy,
          timestamp: driverLocation.timestamp,
          route_id: driver?.route_id,
          route_number: driver?.route_number,
          driver_color: driver?.color,
          bearing: bearing,
          isOnline: true
        }
      };
    }).filter(feature => feature !== null); // 过滤掉无效数据
    
    console.log(`过滤后有${features.length}个有效司机位置数据`);
    
    // 确保至少有一个有效的司机位置
    if (features.length === 0) {
      console.warn('没有有效的司机位置数据可更新');
      return;
    }
    
    // 更新数据源
    try {
      const source = map.value.getSource('drivers-source');
      if (source) {
        source.setData({
          type: 'FeatureCollection',
          features: features
        });
        console.log(`司机位置数据源更新成功，包含${features.length}个司机位置`);
      } else {
        console.warn('司机数据源不存在，无法更新位置');
        console.log('尝试重新添加司机图层');
        addDriversLayer();
        
        // 重试一次更新数据源
        setTimeout(() => {
          const retrySource = map.value.getSource('drivers-source');
          if (retrySource) {
            retrySource.setData({
              type: 'FeatureCollection',
              features: features
            });
            console.log('重试更新司机位置数据源成功');
          } else {
            console.error('重试更新司机位置数据源失败');
          }
        }, 500);
        return;
      }
    } catch (sourceError) {
      console.error('更新司机数据源时发生错误:', sourceError);
      return;
    }
    
    // 更新性能统计
    if (performanceStats.value) {
      performanceStats.value.driverCount = features.length;
    }
    
    console.log(`地图上成功更新了${features.length}个司机位置`);
    
    // 确保订单图层显示在司机图层之上
    moveSymbolLayersToTop();
    
    // 尝试调整视图以显示所有司机
    if (autoZoom.value && features.length > 0) {
      try {
        fitMapToDrivers(features);
      } catch (zoomError) {
        console.error('调整地图视图以显示所有司机时发生错误:', zoomError);
      }
    }
  } catch (error) {
    console.error('更新司机位置失败:', error);
  }
};

// 添加调整视图以显示所有司机的辅助函数
const fitMapToDrivers = (driverFeatures) => {
  if (!map.value || !driverFeatures || driverFeatures.length === 0) return;
  
  try {
    // 提取司机坐标
    const bounds = new maplibregl.LngLatBounds();
    
    driverFeatures.forEach(feature => {
      if (feature.geometry && feature.geometry.coordinates) {
        bounds.extend(feature.geometry.coordinates);
      }
    });
    
    // 调整地图视图
    map.value.fitBounds(bounds, {
      padding: 100,
      maxZoom: 16,
      duration: 1000
    });
    
    console.log('地图视图已调整以显示所有司机');
  } catch (error) {
    console.error('调整地图视图失败:', error);
  }
};

// 切换司机位置显示
const toggleDrivers = () => {
  if (!isMapLoaded.value || !map.value) return
  
  const visibility = showDrivers.value ? 'visible' : 'none'
  
  if (map.value.getLayer('drivers-layer')) {
    map.value.setLayoutProperty('drivers-layer', 'visibility', visibility)
    
    // 如果开启显示，则立即更新一次司机位置
    if (showDrivers.value) {
      const driversWithLocation = driverStore.driversWithLocation
      if (driversWithLocation.length > 0) {
        updateDriversPositions(driversWithLocation)
      }
    }
  }
}

// 处理司机标记点击事件
const onDriverMarkerClicked = (e) => {
  if (!e.features || e.features.length === 0) return
  
  const feature = e.features[0]
  const driverId = feature.properties.id
  const driverLocation = {
    id: driverId,
    latitude: e.lngLat.lat,
    longitude: e.lngLat.lng,
    ...feature.properties
  }
  
  // 关闭现有弹窗
  if (activeDriverPopup.value && typeof activeDriverPopup.value.remove === 'function') {
    activeDriverPopup.value.remove()
  } else if (activeDriverPopup.value) {
    console.warn('无法关闭司机弹窗，remove方法不存在', activeDriverPopup.value)
    // 将弹窗引用设为null
    activeDriverPopup.value = null
  }
  
  // 创建弹窗
  selectedDriverId.value = driverId
  
  // 创建弹窗元素
  const popupElement = document.createElement('div')
  
  // 使用Vue渲染弹窗组件
  const app = createApp({
    render() {
      return h(DriverMarkerPopup, {
        driver: driverLocation,
        onFocusDriver: () => focusDriverLocation(driverLocation),
        onShowRoute: () => showDriverRoute(driverId),
        onAssignOrders: () => assignOrdersToDriver(driverId)
      })
    }
  })
  
  try {
    app.mount(popupElement)
    
    // 创建并显示弹窗
    activeDriverPopup.value = new maplibregl.Popup({
      closeOnClick: true,
      maxWidth: '300px',
      className: 'driver-marker-popup'
    })
      .setLngLat([driverLocation.longitude, driverLocation.latitude])
      .setDOMContent(popupElement)
      .addTo(map.value)
      .on('close', () => {
        selectedDriverId.value = null
        try {
          app.unmount()
        } catch (error) {
          console.error('卸载司机弹窗组件失败', error)
        }
        // 弹窗关闭时重置引用
        activeDriverPopup.value = null
      })
  } catch (error) {
    console.error('创建司机弹窗失败', error)
    app.unmount()
  }
}

// 聚焦司机位置
const focusDriverLocation = (driverLocation) => {
  if (!map.value || !driverLocation) return
  
  map.value.flyTo({
    center: [driverLocation.longitude, driverLocation.latitude],
    zoom: 15,
    essential: true
  })
}

// 显示司机路线
const showDriverRoute = (driverId) => {
  // 获取司机
  const driver = driverStore.getDriverById(driverId)
  if (!driver) return
  
  // 如果司机有路线ID，显示路线
  if (driver.route_id) {
    // 选择路线
    routeStore.selectRoute({ id: driver.route_id })
  } else {
    console.log('司机没有分配路线')
  }
}

// 分配订单给司机
const assignOrdersToDriver = (driverId) => {
  // 获取司机
  const driver = driverStore.getDriverById(driverId)
  if (!driver) return
  
  // 选择司机
  driverStore.selectDriver(driver)
  
  // 如果有选择的订单，分配给司机
  if (orderStore.selectedOrderIds.length > 0) {
    orderStore.assignOrdersToDriver(orderStore.selectedOrderIds, driverId)
      .then(() => {
        console.log(`成功将${orderStore.selectedOrderIds.length}个订单分配给司机${driverId}`)
      })
      .catch(error => {
        console.error('分配订单失败:', error)
      })
  } else {
    console.log('没有选择的订单可分配')
  }
}

// 处理司机位置更新事件
const onDriverPositionsUpdated = (driversPositions) => {
    console.log('MapView组件接收到司机位置更新事件');
    
    // 检查是否是模拟的可见性变化事件
    const isSimulatedVisibilityChange = window.__simulatingVisibilityChange === true;
    if (isSimulatedVisibilityChange) {
        console.log('检测到模拟的visibilitychange事件触发的更新');
    }
    
    // 验证输入数据
    if (!Array.isArray(driversPositions)) {
        console.warn('司机位置更新事件数据无效:', driversPositions);
        
        // 尝试从store直接获取
        driversPositions = driverStore.allDriverPositions;
        console.log('尝试从store直接获取司机位置:', driversPositions);
        
        if (!Array.isArray(driversPositions) || driversPositions.length === 0) {
            console.error('无法获取有效的司机位置数据');
            return;
        }
    }
    
    console.log(`接收到司机位置更新事件，有${driversPositions.length}个司机位置数据`);
    
    // 检查showDrivers状态
    console.log('司机位置显示状态:', showDrivers.value ? '开启' : '关闭');
    if (!showDrivers.value) {
        console.warn('司机位置显示已关闭，强制开启显示');
        showDrivers.value = true;
    }

    // 确保地图已加载
    if (!isMapLoaded.value || !map.value) {
        console.warn('地图未加载完成，无法更新司机位置');
        
        // 如果地图未加载，延迟尝试更新
        setTimeout(() => {
            if (isMapLoaded.value && map.value) {
                console.log('延迟更新司机位置');
                updateDriversPositions(driversPositions);
            } else {
                console.error('延迟后地图仍未加载，无法更新司机位置');
            }
        }, 1000);
        return;
    }
    
    // 检查司机图层是否存在，如果不存在则添加
    if (!map.value.getLayer('drivers-layer')) {
        console.log('司机图层不存在，尝试添加');
        addDriversLayer();
    }
    
    // 确保司机图层可见
    map.value.setLayoutProperty('drivers-layer', 'visibility', 'visible');
    
    // 更新司机位置
    try {
        console.log('开始更新司机位置');
        updateDriversPositions(driversPositions);
    } catch (error) {
        console.error('更新司机位置时发生错误:', error);
    }
};

// Add this new ref
const routeRequestContextId = ref(0)



</script>

<style scoped>
.map-container {
    width: 100%;
    height: 100%;
    position: relative;
}

/* 路线类型控制样式 */

.route-type-control {
    position: absolute;
    top: 10px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
    display: flex;
    flex-direction: column;
}

/* 交通图层控制样式 */

.traffic-control {
    position: absolute;
    top: 90px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
}

/* 性能统计样式 */

.performance-stats {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgb(255 255 255 / 80%);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1;
}

/* 选择框样式 */

.selection-box {
    position: absolute;
    border: 2px dashed #1976D2;
    background-color: rgb(25 118 210 / 10%);
    pointer-events: none;
    z-index: 2;
}

/* 选择提示样式 */

.selection-tooltip {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgb(0 0 0 / 70%);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 10;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* 自动缩放控制样式 */

.zoom-control {
    position: absolute;
    top: 130px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
}

/* 司机位置显示控制样式 */

.driver-control {
    position: absolute;
    top: 170px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
}
</style>

<style>
/* 全局弹窗样式 */

.marker-hover-popup {
    z-index: 2000 !important;
    transition: opacity 0.2s ease-in-out;
}

.marker-hover-popup .maplibregl-popup-content {
    background: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgb(0 0 0 / 20%);
    overflow-y: auto;
    max-height: 180px !important; /* 限制最大高度 */
}

.marker-hover-popup .maplibregl-popup-tip {
    border-top-color: white !important;
}

/* 根据不同的锚点位置调整箭头颜色 */

.marker-hover-popup.maplibregl-popup-anchor-top .maplibregl-popup-tip {
    border-bottom-color: white !important;
    border-top-color: transparent !important;
}

.marker-hover-popup.maplibregl-popup-anchor-bottom .maplibregl-popup-tip {
    border-top-color: white !important;
    border-bottom-color: transparent !important;
}

.marker-hover-popup.maplibregl-popup-anchor-left .maplibregl-popup-tip {
    border-right-color: white !important;
    border-left-color: transparent !important;
}

.marker-hover-popup.maplibregl-popup-anchor-right .maplibregl-popup-tip {
    border-left-color: white !important;
    border-right-color: transparent !important;
}

/* 滚动条美化 */

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar {
    width: 4px;
}

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
}

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
}

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar-thumb:hover {
    background: #ccc;
}
</style>

<!-- 添加到样式部分 -->
<style scoped>
/* 已有样式保持不变 */

/* 自定义司机弹窗样式 */
:global(.driver-mini-popup) {
  padding: 0 !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
}

:global(.driver-mini-popup .maplibregl-popup-content) {
  padding: 0 !important;
  border-radius: 4px !important;
  overflow: hidden !important; 
  box-shadow: none !important;
}

:global(.driver-mini-popup .maplibregl-popup-tip) {
  border-top-color: rgba(255, 255, 255, 0.95) !important;
}
</style>
