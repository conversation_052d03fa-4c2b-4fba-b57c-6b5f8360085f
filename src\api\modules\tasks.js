/*
* Author: <EMAIL>'
* Date: '2023-08-10 19:38:17'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/tasks.js'
* File: 'tasks.js'
* Version: '1.0.0'
*/

import { DEL, GET, PAT, POST, PUT } from "../methods";

const path = 'tasks/'


export const getTasks = (params) => GET(path, params);
export const deleteTask = (params) => DEL(path, params);
export const addTask = (params) => POST(path, params);
export const updateTask = (params) => PUT(path, params);
export const rescheduleTask = (params) => PAT(path, params);
export const pauseTask = (params) => GET(path + 'pause/', params);
export const resumeTask = (params) => GET(path + 'resume/', params);


export const getTaskFunctions = () => GET(path + 'functions/')
