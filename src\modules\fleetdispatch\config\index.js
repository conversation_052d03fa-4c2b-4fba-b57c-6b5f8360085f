/**
 * 调度系统配置文件
 */

console.log('[Config] 调度系统配置文件正在加载...');

// API Keys
export const API_KEYS = {
  // TomTom API Key
  TOMTOM: '********************************'
};

console.log('[Config] API密钥是否已配置:', Object.keys(API_KEYS).map(key => `${key}: ${!!API_KEYS[key]}`).join(', '));

// 仓库配置
export const WAREHOUSE = {
  // 仓库坐标
  latitude: 43.8188751,
  longitude: -79.3452168,

  // 仓库地址
  address_id: 'CA|CP|B|10440098',
  address: '1-110 Torbay Rd',
  city: 'Markham',
  province: 'ON',
  postalCode: 'L3R 1G6',
  country: 'Canada'
};

// 班次配置
export const SHIFTS = {
  // 上午班
  MORNING: {
    value: 'morning',
    label: '上午班',
    start: '08:00',
    end: '12:00',
    // 起点是司机地址，终点是仓库
    startIsWarehouse: false,
    endIsWarehouse: true
  },

  // 下午班
  AFTERNOON: {
    value: 'afternoon',
    label: '下午班',
    start: '13:00',
    end: '17:00',
    // 起点和终点都是仓库
    startIsWarehouse: true,
    endIsWarehouse: true
  },

  // 晚班
  EVENING: {
    value: 'evening',
    label: '晚班',
    start: '18:00',
    end: '22:00',
    // 起点是仓库，终点是司机地址
    startIsWarehouse: true,
    endIsWarehouse: false
  }
};

// 订单类型
export const ORDER_TYPES = {
  PICKUP: 'PICKUP',
  DELIVERY: 'DELIVERY',
  SERVICE: 'SERVICE'
};

// 路线优化配置
export const ROUTE_OPTIMIZATION = {
  // 是否启用TomTom路线优化
  enabled: true,

  // 路线类型
  routeType: 'fastest', // 可选: fastest, shortest, eco

  // 是否考虑交通状况
  considerTraffic: true,

  // 车辆发动机类型
  vehicleEngineType: 'combustion' // 可选: combustion, electric
};

// 系统设置
export const SYSTEM = {
  // 默认刷新时间间隔（毫秒）
  refreshInterval: 30000,

  // 是否启用自动刷新
  autoRefresh: true,

  // 调试模式
  debug: process.env.NODE_ENV === 'development'
};