<script setup>
import { addCostReport, getAvailableCostAttributions } from '@/api/modules/statistics'
import { getReportableReimbursements } from '@/api/modules/staffs';

import StepButtons from './step_buttons.vue'
import { reimbursementStatusStyles } from '@/utils/constants'

const props = defineProps({
    date: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
})

const emit = defineEmits(['setCost'])

const readOnly = computed(() => props.id != null)

const costs = ref([])
const reimbursements = ref([])
const selectedReimbursements = ref([])

onMounted(() => {
    getCostAttrs()
    getReimbursements()
})

function getCostAttrs() {
    getAvailableCostAttributions().then(res => {
        costs.value = res.data.map(a => {
            return {
                id: a.id,
                amount: Number((a.default_amount / 100).toFixed(2)),
                hst: Number((a.default_hst / 100).toFixed(2)),
                name: a.name,
            }
        })
    })
}

function getReimbursements() {
    getReportableReimbursements().then(res => {
        reimbursements.value = res.data;
    })
}

function onCreateCostReport() {
    let params = {
        date: props.date,
        costs: costs.value.map(a => {
            return {
                id: a.id,
                amount: Math.round(a.amount * 100),
                hst: Math.round(a.hst * 100),
            }
        }),
        reimbursements: reimbursements.value.map(e => e.id)
    }
    addCostReport(params).then(res => {
        if (res.data.errCode == 365) {
            emit('setCost', res.data.id)
            getReimbursements()
        }
    })
}


</script>
<template>
    <el-row :gutter="40">
        <el-col :span="10">
            <el-text size="large" tag="b">
                {{ $t('statistics.fields.costs') }}
            </el-text>
            <el-divider />
            <el-table :data="costs">
                <el-table-column prop="name" :label="$t('fields.name')" width="180" />
                <el-table-column prop="amount" :label="$t('fields.amount')" align="center">
                    <template #default="scope">
                        <el-input-number v-model="scope.row.amount" :min="0" :precision="2" :step="1"
                            :disabled="readOnly" />
                    </template>
                </el-table-column>
                <el-table-column prop="hst" :label="$t('fields.tax')" align="center">
                    <template #default="scope">
                        <el-input-number v-model="scope.row.hst" :min="0" :precision="2" :step="1"
                            :disabled="readOnly" />
                    </template>
                </el-table-column>
            </el-table>
        </el-col>
        <el-col :span="12">
            <el-text size="large" tag="b">
                {{ $t('staffs.fields.reimbursements') }}
            </el-text>
            <el-divider />
            <el-table v-loading="data.loading" :data="reimbursements" stripe highlight-current-row
                @selection-change="selectedReimbursements = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="date" :label="$t('fields.date')" width="120" sortable />
                <el-table-column prop="employee" :label="$t('staffs.fields.employee')" width="150" sortable />
                <el-table-column prop="amount" :label="$t('fields.amount')" :formatter="currencyFormatter" sortable />
                <el-table-column prop="status" :label="$t('fields.status')" sortable>

                    <template #default="scope">
                        <el-tag :type="reimbursementStatusStyles[scope.row.status]" round size="small">
                            {{ $t(`staffs.reimbursement.selections.status.${scope.row.status}`) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed="right" width="80">
                    <template #default="scope">
                        <el-button type="danger" size="small" @click="onDeleteSalary(scope.row)"
                            v-if="scope.row.can_update">
                            {{ $t('operations.delete') }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-col>
    </el-row>
    <StepButtons v-bind="$attrs">
        <el-button type="primary" @click="onCreateCostReport">
            {{ $t('statistics.operations.generateReport') }}
        </el-button>
    </StepButtons>
</template>