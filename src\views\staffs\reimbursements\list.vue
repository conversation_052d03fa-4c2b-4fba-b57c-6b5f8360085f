<script setup name="StaffsReimbursementsList">
    import { Delete } from '@element-plus/icons-vue'
    import { usePagination } from '@/utils/composables'
    import FormMode from './components/FormMode/index.vue'
    import { getReimbursements, deleteReimbursement } from '@/api/modules/staffs'
    import { currencyFormatter } from '@/utils/formatter'
    import { reimbursementStatusStyles } from '@/utils/constants'


    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

    const data = ref({
        loading: false,
        formModeProps: {
            visible: false,
            id: '',
            canUpdate: null,
            canReview: null,
        },
        // 搜索
        search: {
            name__icontains: null,
        },
        // 批量操作
        batch: {
            enable: false,
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)
    const status = Object.keys(reimbursementStatusStyles)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            name__icontains: null,
        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
    })


    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getReimbursements(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.reimbursement_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        data.value.formModeProps.id = ''
        data.value.formModeProps.canUpdate = true
        data.value.formModeProps.canReview = null
        data.value.formModeProps.visible = true
    }

    function onEdit(row) {
        data.value.formModeProps.id = row.id
        data.value.formModeProps.canUpdate = row.can_update
        data.value.formModeProps.canReview = row.can_review
        data.value.formModeProps.visible = true
    }

    function onDel(row) {
        ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
            deleteReimbursement({ id: row.id }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.can_update) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-header title="默认模块" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    Reset
                                </el-button>
                                <el-button type="primary" @click="currentChange()">

                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    Filter
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">

                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column type="index" align="center" fixed width="50" />
                <el-table-column prop="date" :label="$t('fields.date')" width="120" />
                <el-table-column prop="employee" :label="$t('staffs.fields.employee')" width="120" />
                <el-table-column prop="description" :label="$t('fields.desc')" show-overflow-tooltip width="130" />
                <el-table-column prop="purpose" :label="$t('staffs.reimbursement.fields.purpose')" width="130"
                    show-overflow-tooltip />
                <el-table-column prop="vendor" :label="$t('staffs.reimbursement.fields.vendor')" width="130"
                    show-overflow-tooltip />
                <el-table-column prop="photos" :label="$t('fields.photos')" />
                <el-table-column prop="amount" :label="$t('fields.preTax')" :formatter="currencyFormatter"
                    align="right" />
                <el-table-column prop="hst" :label="$t('fields.tax')" :formatter="currencyFormatter" align="right" />
                <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter"
                    align="right" />
                <el-table-column prop="status" :label="$t('fields.status')" align="center">

                    <template #default="scope">
                        <el-tag :type="reimbursementStatusStyles[scope.row.status]" round size="small">
                            {{ $t(`staffs.reimbursement.selections.status.${scope.row.status}`) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="reviewed_by" :label="$t('staffs.reimbursement.fields.reviewedBy')" width="160" />
                <el-table-column prop="reviewed_at" :label="$t('staffs.reimbursement.fields.reviewedAt')" width="160"
                    align="right" sortable />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable />
                <el-table-column :label="$t('fields.operations')" width="100" align="right" fixed="right">

                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start"
                            v-if="scope.row.can_update">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" :can-update="data.formModeProps.canUpdate"
            :can-review="data.formModeProps.canReview" v-model="data.formModeProps.visible" @success="getDataList" />
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>