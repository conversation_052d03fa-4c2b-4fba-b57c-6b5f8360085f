// Order模型类
export class Order {
    constructor(data = {}) {
        this.id = data.id || null
        this.type = data.type || 'PICKUP'
        this.status = data.status || 'pending'
        this.driver_id = data.driver_id || null
        this.route_id = data.route_id || null
        this.stop_no = data.stop_no || null
    
        // 保留原始API字段名
        this.no = data.no || ''
        this.name = data.name || ''
        this.company = data.company || ''
        this.phone = data.phone || ''
        this.address = data.address || ''
        this.city = data.city || ''
        this.postal_code = data.postal_code || ''
    
        // 同时保持兼容旧字段名称
        this.pickup_address = data.pickup_address || ''
        this.delivery_address = data.delivery_address || ''
        this.customer_name = data.customer_name || data.name || ''
        this.customerName = data.customerName || data.name || ''
        this.customer_phone = data.customer_phone || data.phone || ''
        this.customerPhone = data.customerPhone || data.phone || ''
        this.customerCompany = data.customerCompany || data.company || ''
        this.customerAddress = data.customerAddress || data.address || ''
    
        this.priority = data.priority || 'normal'
        this.estimated_time = data.estimated_time || null
        this.notes = data.notes || ''
        this.created_at = data.created_at || new Date().toISOString()
        // 添加counterpart属性
        this.counterpart = data.counterpart || {
            name: '',
            phone: '',
            address: ''
        }
        // 添加地理坐标
        this.lng_lat = data.lng_lat || data.coordinates || null
        this.location = data.location || {
            latitude: data.latitude || (data.lng_lat ? data.lng_lat[0] : null),
            longitude: data.longitude || (data.lng_lat ? data.lng_lat[1] : null)
        }
    // 添加其他需要的属性
    }

    // 分配司机
    assignDriver(driverId) {
        this.driver_id = driverId
        this.status = 'assigned'
        return this
    }

    // 分配路线
    assignRoute(routeId, stopNo = null) {
        this.route_id = routeId
        if (stopNo !== null) {
            this.stop_no = stopNo
        }
        return this
    }

    // 取消分配
    unassign() {
    // 保存原始值以便追踪更改
        this._previousDriverId = this.driver_id
        this._previousRouteId = this.route_id
        this._previousStopNo = this.stop_no
    
        this.driver_id = null
        this.route_id = null
        this.stop_no = null
        this.status = 'pending'
        return this
    }

    // 克隆方法，创建一个副本
    clone() {
        return new Order({ ...this })
    }

    // 从普通对象转换为Order对象
    static fromObject(obj) {
        return new Order(obj)
    }

    // 将Order对象数组转换为普通对象数组
    static toObjectArray(orders) {
        return orders.map(order => ({ ...order }))
    }
} 