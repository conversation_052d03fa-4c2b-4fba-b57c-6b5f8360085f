<script setup name="DeliveryAdminOrdersDetail">
import useSettingsStore from '@/store/modules/settings'
import eventBus from '@/utils/eventBus'
import { useTabbar } from '@/utils/composables'
import DetailForm from './components/DetailForm/form.vue'
import DetailIndex from './components/DetailForm/index.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()

const formRef = ref()

function onSubmit() {
    formRef.value.submit(() => {
        eventBus.emit('get-data-list')
        goBack()
    })
}

function onCancel() {
    goBack()
}

// 返回列表页
function goBack() {
    if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
        useTabbar().close({ name: 'deliveryAdminOrdersList' })
    } else {
        router.push({ name: 'deliveryAdminOrdersList' })
    }
}
</script>

<template>
    <div>
        <page-header :title="route.query.id ? t('delivery.adminOrders.detail') : t('delivery.orders.newOrder')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>
        <DetailIndex v-if="route.query.id" :id="route.query.id" />
        <DetailForm v-else ref="formRef" @submitted="onSubmit" @cancelled="onCancel" />
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
