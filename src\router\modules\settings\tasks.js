const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/tasks',
    component: Layout,
    redirect: '/settings/tasks/list',
    name: 'settingTasks',
    meta: {
        title: '定时任务',
        icon: 'grommet-icons:schedule-new',
        auth: ['super'],
        i18n: 'route.system.tasks.title'
    },
    children: [
        {
            path: 'list',
            name: 'settingTaskList',
            component: () => import('@/views/settings/tasks/list.vue'),
            meta: {
                title: '列表',
                icon: 'grommet-icons:schedule-new',
                activeMenu: '/settings/tasks/list',
                i18n: 'route.system.tasks.title',
                sidebar: false,
                auth: ['super']
            }
        },

    ]
}
