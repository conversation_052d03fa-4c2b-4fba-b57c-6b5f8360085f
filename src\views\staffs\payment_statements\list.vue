<script setup name="StaffsPaymentStatementsList">
import { Delete, Promotion, Paperclip, Select, Download } from '@element-plus/icons-vue'
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { getStatements, deleteStatement, sendStatement, completeStatement, downloadStatement, updateStatementPaymentInfo } from '@/api/modules/staffs'
import { currencyFormatter } from '@/utils/formatter';
import PaymentMethodSelector from '../components/payment_method_selector.vue';

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        name__icontains: null,
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getStatements(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.statement_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'paymentStatementDetail'
    })
}

function onEdit(row) {
    router.push({
        name: 'paymentStatementDetail',
        query: {
            id: row.id,
            operation: !row.is_completed,
        }
    })
}

function onComplete(row) {
    data.value.loading = true
    completeStatement(row.id).then(res => {
        if (res.data.errCode == 365) {
            getDataList();
        }
        data.value.loading = false
    })
}

function onSend(row) {
    data.value.loading = true
    sendStatement(row.id).then(res => {
        if (res.data.errCode == 365) {
            data.value.loading = false
        }
    })
}

function onDownload(row) {
    data.value.loading = true
    downloadStatement(row.id).then(res => {
        if (res.data.errCode == 365) {
            window.open(res.data.url);
            data.value.loading = false
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(`确认删除「${row.no}」吗？`, '确认信息').then(() => {
        deleteStatement({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

const updatePaymentInfoVisible = ref(false)
const statementToModify = ref({
    id: null,
    paid_amount: 0.0,
    payment_method: null,
    payment_no: null,
    paid_at: null,
    total: 0.0
})
function onUpdatePaymentInfo(row) {
    statementToModify.value.id = row.id
    statementToModify.value.paid_amount = Number(((row?.paid_amount ?? 0) / 100).toFixed(2))
    statementToModify.value.payment_method = row.payment_method
    statementToModify.value.payment_no = row.payment_no
    statementToModify.value.paid_at = row.paid_at
    statementToModify.value.total = currencyFormatter(null, null, row.total)

    updatePaymentInfoVisible.value = true;
}

function onConfirmUpdatePaymentInfo() {
    let params = JSON.parse(JSON.stringify(statementToModify.value))
    params.paid_amount = Math.round(statementToModify.value.paid_amount * 100)
    updateStatementPaymentInfo(params).then(res => {
        if (res.data.errCode == 365) {
            onCancelPaymentInfo();
            getDataList();
        }
    })

}

function onCancelPaymentInfo() {
    updatePaymentInfoVisible.value = false;
    statementToModify.value = {
        id: null,
        paid_amount: 0.0,
        payment_method: null,
        payment_no: null,
        paid_at: null,
        total: 0.0
    }
}


</script>

<template>
    <div>
        <page-header title="Payment Statements" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    Reset
                                </el-button>
                                <el-button type="primary" @click="currentChange()">

                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    Filter
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">

                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit"
                @sort-change="sortChange">
                <el-table-column prop="no" :label="$t('fields.no')" width="60" />
                <el-table-column prop="employee" :label="$t('staffs.fields.employee')" width="160" class-name="bold" />
                <el-table-column prop="period_start" :label="$t('staffs.fields.period')" width="200" class-name="bold">

                    <template #default="scope">
                        {{ scope.row.period_start }} ~ {{ scope.row.period_end }}
                    </template>
                </el-table-column>
                <el-table-column prop="salary" :label="$t('staffs.stub.fields.salary')" align="right"
                    :formatter="currencyFormatter" />
                <el-table-column prop="hst" :label="$t('staffs.stub.fields.tax')" :formatter="currencyFormatter"
                    align="right" />
                <el-table-column prop="tip" :label="$t('staffs.stub.fields.tip')" :formatter="currencyFormatter"
                    align="right" />
                <el-table-column prop="reimbursed_expense" :label="$t('staffs.fields.reimbursements')" align="right"
                    :formatter="currencyFormatter" />
                <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter" align="right"
                    class-name="bold" />
                <el-table-column prop="paid_amount" :label="$t('staffs.paymentStatement.fields.paidAmount')"
                    align="right" :formatter="currencyFormatter" />
                <el-table-column prop="paid_at_trimmed" :label="$t('staffs.paymentStatement.fields.paidAt')" width="160"
                    sortable="custom" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="200" align="center" fixed="right">

                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.pay')" placement="top-start">
                            <el-button type="primary" :icon="Paperclip" circle plain size="small"
                                v-if="!scope.row.is_completed" @click="onUpdatePaymentInfo(scope.row)" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.download')" placement="top-start">
                            <el-button type="success" :icon="Download" circle plain size="small"
                                @click="onDownload(scope.row)" />
                        </el-tooltip>

                        <el-tooltip class="box-item" :content="$t('operations.send')" placement="top-start">
                            <el-button type="success" :icon="Promotion" circle size="small" @click="onSend(scope.row)"
                                v-loading="data.loading" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start"
                            v-if="!scope.row.is_completed">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)"
                                v-loading="data.loading" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.complete')" placement="top-start"
                            v-if="!scope.row.is_completed">
                            <el-button type="primary" :icon="Select" circle size="small" @click="onComplete(scope.row)"
                                v-loading="data.loading" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <el-dialog v-model="updatePaymentInfoVisible" title="Update Statement Payment Info" width="500">
            <el-form :model="statementToModify" size="default" label-width="100px">
                <el-form-item :label="$t('fields.total')">
                    <el-text size="large" tag="b">{{ statementToModify.total }}</el-text>
                </el-form-item>
                <el-form-item :label="$t('staffs.paymentStatement.fields.paidAmount')">
                    <el-input-number v-model="statementToModify.paid_amount" :min="0" :max="statementToModify.total"
                        :precision="2" />
                </el-form-item>
                <el-form-item :label="$t('staffs.fields.paymentMethod')">
                    <PaymentMethodSelector v-model="statementToModify.payment_method" />
                </el-form-item>
                <el-form-item :label="$t('staffs.paymentStatement.fields.paidAt')">
                    <el-date-picker v-model="statementToModify.paid_at" type="datetime"
                        placeholder="Select date and time" />
                </el-form-item>
                <el-form-item :label="$t('staffs.paymentStatement.fields.paymentNo')">
                    <el-input v-model="statementToModify.payment_no" />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelPaymentInfo">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmUpdatePaymentInfo">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: #bbb;
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}

.bold {
    font-weight: bolder;
}
</style>