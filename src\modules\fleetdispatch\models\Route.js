// Route模型类
export class Route {
    constructor(data = {}) {
        this.id = data.id || null // 保留原始id
        this.routeNumber = data.routeNumber || null
        this.name = data.name || data.routeNumber || data.id || null
        this.driverId = data.driverId || null
        this.driver = data.driver || data.driverId || null // 兼容两种属性名
        this.status = data.status || 'pending'
        this.orders = data.orders || []
        this.created_at = data.created_at || new Date().toISOString()
        this.updated_at = data.updated_at || new Date().toISOString()

        // 添加起点和终点属性
        this.start_point = data.start_point || null // 起点地址ID
        this.end_point = data.end_point || null // 终点地址ID

        // 添加路线几何数据属性
        this.routeGeometry = data.routeGeometry || {
            direct: null,     // 直线路线的坐标点
            realistic: null   // 真实路线的坐标点
        }

        // 添加详细的路线信息属性
        this.routeDetails = data.routeDetails || {
            direct: {
                totalDistance: 0,
                totalDuration: 0,
                segments: []  // 每段的详细信息
            },
            realistic: {
                totalDistance: 0,
                totalDuration: 0,
                weight: 0,
                weightName: null,
                uuid: null,
                segments: [],  // 每段的详细信息 (legs)
                waypoints: []  // 路径点信息
            }
        }

        // 添加路线可见性控制
        this.visible = data.visible !== undefined ? data.visible : true

        // 添加路线颜色属性
        this.color = data.color || null
    }

    // 分配司机
    assignDriver(driverId) {
        this.driverId = driverId
        this.driver = driverId // 同时更新driver字段
        return this
    }

    // 取消司机分配
    unassignDriver() {
        this.driverId = null
        this.driver = null // 同时更新driver字段
        return this
    }

    // 更新路线状态
    updateStatus(status) {
        this.status = status
        this.updated_at = new Date().toISOString()
        return this
    }

    // 添加订单到路线
    addOrder(order) {
    // 确保不重复添加
        if (!this.orders.some(o => o.orderId === order.id)) {
            this.orders.push({
                orderId: order.id,
                stopNumber: this.orders.length + 1
            })
            this.updated_at = new Date().toISOString()
        }
        return this
    }

    // 批量添加订单
    addOrders(orders) {
        orders.forEach(order => this.addOrder(order))
        return this
    }

    // 移除订单
    removeOrder(orderId) {
        const initialLength = this.orders.length
        this.orders = this.orders.filter(o => o.orderId !== orderId)

        // 如果确实移除了订单，更新其他订单的停靠序号
        if (this.orders.length < initialLength) {
            this.orders = this.orders.map((order, index) => ({
                ...order,
                stopNumber: index + 1
            }))
            this.updated_at = new Date().toISOString()
        }

        return this
    }

    // 重新排序订单
    reorderStops(orderIds) {
    // 根据提供的订单ID顺序重新排序
        const newOrders = []
        orderIds.forEach((orderId, index) => {
            const existingOrder = this.orders.find(o => o.orderId === orderId)
            if (existingOrder) {
                newOrders.push({
                    ...existingOrder,
                    stopNumber: index + 1
                })
            }
        })

        // 确保未明确指定的订单也被保留
        this.orders.forEach(order => {
            if (!orderIds.includes(order.orderId)) {
                newOrders.push(order)
            }
        })

        this.orders = newOrders
        this.updated_at = new Date().toISOString()
        return this
    }

    // 设置路线几何数据
    setRouteGeometry(type, coordinates) {
        if (!this.routeGeometry) {
            this.routeGeometry = { direct: null, realistic: null }
        }

        if (type === 'direct' || type === 'realistic') {
            this.routeGeometry[type] = coordinates
            this.updated_at = new Date().toISOString()
        }

        return this
    }

    // 设置详细的路线信息
    setRouteDetails(type, routeData) {
        if (!this.routeDetails) {
            this.routeDetails = {
                direct: { totalDistance: 0, totalDuration: 0, segments: [] },
                realistic: { totalDistance: 0, totalDuration: 0, weight: 0, weightName: null, uuid: null, segments: [], waypoints: [] }
            }
        }

        if (type === 'direct') {
            this.routeDetails.direct = {
                totalDistance: routeData.totalDistance || 0,
                totalDuration: routeData.totalDuration || 0,
                segments: routeData.segments || []
            }
        } else if (type === 'realistic') {
            this.routeDetails.realistic = {
                totalDistance: routeData.distance || 0,
                totalDuration: routeData.duration || 0,
                weight: routeData.weight || 0,
                weightName: routeData.weight_name || null,
                uuid: routeData.uuid || null,
                segments: routeData.legs || [],  // Mapbox API 中的 legs 包含每段信息
                waypoints: routeData.waypoints || []
            }
        }

        this.updated_at = new Date().toISOString()
        return this
    }

    // 获取路线详细信息
    getRouteDetails(type = 'direct') {
        if (!this.routeDetails) {
            return null
        }
        return this.routeDetails[type]
    }

    // 获取路线几何数据
    getRouteGeometry(type = 'direct') {
        if (!this.routeGeometry) {
            return null
        }

        return this.routeGeometry[type]
    }

    // 设置路线可见性
    setVisible(visible) {
        this.visible = visible
        return this
    }

    // 设置路线颜色
    setColor(color) {
        this.color = color
        return this
    }

    // 获取路线颜色
    getColor() {
        // 如果路线有自定义颜色，优先使用
        if (this.color) {
            return this.color
        }

        // 否则使用默认颜色
        return '#FF9800'
    }

    // 设置路线起点
    setStartPoint(addressId) {
        this.start_point = addressId
        this.updated_at = new Date().toISOString()
        return this
    }

    // 设置路线终点
    setEndPoint(addressId) {
        this.end_point = addressId
        this.updated_at = new Date().toISOString()
        return this
    }

    // 获取路线起点
    getStartPoint() {
        return this.start_point
    }

    // 获取路线终点
    getEndPoint() {
        return this.end_point
    }

    // 克隆方法
    clone() {
        return new Route({ ...this })
    }

    // 从普通对象转换为Route对象
    static fromObject(obj) {
        return new Route(obj)
    }

    // 将Route对象数组转换为普通对象数组
    static toObjectArray(routes) {
        return routes.map(route => ({ ...route }))
    }
}