<script setup name="DeliveryTimetablesPickupTimesList">
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { getPickupTimes, alterPickupTimeAvailability, deletePickupTime, batchAlterTimetableAvailability } from '@/api/modules/delivery'
import { currencyFormatter } from '@/utils/formatter'
import { Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { userTypes, weekdays } from '@/utils/constants'
import { getUserGroups } from '@/api/modules/users';
import { getZones } from '@/api/modules/dispatcher'

const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()

const data = ref({
    loading: false,
    batch: {
        selectionDataList: []
    },
    dataList: [],
    search: {
        name__icontains: null,
        description: null,
        fee__gte: 0,
        fee__lte: null,
        weekdays__contains: [],
        holidays__in: [],
        zones__in: [],
        user_type: null,
        user_group: null,
    },

})
const userGroups = ref([])
const holidays = ref([])
const zones = ref([])


onMounted(() => {
    setUserGroups()
    setZones()
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getPickupTimes(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.pickup_time_list
        pagination.value.total = res.data.total
    })
}


function setUserGroups() {
    getUserGroups().then(res => {
        userGroups.value = res.data.group_list
    })
}
function setZones() {
    getZones().then(res => {
        zones.value = res.data.zone_list
    })
}

// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
        description: null,
        fee__gte: 0,
        fee__lte: null,
        weekdays__contains: [],
        holidays__in: [],
        zones__in: [],
        user_type: null,
        user_group: null,
    }
    currentChange()
}



// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'deliveryPickupTimeDetail'
    })
}

function onEdit(row) {
    router.push({
        name: 'deliveryPickupTimeDetail',
        query: {
            id: row.id
        }
    })
}
function onUpdateAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    alterPickupTimeAvailability(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}


function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        let params = {
            id: JSON.stringify([row.id])
        }
        deletePickupTime(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onBatchDel() {
    ElMessageBox.confirm(t('dialog.messages.batchDeletion', { module: t('delivery.services.sizeWeight') }), t('dialog.titles.confirmation')).then(() => {
        let ids = []
        data.value.batch.selectionDataList.forEach(i => ids.push(i.id))

        let params = {
            id: JSON.stringify(ids)
        }
        deletePickupTime(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}


function onBatchAlterAvailability(isAvailable) {
    let ids = []
    data.value.batch.selectionDataList.forEach(i => ids.push(i.id))

    let params = {
        id: ids,
        isAvailable: isAvailable,
        module: 'pt'
    }
    batchAlterTimetableAvailability(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}
const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}
</script>

<template>
    <div>
        <page-header :title="$t('delivery.services.pickupTime')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.name')">
                                        <el-input v-model="data.search.name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.name') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.desc')">
                                        <el-input v-model="data.search.description__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.desc') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('delivery.services.fields.fee')">
                                        <el-input-number v-model="data.search.fee__gte" :min="0"
                                            :placeholder="$t('delivery.services.fields.feeMin')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.fee__lte" :min="0"
                                            :placeholder="$t('delivery.services.fields.feeMax')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>

                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.services.fields.zones')">
                                        <el-select v-model="data.search.zones__in" :multiple="true"
                                            :placeholder="$t('statSelectHolder', { field: $t('delivery.services.fields.zones') })"
                                            clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in zones" :value="item.id" :key="item.id"
                                                :label="item.name" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.type')">
                                        <el-select v-model="data.search.user_type"
                                            :placeholder="$t('statSelectHolder', { field: $t('user.fields.type') })"
                                            clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in userTypes" :value="item" :key="item"
                                                :label="$t(`user.selection.userType.${item}`)">
                                                {{ $t(`user.selection.userType.${item}`) }}
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.group')">
                                        <el-select v-model="data.search.user_group"
                                            :placeholder="$t('statSelectHolder', { field: $t('user.fields.group') })"
                                            clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in userGroups" :value="item.id" :key="item.id"
                                                :label="item.name" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.weekdays')">
                                        <el-select v-model="data.search.weekdays__contains" multiple
                                            :placeholder="$t('statSelectHolder', { field: $t('fields.weekdays') })"
                                            clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in weekdays" :key="item"
                                                :label="$t(`selection.weekday.${item}`)" :value="item" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.holidays')">
                                        <el-select v-model="data.search.holidays__in" multiple
                                            :placeholder="$t('statSelectHolder', { field: $t('fields.holidays') })"
                                            clearable @change="currentChange()" @clear="currentChange()"
                                            :disabled="holidays.length == 0">
                                            <el-option v-for="item in holidays" :key="item.id" :label="items.name"
                                                :value="item.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button @click="onBatchDel" type="danger">
                        {{ $t('operations.batch', { op: t('operations.delete') }) }}
                    </el-button>
                    <el-button-group>
                        <el-button type="success" @click="onBatchAlterAvailability(true)">
                            {{ $t('operations.batch', { op: t('operations.enable') }) }}
                        </el-button>
                        <el-button type="warning" @click="onBatchAlterAvailability(false)">
                            {{ $t('operations.batch', { op: t('operations.disable') }) }}
                        </el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')" min-width="150" show-overflow-tooltip />
                <el-table-column prop="zones.length" :label="$t('delivery.services.fields.zones')" show-overflow-tooltip
                    align="center" width="100" />
                <el-table-column prop="start_at" :label="$t('delivery.services.fields.startAt')" sortable="custom"
                    width="100" />
                <el-table-column prop="end_at" :label="$t('delivery.services.fields.endAt')" sortable="custom"
                    width="100" />
                <el-table-column prop="upload_at" :label="$t('delivery.services.fields.uploadAt')" sortable="custom"
                    width="110" />
                <el-table-column prop="available_until" :label="$t('delivery.services.fields.availableUntil')"
                    sortable="custom" width="134" />
                <el-table-column prop="fee" :label="$t('delivery.services.fields.fee')" :formatter="currencyFormatter"
                    sortable="custom" align="center" width="100" />
                <el-table-column prop="weekdays.length" :label="$t('fields.weekdays')" align="center" width="100" />
                <el-table-column prop="holidays.length" :label="$t('fields.holidays')" align="center" width="100" />
                <el-table-column prop="user_type" :label="$t('user.fields.type')" align="center" width="100">
                    <template #default="scope">
                        <el-tag type="primary" plain v-if="scope.row.user_type === 'business'">B</el-tag>
                        <el-tag type="success" plain v-else-if="scope.row.user_type === 'personal'">P</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="user_group.name" :label="$t('user.fields.group')" align="center" width="110" />
                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
