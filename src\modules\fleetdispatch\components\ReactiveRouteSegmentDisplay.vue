<template>
    <div class="route-segment-display">
        <!-- 路线总览 -->
        <div class="route-summary" v-if="routeSegmentInfo">
            <div class="summary-item">
                <span class="label">总距离:</span>
                <span class="value">{{ formatDistance(routeSegmentInfo.totalDistance) }}</span>
            </div>
            <div class="summary-item">
                <span class="label">总时间:</span>
                <span class="value">{{ formatDuration(routeSegmentInfo.totalDuration) }}</span>
            </div>
            <div class="summary-item">
                <span class="label">订单数:</span>
                <span class="value">{{ routeSegmentInfo.orders.length }}</span>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="orders-list">
            <div 
                v-for="(order, index) in ordersWithSegments" 
                :key="order.id"
                class="order-item"
                :class="{
                    'has-segment': order.segment_distance > 0,
                    'no-segment': order.segment_distance === 0
                }"
            >
                <!-- 订单基本信息 -->
                <div class="order-header">
                    <div class="order-number">{{ index + 1 }}</div>
                    <div class="order-info">
                        <div class="order-name">{{ order.name || order.id }}</div>
                        <div class="order-address">{{ order.address || '地址未知' }}</div>
                    </div>
                </div>

                <!-- 路段信息 -->
                <div class="segment-info" v-if="order.segment_distance > 0">
                    <div class="segment-item">
                        <i class="icon-distance"></i>
                        <span class="segment-label">距离:</span>
                        <span class="segment-value">{{ formatDistance(order.segment_distance) }}</span>
                    </div>
                    <div class="segment-item">
                        <i class="icon-time"></i>
                        <span class="segment-label">时间:</span>
                        <span class="segment-value">{{ formatDuration(order.segment_duration) }}</span>
                    </div>
                    <div class="segment-item">
                        <i class="icon-route"></i>
                        <span class="segment-label">类型:</span>
                        <span class="segment-value">{{ formatSegmentType(order.segment_type) }}</span>
                    </div>
                </div>

                <!-- 无路段信息提示 -->
                <div class="no-segment-info" v-else>
                    <span class="no-segment-text">同地址订单</span>
                </div>
            </div>
        </div>

        <!-- 刷新按钮 -->
        <div class="actions">
            <button 
                class="refresh-btn"
                @click="refreshRouteSegments"
                :disabled="isRefreshing"
            >
                <i class="icon-refresh" :class="{ 'spinning': isRefreshing }"></i>
                {{ isRefreshing ? '刷新中...' : '刷新路段' }}
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, inject, onMounted, onUnmounted } from 'vue'

const props = defineProps({
    routeId: {
        type: String,
        required: true
    }
})

// 注入响应式路线管理
const reactiveRouteManagement = inject('reactiveRouteManagement', null)

// 状态
const isRefreshing = ref(false)
const routeSegmentInfo = ref(null)

// 计算属性
const ordersWithSegments = computed(() => {
    if (!routeSegmentInfo.value || !routeSegmentInfo.value.orders) {
        return []
    }
    
    return routeSegmentInfo.value.orders.map(order => ({
        ...order,
        segment_distance: order.segment_distance || 0,
        segment_duration: order.segment_duration || 0,
        segment_type: order.segment_type || null
    }))
})

// 格式化函数
const formatDistance = (distance) => {
    if (!distance || distance === 0) return '0km'
    
    if (distance < 1000) {
        return `${Math.round(distance)}m`
    } else {
        return `${(distance / 1000).toFixed(2)}km`
    }
}

const formatDuration = (duration) => {
    if (!duration || duration === 0) return '0分钟'
    
    const minutes = Math.round(duration / 60)
    if (minutes < 60) {
        return `${minutes}分钟`
    } else {
        const hours = Math.floor(minutes / 60)
        const remainingMinutes = minutes % 60
        return `${hours}小时${remainingMinutes}分钟`
    }
}

const formatSegmentType = (type) => {
    const typeMap = {
        'start_to_first': '起点→首单',
        'order_to_order': '订单间',
        'last_to_end': '末单→终点'
    }
    return typeMap[type] || '未知'
}

// 刷新路段信息
const refreshRouteSegments = async () => {
    if (!reactiveRouteManagement || isRefreshing.value) return
    
    isRefreshing.value = true
    try {
        await reactiveRouteManagement.refreshRoute(props.routeId)
        updateRouteSegmentInfo()
    } catch (error) {
        console.error('刷新路段信息失败:', error)
    } finally {
        isRefreshing.value = false
    }
}

// 更新路段信息
const updateRouteSegmentInfo = () => {
    if (!reactiveRouteManagement) return
    
    const segmentInfo = reactiveRouteManagement.getRouteSegmentInfo(props.routeId)
    routeSegmentInfo.value = segmentInfo
    
    console.log(`更新路线 ${props.routeId} 的路段显示信息:`, segmentInfo)
}

// 监听路段更新事件
const handleSegmentUpdate = (data) => {
    if (data.routeId === props.routeId) {
        console.log(`收到路线 ${props.routeId} 的路段更新事件`)
        updateRouteSegmentInfo()
    }
}

// 生命周期
onMounted(() => {
    // 初始加载
    updateRouteSegmentInfo()
    
    // 监听路段更新事件
    if (window.eventBus) {
        window.eventBus.on('ROUTE_SEGMENTS_UPDATED', handleSegmentUpdate)
    }
})

onUnmounted(() => {
    // 清理事件监听
    if (window.eventBus) {
        window.eventBus.off('ROUTE_SEGMENTS_UPDATED', handleSegmentUpdate)
    }
})

// 监听路线ID变化
watch(() => props.routeId, () => {
    updateRouteSegmentInfo()
}, { immediate: true })
</script>

<style scoped>
.route-segment-display {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
}

.route-summary {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.summary-item .label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.summary-item .value {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.orders-list {
    max-height: 400px;
    overflow-y: auto;
}

.order-item {
    background: white;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.order-item:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.order-item.has-segment {
    border-left: 4px solid #28a745;
}

.order-item.no-segment {
    border-left: 4px solid #ffc107;
}

.order-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.order-number {
    width: 24px;
    height: 24px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-right: 12px;
}

.order-info {
    flex: 1;
}

.order-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 2px;
}

.order-address {
    font-size: 12px;
    color: #666;
}

.segment-info {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.segment-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.segment-label {
    color: #666;
}

.segment-value {
    font-weight: bold;
    color: #333;
}

.no-segment-info {
    text-align: center;
    padding: 8px;
}

.no-segment-text {
    font-size: 12px;
    color: #ffc107;
    font-style: italic;
}

.actions {
    margin-top: 16px;
    text-align: center;
}

.refresh-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
    background: #0056b3;
}

.refresh-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.icon-refresh.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 图标样式 */
.icon-distance::before { content: "📏"; }
.icon-time::before { content: "⏱️"; }
.icon-route::before { content: "🛣️"; }
.icon-refresh::before { content: "🔄"; }
</style>
