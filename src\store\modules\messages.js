/*
* Author: <EMAIL>'
* Date: '2023-06-08 19:50:25'
* Project: 'FleetNowV3'
* Path: 'src/store/modules/messages.js'
* File: 'messages.js'
* Version: '1.0.0'
*/
import { getAdminWorkStatus, adminPunchIn, adminPunchOut, sendCSMessage, readCSMessages, confirmConversationRequest, endConversation, confirmTransferConversation, } from '@/api/modules/messenger'
import useTodosStore from './todos';
import storage from '@/utils/storage'
import { ElNotification, ElMessageBox } from 'element-plus'
import router from "@/router/index";
// 导入FirebaseMessagingService服务以便转发消息
import { firebaseMessagingService } from '@/modules/fleetdispatch/services/FirebaseMessagingService';
import useUserStore from './user';

const useMessagesStore = defineStore(
    'messages',
    {
        state: () => ({
            adminIsOnline: false,
            lastCheckAt: null,
            activeUsers: [],
            csMessages: {},
        }),
        getters: {
            messages: (state) => {
                return (userId) => state.csMessages[userId] ?? [];
            },
            newMessagesCount: (state) => {
                let count = 0;
                for (let msgs of Object.values(state.csMessages)) {
                    if (Array.isArray(msgs)) {
                        let unreadMsgs = msgs.filter(m => m.admin_name == null && !m.is_read)
                        count += unreadMsgs.length
                    }
                }
                return count
            },
        },
        actions: {
            onReceiveMessage(msg) {
                // 必须先处理原始消息，确保各个系统的基本消息处理逻辑
                console.log('消息存储收到Firebase消息:', msg);
                
                // 1. 处理CS消息和系统消息
                let message = {
                    ...msg.data,
                    ...msg.notification,
                }
                
                // 2. 如果是调度系统消息(前台消息)，先转发给FirebaseMessagingService处理
                this.forwardToFleetDispatch(msg);
                
                // 3. 继续处理普通CS消息
                this.addMessage(message);
            },
            
            /**
             * 转发消息到FleetDispatch系统
             * @param {Object} msg - 原始消息对象
             */
            forwardToFleetDispatch(msg) {
                try {
                    // 检查是否是调度系统相关消息
                    if (!msg || !msg.data) return;
                    
                    // 获取用户store，检查是否停止接收消息
                    const userStore = useUserStore();
                    if (userStore.stopReceivingMessages) {
                        console.log('用户已设置停止接收消息，跳过转发到FleetDispatch');
                        return;
                    }
                    
                    // 检查是否有特定的调度系统action
                    const action = msg.data.action;
                    const isDispatcherNotification = 
                        msg.data.catalog === 'DISPATCHER_NOTIFICATION' || 
                        (action && ['DRIVER_POSITIONS', 'DRIVER_UPDATE', 'ORDER_STATUS_UPDATED', 'ORDER_CREATED'].includes(action));
                    
                    // 如果是调度系统消息，则转发
                    if (isDispatcherNotification) {
                        console.log('检测到调度系统消息，转发到FirebaseMessagingService:', action);
                        
                        // 确保FirebaseMessagingService已初始化
                        if (firebaseMessagingService && typeof firebaseMessagingService.onReceiveMessage === 'function') {
                            // 转发消息给FirebaseMessagingService处理
                            firebaseMessagingService.onReceiveMessage(msg);
                        } else {
                            console.error('FirebaseMessagingService未初始化或onReceiveMessage不是函数');
                        }
                    }
                } catch (error) {
                    console.error('转发消息到FleetDispatch系统失败:', error);
                }
            },

            addMessage(message) {
                console.log('Store Adding: ', message)
                switch (message.type) {
                    case 'todo':
                        const todoStore = useTodosStore();
                        todoStore.onReceiveTodo(message.data);
                        break;
                    case 'csChat':
                    case 'csLink':
                        if (!this.activeUsers.some(u => u.id == message.user_id)) {
                            return;
                        }

                        if (!Array.isArray(this.csMessages[message.user_id])) {
                            this.csMessages[message.user_id] = []
                        }
                        if (!this.csMessages[message.user_id].some(m => m.id == message.id)) {
                            this.csMessages[message.user_id].push(message)
                            storage.local.set('messengerCSMessages', JSON.stringify(this.csMessages))
                        }
                        break;
                    case 'csRequestConversation':
                        if (!this.activeUsers.some(u => u.id == message.user_id)) {
                            ElMessageBox.confirm(`New Request for conversation from ${message.user_name}`, 'Confirm').then(() => {
                                let params = {
                                    userId: message.user_id,
                                    userToken: message.from_token
                                }
                                confirmConversationRequest(params).then(res => {
                                    if (res.data.errCode == null) {
                                        this.activeUsers.push(res.data);
                                        storage.local.set('messengerCSActiveUsers', JSON.stringify(this.activeUsers))
                                        router.push({ name: 'csMessageChat', params: { userId: message.user_id } })

                                    }
                                })
                            }).catch(() => { })
                        }
                        break;
                    case 'csTransferConversation':
                        if (!this.activeUsers.some(u => u.id == message.user_id)) {
                            ElMessageBox.confirm(`${message.content} ${message.user_name}`, 'Confirm').then(() => {
                                let params = {
                                    userId: message.user_id,
                                    userToken: message.user_token,
                                    adminToken: message.from_token,
                                }
                                confirmTransferConversation(params).then(res => {
                                    if (res.data.errCode == null) {
                                        this.activeUsers.push(res.data);
                                        storage.local.set('messengerCSActiveUsers', JSON.stringify(this.activeUsers))
                                        router.push({ name: 'csMessageChat', params: { userId: message.user_id } })
                                    }
                                })
                            }).catch(() => { })
                        }
                        break;
                    case 'csConfirmTransferConversation':
                        this.removeUser(message.user_id);
                        ElMessageBox.alert(`${message.user_name}: ${message.content}`, 'Notice')
                        break;

                    case 'orderUploadedNotification':
                        let tone = new Audio(new URL('@/assets/audios/notification/order_uploaded.mp3', import.meta.url).href)
                        tone.loop = false
                        tone.play().catch(e => { })
                        ElNotification({
                            title: message.title,
                            message: message.body,
                            position: 'bottom-right',
                            type: 'success',
                        });
                    default:
                        break;
                }
            },

            endConversation(userId) {
                endConversation({ userId: userId }).then(res => {
                    if (res.data.errCode == 365) {
                        this.removeUser(userId);
                    }
                })
            },


            removeUser(id) {
                let userIdx = this.activeUsers.findIndex(u => u.id == id);
                if (userIdx >= 0) {
                    this.activeUsers.splice(userIdx, 1);
                    delete this.csMessages[id];
                    storage.local.set('messengerCSActiveUsers', JSON.stringify(this.activeUsers))
                    storage.local.set('messengerCSMessages', JSON.stringify(this.csMessages))
                }
            },


            setActiveUsers(activeUsers) {
                this.activeUsers = JSON.parse(activeUsers);
            },

            setCSMessages(csMessages) {
                this.csMessages = JSON.parse(csMessages);
            },

            async getAdminStatus() {
                let res = await getAdminWorkStatus();
                this.adminIsOnline = res.data.status;
            },

            async punchIn() {
                await adminPunchIn();
                this.getAdminStatus();
            },

            async punchOut() {
                if (this.activeUsers.length > 0) {
                    for (let user of this.activeUsers) {
                        await endConversation(user.user_id)
                    }
                }
                await adminPunchOut();
                await this.getAdminStatus();
                if (!this.adminIsOnline) {
                    this.$reset()
                    storage.local.remove('messengerCSMessages')
                    storage.local.remove('messengerCSActiveUsers')

                }
            },

            async readMessages(userId) {
                let unreadMessages = this.messages(userId).filter(m => m.admin_name == null && !m.is_read)
                if (unreadMessages.length > 0) {
                    let params = { ids: unreadMessages.map(m => m.id) }
                    await readCSMessages(params);
                    unreadMessages.forEach((e) => e.is_read = true);
                }
            },

            async sendMessage(token, content, type, user_id) {
                let params = {
                    content: content,
                    to_tokens: [token],
                    type: type
                };
                console.log(params)
                let res = await sendCSMessage(params);
                if (res.data.errCode == null) {
                    if (!Array.isArray(this.csMessages[user_id])) {
                        this.csMessages[user_id] = [];
                    }
                    this.csMessages[user_id].push(res.data);
                    await this.readMessages(user_id);
                    storage.local.set('messengerCSMessages', JSON.stringify(this.csMessages))
                }
            }
        }

    })

export default useMessagesStore;
