<script setup name="MessengerCsMessagesOfflineMessages">
import { getOfflineMessages, readCSMessages, respondOfflineMessages } from '@/api/modules/messenger';
import { SuccessFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const emit = defineEmits(['setCurrentUser', 'setUnreadMessageCount'])

const currentUserPk = ref()

watch(
    () => currentUserPk.value,
    (val) => { emit('setCurrentUser', val) },
)

const data = ref({
    loading: false,
    dataList: [],
})

defineExpose({
    reload() { getData(); }
})

onMounted(() => {
    getData();
})

function getData() {
    data.value.loading = true
    getOfflineMessages().then(res => {
        data.value.dataList = res.data
        let unreadCount = 0
        if (res.data.length) {
            currentUserPk.value = res.data[0].user.id;
            unreadCount = res.data.reduce((total, curr) => total + curr.message_list.length, 0)
        }
        emit('setUnreadMessageCount', unreadCount)
        data.value.loading = false
    })
}

function onRead(messages) {
    ElMessageBox.confirm(t('dialog.messages.readMessages'), t('dialog.titles.confirmation')).then(() => {
        let params = { ids: messages.map(e => e.id) }
        readCSMessages(params).then(res => {
            if (res.data.errCode == 365) {
                getData();
            }
        })
    }).catch(() => { })
}

const respondDialogVisible = ref(false)
const responseToItem = ref()
const respondToAllDevices = ref(true)
const respondWithEmail = ref(true)
const readAllMessages = ref(true)
const responseContent = ref()

function onRespond(item) {
    responseToItem.value = item;
    respondDialogVisible.value = true;
}
function onCancelRespond() {
    responseToItem.value = null;
    respondDialogVisible.value = false;
}
function onConfirmRespond() {
    data.value.loading = true

    let params = {
        userId: responseToItem.value.user.id,
        toAllDevices: respondToAllDevices.value,
        withEmail: respondWithEmail.value,
        content: responseContent.value
    }
    respondOfflineMessages(params).then(res => {
        if (res.data.errCode == 365) {
            if (readAllMessages) {
                onRead(responseToItem.value.message_list);
            }
            onCancelRespond();
        }
        data.value.loading = false
    })
}

function onPutInTodo() { }
</script>

<template>
    <div style="height:calc(100vh - 422px);">
        <el-tabs tab-position="left" v-if="data.dataList.length" style="height: 100%" v-model="currentUserPk">
            <el-tab-pane v-for=" item in data.dataList" :name="item.user.id">
                <template #label>
                    <el-tooltip effect="light">
                        <template #content>
                            <el-descriptions :column="1">
                                <template #title>
                                    <el-space>
                                        <el-avatar size="small">
                                            <img :src="item.user.avatar" v-if="item.user.avatar">
                                            <span v-else style="font-size: 0.7em;">
                                                {{
                                                    item.user.user_code
                                                    ? item.user.user_code
                                                    : item.user.name?.toUpperCase()[0]
                                                }}
                                            </span>
                                        </el-avatar>
                                        {{ item.user.name }}
                                    </el-space>
                                </template>
                                <el-descriptions-item :label="$t('user.fields.name')">
                                    {{ item.user.contact }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.email')">
                                    {{ item.user.email }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.phoneNumber')">
                                    {{ item.user.phone }}
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.group')">
                                    <ElTag v-if="item.user.user_group" size="small" type="success">
                                        {{ item.user.user_group.name }}
                                    </ElTag>
                                </el-descriptions-item>
                                <el-descriptions-item :label="$t('user.fields.type')">
                                    <ElTag v-if="item.user.user_type === 'business'" size="small">
                                        B
                                    </ElTag>
                                    <ElTag v-else size="small" type="info">
                                        P
                                    </ElTag>
                                </el-descriptions-item>
                            </el-descriptions>
                        </template>
                        {{ item.user.name }}
                    </el-tooltip>
                </template>
                <div class="messages-operation">
                    <el-button size="small" text type="primary" @click="onRespond(item)">回复</el-button>
                    <el-button size="small" text type="info" @click="onPutInTodo">待办</el-button>
                    <el-button size="small" text type="danger" @click="onRead(item.message_list)">全部标记为已处理</el-button>
                </div>
                <div class="messages-container">
                    <div v-for="msg in item.message_list" class="messages-item">
                        <el-space>
                            <el-text tag="sup" size="small">
                                {{ msg.created_at }}
                            </el-text>
                            <el-tooltip content="标记为已处理">
                                <el-button size="small" text type="danger" :icon="SuccessFilled" @click="onRead([msg])" />
                            </el-tooltip>
                        </el-space>
                        <el-text tag="b">
                            {{ msg.content }}
                        </el-text>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
        <el-empty v-else />
        <el-dialog v-model="respondDialogVisible" :title="'Respond to ' + responseToItem?.user.name" width="40%">
            <el-input type="textarea" :rows="6" v-model="responseContent" :loading="data.loading" />
            <div>
                <el-checkbox v-model="readAllMessages" :label="$t('messenger.fields.readAllMessages')" size="large"
                    :loading="data.loading" />
            </div>
            <el-space size="large">
                <el-checkbox v-model="respondToAllDevices" :label="$t('messenger.fields.respondToAllDevices')" size="large"
                    :loading="data.loading" />
                <el-checkbox v-model="respondWithEmail" :label="$t('messenger.fields.respondWithEmail')" size="large"
                    :loading="data.loading" />
            </el-space>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelRespond" :loading="data.loading">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmRespond" :loading="data.loading"
                        :disabled="!responseContent || responseContent.length == 0">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
.messages-operation {
    background-color: #efefef;
    margin-right: 24px;
    margin-bottom: 12px;

    .el-button {
        font-weight: bolder;
    }
}

.messages-container {
    height: calc(100vh - 460px);
    overflow-y: auto;

    .messages-item {
        width: 100%;
        display: flex;
        flex-flow: column wrap;
        align-self: self-start;
        margin-bottom: 6px;
        text-align: left;

        .el-text {
            align-self: self-start;
        }
    }
}
</style>
