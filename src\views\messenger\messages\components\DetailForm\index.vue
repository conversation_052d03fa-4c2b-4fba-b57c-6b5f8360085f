<script setup>
import { getMessages, getCategoryList, addMessage, updateMessage } from '@/api/modules/messenger'
import { languages } from '@/utils/constants'
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: '',
        category: {}
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})
const disabledDate = (time) => {
    return time.getTime() < Date.now()
}
const categories = ref([]);

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
    getCategoryList().then(res => {
        categories.value = res.data;
    })

})

function getInfo() {
    data.value.loading = true
    getMessages({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.category = data.value.form.category.id;
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addMessage(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    if (data.value.form.status != 'sent') {
                        updateMessage(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    } else {
                        callback && callback()
                    }
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('messenger.fields.category')" prop="category">
                <el-select v-model="data.form.category" class="m-2" placeholder="Select" value-key="id"
                    :disabled="data.form.status == 'sent'">
                    <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('messenger.fields.language')" prop="language">
                <el-select v-model="data.form.language" class="m-2" placeholder="Select"
                    :disabled="data.form.status == 'sent'">
                    <el-option v-for="item in languages" :key="item"
                        :label="$t(`user.selection.languageSelections.${item}`)" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('messenger.fields.title')" prop="title">
                <el-input v-model="data.form.title" placeholder="请输入标题" :disabled="data.form.status == 'sent'" />
            </el-form-item>
            <el-form-item :label="$t('messenger.fields.content')" prop="content">
                <el-input type="textarea" v-model="data.form.content" placeholder="请输入标题" :rows="5" show-word-limit
                    maxlength="200" :disabled="[null, 'sent'].includes(data.form.status)" />
            </el-form-item>
            <el-form-item :label="$t('messenger.fields.expiredAt')" prop="expired_at">
                <el-date-picker v-model="data.form.expired_at" type="date" placeholder="Pick a day"
                    :disabled-date="disabledDate" value-format="YYYY-MM-DD" :disabled="data.form.status == 'sent'" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
