<script setup>
import {
    addStatement, getStatements, updateStatement, calculateStatement, updateStatementPaymentInfo,
    sendStatement, completeStatement, downloadStatement, deleteStatement
} from '@/api/modules/staffs';
import EmployeeSelector from '@/views/staffs/components/employee_selector.vue';
import StubList from '@/views/staffs/components/stub_list.vue';
import ReimbursementList from '@/views/staffs/components/reimbursement_list.vue';
import { reimbursementStatusStyles } from '@/utils/constants'
import { currencyFormatter } from '@/utils/formatter';
import eventBus from '@/utils/eventBus'
import PaymentMethodSelector from '@/views/staffs/components/payment_method_selector.vue';

import { useI18n } from 'vue-i18n'


const { t } = useI18n()
const router = useRouter()

const props = defineProps({
    id: {
        type: String,
        default: ''
    }
})

const data = ref({
    loading: false,
    form: {
        id: props.id,
        employee: ''
    },
    rules: {
        employee: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const selectedEmployee = ref(null)

const dateMin = ref(null)
const dateMax = ref(null)

const stubListRef = ref()
const reimbursementListRef = ref()
const selectedStubs = ref([])
const defaultSelectedStubs = ref([])
const selectedReimbursements = ref([])
const defaultSelectedReimbursements = ref([])



onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getDataList() {
    stubListRef.value.fetch();
    reimbursementListRef.value.fetch();
}

function getInfo() {
    data.value.loading = true
    getStatements({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        if (!data.value.form.is_completed) {
            selectedEmployee.value = res.data.employee.id
            defaultSelectedStubs.value = res.data.stubs
            defaultSelectedReimbursements.value = res.data.reimbursements
            nextTick().then(() => { getDataList(); })
        }

    })
}


defineExpose({
    submit(callback) {
        if (selectedEmployee.value && selectedStubs.value.length > 0) {
            let params = {
                employee: selectedEmployee.value,
                stubs: selectedStubs.value.map(e => e.id),
                reimbursements: selectedReimbursements.value.map(e => e.id),
                period_start_at: dateMin.value,
                period_end_at: dateMax.value,
            }
            if (data.value.form.id == '') {
                addStatement(params).then((res) => {
                    if (res.data.errCode == 365) {
                        calculateStatement(res.data.id).then(() => callback && callback())
                    }
                })
            } else {
                params.id = data.value.form.id
                updateStatement(params).then((res) => {
                    if (res.data.errCode == 365) {
                        callback && callback()
                    }
                })
            }

        }
    }
})


function getStubSummaries(param) {
    const { columns, data } = param
    const sums = []
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = t('fields.total')
            return
        }
        else if (index >= 3 && index <= 4) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)

            sums[index] = currencyFormatter(null, null, sum, null)
        }
    })
    return sums
}


function getReimbursementSummaries(param) {
    const { columns, data } = param
    const sums = []
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = t('fields.total')
            return
        }
        else if (index === 6) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)

            sums[index] = currencyFormatter(null, null, sum, null)
        }
    })
    return sums
}

function onComplete() {
    data.value.loading = true
    completeStatement(data.value.form.id).then(res => {
        if (res.data.errCode == 365) {
            getInfo();
        }
        data.value.loading = false
    })
}

function onSend() {
    data.value.loading = true
    sendStatement(data.value.form.id).then(res => {
        if (res.data.errCode == 365) {
            data.value.loading = false
        }
    })
}

function onDownload() {
    data.value.loading = true
    downloadStatement(data.value.form.id).then(res => {
        if (res.data.errCode == 365) {
            window.open(res.data.url);
            data.value.loading = false
        }
    })
}

function onDel() {
    ElMessageBox.confirm(`确认删除「${data.value.form.no}」吗？`, '确认信息').then(() => {
        data.value.loading = true
        deleteStatement({ id: data.value.form.id }).then((res) => {
            if (res.data.errCode == 365) {
                eventBus.emit('get-data-list')
                router.push({ name: 'paymentStatements' })
            }
            data.value.loading = false
        })
    }).catch(() => { })
}


const updatePaymentInfoVisible = ref(false)
const statementToModify = ref({
    id: null,
    paid_amount: 0.0,
    payment_method: null,
    payment_no: null,
    paid_at: null,
    total: 0.0
})
function onUpdatePaymentInfo() {
    statementToModify.value.id = data.value.form.id
    statementToModify.value.paid_amount = Number(((data.value.form?.paid_amount ?? 0) / 100).toFixed(2))
    statementToModify.value.payment_method = data.value.form.payment_method
    statementToModify.value.payment_no = data.value.form.payment_no
    statementToModify.value.paid_at = data.value.form.paid_at
    statementToModify.value.total = currencyFormatter(null, null, data.value.form.total)

    updatePaymentInfoVisible.value = true;
}

function onConfirmUpdatePaymentInfo() {
    let params = JSON.parse(JSON.stringify(statementToModify.value))
    params.paid_amount = Math.round(statementToModify.value.paid_amount * 100)
    updateStatementPaymentInfo(params).then(res => {
        if (res.data.errCode == 365) {
            onCancelPaymentInfo();
            getDataList();
        }
    })

}

function onCancelPaymentInfo() {
    updatePaymentInfoVisible.value = false;
    statementToModify.value = {
        id: null,
        paid_amount: 0.0,
        payment_method: null,
        payment_no: null,
        paid_at: null,
        total: 0.0
    }
}


</script>

<template>
    <div v-loading="data.loading">
        <page-main title="Payment Statement Info">
            <template #extra v-if="data.form.id != ''">
                <el-space>
                    <el-button type="primary" @click="onUpdatePaymentInfo" plain v-if="!data.form.is_completed">
                        {{ $t('operations.pay') }}
                    </el-button>
                    <el-button type="success" @click="onDownload" plain v-loading="data.loading">
                        {{ $t('operations.download') }}
                    </el-button>
                    <el-button type="success" @click="onSend" v-loading="data.loading">
                        {{ $t('operations.send') }}
                    </el-button>
                    <template v-if="!data.form.is_completed">
                        <el-button type="danger" @click="onDel" v-loading="data.loading">
                            {{ $t('operations.delete') }}
                        </el-button>
                        <el-button type="primary" @click="onComplete" v-loading="data.loading">
                            {{ $t('operations.complete') }}
                        </el-button>
                    </template>
                </el-space>
            </template>
            <el-form :model="data.search" size="default" label-width="100px" v-if="data.form.id == ''">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item :label="$t('staffs.fields.employee')">
                            <EmployeeSelector v-model="selectedEmployee" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('fields.createdAt')">
                            <el-space>
                                <el-date-picker v-model="dateMin" type="date" :placeholder="$t('fields.createdAtMin')"
                                    clearable format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                                <span>~</span>
                                <el-date-picker v-model="dateMax" type="date" :placeholder="$t('fields.createdAtMax')"
                                    clearable format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                            </el-space>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3" align="center">
                        <el-button type="primary" @click="getDataList" :disabled="selectedEmployee == null">

                            <template #icon>
                                <el-icon>
                                    <svg-icon name="ep:search" />
                                </el-icon>
                            </template>
                            {{ $t('operations.query') }}
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-descriptions v-else column="5">
                <el-descriptions-item :label="$t('fields.no')">{{ data.form.no }}</el-descriptions-item>
                <el-descriptions-item :label="$t('staffs.fields.employee')">
                    {{ data.form.employee.name }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('staffs.fields.period')" span="3">
                    {{ data.form.period_start }} ~ {{ data.form.period_end }}
                </el-descriptions-item>

                <template v-if="data.form.is_completed">
                    <el-descriptions-item :label="$t('staffs.stub.fields.salary')">
                        {{ currencyFormatter(null, null, data.form.salary) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.stub.fields.tip')">
                        {{ currencyFormatter(null, null, data.form.tip) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.fields.reimbursements')">
                        {{ currencyFormatter(null, null, data.form.reimbursed_expense) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.stub.fields.tax')">
                        {{ currencyFormatter(null, null, data.form.hst) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('fields.total')">
                        {{ currencyFormatter(null, null, data.form.total) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.paymentStatement.fields.paidAmount')">
                        {{ currencyFormatter(null, null, data.form.paid_amount) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.paymentStatement.fields.paidAt')">
                        {{ data.form.paid_at }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.fields.paymentMethod')">
                        {{ data.form.payment_method }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.paymentStatement.fields.paymentNo')">
                        {{ data.form.payment_no }}
                    </el-descriptions-item>
                </template>
            </el-descriptions>
        </page-main>

        <template v-if="data.form.is_completed">
            <page-main title="Stubs">
                <el-table v-loading="data.loading" :data="data.form.stubs" stripe highlight-current-row show-summary
                    :summary-method="getStubSummaries">
                    <el-table-column type="index" align="center" fixed width="50" />
                    <el-table-column prop="driver" :label="$t('staffs.fields.employee')" />
                    <el-table-column prop="date" :label="$t('fields.date')" />
                    <el-table-column prop="hst" :label="$t('staffs.stub.fields.tax')" :formatter="currencyFormatter" />
                    <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter" />
                    <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                    <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                </el-table>
            </page-main>
            <page-main title="Reimbursements">
                <el-table v-loading="data.loading" :data="data.form.reimbursements" stripe highlight-current-row
                    show-summary :summary-method="getReimbursementSummaries">
                    <el-table-column type="index" align="center" fixed width="50" />
                    <el-table-column prop="date" :label="$t('fields.date')" width="120" />
                    <el-table-column prop="employee" :label="$t('staffs.fields.employee')" width="150" />
                    <el-table-column prop="description" :label="$t('fields.desc')" show-overflow-tooltip width="150" />
                    <el-table-column prop="purpose" :label="$t('staffs.reimbursement.fields.purpose')" width="150"
                        show-overflow-tooltip />
                    <el-table-column prop="vendor" :label="$t('staffs.reimbursement.fields.vendor')" width="150"
                        show-overflow-tooltip />
                    <el-table-column prop="amount" :label="$t('fields.amount')" :formatter="currencyFormatter" />
                    <el-table-column prop="status" :label="$t('fields.status')">

                        <template #default="scope">
                            <el-tag :type="reimbursementStatusStyles[scope.row.status]" round size="small">
                                {{ $t(`staffs.reimbursement.selections.status.${scope.row.status}`) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="reviewed_by" :label="$t('staffs.reimbursement.fields.reviewedBy')"
                        width="160" />
                    <el-table-column prop="reviewed_at" :label="$t('staffs.reimbursement.fields.reviewedAt')"
                        width="160" sortable="custom" />
                    <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                </el-table>
            </page-main>
        </template>

        <template v-else>
            <page-main v-if="selectedEmployee" title="Stubs">
                <StubList ref="stubListRef" v-model="selectedStubs" :employee="selectedEmployee" :date-min="dateMin"
                    :date-max="dateMax" :all="data.form.id != ''" :default-selected="defaultSelectedStubs" />
            </page-main>
            <page-main v-if="selectedEmployee" title="Reimbursements">
                <ReimbursementList ref="reimbursementListRef" v-model="selectedReimbursements"
                    :employee="selectedEmployee" :date-min="dateMin" :date-max="dateMax" :all="data.form.id != ''"
                    :default-selected="defaultSelectedReimbursements" />
            </page-main>
        </template>
        <el-dialog v-model="updatePaymentInfoVisible" title="Update Statement Payment Info" width="500">
            <el-form :model="statementToModify" size="default" label-width="100px">
                <el-form-item :label="$t('fields.total')">
                    <el-text size="large" tag="b">{{ statementToModify.total }}</el-text>
                </el-form-item>
                <el-form-item :label="$t('staffs.paymentStatement.fields.paidAmount')">
                    <el-input-number v-model="statementToModify.paid_amount" :min="0" :max="statementToModify.total"
                        :precision="2" />
                </el-form-item>
                <el-form-item :label="$t('staffs.fields.paymentMethod')">
                    <PaymentMethodSelector v-model="statementToModify.payment_method" />
                </el-form-item>
                <el-form-item :label="$t('staffs.paymentStatement.fields.paidAt')">
                    <el-date-picker v-model="statementToModify.paid_at" type="datetime"
                        placeholder="Select date and time" />
                </el-form-item>
                <el-form-item :label="$t('staffs.paymentStatement.fields.paymentNo')">
                    <el-input v-model="statementToModify.payment_no" />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelPaymentInfo">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmUpdatePaymentInfo">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
