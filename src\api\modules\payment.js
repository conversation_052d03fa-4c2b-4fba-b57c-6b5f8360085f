import { GET, PUT, POST, PAT, DEL } from "../methods";

const path = 'payment/'

// Settings
export const getPaymentSettings = () => GET(path + 'settings/')
export const updatePaymentSettings = (params) => PUT(path + 'settings/', params)

// Suppliers
export const getPaymentSuppliers = (params) => GET(path + 'suppliers/', params)
export const addPaymentSupplier = (params) => POST(path + 'suppliers/', params)
export const updatePaymentSupplier = (params) => PUT(path + 'suppliers/', params)
export const deletePaymentSupplier = (params) => DEL(path + 'suppliers/', params)
export const alterPaymentSupplierAvailability = (params) => PAT(path + 'suppliers/', params)

// Orders
export const getPaymentOrders = (params) => GET(path + 'orders/', params)
export const verifyPaymentOrder = (params) => GET(path + 'orders/verify/', params)
export const AlterPaymentOrderStatus = (params) => PAT(path + 'orders/', params)
export const refundPaymentOrder = (params) => POST(path + 'orders/refund/', params)

export const getEODPaymentOrders = (params) => POST(path + 'orders/eod-list/', params)
export const stableEODPaymentOrders = (params) => POST(path + 'orders/eod-list/stable/', params)

// Supplier Orders
export const getElavonOrders = (params) => GET(path + 'cc-orders/', params)
export const getAlphapayOrders = (params) => GET(path + 'ap-orders/', params)
export const queryOrderPayments = (params) => GET(path + 'orders/query-payments/', params)

// Logs
export const getPaymentOrderLogs = (params) => GET(path + 'logs/', params)
export const getPaymentCreditLogs = (params) => GET(path + 'credits/logs/payment/', params)


export const getAllUserCreditLogs = (params) => GET(path + 'credits/logs/', params);
export const getCreditsTotalBalance = () => GET(path + 'credits/balance/');
export const directChargeUserCredits = (params) => POST(path + 'credits/direct-recharge/', params);


// Statistics
export const getPaymentGeneralStatistics = (params) => GET(path + 'statistics/', params)
export const getPaymentOrderStatistics = (params) => GET(path + 'statistics/orders/', params)
export const getPaymentsStatistics = (params) => GET(path + 'statistics/payments/', params)

// Invoices
export const getUserInvoices = (params) => GET(path + 'invoices/', params)
export const getUserInvoice = (id) => GET(path + `invoices/${id}/`)
export const voidUserInvoice = (id) => PAT(path + `invoices/${id}/`)
