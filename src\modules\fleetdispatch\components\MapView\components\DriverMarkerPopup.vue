<template>
  <div class="driver-popup-mini">
    <div class="driver-name">{{ driver.name || `司机 ${driver.id}` }}</div>
  </div>
</template>

<script setup>
// 简化的弹窗组件，只显示司机名字
const props = defineProps({
  driver: {
    type: Object,
    required: true
  }
});
</script>

<style scoped>
.driver-popup-mini {
  padding: 5px 8px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 150px;
}

.driver-name {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}
</style> 