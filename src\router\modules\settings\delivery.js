/*
* Author: <EMAIL>'
* Date: '2023-04-10 16:44:31'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/delivery/settings.js'
* File: 'settings.js'
* Version: '1.0.0'
*/

import CMS from '../../modules/cms'


const Layout = () => import('@/layout/index.vue')
export default {
    path: '/delivery/settings',
    component: Layout,
    redirect: '/delivery/settings/settings',
    name: 'deliverySettings',
    meta: {
        title: '系统设置',
        icon: 'mdi:wrench-settings-outline',
        auth: ['super'],
        i18n: 'route.system.deliverySettings.title'
    },
    children: [
        {
            path: 'settings',
            name: 'deliverySettingsSettings',
            component: () => import('@/views/delivery/settings/settings.vue'),
            meta: {
                title: '系统',
                icon: 'carbon:settings-adjust',
                activeMenu: '/delivery/settings/settings',
                i18n: 'route.system.deliverySettings.settings',
                auth: ['super']
            }
        },
        {
            path: 'destinations',
            name: 'deliveryDestinations',
            component: () => import('@/views/settings/delivery_destinations/list.vue'),
            meta: {
                title: '目的地',
                icon: 'streamline:travel-map-triangle-flag-navigation-map-maps-flag-gps-location-destination-goal',
                auth: ['super'],
                i18n: 'route.system.deliverySettings.destinations',
                activeMenu: '/delivery/settings/destinations',
                auth: ['super']
            }
        },
        {
            path: 'distribution-centers',
            name: 'deliveryDistributionCenters',
            component: () => import('@/views/delivery/distributions/list.vue'),
            meta: {
                title: '集散中心',
                icon: 'tabler:building-warehouse',
                activeMenu: '/delivery/settings/distribution-centers',
                i18n: 'route.system.deliverySettings.distributionCenters',
                auth: ['super']
            }
        },
        {
            path: 'distribution-centers/detail',
            name: 'deliveryDistributionCenterDetail',
            component: () => import('@/views/delivery/distributions/detail.vue'),
            meta: {
                title: '集散中心',
                icon: 'tabler:building-warehouse',
                activeMenu: '/delivery/settings/distributions-centers/detail',
                i18n: 'route.system.deliverySettings.distributionCenters',
                sidebar: false,
                auth: ['super']
            }
        },
        {
            path: 'suppliers',
            name: 'deliverySuppliers',
            component: () => import('@/views/delivery/suppliers/list.vue'),
            meta: {
                title: '供应商',
                icon: 'solar:delivery-broken',
                activeMenu: '/delivery/settings/suppliers',
                i18n: 'route.system.deliverySettings.suppliers',
                auth: ['super']
            }
        },
        {
            path: 'suppliers/stores',
            name: 'deliverySupplierDetail',
            component: () => import('@/views/delivery/suppliers/detail.vue'),
            meta: {
                title: '供应商门店',
                icon: 'fluent:notepad-edit-16-filled',
                activeMenu: '/delivery/settings/suppliers',
                i18n: 'route.system.deliverySettings.supplierStores',
                sidebar: false,
                auth: ['super']
            }
        },
        ...CMS,
        {
            path: 'marks',
            name: 'deliverySettingsMarks',
            component: () => import('@/views/delivery/settings/marks/list.vue'),
            meta: {
                title: '订单标记',
                icon: 'carbon:settings-adjust',
                activeMenu: '/delivery/settings/marks',
                i18n: 'route.system.deliverySettings.marks',
                auth: ['super']
            }
        },
    ]
}
