import { ref, computed, watch } from 'vue';
import { useRouteStore } from '../../../stores/route';
import { useOrderStore } from '../../../stores/order';
import { useDriverStore } from '../../../stores/driver';
import { useAddressStore } from '../../../stores/address';

// 添加Mapbox API密钥
const MAPBOX_API_KEY = 'pk.eyJ1Ijoic2FzaGltaXRoZXB1ZyIsImEiOiJjbThxbnlzMTgwbjU4MmpxNDMzcTVrN3JqIn0.HKec-0aGPHdLlEtX7bn2jg'

export function useRouteManagement(map, moveSymbolLayersToTopFn, isMapLoaded = ref(true)) {
    const routeStore = useRouteStore();
    const orderStore = useOrderStore();
    const driverStore = useDriverStore();
    const addressStore = useAddressStore();

    // 路线图层ID前缀
    const ROUTE_LAYER_ID_PREFIX = 'route-layer-';
    const ROUTE_SOURCE_ID_PREFIX = 'route-source-';

    // 路线类型
    const routeType = ref('direct'); // 'direct' 或 'realistic'

    // 添加全局路线请求队列和控制变量
    const routeRequestQueue = ref([]);
    const isProcessingRouteQueue = ref(false);
    const maxConcurrentRequests = 6; // 同时发送的最大请求数
    const activeRequests = ref(0);
    const lastRequestTime = ref(0);
    const REQUEST_INTERVAL = 100; // 请求间隔，避免同时发送大量请求

    // 当前是否正在更新路线
    const isUpdatingRoutes = ref(false);

    // 添加路线请求上下文ID，用于取消过期请求
    const routeRequestContextId = ref(Date.now());

    // 当前显示的路线ID
    const visibleRouteId = computed(() => {
        return routeStore.selectedRoute?.id || null;
    });

    // 隐藏所有路线
    const hideAllRoutes = () => {
        if (!map.value || !map.value.getStyle()) return;

        try {
            // 查找所有路线图层
            const style = map.value.getStyle();
            if (!style || !style.layers) return;

            // 查找所有以路线图层前缀开头的图层
            const routeLayers = style.layers
                .filter(layer => layer.id.startsWith(ROUTE_LAYER_ID_PREFIX))
                .map(layer => layer.id);

            // 隐藏所有路线图层
            routeLayers.forEach(layerId => {
                if (map.value.getLayer(layerId)) {
                    map.value.setLayoutProperty(layerId, 'visibility', 'none');
                }
            });

            console.log(`已隐藏 ${routeLayers.length} 条路线`);
        } catch (error) {
            console.error('隐藏路线时出错:', error)
        }
    };

    // 显示指定路线
    const showRoute = (routeId) => {
        if (!map.value || !map.value.getStyle() || !routeId) return;

        try {
            const layerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`;

            if (map.value.getLayer(layerId)) {
                map.value.setLayoutProperty(layerId, 'visibility', 'visible');
                console.log(`已显示路线 ${routeId}`);
            }
        } catch (error) {
            console.error(`显示路线 ${routeId} 时出错:`, error)
        }
    };

    // 清除特定路线的图层和源
    const clearRouteById = (routeId) => {
        if (!map.value || !map.value.getStyle() || !routeId) return;

        try {
            console.log(`开始清除路线 ${routeId} 的图层和源`);

            // 获取地图样式
            const style = map.value.getStyle();
            if (!style || !style.layers) return;

            // 创建标准ID
            const standardLayerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`;
            const standardSourceId = `${ROUTE_SOURCE_ID_PREFIX}${routeId}`;
            const startLayerId = `route-start-layer-${routeId}`;
            const startSourceId = `route-start-source-${routeId}`;
            const endLayerId = `route-end-layer-${routeId}`;
            const endSourceId = `route-end-source-${routeId}`;

            // 移除标准路线图层
            if (map.value.getLayer(standardLayerId)) {
                console.log(`清除路线图层: ${standardLayerId}`);
                map.value.removeLayer(standardLayerId);
            }

            // 移除标准路线源
            if (map.value.getSource(standardSourceId)) {
                console.log(`清除路线源: ${standardSourceId}`);
                map.value.removeSource(standardSourceId);
            }

            // 移除起点图层和源
            if (map.value.getLayer(startLayerId)) {
                console.log(`清除起点图层: ${startLayerId}`);
                map.value.removeLayer(startLayerId);
            }
            if (map.value.getSource(startSourceId)) {
                console.log(`清除起点源: ${startSourceId}`);
                map.value.removeSource(startSourceId);
            }

            // 移除终点图层和源
            if (map.value.getLayer(endLayerId)) {
                console.log(`清除终点图层: ${endLayerId}`);
                map.value.removeLayer(endLayerId);
            }
            if (map.value.getSource(endSourceId)) {
                console.log(`清除终点源: ${endSourceId}`);
                map.value.removeSource(endSourceId);
            }

            // 查找并移除任何其他与该路线相关的图层和源
            // 这是为了处理可能的命名变体或旧版本的图层和源
            const routeLayers = style.layers
                .filter(layer => layer.id.includes(routeId) &&
                    (layer.id.startsWith(ROUTE_LAYER_ID_PREFIX) ||
                     layer.id.startsWith('route-start-layer-') ||
                     layer.id.startsWith('route-end-layer-')))
                .map(layer => layer.id);

            routeLayers.forEach(layerId => {
                if (map.value.getLayer(layerId) &&
                    layerId !== standardLayerId &&
                    layerId !== startLayerId &&
                    layerId !== endLayerId) {
                    console.log(`清除额外路线图层: ${layerId}`);
                    map.value.removeLayer(layerId);
                }
            });

            const routeSources = Object.keys(style.sources || {})
                .filter(sourceId => sourceId.includes(routeId) &&
                    (sourceId.startsWith(ROUTE_SOURCE_ID_PREFIX) ||
                     sourceId.startsWith('route-start-source-') ||
                     sourceId.startsWith('route-end-source-')));

            routeSources.forEach(sourceId => {
                if (map.value.getSource(sourceId) &&
                    sourceId !== standardSourceId &&
                    sourceId !== startSourceId &&
                    sourceId !== endSourceId) {
                    console.log(`清除额外路线源: ${sourceId}`);
                    map.value.removeSource(sourceId);
                }
            });

            console.log(`路线 ${routeId} 的所有图层和源已清除`);
        } catch (error) {
            console.error(`清除路线 ${routeId} 时出错:`, error);
        }
    };

    // 清除所有路线图层和源
    const clearAllRoutes = () => {
        if (!map.value || !map.value.getStyle()) return;

        try {
        // 查找并移除所有路线图层和源
        const style = map.value.getStyle();
        if (!style || !style.layers) return;

        // 移除图层
        const routeLayers = style.layers
            .filter(layer => layer.id.startsWith(ROUTE_LAYER_ID_PREFIX))
            .map(layer => layer.id);

        routeLayers.forEach(layerId => {
            if (map.value.getLayer(layerId)) {
                map.value.removeLayer(layerId);
            }
        });

        // 移除源
        const routeSources = Object.keys(style.sources || {})
            .filter(sourceId => sourceId.startsWith(ROUTE_SOURCE_ID_PREFIX));

        routeSources.forEach(sourceId => {
            if (map.value.getSource(sourceId)) {
                map.value.removeSource(sourceId);
            }
        });

        // 移除所有路线的起点和终点标记
        // 内联实现，避免引用错误
        if (map.value && map.value.getStyle()) {
            try {
                // 查找所有路线图层
                const style = map.value.getStyle();
                if (!style || !style.layers) return;

                // 查找所有以路线起点和终点图层前缀开头的图层
                const endpointLayers = style.layers
                    .filter(layer => layer.id.startsWith('route-start-layer-') || layer.id.startsWith('route-end-layer-'))
                    .map(layer => layer.id);

                // 移除所有起点和终点图层
                endpointLayers.forEach(layerId => {
                    if (map.value.getLayer(layerId)) {
                        map.value.removeLayer(layerId);
                    }
                });

                // 查找所有以路线起点和终点源前缀开头的源
                const endpointSources = Object.keys(style.sources || {})
                    .filter(sourceId => sourceId.startsWith('route-start-source-') || sourceId.startsWith('route-end-source-'));

                // 移除所有起点和终点源
                endpointSources.forEach(sourceId => {
                    if (map.value.getSource(sourceId)) {
                        map.value.removeSource(sourceId);
                    }
                });

                console.log(`已移除 ${endpointLayers.length} 个路线起点和终点标记`);
            } catch (error) {
                console.error('移除路线起点和终点标记时出错:', error)
            }
        }
        } catch (error) {
            console.error('清除路线时出错:', error)
        }
    };

    // 重新绘制路线
    const redrawRoutes = () => {
        if (!map.value) return

        // 防止重复绘制
        if (isUpdatingRoutes.value) {
            console.log('路线正在更新中，跳过重复绘制')
            return
        }

        // 重置路线请求队列
        routeRequestQueue.value = []
        isProcessingRouteQueue.value = false
        activeRequests.value = 0

        // 更新路线请求上下文ID
        routeRequestContextId.value = Date.now();

        // 先隐藏所有路线，而不是删除它们
        hideAllRoutes()

        // 获取所有路线
        const routes = routeStore.routes || []

        if (routes.length === 0) {
            console.log('没有可用的路线，不绘制路线')
            return
        }

        console.log(`开始绘制 ${routes.length} 条路线`)

        // 标记开始更新
        isUpdatingRoutes.value = true

        try {
            // 遍历所有路线，绘制每条路线
            routes.forEach(route => {
                // 跳过不可见的路线
                if (!route.visible) {
                    console.log(`路线 ${route.id} 不可见，跳过绘制`)
                    return
                }

                // 获取路线的订单
                const routeId = route.id
                const routeOrders = orderStore.getOrdersByRouteNumber(routeId)
                const validOrders = routeOrders.filter(o => o && (o.lng_lat || o.location))

                console.log(`为路线 ${routeId} 绘制路线，有效订单数量: ${validOrders.length}`)

                // 绘制路线
                if (validOrders.length > 0) {
                    // 使用 try-catch 避免一个路线的错误影响其他路线
                    try {
                        drawRoutes(validOrders, routeId)
                    } catch (error) {
                        console.error(`绘制路线 ${routeId} 时出错:`, error)
                    }
                }
            })
        } finally {
            // 标记结束更新
            isUpdatingRoutes.value = false
        }
    };

    // 绘制路线
    const drawRoutes = (orders, routeId) => {
        if (!map.value) return

        try {
            console.log(`开始为路线 ${routeId} 绘制路线`)

            // 获取路线对象
            const route = routeStore.routes.find(r => r.id === routeId)

            // 如果路线不可见，则不绘制
            if (route && !route.visible) {
                console.log(`路线 ${routeId} 不可见，不绘制路线`)
                return
            }

            // 获取路线的司机ID
            const driverId = route?.driverId || orders[0]?.driver_id

            if (!driverId) {
                console.log(`路线 ${routeId} 没有关联司机，不绘制路线`)
                return
            }

            // 按停靠点编号排序
            const sortedOrders = [...orders].sort((a, b) => {
                const stopA = parseInt(a.stop_no) || 0
                const stopB = parseInt(b.stop_no) || 0
                return stopA - stopB
            })

            // 检查路线图层是否已存在
            const layerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`;
            const sourceId = `${ROUTE_SOURCE_ID_PREFIX}${routeId}`;

            // 如果是直线路线模式，并且路线图层已存在，可以直接显示
            if (routeType.value === 'direct' && map.value.getLayer(layerId)) {
                // 路线已存在，只需显示它
                map.value.setLayoutProperty(layerId, 'visibility', 'visible');
                console.log(`直线路线 ${routeId} 已存在，显示它`);

                // 如果直线路线几何数据已存在，不需要重新绘制
                if (route && route.getRouteGeometry('direct')) {
                    console.log(`直线路线 ${routeId} 的几何数据已存在，不需要重新绘制`);
                    return;
                }
            }

            // 如果是真实路线模式，并且路线图层已存在，但我们需要确保它是真实路线
            if (routeType.value === 'realistic' && map.value.getLayer(layerId)) {
                // 如果已经有真实路线数据，直接显示
                if (route && route.getRouteGeometry('realistic')) {
                    map.value.setLayoutProperty(layerId, 'visibility', 'visible');
                    console.log(`真实路线 ${routeId} 已存在，显示它`);
                    return;
                } else {
                    // 如果没有真实路线数据，需要清除现有的直线路线，重新请求真实路线
                    console.log(`路线 ${routeId} 存在，但没有真实路线数据，需要重新请求`);

                    // 清除现有图层和源
                    if (map.value.getLayer(layerId)) {
                        map.value.removeLayer(layerId);
                    }

                    if (map.value.getSource(sourceId)) {
                        map.value.removeSource(sourceId);
                    }
                }
            }

            // 根据用户选择的模式绘制路线
            let coordinates = null;

            // 如果用户选择了直线路线模式，绘制直线路线
            if (routeType.value === 'direct') {
                // 绘制直线路线
                coordinates = drawDirectRoute(routeId, driverId, sortedOrders)

                // 如果路线对象存在，保存直线路线几何数据
                if (route && coordinates) {
                    route.setRouteGeometry('direct', coordinates)
                }
            }
            // 如果用户选择了真实路线模式，但我们还没有真实路线数据，不绘制任何路线
            // 我们将等待真实路线数据

            // 如果用户选择了真实路线模式，添加到队列进行处理
            if (routeType.value === 'realistic') {
                // 使用优先级将请求加入队列
                // 分配一个优先级，较少点的路线优先处理（因为响应更快）
                const priority = 1000 - sortedOrders.length // 点越少，优先级越高

                routeRequestQueue.value.push({
                    routeId,
                    driverId,
                    orders: sortedOrders,
                    priority
                })

                // 如果队列处理器没有运行，启动它
                if (!isProcessingRouteQueue.value) {
                    processRouteQueue()
                }
            }

            console.log(`路线 ${routeId} 绘制任务已加入队列，直线路线绘制完成`)

            // 确保标记图层在路线图层之上
            if (typeof moveSymbolLayersToTopFn === 'function') {
                moveSymbolLayersToTopFn()
            }

            // 创建起点和终点图标（如果尚未创建）
            // 使用内部函数，避免引用错误
            if (map.value) {
                // 检查图标是否已存在
                if (!map.value.hasImage('start-point-icon') || !map.value.hasImage('end-point-icon')) {
                    // 创建起点图标（绿色旗帜）
                    const startIcon = new Image(24, 24);
                    startIcon.onload = () => {
                        if (map.value && !map.value.hasImage('start-point-icon')) {
                            map.value.addImage('start-point-icon', startIcon);
                            console.log('已创建起点图标');
                        }
                    };
                    startIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                            <line x1="4" y1="22" x2="4" y2="15"></line>
                        </svg>
                    `);

                    // 创建终点图标（红色标记）
                    const endIcon = new Image(24, 24);
                    endIcon.onload = () => {
                        if (map.value && !map.value.hasImage('end-point-icon')) {
                            map.value.addImage('end-point-icon', endIcon);
                            console.log('已创建终点图标');
                        }
                    };
                    endIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                    `);
                }
            }

            // 添加起点和终点标记
            // 内联实现，避免引用错误
            if (map.value) {
                // 获取路线对象
                const route = routeStore.routes.find(r => r.id === routeId);
                if (route) {
                    // 创建起点和终点标记的源和图层ID
                    const startSourceId = `route-start-source-${routeId}`;
                    const startLayerId = `route-start-layer-${routeId}`;
                    const endSourceId = `route-end-source-${routeId}`;
                    const endLayerId = `route-end-layer-${routeId}`;

                    // 获取路线颜色
                    let routeColor = '#FF9800';
                    if (route.color) {
                        routeColor = route.color;
                    } else if (route.driverId) {
                        const driver = driverStore.getDriverById(route.driverId);
                        if (driver && driver.color) {
                            routeColor = driver.color;
                        }
                    }

                    // 处理起点
                    if (route.start_point) {
                        const startCoords = addressStore.getCoordinatesByAddressId(route.start_point);
                        if (startCoords) {
                            console.log(`为路线 ${routeId} 添加起点标记:`, startCoords);

                            // 移除现有的起点标记（如果存在）
                            if (map.value.getLayer(startLayerId)) {
                                map.value.removeLayer(startLayerId);
                            }
                            if (map.value.getSource(startSourceId)) {
                                map.value.removeSource(startSourceId);
                            }

                            // 添加起点标记源
                            map.value.addSource(startSourceId, {
                                type: 'geojson',
                                data: {
                                    type: 'Feature',
                                    properties: {
                                        routeId: routeId,
                                        type: 'start_point',
                                        description: '起点'
                                    },
                                    geometry: {
                                        type: 'Point',
                                        coordinates: [startCoords[1], startCoords[0]] // [lng, lat] 格式
                                    }
                                }
                            });

                            // 添加起点标记图层
                            map.value.addLayer({
                                id: startLayerId,
                                type: 'symbol',
                                source: startSourceId,
                                layout: {
                                    'icon-image': 'start-point-icon',
                                    'icon-size': 1.0,
                                    'icon-allow-overlap': true,
                                    'icon-anchor': 'center',
                                    'text-field': '起点',
                                    'text-font': ['Open Sans Regular'],
                                    'text-size': 12,
                                    'text-offset': [0, 1.5],
                                    'text-anchor': 'top'
                                },
                                paint: {
                                    'text-color': routeColor,
                                    'text-halo-color': '#ffffff',
                                    'text-halo-width': 2
                                }
                            });
                        }
                    }

                    // 处理终点
                    if (route.end_point) {
                        const endCoords = addressStore.getCoordinatesByAddressId(route.end_point);
                        if (endCoords) {
                            console.log(`为路线 ${routeId} 添加终点标记:`, endCoords);

                            // 移除现有的终点标记（如果存在）
                            if (map.value.getLayer(endLayerId)) {
                                map.value.removeLayer(endLayerId);
                            }
                            if (map.value.getSource(endSourceId)) {
                                map.value.removeSource(endSourceId);
                            }

                            // 添加终点标记源
                            map.value.addSource(endSourceId, {
                                type: 'geojson',
                                data: {
                                    type: 'Feature',
                                    properties: {
                                        routeId: routeId,
                                        type: 'end_point',
                                        description: '终点'
                                    },
                                    geometry: {
                                        type: 'Point',
                                        coordinates: [endCoords[1], endCoords[0]] // [lng, lat] 格式
                                    }
                                }
                            });

                            // 添加终点标记图层
                            map.value.addLayer({
                                id: endLayerId,
                                type: 'symbol',
                                source: endSourceId,
                                layout: {
                                    'icon-image': 'end-point-icon',
                                    'icon-size': 1.0,
                                    'icon-allow-overlap': true,
                                    'icon-anchor': 'center',
                                    'text-field': '终点',
                                    'text-font': ['Open Sans Regular'],
                                    'text-size': 12,
                                    'text-offset': [0, 1.5],
                                    'text-anchor': 'top'
                                },
                                paint: {
                                    'text-color': routeColor,
                                    'text-halo-color': '#ffffff',
                                    'text-halo-width': 2
                                }
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`绘制路线 ${routeId} 时出错:`, error)
        }
    }

    // 处理路线请求队列
    const processRouteQueue = async() => {
        if (isProcessingRouteQueue.value) return
        isProcessingRouteQueue.value = true

        console.log(`开始处理路线队列，总共 ${routeRequestQueue.value.length} 条路线待处理`)

        try {
            // 按优先级排序队列
            routeRequestQueue.value.sort((a, b) => b.priority - a.priority)

            while (routeRequestQueue.value.length > 0) {
                // 检查是否达到最大并发请求数
                if (activeRequests.value >= maxConcurrentRequests) {
                    // 等待一些请求完成
                    await new Promise(resolve => setTimeout(resolve, 500))
                    continue
                }

                // 处理下一个请求
                const request = routeRequestQueue.value.shift()

                // 增加活动请求计数
                activeRequests.value++

                // 使用Promise处理，确保无论成功或失败都减少活动请求计数
                drawRealisticRoute(request.routeId, request.driverId, request.orders)
                    .finally(() => {
                        // 完成后减少活动请求计数
                        activeRequests.value--
                    })

                // 允许一些时间给事件循环处理其他任务
                // 添加一个小的延迟，给事件循环留出时间处理其他任务
                await new Promise(resolve => setTimeout(resolve, REQUEST_INTERVAL / 2)); // 使用请求间隔的一半作为延迟
            }
        } catch (error) {
            console.error('处理路线请求队列时出错:', error)
        } finally {
            console.log('路线队列处理完成')
            isProcessingRouteQueue.value = false
        }
    }

    // 绘制直线连接路线
    const drawDirectRoute = (routeId, driverId, driverOrders) => {
        if (!map.value) return null

        // 获取路线对象，以获取起点和终点信息
        const route = routeStore.routes.find(r => r.id === routeId)

        // 提取订单坐标
        const orderCoordinates = driverOrders.map(order => {
            const coords = order.lng_lat || order.location
            // 注意：我们的坐标是 [lat, lng] 格式，而 MapLibre 需要 [lng, lat] 格式
            return [coords[1], coords[0]]
        })

        // 最终的完整路线坐标（包括起点和终点）
        let coordinates = []

        // 如果路线有起点和终点，添加它们
        if (route && (route.start_point || route.end_point)) {
            console.log(`路线 ${routeId} 有起点/终点信息:`, route.start_point, route.end_point)

            // 处理起点
            if (route.start_point) {
                const startCoords = addressStore.getCoordinatesByAddressId(route.start_point)
                if (startCoords) {
                    console.log(`路线 ${routeId} 起点坐标:`, startCoords)
                    // 添加起点坐标 [lng, lat] 格式
                    coordinates.push([startCoords[1], startCoords[0]])
                }
            }

            // 添加订单坐标
            coordinates = coordinates.concat(orderCoordinates)

            // 处理终点
            if (route.end_point) {
                const endCoords = addressStore.getCoordinatesByAddressId(route.end_point)
                if (endCoords) {
                    console.log(`路线 ${routeId} 终点坐标:`, endCoords)
                    // 添加终点坐标 [lng, lat] 格式
                    coordinates.push([endCoords[1], endCoords[0]])
                }
            }
        } else {
            // 如果路线没有起点和终点信息，只使用订单坐标
            coordinates = orderCoordinates
        }

        if (coordinates.length < 2) return null // 至少需要两个点来绘制线

        // 获取路线颜色
        let routeColor = '#FF9800'

        // 优先使用路线自定义颜色
        if (route && route.color) {
            routeColor = route.color
        } else {
            // 其次使用司机颜色
            const driver = driverStore.getDriverById(driverId)
            if (driver && driver.color) {
                routeColor = driver.color
            }
        }

        // 创建源和图层ID - 使用routeId而不是driverId
        const sourceId = `${ROUTE_SOURCE_ID_PREFIX}${routeId}`
        const layerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`

        // 添加源
        if (map.value.getSource(sourceId)) {
            // 更新现有源
            const source = map.value.getSource(sourceId)
            source.setData({
                type: 'Feature',
                properties: {
                    routeId: routeId,
                    driverId: driverId
                },
                geometry: {
                    type: 'LineString',
                    coordinates: coordinates
                }
            })
        } else {
            // 创建新源
            map.value.addSource(sourceId, {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: {
                        routeId: routeId,
                        driverId: driverId
                    },
                    geometry: {
                        type: 'LineString',
                        coordinates: coordinates
                    }
                }
            })

            // 添加图层
            map.value.addLayer({
                id: layerId,
                type: 'line',
                source: sourceId,
                layout: {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                paint: {
                    'line-color': routeColor,
                    'line-width': 3,
                    'line-opacity': 0.8
                }
            })
        }

        // 返回坐标数组，用于保存到Route对象
        return coordinates
    }

    // 绘制真实道路路线
    const drawRealisticRoute = async(routeId, driverId, driverOrders) => {
        // Capture the context ID when this specific request starts
        const requestContextId = routeRequestContextId.value;

        // Initial check
        if (requestContextId !== routeRequestContextId.value) {
            console.log(`绘制真实路线 ${routeId} (Ctx ${requestContextId}) 立即中止: 上下文已过时`);
            return null;
        }

        if (!map.value || driverOrders.length < 2) return null

        // 清除现有的所有路线，避免同时显示直线和真实路线
        const clearExistingRoutes = () => {
            try {
                // 获取地图样式
                const style = map.value.getStyle();
                if (!style || !style.layers) return;

                // 查找所有与当前路线相关的图层，包括路线图层和起点/终点标记图层
                const routeLayers = style.layers
                    .filter(layer =>
                        layer.id.includes(routeId) ||
                        layer.id.includes(`route-start-layer-${routeId}`) ||
                        layer.id.includes(`route-end-layer-${routeId}`)
                    )
                    .map(layer => layer.id);

                console.log(`找到 ${routeLayers.length} 个与路线 ${routeId} 相关的图层`);

                // 移除所有找到的图层
                routeLayers.forEach(layerId => {
                    if (map.value.getLayer(layerId)) {
                        console.log(`清除路线图层: ${layerId}`);
                        map.value.removeLayer(layerId);
                    }
                });

                // 查找所有与当前路线相关的源，包括路线源和起点/终点标记源
                const routeSources = Object.keys(style.sources || {})
                    .filter(sourceId =>
                        sourceId.includes(routeId) ||
                        sourceId.includes(`route-start-source-${routeId}`) ||
                        sourceId.includes(`route-end-source-${routeId}`)
                    );

                // 移除所有找到的源
                routeSources.forEach(sourceId => {
                    if (map.value.getSource(sourceId)) {
                        console.log(`清除路线源: ${sourceId}`);
                        map.value.removeSource(sourceId);
                    }
                });

                // 特别检查标准ID格式的图层和源
                const standardLayerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`;
                const standardSourceId = `${ROUTE_SOURCE_ID_PREFIX}${routeId}`;

                // 如果标准图层存在，移除它
                if (map.value.getLayer(standardLayerId)) {
                    console.log(`清除标准路线图层: ${standardLayerId}`);
                    map.value.removeLayer(standardLayerId);
                }

                // 如果标准源存在，移除它
                if (map.value.getSource(standardSourceId)) {
                    console.log(`清除标准路线源: ${standardSourceId}`);
                    map.value.removeSource(standardSourceId);
                }

                console.log(`路线 ${routeId} 的所有现有路线已清除`);
            } catch (error) {
                console.error(`清除路线 ${routeId} 的现有路线时出错:`, error);
            }
        };

        // 调用清除函数
        clearExistingRoutes();

        // 获取路线颜色
        let routeColor = '#FF9800'

        // 优先使用路线自定义颜色
        const route = routeStore.routes.find(r => r.id === routeId)
        if (route && route.color) {
            routeColor = route.color
        } else {
            // 其次使用司机颜色
            const driver = driverStore.getDriverById(driverId)
            if (driver && driver.color) {
                routeColor = driver.color
            }
        }

        // 创建源和图层ID - 使用routeId而不是driverId
        const sourceId = `${ROUTE_SOURCE_ID_PREFIX}${routeId}`
        const layerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`

        try {
            // 准备所有路段的坐标数组
            let allSegments = []

            console.log(`开始绘制司机 ${driverId} 的真实路线 (上下文 ${requestContextId})`);

            // 初始化地图数据源，但不显示任何路线，直到真实路线数据到达
            // 创建空的路线数据源
            if (!map.value.getSource(sourceId)) {
                // 创建新源，但使用空的坐标数组
                map.value.addSource(sourceId, {
                    type: 'geojson',
                    data: {
                        type: 'Feature',
                        properties: {},
                        geometry: {
                            type: 'LineString',
                            coordinates: [] // 空坐标数组，不显示任何路线
                        }
                    }
                });

                // 添加图层
                map.value.addLayer({
                    id: layerId,
                    type: 'line',
                    source: sourceId,
                    layout: {
                        'line-join': 'round',
                        'line-cap': 'round'
                    },
                    paint: {
                        'line-color': routeColor,
                        'line-width': 3,
                        'line-opacity': 0.8
                    }
                });
            } else {
                // 更新现有源为空坐标数组
                map.value.getSource(sourceId).setData({
                    type: 'Feature',
                    properties: {},
                    geometry: {
                        type: 'LineString',
                        coordinates: [] // 空坐标数组，不显示任何路线
                    }
                });
            }

            // 动态更新函数 - 用于在处理过程中实时更新地图上的路线
            const updateRouteOnMap = () => {
                 // Check global context before updating map
                 if (requestContextId !== routeRequestContextId.value) {
                      console.log(`更新地图路线 ${driverId} (Ctx ${requestContextId}) 中止: 上下文已过时 (当前 ${routeRequestContextId.value})`);
                      return;
                 }
                if (map.value && map.value.getSource(sourceId) && allSegments.length > 1) {
                    map.value.getSource(sourceId).setData({
                        type: 'Feature',
                        properties: {},
                        geometry: {
                            type: 'LineString',
                            coordinates: allSegments
                        }
                    })
                }
            }

            // 处理一对点之间的路线的函数
            const processRouteBetweenPoints = async(startPoint, endPoint, index, totalPairs) => {
                 // Check context at the beginning
                 if (requestContextId !== routeRequestContextId.value) {
                     console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 中止: 上下文已过时`);
                     return null;
                 }

                const startCoords = startPoint.lng_lat || startPoint.location
                const endCoords = endPoint.lng_lat || endPoint.location

                if (!startCoords || !endCoords) return null // Return null if coords invalid

                // 检查起点和终点是否相同或距离太近
                const isSamePoint =
                    Math.abs(startCoords[0] - endCoords[0]) < 0.0001 &&
                    Math.abs(startCoords[1] - endCoords[1]) < 0.0001

                if (isSamePoint) {
                      if (requestContextId !== routeRequestContextId.value) return null;
                     return [[startCoords[1], startCoords[0]]];
                }

                // 计算两点之间的距离
                const distance = calculateDistance(
                    startCoords[0], startCoords[1],
                    endCoords[0], endCoords[1]
                )

                // 获取当前路段的类型（如果在点对中有定义）
                const pairType = startPoint.type && endPoint.type ?
                    (startPoint.type === 'start_point' ? 'start_to_first' :
                     endPoint.type === 'end_point' ? 'last_to_end' : 'order_to_order') :
                    'unknown';

                // 在真实路线模式下，无论距离多短，都不使用直线连接
                // 只记录距离信息，但始终使用真实路线
                if (distance < 0.1) {
                    console.log(`路段 ${index + 1}/${totalPairs} (${pairType}): 距离过短(${(distance * 1000).toFixed(0)}m)，但仍使用真实路线`)
                }

                // 记录路段类型信息
                console.log(`处理${pairType}路段 ${index + 1}/${totalPairs}: 距离 ${(distance * 1000).toFixed(0)}m，使用真实路线`);

                // API请求限流 - 确保请求间隔足够长
                const now = Date.now()
                const timeSinceLastRequest = now - lastRequestTime.value
                if (timeSinceLastRequest < REQUEST_INTERVAL) {
                    await new Promise(resolve =>
                        setTimeout(resolve, REQUEST_INTERVAL - timeSinceLastRequest)
                    )
                }
                lastRequestTime.value = Date.now()

                // 使用Mapbox Directions API
                try {
                    // 检查坐标有效性
                     if (!isValidCoordinate(startCoords) || !isValidCoordinate(endCoords)) {
                         console.warn('无效的坐标', startCoords, endCoords)
                         console.log('坐标无效，该路段将不会显示')
                         if (requestContextId !== routeRequestContextId.value) return null;
                         // 在真实路线模式下，如果坐标无效，返回null表示该路段无法绘制
                         return null;
                     }

                    // 显式转换为数字并进行校验
                    const startLat = parseFloat(startCoords[0]);
                    const startLng = parseFloat(startCoords[1]);
                    const endLat = parseFloat(endCoords[0]);
                    const endLng = parseFloat(endCoords[1]);

                    if (isNaN(startLat) || isNaN(startLng) || isNaN(endLat) || isNaN(endLng)) {
                        console.error('坐标转换失败，包含非数字值:', startCoords, endCoords);
                        console.log('坐标转换失败，该路段将不会显示');
                        // 根据上下文决定是否继续
                        if (requestContextId !== routeRequestContextId.value) return null;
                        // 在真实路线模式下，如果坐标转换失败，返回null表示该路段无法绘制
                        return null;
                    }

                    // 构建Mapbox Directions API请求URL
                    // 格式: /directions/v5/{profile}/{coordinates}
                    const profile = 'mapbox/driving' // 驾车模式
                    const coordinates = `${startLng},${startLat};${endLng},${endLat}`
                    // 简化请求参数，只获取必要的数据
                    const url = `https://api.mapbox.com/directions/v5/${profile}/${coordinates}?geometries=geojson&access_token=${MAPBOX_API_KEY}&overview=full&steps=false`

                    console.log(`请求Mapbox路线数据 ${index + 1}/${totalPairs}: 从 [${startLat.toFixed(6)},${startLng.toFixed(6)}] 到 [${endLat.toFixed(6)},${endLng.toFixed(6)}] (Ctx ${requestContextId})`)

                    // 添加超时时间和重试逻辑
                    let retries = 0
                    const maxRetries = 2
                    let success = false
                    let data

                    while (retries <= maxRetries && !success) {
                          // Check context before each retry
                          if (requestContextId !== routeRequestContextId.value) {
                              console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 重试中止: 上下文已过时`);
                              return null;
                          }
                        try {
                            // 使用较长的超时时间
                            const controller = new AbortController()
                            const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

                            const response = await fetch(url, {
                                method: 'GET',
                                signal: controller.signal,
                                headers: {
                                    'Accept': 'application/json'
                                }
                            })

                            clearTimeout(timeoutId)

                            if (!response.ok) {
                                const errStatus = response.status
                                let errText = await response.text()

                                // 如果是429（请求过多），等待更长时间后重试
                                if (errStatus === 429 && retries < maxRetries) {
                                    console.log(`请求限制(429)，等待重试... (${retries + 1}/${maxRetries + 1})`)
                                    // 指数退避策略
                                    await new Promise(resolve => setTimeout(resolve, 2000 * Math.pow(2, retries)))
                                    retries++
                                    continue
                                }

                                throw new Error(`Mapbox API请求失败: ${errStatus} ${errText}`)
                            }

                            data = await response.json()
                            success = true
                        } catch (e) {
                            if (e.name === 'AbortError') {
                                console.log('请求超时，正在重试...')
                            } else if (retries < maxRetries) {
                                console.log(`请求失败，重试中... (${retries + 1}/${maxRetries + 1})`, e)
                            } else {
                                throw e // Throw error after max retries
                            }
                            retries++

                            // 重试前等待
                            await new Promise(resolve => setTimeout(resolve, 1000 * retries))
                        }
                    }

                     // Check context after fetch/retry loop
                     if (requestContextId !== routeRequestContextId.value) {
                          console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 结果丢弃: 上下文已过时`);
                          return null;
                     }

                     if (!success || !data) {
                          // 在真实路线模式下，如果无法获取路线数据，返回null表示该路段无法绘制
                          console.log('无法获取Mapbox路线数据，该路段将不会显示')
                          return null; // 返回null，表示该路段无法绘制
                     }

                    // 从Mapbox API响应提取路线坐标
                    if (data.routes && data.routes.length > 0) {
                        const routeData = data.routes[0]

                        // 打印路线信息用于调试
                        console.log(`路段 ${index + 1} - 距离: ${(routeData.distance / 1000).toFixed(2)}公里，预计时间: ${(routeData.duration / 60).toFixed(2)}分钟`)

                        // 存储路段详细信息（用于后续保存到 Route 对象）
                        const segmentInfo = {
                            distance: routeData.distance,
                            duration: routeData.duration,
                            weight: routeData.weight,
                            weight_name: routeData.weight_name,
                            legs: routeData.legs || [],
                            waypoints: routeData.waypoints || [],
                            geometry: routeData.geometry
                        }

                        // Mapbox直接返回GeoJSON格式的geometry
                        if (routeData.geometry && routeData.geometry.coordinates) {
                            const coordinates = routeData.geometry.coordinates
                            const pointCount = coordinates.length

                            // 筛选有效坐标
                            const validCoordinates = coordinates.filter(coord =>
                                Array.isArray(coord) && coord.length >= 2 &&
                                !isNaN(coord[0]) && !isNaN(coord[1])
                            )

                            console.log(`路段 ${index + 1} 获取到 ${validCoordinates.length} 个有效点`)

                            // 返回坐标和详细信息
                            return {
                                coordinates: validCoordinates,
                                segmentInfo: segmentInfo
                            };
                        } else {
                             throw new Error('Mapbox响应中缺少路线几何数据'); // Or handle API error message
                        }
                    } else {
                         throw new Error('未找到路线数据'); // Or handle API error message
                    }
                } catch (error) {
                    console.error('获取Mapbox路线失败:', error)
                      // Check context before returning fallback on error
                      if (requestContextId !== routeRequestContextId.value) {
                           console.log(`处理路段 ${driverId}-${index+1} (Ctx ${requestContextId}) 回退中止: 上下文已过时`);
                          return null;
                      }
                     // 在真实路线模式下，如果获取路线失败，返回null表示该路段无法绘制
                     console.log('获取Mapbox路线失败，该路段将不会显示')
                     return null; // 返回null，表示该路段无法绘制
                }
            }; // end processRouteBetweenPoints

            // 获取路线的起点和终点信息
            let startPoint = null
            let endPoint = null

            // 如果路线有起点和终点，获取它们的坐标
            if (route && (route.start_point || route.end_point)) {
                console.log(`真实路线 ${routeId} 有起点/终点信息:`, route.start_point, route.end_point)

                // 处理起点
                if (route.start_point) {
                    const startCoords = addressStore.getCoordinatesByAddressId(route.start_point)
                    if (startCoords) {
                        console.log(`真实路线 ${routeId} 起点坐标:`, startCoords)
                        // 创建虚拟起点对象
                        startPoint = {
                            id: 'start_point',
                            address_id: route.start_point,
                            lng_lat: [startCoords[0], startCoords[1]], // [lat, lng] 格式
                            type: 'start_point'
                        }
                    }
                }

                // 处理终点
                if (route.end_point) {
                    const endCoords = addressStore.getCoordinatesByAddressId(route.end_point)
                    if (endCoords) {
                        console.log(`真实路线 ${routeId} 终点坐标:`, endCoords)
                        // 创建虚拟终点对象
                        endPoint = {
                            id: 'end_point',
                            address_id: route.end_point,
                            lng_lat: [endCoords[0], endCoords[1]], // [lat, lng] 格式
                            type: 'end_point'
                        }
                    }
                }
            }

            // 创建路线点对数组（包括起点和终点）
            const pointPairs = []

            // 如果有起点，添加起点到第一个订单的路段
            if (startPoint && driverOrders.length > 0) {
                pointPairs.push({
                    start: startPoint,
                    end: driverOrders[0],
                    index: 0,
                    type: 'start_to_first'
                })
            }

            // 添加订单之间的路段
            for (let i = 0; i < driverOrders.length - 1; i++) {
                pointPairs.push({
                    start: driverOrders[i],
                    end: driverOrders[i + 1],
                    index: startPoint ? i + 1 : i, // 如果有起点，索引需要偏移
                    type: 'order_to_order'
                })
            }

            // 如果有终点，添加最后一个订单到终点的路段
            if (endPoint && driverOrders.length > 0) {
                pointPairs.push({
                    start: driverOrders[driverOrders.length - 1],
                    end: endPoint,
                    index: pointPairs.length,
                    type: 'last_to_end'
                })
            }

            console.log(`司机 ${driverId} 路线点对总数: ${pointPairs.length} (Ctx ${requestContextId})`)

            // 将点对分组，每组最多4个点对，避免并发请求过多
            const batchSize = 4 // 由于全局队列控制，每批次处理4个即可
            const batches = []
            for (let i = 0; i < pointPairs.length; i += batchSize) {
                batches.push(pointPairs.slice(i, i + batchSize))
            }

            console.log(`司机 ${driverId} 路线分为 ${batches.length} 个批次处理 (Ctx ${requestContextId})`)

            // 跟踪进度
            let processedBatches = 0
            const totalBatches = batches.length

            // 标记是否有任何路段失败
            let allSegmentsFailed = false;

            // 存储所有路段的详细信息
            const allSegmentDetails = [];

            // 按批次并行处理点对
            for (const batch of batches) {
                 // Check context before processing a batch
                 if (requestContextId !== routeRequestContextId.value) {
                     console.log(`处理批次 ${driverId} (Ctx ${requestContextId}) 中止: 上下文已过时`);
                     break; // Stop processing further batches
                 }
                processedBatches++
                console.log(`处理司机 ${driverId} 批次 ${processedBatches}/${totalBatches}，包含 ${batch.length} 对点 (Ctx ${requestContextId})`)

                // 并行处理当前批次的所有点对
                const batchResults = await Promise.all(batch.map(pair =>
                    processRouteBetweenPoints(pair.start, pair.end, pair.index, pointPairs.length)
                ));

                // 检查是否所有路段都成功获取到真实路线
                const hasFailedSegment = batchResults.some(result => result === null);
                if (hasFailedSegment) {
                    console.log(`批次 ${processedBatches}/${totalBatches} 中有路段无法获取真实路线，整条路线将不会显示`);
                    // 如果有任何一个路段失败，标记整条路线失败，但继续处理其他批次
                    // 我们将在所有批次处理完后检查这个标志
                    allSegmentsFailed = true;
                    continue; // 跳过当前批次的处理，继续下一个批次
                }

                // 处理结果（所有路段都成功获取到真实路线）
                for (const segmentResult of batchResults) {
                    if (segmentResult && segmentResult.coordinates && segmentResult.coordinates.length > 0) {
                        const segmentCoords = segmentResult.coordinates;
                        const segmentInfo = segmentResult.segmentInfo;

                        // 存储路段详细信息
                        allSegmentDetails.push(segmentInfo);

                        if (allSegments.length > 0 && segmentCoords.length > 1) {
                            // 如果不是第一个路段，跳过第一个点（它应该与前一个路段的最后一个点匹配）
                            // 检查潜在的空第一段
                            if (allSegments.length === 1 && allSegments[0].length === 2) {
                                // 特殊情况：如果allSegments只有起点，不要切片
                                allSegments.push(...segmentCoords);
                            } else {
                                allSegments.push(...segmentCoords.slice(1));
                            }
                        } else {
                            // 处理第一个路段
                            allSegments.push(...segmentCoords);
                        }
                    }
                }
                // 更新地图（内部检查上下文）
                updateRouteOnMap();

                 console.log(`司机 ${driverId} 进度: ${processedBatches}/${totalBatches} 批次完成 (${Math.round(processedBatches / totalBatches * 100)}%) (Ctx ${requestContextId})`);
            }

            // Final check before returning
            if (requestContextId !== routeRequestContextId.value) {
                console.log(`司机 ${driverId} (Ctx ${requestContextId}) 真实路线绘制完成，但上下文已过时`);
                return null;
            }

            // 如果有任何路段失败，不显示整条路线
            if (allSegmentsFailed) {
                console.log(`路线 ${routeId} 的真实路线绘制失败，因为有路段无法获取真实路线数据`);

                // 清除已绘制的部分路线
                if (map.value.getLayer(layerId)) {
                    map.value.removeLayer(layerId);
                }
                if (map.value.getSource(sourceId)) {
                    map.value.removeSource(sourceId);
                }

                return null;
            }

            // 确保终点路段的真实路线数据被正确处理
            // 如果终点路段的真实路线数据没有被正确添加到路线中，可能需要特殊处理
            // 但我们不应该使用直线，而是确保终点路段使用真实路线

            // 检查是否有终点
            if (route && route.end_point && allSegments.length > 0) {
                // 获取终点坐标
                const endCoords = addressStore.getCoordinatesByAddressId(route.end_point);
                if (endCoords) {
                    console.log(`检查路线 ${routeId} 的终点是否已包含在路线中:`, endCoords);

                    // 检查终点是否已包含在路线中
                    const lastPoint = allSegments[allSegments.length - 1];
                    const endPoint = [endCoords[1], endCoords[0]]; // [lng, lat] 格式

                    // 计算最后一个点和终点之间的距离
                    const distance = calculateDistance(
                        lastPoint[1], lastPoint[0], // [lng, lat] 格式转换为 [lat, lng] 格式
                        endCoords[0], endCoords[1]  // [lat, lng] 格式
                    );

                    console.log(`路线 ${routeId} 的最后一个点和终点之间的距离: ${(distance * 1000).toFixed(0)}m`);

                    // 如果距离太远，可能终点路段的真实路线数据没有被正确添加到路线中
                    // 但我们不应该使用直线，而是记录警告
                    if (distance > 0.1) { // 距离大于100米
                        console.warn(`路线 ${routeId} 的最后一个点和终点之间的距离太远，可能终点路段的真实路线数据没有被正确添加到路线中`);
                    }
                }
            }

            // 最终更新，确保显示完整路线 (checks context internally)
            updateRouteOnMap();

            console.log(`路线 ${routeId} 的真实路线绘制完成 (上下文 ${requestContextId})，总计 ${allSegments.length} 个点`);

            // 如果路线对象存在，保存真实路线几何数据和详细信息
            if (route) {
                route.setRouteGeometry('realistic', allSegments)

                // 计算总距离和总时间
                const totalDistance = allSegmentDetails.reduce((sum, segment) => sum + (segment.distance || 0), 0);
                const totalDuration = allSegmentDetails.reduce((sum, segment) => sum + (segment.duration || 0), 0);
                const totalWeight = allSegmentDetails.reduce((sum, segment) => sum + (segment.weight || 0), 0);

                // 构建详细路线信息
                const routeDetails = {
                    distance: totalDistance,
                    duration: totalDuration,
                    weight: totalWeight,
                    weight_name: allSegmentDetails[0]?.weight_name || 'auto',
                    uuid: `combined-${Date.now()}`, // 组合路线的唯一标识
                    legs: allSegmentDetails.map((segment, index) => ({
                        ...segment.legs[0], // 取第一个 leg（通常每个路段只有一个 leg）
                        segment_index: index,
                        distance: segment.distance,
                        duration: segment.duration,
                        weight: segment.weight
                    })),
                    waypoints: allSegmentDetails.flatMap(segment => segment.waypoints || []),
                    segments: allSegmentDetails // 保存所有路段的原始信息
                };

                // 保存详细路线信息
                route.setRouteDetails(routeDetails);

                console.log(`路线 ${routeId} 详细信息已保存:`, {
                    totalDistance: `${(totalDistance / 1000).toFixed(2)}km`,
                    totalDuration: `${(totalDuration / 60).toFixed(2)}min`,
                    totalWeight: totalWeight.toFixed(2),
                    segmentCount: allSegmentDetails.length
                });
            }

            // 创建起点和终点图标（如果尚未创建）
            // 使用内部函数，避免引用错误
            if (map.value) {
                // 检查图标是否已存在
                if (!map.value.hasImage('start-point-icon') || !map.value.hasImage('end-point-icon')) {
                    // 创建起点图标（绿色旗帜）
                    const startIcon = new Image(24, 24);
                    startIcon.onload = () => {
                        if (map.value && !map.value.hasImage('start-point-icon')) {
                            map.value.addImage('start-point-icon', startIcon);
                            console.log('已创建起点图标');
                        }
                    };
                    startIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                            <line x1="4" y1="22" x2="4" y2="15"></line>
                        </svg>
                    `);

                    // 创建终点图标（红色标记）
                    const endIcon = new Image(24, 24);
                    endIcon.onload = () => {
                        if (map.value && !map.value.hasImage('end-point-icon')) {
                            map.value.addImage('end-point-icon', endIcon);
                            console.log('已创建终点图标');
                        }
                    };
                    endIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                    `);
                }
            }

            // 添加起点和终点标记
            // 内联实现，避免引用错误
            if (map.value) {
                // 获取路线对象
                const route = routeStore.routes.find(r => r.id === routeId);
                if (route) {
                    // 创建起点和终点标记的源和图层ID
                    const startSourceId = `route-start-source-${routeId}`;
                    const startLayerId = `route-start-layer-${routeId}`;
                    const endSourceId = `route-end-source-${routeId}`;
                    const endLayerId = `route-end-layer-${routeId}`;

                    // 获取路线颜色
                    let routeColor = '#FF9800';
                    if (route.color) {
                        routeColor = route.color;
                    } else if (route.driverId) {
                        const driver = driverStore.getDriverById(route.driverId);
                        if (driver && driver.color) {
                            routeColor = driver.color;
                        }
                    }

                    // 处理起点
                    if (route.start_point) {
                        const startCoords = addressStore.getCoordinatesByAddressId(route.start_point);
                        if (startCoords) {
                            console.log(`为路线 ${routeId} 添加起点标记:`, startCoords);

                            // 移除现有的起点标记（如果存在）
                            if (map.value.getLayer(startLayerId)) {
                                map.value.removeLayer(startLayerId);
                            }
                            if (map.value.getSource(startSourceId)) {
                                map.value.removeSource(startSourceId);
                            }

                            // 添加起点标记源
                            map.value.addSource(startSourceId, {
                                type: 'geojson',
                                data: {
                                    type: 'Feature',
                                    properties: {
                                        routeId: routeId,
                                        type: 'start_point',
                                        description: '起点'
                                    },
                                    geometry: {
                                        type: 'Point',
                                        coordinates: [startCoords[1], startCoords[0]] // [lng, lat] 格式
                                    }
                                }
                            });

                            // 添加起点标记图层
                            map.value.addLayer({
                                id: startLayerId,
                                type: 'symbol',
                                source: startSourceId,
                                layout: {
                                    'icon-image': 'start-point-icon',
                                    'icon-size': 1.0,
                                    'icon-allow-overlap': true,
                                    'icon-anchor': 'center',
                                    'text-field': '起点',
                                    'text-font': ['Open Sans Regular'],
                                    'text-size': 12,
                                    'text-offset': [0, 1.5],
                                    'text-anchor': 'top'
                                },
                                paint: {
                                    'text-color': routeColor,
                                    'text-halo-color': '#ffffff',
                                    'text-halo-width': 2
                                }
                            });
                        }
                    }

                    // 处理终点
                    if (route.end_point) {
                        const endCoords = addressStore.getCoordinatesByAddressId(route.end_point);
                        if (endCoords) {
                            console.log(`为路线 ${routeId} 添加终点标记:`, endCoords);

                            // 移除现有的终点标记（如果存在）
                            if (map.value.getLayer(endLayerId)) {
                                map.value.removeLayer(endLayerId);
                            }
                            if (map.value.getSource(endSourceId)) {
                                map.value.removeSource(endSourceId);
                            }

                            // 添加终点标记源
                            map.value.addSource(endSourceId, {
                                type: 'geojson',
                                data: {
                                    type: 'Feature',
                                    properties: {
                                        routeId: routeId,
                                        type: 'end_point',
                                        description: '终点'
                                    },
                                    geometry: {
                                        type: 'Point',
                                        coordinates: [endCoords[1], endCoords[0]] // [lng, lat] 格式
                                    }
                                }
                            });

                            // 添加终点标记图层
                            map.value.addLayer({
                                id: endLayerId,
                                type: 'symbol',
                                source: endSourceId,
                                layout: {
                                    'icon-image': 'end-point-icon',
                                    'icon-size': 1.0,
                                    'icon-allow-overlap': true,
                                    'icon-anchor': 'center',
                                    'text-field': '终点',
                                    'text-font': ['Open Sans Regular'],
                                    'text-size': 12,
                                    'text-offset': [0, 1.5],
                                    'text-anchor': 'top'
                                },
                                paint: {
                                    'text-color': routeColor,
                                    'text-halo-color': '#ffffff',
                                    'text-halo-width': 2
                                }
                            });
                        }
                    }
                }
            }

            return allSegments; // 返回生成的路线点，便于调试

        } catch (error) {
            console.error(`绘制路线 ${routeId} 的真实路线时出错:`, error);
            // 不再回退到直线路线，避免同时显示两种路线
            // 只记录错误，不执行任何操作
            console.log(`路线 ${routeId} 的真实路线绘制失败，请重试`);
            return null;
        }
    }; // end drawRealisticRoute

    // 计算两点之间的距离（公里）
    const calculateDistance = (lat1, lon1, lat2, lon2) => {
        const R = 6371 // 地球半径（公里）
        const dLat = (lat2 - lat1) * Math.PI / 180
        const dLon = (lon2 - lon1) * Math.PI / 180
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2)
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        const distance = R * c
        return distance // 公里
    }

    // 检查坐标是否有效
    const isValidCoordinate = coord => {
        if (!coord || !Array.isArray(coord) || coord.length !== 2) return false

        const [lat, lng] = coord

        // 检查纬度是否在有效范围内 (-90 到 90)
        if (lat < -90 || lat > 90) return false

        // 检查经度是否在有效范围内 (-180 到 180)
        if (lng < -180 || lng > 180) return false

        // 检查坐标是否为数字
        if (isNaN(lat) || isNaN(lng)) return false

        return true
    }

    // 添加路线顺序变更时的自动更新功能
    const updateRouteOnOrdersReordered = (routeId, orders) => {
        if (!map.value || !routeId || !orders || orders.length < 2) return;

        console.log(`路线 ${routeId} 订单顺序已变更，更新路线显示`);

        // 获取路线对象
        const route = routeStore.routes.find(r => r.id === routeId);
        if (!route) return;

        // 获取路线的订单
        const validOrders = orders.filter(o => o && (o.lng_lat || o.location));

        // 如果有有效订单，直接绘制这条路线，而不是调用 redrawRoutes
        if (validOrders.length > 0) {
            try {
                // 获取这条路线的图层ID
                const layerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`;
                const sourceId = `${ROUTE_SOURCE_ID_PREFIX}${routeId}`;

                // 如果图层已存在，我们只更新数据源，而不是删除和重新创建
                if (map.value.getLayer(layerId) && map.value.getSource(sourceId)) {
                    console.log(`更新路线 ${routeId} 的数据源`);
                    // 我们将在 drawRoutes 中更新数据源
                } else {
                    console.log(`路线 ${routeId} 的图层不存在，将创建新图层`);
                }

                // 绘制新的路线
                drawRoutes(validOrders, routeId);
            } catch (error) {
                console.error(`更新路线 ${routeId} 时出错:`, error);
            }
        }
    };

    // 添加路线可见性控制
    const setRouteVisibility = (routeId, visible) => {
        if (!map.value || !routeId) return;

        // 获取路线对象
        const route = routeStore.routes.find(r => r.id === routeId);
        if (!route) return;

        // 更新路线可见性
        route.setVisible(visible);

        // 如果是当前选中的路线，更新地图显示
        if (routeStore.selectedRoute && routeStore.selectedRoute.id === routeId) {
            // 获取图层ID
            const layerId = `${ROUTE_LAYER_ID_PREFIX}${routeId}`;

            // 检查图层是否存在
            if (map.value.getLayer(layerId)) {
                // 设置图层可见性
                map.value.setLayoutProperty(
                    layerId,
                    'visibility',
                    visible ? 'visible' : 'none'
                );
            }

            // 设置起点和终点标记的可见性
            const startLayerId = `route-start-layer-${routeId}`;
            const endLayerId = `route-end-layer-${routeId}`;

            // 检查起点标记图层是否存在
            if (map.value.getLayer(startLayerId)) {
                // 设置起点标记图层可见性
                map.value.setLayoutProperty(
                    startLayerId,
                    'visibility',
                    visible ? 'visible' : 'none'
                );
            }

            // 检查终点标记图层是否存在
            if (map.value.getLayer(endLayerId)) {
                // 设置终点标记图层可见性
                map.value.setLayoutProperty(
                    endLayerId,
                    'visibility',
                    visible ? 'visible' : 'none'
                );
            }
        }
    };

    // 我们暂时移除这些 watch 函数，因为它们可能导致无限循环
    // 这些更新应该由外部事件触发，而不是自动监听

    return {
        routeType,
        isUpdatingRoutes,
        routeRequestQueue,
        isProcessingRouteQueue,
        activeRequests,
        clearAllRoutes,
        clearRouteById, // 添加新函数到导出列表
        hideAllRoutes,
        showRoute,
        redrawRoutes,
        drawDirectRoute,
        drawRealisticRoute,
        drawRoutes,
        updateRouteOnOrdersReordered,
        setRouteVisibility,
        visibleRouteId,

        // 添加起点和终点标记相关函数
        addRouteEndpointMarkers: (routeId) => {
            if (!map.value) return;

            // 获取路线对象
            const route = routeStore.routes.find(r => r.id === routeId);
            if (!route) return;

            // 创建起点和终点标记的源和图层ID
            const startSourceId = `route-start-source-${routeId}`;
            const startLayerId = `route-start-layer-${routeId}`;
            const endSourceId = `route-end-source-${routeId}`;
            const endLayerId = `route-end-layer-${routeId}`;

            // 获取路线颜色
            let routeColor = '#FF9800';
            if (route.color) {
                routeColor = route.color;
            } else if (route.driverId) {
                const driver = driverStore.getDriverById(route.driverId);
                if (driver && driver.color) {
                    routeColor = driver.color;
                }
            }

            // 处理起点
            if (route.start_point) {
                const startCoords = addressStore.getCoordinatesByAddressId(route.start_point);
                if (startCoords) {
                    console.log(`为路线 ${routeId} 添加起点标记:`, startCoords);

                    // 移除现有的起点标记（如果存在）
                    if (map.value.getLayer(startLayerId)) {
                        map.value.removeLayer(startLayerId);
                    }
                    if (map.value.getSource(startSourceId)) {
                        map.value.removeSource(startSourceId);
                    }

                    // 添加起点标记源
                    map.value.addSource(startSourceId, {
                        type: 'geojson',
                        data: {
                            type: 'Feature',
                            properties: {
                                routeId: routeId,
                                type: 'start_point',
                                description: '起点'
                            },
                            geometry: {
                                type: 'Point',
                                coordinates: [startCoords[1], startCoords[0]] // [lng, lat] 格式
                            }
                        }
                    });

                    // 添加起点标记图层
                    map.value.addLayer({
                        id: startLayerId,
                        type: 'symbol',
                        source: startSourceId,
                        layout: {
                            'icon-image': 'start-point-icon',
                            'icon-size': 1.0,
                            'icon-allow-overlap': true,
                            'icon-anchor': 'center',
                            'text-field': '起点',
                            'text-font': ['Open Sans Regular'],
                            'text-size': 12,
                            'text-offset': [0, 1.5],
                            'text-anchor': 'top'
                        },
                        paint: {
                            'text-color': routeColor,
                            'text-halo-color': '#ffffff',
                            'text-halo-width': 2
                        }
                    });
                }
            }

            // 处理终点
            if (route.end_point) {
                const endCoords = addressStore.getCoordinatesByAddressId(route.end_point);
                if (endCoords) {
                    console.log(`为路线 ${routeId} 添加终点标记:`, endCoords);

                    // 移除现有的终点标记（如果存在）
                    if (map.value.getLayer(endLayerId)) {
                        map.value.removeLayer(endLayerId);
                    }
                    if (map.value.getSource(endSourceId)) {
                        map.value.removeSource(endSourceId);
                    }

                    // 添加终点标记源
                    map.value.addSource(endSourceId, {
                        type: 'geojson',
                        data: {
                            type: 'Feature',
                            properties: {
                                routeId: routeId,
                                type: 'end_point',
                                description: '终点'
                            },
                            geometry: {
                                type: 'Point',
                                coordinates: [endCoords[1], endCoords[0]] // [lng, lat] 格式
                            }
                        }
                    });

                    // 添加终点标记图层
                    map.value.addLayer({
                        id: endLayerId,
                        type: 'symbol',
                        source: endSourceId,
                        layout: {
                            'icon-image': 'end-point-icon',
                            'icon-size': 1.0,
                            'icon-allow-overlap': true,
                            'icon-anchor': 'center',
                            'text-field': '终点',
                            'text-font': ['Open Sans Regular'],
                            'text-size': 12,
                            'text-offset': [0, 1.5],
                            'text-anchor': 'top'
                        },
                        paint: {
                            'text-color': routeColor,
                            'text-halo-color': '#ffffff',
                            'text-halo-width': 2
                        }
                    });
                }
            }
        },

        // 创建起点和终点图标
        createEndpointIcons: () => {
            if (!map.value) return;

            // 检查图标是否已存在
            if (map.value.hasImage('start-point-icon') && map.value.hasImage('end-point-icon')) {
                return; // 图标已存在，不需要重新创建
            }

            // 创建起点图标（绿色旗帜）
            const startIcon = new Image(24, 24);
            startIcon.onload = () => {
                if (map.value && !map.value.hasImage('start-point-icon')) {
                    map.value.addImage('start-point-icon', startIcon);
                    console.log('已创建起点图标');
                }
            };
            startIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                    <line x1="4" y1="22" x2="4" y2="15"></line>
                </svg>
            `);

            // 创建终点图标（红色标记）
            const endIcon = new Image(24, 24);
            endIcon.onload = () => {
                if (map.value && !map.value.hasImage('end-point-icon')) {
                    map.value.addImage('end-point-icon', endIcon);
                    console.log('已创建终点图标');
                }
            };
            endIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                </svg>
            `);
        },

        // 移除路线的起点和终点标记
        removeRouteEndpointMarkers: (routeId) => {
            if (!map.value) return;

            // 起点和终点标记的图层和源ID
            const startLayerId = `route-start-layer-${routeId}`;
            const startSourceId = `route-start-source-${routeId}`;
            const endLayerId = `route-end-layer-${routeId}`;
            const endSourceId = `route-end-source-${routeId}`;

            // 移除起点标记
            if (map.value.getLayer(startLayerId)) {
                map.value.removeLayer(startLayerId);
            }
            if (map.value.getSource(startSourceId)) {
                map.value.removeSource(startSourceId);
            }

            // 移除终点标记
            if (map.value.getLayer(endLayerId)) {
                map.value.removeLayer(endLayerId);
            }
            if (map.value.getSource(endSourceId)) {
                map.value.removeSource(endSourceId);
            }
        },

        // 移除所有路线的起点和终点标记
        removeAllRouteEndpointMarkers: () => {
            if (!map.value || !map.value.getStyle()) return;

            try {
                // 查找所有路线图层
                const style = map.value.getStyle();
                if (!style || !style.layers) return;

                // 查找所有以路线起点和终点图层前缀开头的图层
                const endpointLayers = style.layers
                    .filter(layer => layer.id.startsWith('route-start-layer-') || layer.id.startsWith('route-end-layer-'))
                    .map(layer => layer.id);

                // 移除所有起点和终点图层
                endpointLayers.forEach(layerId => {
                    if (map.value.getLayer(layerId)) {
                        map.value.removeLayer(layerId);
                    }
                });

                // 查找所有以路线起点和终点源前缀开头的源
                const endpointSources = Object.keys(style.sources || {})
                    .filter(sourceId => sourceId.startsWith('route-start-source-') || sourceId.startsWith('route-end-source-'));

                // 移除所有起点和终点源
                endpointSources.forEach(sourceId => {
                    if (map.value.getSource(sourceId)) {
                        map.value.removeSource(sourceId);
                    }
                });

                console.log(`已移除 ${endpointLayers.length} 个路线起点和终点标记`);
            } catch (error) {
                console.error('移除路线起点和终点标记时出错:', error)
            }
        },

        // 动态更新路线的特定路段
        updateRouteSegments: async (routeId, changedSegments) => {
            console.log(`开始动态更新路线 ${routeId} 的 ${changedSegments.length} 个路段`)

            const route = routeStore.routes.find(r => r.id === routeId)
            if (!route) {
                console.error(`路线 ${routeId} 不存在`)
                return
            }

            // 获取当前路线的订单
            const driverOrders = orderStore.getOrdersByDriverId(route.driver || route.driverId)
                .filter(order => order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2)
                .sort((a, b) => (a.stop_no || 0) - (b.stop_no || 0))

            if (driverOrders.length === 0) {
                console.log(`路线 ${routeId} 没有有效订单，跳过更新`)
                return
            }

            // 获取起点和终点
            let startPoint = null
            let endPoint = null

            if (route.start_point) {
                const startCoords = addressStore.getCoordinatesByAddressId(route.start_point)
                if (startCoords) {
                    startPoint = {
                        id: 'start_point',
                        address_id: route.start_point,
                        lng_lat: [startCoords[0], startCoords[1]],
                        type: 'start_point'
                    }
                }
            }

            if (route.end_point) {
                const endCoords = addressStore.getCoordinatesByAddressId(route.end_point)
                if (endCoords) {
                    endPoint = {
                        id: 'end_point',
                        address_id: route.end_point,
                        lng_lat: [endCoords[0], endCoords[1]],
                        type: 'end_point'
                    }
                }
            }

            // 创建完整的点序列（起点 + 订单 + 终点）
            const allPoints = []
            if (startPoint) allPoints.push(startPoint)
            allPoints.push(...driverOrders)
            if (endPoint) allPoints.push(endPoint)

            // 获取现有的路线详细信息
            const existingDetails = route.getRouteDetails()
            const existingSegments = existingDetails?.segments || []

            // 创建新的路段数组，只更新变更的路段
            const newSegments = [...existingSegments]

            // 处理每个需要更新的路段
            for (const segmentIndex of changedSegments) {
                if (segmentIndex >= allPoints.length - 1) {
                    console.warn(`路段索引 ${segmentIndex} 超出范围，跳过`)
                    continue
                }

                const startPoint = allPoints[segmentIndex]
                const endPoint = allPoints[segmentIndex + 1]

                console.log(`更新路段 ${segmentIndex}: 从 ${startPoint.id || startPoint.name} 到 ${endPoint.id || endPoint.name}`)

                try {
                    // 获取新的路段数据
                    const segmentResult = await fetchRouteSegment(startPoint, endPoint)

                    if (segmentResult && segmentResult.coordinates && segmentResult.segmentInfo) {
                        // 更新路段信息
                        newSegments[segmentIndex] = segmentResult.segmentInfo
                        console.log(`路段 ${segmentIndex} 更新成功`)
                    } else {
                        console.warn(`路段 ${segmentIndex} 更新失败`)
                    }
                } catch (error) {
                    console.error(`更新路段 ${segmentIndex} 时出错:`, error)
                }
            }

            // 重新计算总距离和总时间
            const totalDistance = newSegments.reduce((sum, segment) => sum + (segment.distance || 0), 0)
            const totalDuration = newSegments.reduce((sum, segment) => sum + (segment.duration || 0), 0)
            const totalWeight = newSegments.reduce((sum, segment) => sum + (segment.weight || 0), 0)

            // 更新路线详细信息
            const updatedRouteDetails = {
                distance: totalDistance,
                duration: totalDuration,
                weight: totalWeight,
                weight_name: newSegments[0]?.weight_name || 'auto',
                uuid: `updated-${Date.now()}`,
                legs: newSegments.map((segment, index) => ({
                    ...segment.legs?.[0],
                    segment_index: index,
                    distance: segment.distance,
                    duration: segment.duration,
                    weight: segment.weight
                })),
                waypoints: newSegments.flatMap(segment => segment.waypoints || []),
                segments: newSegments
            }

            // 保存更新后的详细信息
            route.setRouteDetails(updatedRouteDetails)

            console.log(`路线 ${routeId} 动态更新完成:`, {
                updatedSegments: changedSegments.length,
                totalDistance: `${(totalDistance / 1000).toFixed(2)}km`,
                totalDuration: `${(totalDuration / 60).toFixed(2)}min`
            })

            // 重新绘制整条路线（简化实现）
            await drawRealisticRoute(routeId)
        },

        // 获取单个路段的数据
        fetchRouteSegment: async (startPoint, endPoint) => {
            const startCoords = startPoint.lng_lat
            const endCoords = endPoint.lng_lat

            if (!startCoords || !endCoords || startCoords.length !== 2 || endCoords.length !== 2) {
                console.error('无效的坐标数据:', startCoords, endCoords)
                return null
            }

            const startLat = parseFloat(startCoords[0])
            const startLng = parseFloat(startCoords[1])
            const endLat = parseFloat(endCoords[0])
            const endLng = parseFloat(endCoords[1])

            if (isNaN(startLat) || isNaN(startLng) || isNaN(endLat) || isNaN(endLng)) {
                console.error('坐标转换失败:', startCoords, endCoords)
                return null
            }

            const profile = 'mapbox/driving'
            const coordinates = `${startLng},${startLat};${endLng},${endLat}`
            const url = `https://api.mapbox.com/directions/v5/${profile}/${coordinates}?geometries=geojson&access_token=${MAPBOX_API_KEY}&overview=full&steps=false`

            try {
                const response = await fetch(url)
                if (!response.ok) {
                    throw new Error(`Mapbox API请求失败: ${response.status}`)
                }

                const data = await response.json()
                if (data.routes && data.routes.length > 0) {
                    const routeData = data.routes[0]

                    const segmentInfo = {
                        distance: routeData.distance,
                        duration: routeData.duration,
                        weight: routeData.weight,
                        weight_name: routeData.weight_name,
                        legs: routeData.legs || [],
                        waypoints: routeData.waypoints || [],
                        geometry: routeData.geometry
                    }

                    const coordinates = routeData.geometry.coordinates.filter(coord =>
                        Array.isArray(coord) && coord.length >= 2 &&
                        !isNaN(coord[0]) && !isNaN(coord[1])
                    )

                    return {
                        coordinates: coordinates,
                        segmentInfo: segmentInfo
                    }
                }
            } catch (error) {
                console.error('获取路段数据失败:', error)
            }

            return null
        },

        // 检测路线变更并更新受影响的路段
        handleRouteOrderChange: async (routeId, oldOrderIds, newOrderIds) => {
            console.log(`检测路线 ${routeId} 订单变更:`)
            console.log('原订单:', oldOrderIds)
            console.log('新订单:', newOrderIds)

            // 找出变更的位置
            const changedSegments = []
            const maxLength = Math.max(oldOrderIds.length, newOrderIds.length)

            for (let i = 0; i < maxLength; i++) {
                if (oldOrderIds[i] !== newOrderIds[i]) {
                    // 如果第i个位置的订单发生变化，需要更新：
                    // 1. 第i-1个订单到第i个订单的路段（如果i > 0）
                    // 2. 第i个订单到第i+1个订单的路段（如果i+1 < newOrderIds.length）

                    if (i > 0) {
                        changedSegments.push(i - 1) // 前一段
                    }
                    changedSegments.push(i) // 当前段

                    // 如果是插入操作，后续所有路段都需要更新
                    if (newOrderIds.length > oldOrderIds.length) {
                        for (let j = i + 1; j < newOrderIds.length; j++) {
                            changedSegments.push(j)
                        }
                        break
                    }
                }
            }

            // 去重
            const uniqueChangedSegments = [...new Set(changedSegments)]

            if (uniqueChangedSegments.length > 0) {
                console.log(`需要更新的路段:`, uniqueChangedSegments)
                await updateRouteSegments(routeId, uniqueChangedSegments)
            } else {
                console.log('没有检测到路段变更')
            }
        }
    };
}