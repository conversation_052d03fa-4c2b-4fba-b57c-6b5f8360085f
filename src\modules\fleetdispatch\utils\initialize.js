/**
 * FleetDispatch模块初始化工具
 * 用于在主应用中集成和初始化FleetDispatch模块所需的环境
 */

import { useDriverStore } from '../stores/driver'
import { useOrderStore } from '../stores/order'
import { useTimeStore } from '../stores/time'
import { useRouteStore } from '../stores/route'
import { useMapStore } from '../stores/map'
import { useAddressStore } from '../stores/address'
import { eventBus, EVENT_TYPES } from './eventBus'
import { firebaseMessagingService } from '../services/FirebaseMessagingService'
import { onMessage, getToken } from 'firebase/messaging'
import { useFirebaseMessaging } from '@/utils/composables/firebase_messaging'
import useUserStore from '@/store/modules/user'
import { vapidKey } from '@/firebase'

/**
 * 初始化FleetDispatch模块
 * 包括初始化Store、事件总线和配置
 */
export async function initializeFleetDispatch() {
    console.log('正在初始化FleetDispatch模块...')

    try {
    // 初始化各个Store
        const driverStore = useDriverStore()
        const orderStore = useOrderStore()
        const timeStore = useTimeStore()
        const routeStore = useRouteStore()
        const mapStore = useMapStore()
        const addressStore = useAddressStore()
        const userStore = useUserStore()

        // 初始化地址存储
        addressStore.initialize()

        // 将 driverStore 和 routeStore 添加到全局，以便其他模块可以访问
        if (typeof window !== 'undefined') {
            window.driverStore = driverStore
            window.routeStore = routeStore
            console.log('已将 driverStore 和 routeStore 添加到全局')
        }

        // 设置当前时间和班次
        timeStore.setCurrentShift()

        // 初始化Firebase消息服务
        firebaseMessagingService.initialize()

        // 如果Firebase消息服务初始化成功，注册消息监听器
        if (firebaseMessagingService.initialized) {
            console.log('[Init] FirebaseMessagingService 已初始化'); // Log initialization

            let messaging;
            try {
                messaging = useFirebaseMessaging();
                console.log('[Init] useFirebaseMessaging() 返回:', messaging); // Log the messaging object
            } catch (e) {
                console.error('[Init] 调用 useFirebaseMessaging() 时出错:', e);
                messaging = null;
            }

            // 检查 messaging 对象是否有效
            if (messaging) {
                console.log('[Init] Messaging 对象有效, 准备注册 onMessage');

                // 检查通知权限
                console.log('[Init] 当前通知权限状态:', Notification.permission);

                // 注册前台消息监听器
                try {
                    console.log('[Init] 尝试注册 onMessage 监听器...');
                    onMessage(messaging, (message) => {
                        // ** 这个日志现在是最关键的 **
                        console.log('>>>>>>>>>>>> [Init] onMessage 回调被触发! <<<<<<<<<<<<', message);

                        // 原始逻辑
                        console.log('收到Firebase前台消息，特殊处理', message);
                        firebaseMessagingService.onReceiveMessage(message);

                        if (message.data && message.data.action === 'DRIVER_UPDATE') {
                            console.log('前台收到司机位置更新，使用单一延迟');

                            // 使用单一延迟，而不是多次触发
                            setTimeout(() => {
                                console.log('延迟3000ms触发司机位置更新');

                                // 检查最后更新时间，避免短时间内重复更新
                                const now = Date.now();
                                const lastUpdate = window.__lastDriverPositionUpdate || 0;
                                const timeSinceLastUpdate = now - lastUpdate;

                                if (timeSinceLastUpdate > 3000) {
                                    console.log(`距上次更新已过 ${timeSinceLastUpdate}ms，执行更新`);
                                    if (driverStore.allDriverPositions && driverStore.allDriverPositions.length > 0) {
                                        eventBus.emit(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, driverStore.allDriverPositions);
                                        window.__lastDriverPositionUpdate = now;
                                    }
                                } else {
                                    console.log(`最近已更新过（${timeSinceLastUpdate}ms前），跳过此次更新`);
                                }
                            }, 3000);
                        }
                    });
                    console.log('[Init] onMessage 监听器注册调用完成 (不保证成功)');
                } catch (error) {
                    console.error('[Init] 注册 onMessage 监听器时发生错误:', error);
                }

                // 请求权限 (如果需要)
                if (Notification.permission !== 'granted') {
                     console.log('[Init] 权限不是 granted, 尝试请求权限...');
                     userStore.requestFCMPermission(); // 确保这个函数能处理请求逻辑和错误
                } else {
                     console.log('[Init] 权限已经是 granted');
                }

            } else {
                console.error('[Init] Messaging 对象无效或获取失败，无法注册 onMessage');
            }

            // 设置定期清理司机位置数据的任务
            // 每5分钟清理一次过期的司机位置数据
            const driverLocationCleanupInterval = setInterval(() => {
                driverStore.cleanupDriverLocations()
            }, 5 * 60 * 1000)

            // 将清理任务保存到全局对象，以便在模块卸载时清理
            window.fleetDispatchTimers = window.fleetDispatchTimers || {}
            window.fleetDispatchTimers.driverLocationCleanup = driverLocationCleanupInterval

            // 注册全局事件总线到window对象
            // 这是为了让所有组件都能访问同一个事件总线实例
            if (typeof window !== 'undefined') {
                window.eventBus = eventBus
                window.EVENT_TYPES = EVENT_TYPES
            }

            // 注册服务工作器广播消息监听器
            if (typeof BroadcastChannel !== 'undefined') {
                const broadcastChannel = new BroadcastChannel('sw-messages');
                broadcastChannel.onmessage = (event) => {
                    console.log('收到服务工作器广播消息:', event.data);

                    // 处理司机位置更新消息 - 兼容旧格式
                    if (event.data.data &&
                        event.data.data.action === 'DRIVER_UPDATE' &&
                        event.data.data.catalog === 'DISPATCHER_NOTIFICATION') {
                        try {
                            // 解析司机位置数据
                            const driversData = JSON.parse(event.data.data.body);
                            console.log('从服务工作器收到司机位置数据:', driversData);

                            if (Array.isArray(driversData)) {
                                // 检查最后更新时间，避免短时间内重复更新
                                const now = Date.now();
                                const lastUpdate = window.__lastDriverPositionUpdate || 0;
                                const timeSinceLastUpdate = now - lastUpdate;

                                if (timeSinceLastUpdate > 3000) {
                                    console.log(`距上次更新已过 ${timeSinceLastUpdate}ms，执行更新`);

                                    // 更新司机位置
                                    const updateResult = driverStore.updateMultipleDriverLocations(driversData);
                                    console.log('司机位置更新结果:', updateResult);

                                    // 触发司机位置更新事件
                                    console.log('广播频道触发司机位置更新事件');
                                    eventBus.emit(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, driverStore.allDriverPositions);
                                    window.__lastDriverPositionUpdate = now;
                                } else {
                                    console.log(`最近已更新过（${timeSinceLastUpdate}ms前），跳过此次更新`);
                                }

                                // 禁用声音提醒
                                // 通知已被禁用
                            } else {
                                console.warn('服务工作器消息中的司机位置数据不是数组');
                            }
                        } catch (error) {
                            console.error('处理服务工作器广播的司机位置数据失败:', error);
                        }
                    }
                    // 处理新格式的司机位置消息
                    else if (event.data.data && event.data.data.action === 'DRIVER_POSITIONS') {
                        try {
                            console.log('收到服务工作器推送的司机位置更新');
                            console.log('消息数据:', event.data);

                            // 解析司机位置数据 - 新格式
                            let driversData = [];

                            // 尝试解析notification中的body
                            if (event.data.notification && event.data.notification.body) {
                                try {
                                    console.log('尝试从notification.body解析司机位置JSON');
                                    const parsedData = JSON.parse(event.data.notification.body);
                                    console.log('解析到司机位置数据:', parsedData);

                                    // 转换为Driver store需要的格式
                                    if (Array.isArray(parsedData)) {
                                        driversData = parsedData.map(item => ({
                                            driver: { id: item.id }, // 新格式使用id而不是driver.id
                                            positions: item.positions // positions是位置数组
                                        }));
                                    } else {
                                        // 单一司机数据
                                        driversData = [{
                                            driver: { id: parsedData.id },
                                            positions: parsedData.positions
                                        }];
                                    }

                                    console.log('转换后的司机位置数据:', driversData);
                                } catch (parseError) {
                                    console.error('解析司机位置JSON数据失败:', parseError);

                                    // 尝试使用备选格式解析
                                    try {
                                        // 有些情况下，整个消息可能已经是结构化的对象
                                        if (event.data.data && event.data.data.body) {
                                            console.log('尝试从data.body解析司机位置');
                                            const parsedData = JSON.parse(event.data.data.body);

                                            if (Array.isArray(parsedData)) {
                                                driversData = parsedData.map(item => ({
                                                    driver: { id: item.id },
                                                    positions: item.positions
                                                }));
                                            }
                                        }
                                    } catch (altError) {
                                        console.error('备选解析方式也失败:', altError);
                                        return;
                                    }
                                }
                            } else if (event.data.data && event.data.data.body) {
                                // 如果notification.body不存在，尝试从data.body解析
                                try {
                                    console.log('尝试从data.body解析司机位置JSON');
                                    const parsedData = JSON.parse(event.data.data.body);

                                    if (Array.isArray(parsedData)) {
                                        driversData = parsedData.map(item => ({
                                            driver: { id: item.id },
                                            positions: item.positions
                                        }));
                                    } else {
                                        driversData = [{
                                            driver: { id: parsedData.id },
                                            positions: parsedData.positions
                                        }];
                                    }
                                } catch (parseError) {
                                    console.error('解析data.body中的司机位置数据失败:', parseError);
                                    return;
                                }
                            } else {
                                console.warn('无法找到司机位置数据', event.data);
                                return;
                            }

                            if (driversData.length === 0) {
                                console.warn('没有有效的司机位置数据');
                                return;
                            }

                            console.log(`准备更新${driversData.length}个司机位置`);

                            // 强制更新 - 禁用时间检查
                            window.__lastDriverPositionUpdate = 0; // 重置上次更新时间

                            // 获取司机存储模块
                            const driverStore = useDriverStore();

                            // 更新司机位置
                            const updateResult = driverStore.updateMultipleDriverLocations(driversData);
                            console.log('司机位置更新结果:', updateResult);

                            // 触发司机位置更新事件
                            console.log('广播频道触发司机位置更新事件');

                            // 获取所有司机位置
                            const allPositions = driverStore.allDriverPositions || [];
                            console.log(`当前所有司机位置数: ${allPositions.length}`, allPositions);

                            // 发送事件
                            eventBus.emit(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, allPositions);

                            // 更新最后更新时间
                            window.__lastDriverPositionUpdate = Date.now();

                            // 禁用所有通知声音和显示
                            window.__suppressNotificationSounds = true;
                            window.__suppressNotifications = true;
                        } catch (error) {
                            console.error('处理服务工作器司机位置消息失败:', error);
                        }
                    }
                };

                // 将广播通道保存到全局对象，以便在模块卸载时清理
                window.fleetDispatchBroadcastChannel = broadcastChannel;

                // 添加可见性变更事件监听 - 仅在页面真正从隐藏状态变为可见状态时触发一次更新
                let wasHidden = document.hidden;
                document.addEventListener('visibilitychange', () => {
                    // 只在页面从隐藏状态变为可见状态时触发更新
                    if (wasHidden && !document.hidden) {
                        console.log('页面从后台切换到前台，刷新司机位置');

                        // 检查最后更新时间，避免短时间内重复更新
                        const now = Date.now();
                        const lastUpdate = window.__lastDriverPositionUpdate || 0;
                        const timeSinceLastUpdate = now - lastUpdate;

                        if (timeSinceLastUpdate > 10000) {  // 从后台切换至少间隔10秒才更新
                            console.log(`距上次更新已过 ${timeSinceLastUpdate}ms，执行更新`);

                            // 更新司机位置
                            if (driverStore.allDriverPositions && driverStore.allDriverPositions.length > 0) {
                                console.log('刷新司机位置数据，数量:', driverStore.allDriverPositions.length);
                                eventBus.emit(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, driverStore.allDriverPositions);
                                window.__lastDriverPositionUpdate = now;
                            } else {
                                console.log('没有司机位置数据可刷新');
                            }
                        } else {
                            console.log(`最近已更新过（${timeSinceLastUpdate}ms前），跳过此次更新`);
                        }
                    }

                    // 更新状态
                    wasHidden = document.hidden;
                });
            }

            console.log('FleetDispatch模块初始化完成')
            return true
        } else {
            console.error('[Init] FirebaseMessagingService 初始化失败，无法注册 onMessage')
            return false
        }
    } catch (error) {
        console.error('FleetDispatch模块初始化失败:', error)
        return false
    }
}

/**
 * 清理FleetDispatch模块
 * 在模块卸载时调用，清理全局变量和事件监听器
 */
export function cleanupFleetDispatch() {
    console.log('正在清理FleetDispatch模块...')

    try {
        // 清理司机位置数据定时器
        if (window.fleetDispatchTimers && window.fleetDispatchTimers.driverLocationCleanup) {
            clearInterval(window.fleetDispatchTimers.driverLocationCleanup)
            delete window.fleetDispatchTimers.driverLocationCleanup
        }

        // 清理广播通道
        if (window.fleetDispatchBroadcastChannel) {
            window.fleetDispatchBroadcastChannel.close();
            delete window.fleetDispatchBroadcastChannel;
        }

        // 移除全局事件总线
        if (typeof window !== 'undefined') {
            delete window.eventBus
            delete window.EVENT_TYPES

            // 清理其他全局变量
            if (window.fleetDispatchTimers) {
                delete window.fleetDispatchTimers
            }

            // 清理全局 Store
            if (window.driverStore) {
                delete window.driverStore
            }
            if (window.routeStore) {
                delete window.routeStore
            }
        }

        console.log('FleetDispatch模块清理完成')
        return true
    } catch (error) {
        console.error('FleetDispatch模块清理失败:', error)
        return false
    }
}