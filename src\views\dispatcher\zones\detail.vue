<script setup name="DispatcherZonesDetail">
import useSettingsStore from '@/store/modules/settings'
import DetailForm from './components/DetailForm/index.vue'
import eventBus from '@/utils/eventBus'
import { useTabbar } from '@/utils/composables'

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()

const formRef = ref()

function onSubmit() {
    formRef.value.submit(() => {
        eventBus.emit('get-data-list')
        goBack()
    })
}

function onCancel() {
    goBack()
}

// 返回列表页
function goBack() {
    if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
        useTabbar().close({ name: 'dispatcherZoneList' })
    } else {
        router.push({ name: 'dispatcherZoneList' })
    }
}
</script>

<template>
    <div>
        <page-header
            :title="(route.query.id == '' ? $t('operations.add') : $t('operations.edit')) + ' ' + $t('dispatcher.zones.title')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>
        <DetailForm :id="route.query.id" ref="formRef" />
        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onSubmit">{{ $t('operations.submit') }}</el-button>
            <el-button size="large" @click="onCancel">{{ $t('operations.cancel') }}</el-button>
        </fixed-action-bar>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
