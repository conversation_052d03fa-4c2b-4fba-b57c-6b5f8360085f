<script setup>
import api from '@/api'
import { getCategoryList, getSMSTemplates, addSMSTemplate, updateSMSTemplate } from '@/api/modules/messenger';
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const categories = ref([])


onMounted(() => {
    getCategories();

    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getSMSTemplates({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}


function getCategories() {
    getCategoryList().then(res => {
        categories.value = res.data;
    })
}


defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form));
        params.category = params.category.id;
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addSMSTemplate(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateSMSTemplate(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.priority')" prop="priority">
                <el-input v-model="data.form.priority" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" :maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" />
            </el-form-item>
            <el-form-item :label="$t('fields.language')" prop="language">
                <el-select v-model="data.form.language" class="m-2" placeholder="Select">
                    <el-option v-for="item in languages" :key="item"
                        :label="$t(`messenger.selections.languageSelections.${item}`)" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('fields.category')" prop="category">
                <el-select v-model="data.form.category" value-key="id" class="m-2" placeholder="Select" size="large">
                    <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('messenger.emailTemplate.fields.params')" prop="args">
                <el-space v-for="item in data.form?.args">
                    {{ item }},
                </el-space>
            </el-form-item>
            <br />
            <el-form-item :label="$t('messenger.email.fields.plainText')" prop="plain_text">
                <el-input v-model="data.form.content" placeholder="请输入标题" type="textarea" rows="5" maxlength="160" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>