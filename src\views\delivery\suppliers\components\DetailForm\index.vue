<script setup>
import {
    getDeliverySuppliers,
    getZones,
    getDestinations,
    uploadDeliverySupplierLogo,
    updateDeliverySupplier,
    addDeliverySupplier
} from '@/api/modules/delivery';
import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'
import StoreList from '../stores/list.vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        priority: 10,
        logo: '',
        address: {},
        zones: [],
        destinations: []
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        priority: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        sku: [
            { message: '请输入标题', trigger: 'blur' }
        ],
        description: [
            { message: '请输入标题', trigger: 'blur' }
        ],
        address: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        contact: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        phone_number: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        email: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})
const zones = ref([])
const destinations = ref([])

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
    getZones().then(res => zones.value = res.data)
    getDestinations().then(res => destinations.value = res.data)

})

function getInfo() {
    data.value.loading = true
    getDeliverySuppliers({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })

}


function _onClearAddress() {
    data.value.form.address = {}
}

function _onRetrieveAddress(val) {
    data.value.form.address.address = val.Line1
    data.value.form.address.city = val.City
    data.value.form.address.province = val.ProvinceName
    data.value.form.address.postal_code = val.PostalCode
    data.value.form.address.id = val.Id
}

function handleSuccess(res) {
    console.log(res)
    if (res.data.errCode == 365) {
        data.value.form.logo = res.data.logo
    } else {
        ElMessage.warning(res.data.msg)
    }
}



defineExpose({
    submit(callback) {
        var params = JSON.parse(JSON.stringify(data.value.form))
        params.address = data.value.form.address?.id

        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addDeliverySupplier(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateDeliverySupplier(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <el-row>
                <el-col :md="20" :lg="16">
                    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                        <el-form-item :label="$t('fields.priority')" prop="priority">
                            <el-input-number v-model="data.form.priority" placeholder="请输入标题" :step="10" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.sku')" prop="sku">
                            <el-input v-model="data.form.sku" placeholder="请输入标题" maxlength="10" show-word-limit />
                        </el-form-item>
                        <el-form-item :label="$t('fields.name')" prop="name">
                            <el-input v-model="data.form.name" placeholder="请输入标题" maxlength="50" show-word-limit />
                        </el-form-item>
                        <el-form-item :label="$t('fields.desc')" prop="description">
                            <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" rows="5"
                                maxlength="200" show-word-limit />
                        </el-form-item>
                        <el-form-item :label="$t('user.fields.address')" prop="address">
                            <AddressSelect ref="senderAddressSelectRef" :address="data.form.address.address"
                                :city="data.form.address.city" :province="data.form.address.province"
                                :postal-code="data.form.address.postal_code" :on-clear="_onClearAddress"
                                @success="_onRetrieveAddress" />
                            <el-space>
                                <span>{{ data.form.address.postal_code }}</span>
                                <span>{{ data.form.address.city }}</span>
                                <span>{{ data.form.address.province }}</span>
                            </el-space>
                        </el-form-item>
                        <el-form-item :label="$t('fields.contact')" prop="contact">
                            <el-input v-model="data.form.contact" placeholder="请输入标题" maxlength="50" show-word-limit />
                        </el-form-item>
                        <el-form-item :label="$t('user.fields.phoneNumber')" prop="phone_number" maxlength="11"
                            show-word-limit>
                            <el-input v-model="data.form.phone_number" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item :label="$t('user.fields.email')" prop="email">
                            <el-input v-model="data.form.email" placeholder="请输入标题" maxlength="50" show-word-limit />
                        </el-form-item>
                        <el-form-item :label="$t('delivery.services.fields.zones')">
                            <el-select v-model="data.form.zones" :multiple="true"
                                :placeholder="$t('selectPlaceHolder', { field: $t('delivery.services.fields.zones') })"
                                clearable>
                                <el-option v-for="item in zones" :value="item.id" :key="item.id" :label="item.name" />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('delivery.suppliers.fields.destinations')">
                            <el-select v-model="data.form.destinations" :multiple="true"
                                :placeholder="$t('selectPlaceHolder', { field: $t('delivery.suppliers.fields.destinations') })"
                                clearable>
                                <el-option v-for="item in destinations" :value="item.id" :key="item.id" :label="item.en" />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('fields.isAvailable')" prop="priority">
                            <el-switch v-model="data.form.is_available" />
                        </el-form-item>
                    </el-form>
                </el-col>
                <el-col :span="8" align="middle">
                    <image-upload v-model:url="data.form.logo" :action="uploadDeliverySupplierLogo" noTip
                        class="avatar-upload" @on-success="handleSuccess" />
                </el-col>
            </el-row>
        </page-main>
        <StoreList :supplier-id="data.form.id" :zones="zones" v-if="data.form.id && data.form.id != ''" />
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
