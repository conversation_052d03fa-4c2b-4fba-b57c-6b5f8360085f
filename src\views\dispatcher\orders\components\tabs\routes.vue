<script setup>
    import { getRoutes, updateRoutePriority, completeReviseRoute, deleteRoute } from '@/api/modules/dispatcher'
    import { usePagination } from '@/utils/composables'
    import { Delete, Check, Close } from '@element-plus/icons-vue'
    import { useI18n } from 'vue-i18n'
    import Orders from './driver_orders.vue'

    const { t } = useI18n()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

    const props = defineProps({
        date: {
            type: String,
        },
    })

    onMounted(() => {
        console.log(props.date)
        getData()
    })

    watch(() => props.date, (_) => { getData() })

    defineExpose({
        reload() {
            getData();
        }
    })



    const data = ref({
        loading: false,
        data: []
    })


    function getData() {
        data.value.loading = true
        let params = getParams({ date: props.date });
        getRoutes(params).then(res => {
            data.value.loading = false
            data.value.data = res.data.route_list;
            pagination.value.total = res.data.total
        })
    }

    function onUpdatePriority(row) {
        let params = {
            id: row.id,
            priority: row.priority
        }
        data.value.loading = true
        updateRoutePriority(params).then(res => {
            if (res.data.errCode == 365) {
                getData()
            }
            data.value.loading = false
        })
    }

    function completeRevise(row) {
        let params = {
            id: row.id,
        }
        data.value.loading = true
        completeReviseRoute(params).then(res => {
            if (res.data.errCode == 365) {
                getData()
            }
            data.value.loading = false

        })
    }


    function onDel(row) {
        ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
            let params = {
                id: row.id
            }
            data.value.loading = true
            deleteRoute(params).then((res) => {
                if (res.data.errCode == 365) {
                    getData()
                }
                data.value.loading = false
            })
        }).catch(() => { })
    }
    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getData())
    }
    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getData())
    }
    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getData())
    }
    function onRouteExpanded(row, expandedRows) {
        console.log(row.date)
        console.log(expandedRows.includes(row))
    }
    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (row.completed_at != null) {
            return 'not-available-row'
        } else {
            return ''
        }
    }


</script>

<template>

    <el-table :data="data.data" @expand-change="onRouteExpanded" stripe highlight-current-row v-loading="data.loading"
        :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName">
        <el-table-column type="expand">
            <template #default="scope">
                <div style="padding-right: 40px; padding-left: 40px;">
                    <Orders :id="scope.row.id" />
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('dispatcher.routes.fields.name')" width="200" />
        <el-table-column prop="priority" :label="$t('fields.priority')" align="center" width="80">
            <template #default="scope">
                <el-input v-model="scope.row.priority" :input-style="{ 'text-align': 'center' }"
                    @blur="onUpdatePriority(scope.row)" @keyup.enter="onUpdatePriority(scope.row)" />
            </template>
        </el-table-column>

        <el-table-column prop="driver.name" :label="$t('dispatcher.routes.fields.driver')" />
        <el-table-column prop="pickups" :label="$t('dispatcher.routes.fields.pickups')" align="center" />
        <el-table-column prop="deliveries" :label="$t('dispatcher.routes.fields.deliveries')" align="center" />
        <el-table-column prop="start_at" :label="$t('dispatcher.routes.fields.startAt')" />
        <el-table-column prop="completed_at" :label="$t('dispatcher.routes.fields.completedAt')" />
        <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" />
        <el-table-column prop="created_at" :label="$t('fields.createdAt')" />
        <el-table-column :label="$t('fields.operations')" width="90" align="right" fixed="right">
            <template #default="scope">
                <el-tooltip class="box-item"
                    :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                    placement="top-start">
                    <el-button :type="scope.row.completed_at != null ? 'warning' : 'success'" circle size="small"
                        v-if="scope.row.start_at != null" :icon="scope.row.completed_at != null ? Close : Check"
                        @click="completeRevise(scope.row)">
                    </el-button>
                </el-tooltip>
                <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                    <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                </el-tooltip>
            </template>
        </el-table-column>

    </el-table>
    <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
        :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
        background @size-change="sizeChange" @current-change="currentChange" />

</template>
<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

</style>
