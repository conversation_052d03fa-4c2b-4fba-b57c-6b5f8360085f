<script setup>
import api from '@/api'
import { addOperationHours, getOperationHours, updateOperationHours } from '@/api/modules/messenger'
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getOperationHours({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addOperationHours(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateOperationHours(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('selection.weekday.0')">
                <el-space>
                    <el-time-select v-model="data.form.mon_start_at" start="08:30" step="00:15" end="18:30"
                        :max="data.form.mon_end_at" placeholder="Select time" />
                    ~
                    <el-time-select v-model="data.form.mon_end_at" start="08:30" step="00:15" end="18:30"
                        :min="data.form.mon_start_at" placeholder="Select time" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('selection.weekday.1')">
                <el-space>
                    <el-time-select v-model="data.form.tue_start_at" start="08:30" step="00:15" end="18:30"
                        :max="data.form.tue_end_at" placeholder="Select time" />
                    ~
                    <el-time-select v-model="data.form.tue_end_at" start="08:30" step="00:15" end="18:30"
                        :min="data.form.tue_start_at" placeholder="Select time" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('selection.weekday.2')">
                <el-space>
                    <el-time-select v-model="data.form.wed_start_at" start="08:30" step="00:15" end="18:30"
                        :max="data.form.wed_end_at" placeholder="Select time" />
                    ~
                    <el-time-select v-model="data.form.wed_end_at" start="08:30" step="00:15" end="18:30"
                        :min="data.form.wed_start_at" placeholder="Select time" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('selection.weekday.3')">
                <el-space>
                    <el-time-select v-model="data.form.thu_start_at" start="08:30" step="00:15" end="18:30"
                        :max="data.form.thu_end_at" placeholder="Select time" />
                    ~
                    <el-time-select v-model="data.form.thu_end_at" start="08:30" step="00:15" end="18:30"
                        :min="data.form.thu_start_at" placeholder="Select time" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('selection.weekday.4')">
                <el-space>
                    <el-time-select v-model="data.form.fri_start_at" start="08:30" step="00:15" end="18:30"
                        :max="data.form.fri_end_at" placeholder="Select time" />
                    ~
                    <el-time-select v-model="data.form.fri_end_at" start="08:30" step="00:15" end="18:30"
                        :min="data.form.fri_start_at" placeholder="Select time" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('selection.weekday.5')">
                <el-space>
                    <el-time-select v-model="data.form.sat_start_at" start="08:30" step="00:15" end="18:30"
                        :max="data.form.sat_end_at" placeholder="Select time" />
                    ~
                    <el-time-select v-model="data.form.sat_end_at" start="08:30" step="00:15" end="18:30"
                        :min="data.form.sat_start_at" placeholder="Select time" />
                </el-space>
            </el-form-item>
            <el-form-item :label="$t('selection.weekday.6')">
                <el-space>
                    <el-time-select v-model="data.form.sun_start_at" start="08:30" step="00:15" end="18:30"
                        :max="data.form.sun_end_at" placeholder="Select time" />
                    ~
                    <el-time-select v-model="data.form.sun_end_at" start="08:30" step="00:15" end="18:30"
                        :min="data.form.sun_start_at" placeholder="Select time" />
                </el-space>
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
