<script setup>
import { getEODPaymentOrders, stableEODPaymentOrders } from '@/api/modules/payment'
import { userNameFormatter, orderNoFormatter, currencyFormatter } from '@/utils/formatter'
import statusStyles from '@/views/finance/payment_orders/status'

import StepButtons from './step_buttons.vue'

const props = defineProps({
    date: {
        type: String,
        default: ''
    }

})

const data = ref({
    loading: false,
    orderList: [],
})

const actionDisabled = computed(() => data.value.orderList.reduce(
    (ret, order) => ret && (order.disable_actions || order.to_status == null), true
))


onMounted(() => {
    getOrders()
})


function getOrders() {
    let params = { date: props.date }
    getEODPaymentOrders(params).then(res => {
        data.value.orderList = res.data
    })
}

async function stableOrders() {
    data.value.loading = true;
    let params = data.value.orderList.map(e => {
        return {
            id: e.id, to_status: e.to_status
        }
    })
    for (let i = 0; i <= params.length / 40; i++) {
        let p = params.slice(i * 40, (i + 1) * 40)
        stableEODPaymentOrders(p).then(res => {
            if (res.data.errCode == 365) getOrders()
            data.value.loading = false;
        })
    }


}
</script>
<template>
    <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.orderList" stripe
        highlight-current-row>
        <el-table-column type="index" />
        <el-table-column prop="no" :label="$t('fields.no')" width="100" :formatter="orderNoFormatter" />
        <el-table-column prop="user" :label="$t('fields.user')" :formatter="userNameFormatter" />
        <el-table-column prop="amount" :label="$t('finance.paymentOrder.fields.amount')" sortable="custom"
            :formatter="currencyFormatter" class-name="order-number" align="right" width="100" />
        <el-table-column prop="refunded_amount" :label="$t('finance.paymentOrder.fields.refunded')"
            :formatter="currencyFormatter" align="right" width="100">
            <template #default="scope">
                <span v-if="scope.row.refunded_amount" class="refunded-column">
                    ({{ currencyFormatter(_, _, scope.row.refunded_amount) }})
                </span>
                <span v-else>-</span>
            </template>
        </el-table-column>
        <el-table-column prop="paid_at" width="160" :label="$t('finance.paymentOrder.fields.paidAt')" sortable="custom"
            align="center" />
        <el-table-column prop="method_img" width="160" :label="$t('finance.paymentOrder.fields.paymentMethod')"
            sortable="custom" align="center">
            <template #default="scope">
                <el-image :src="scope.row.method_img" style="height: 20px;" v-if="scope.row.method_img" />
                <span v-else>-</span>
            </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('fields.status')" align="center" width="110">
            <template #default="scope">
                <el-tag :type="statusStyles[scope.row.status]" round size="small">
                    {{ $t(`finance.paymentOrder.selections.status.${scope.row.status}`) }}
                </el-tag>
            </template>
        </el-table-column>
        <!-- <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" /> -->
        <el-table-column :label="$t('fields.operations')" width="300" align="center" fixed="right">
            <template #default="scope">
                <el-radio-group v-model="scope.row.to_status" size="small" :disabled="scope.row.disable_actions">
                    <el-radio label="closed">{{ $t('finance.eod.operations.close') }}</el-radio>
                    <el-radio label="completed">{{ $t('finance.eod.operations.complete') }}</el-radio>
                    <el-radio :label="null">{{ $t('finance.eod.operations.noAction') }}</el-radio>
                </el-radio-group>
            </template>
        </el-table-column>
    </el-table>
    <StepButtons v-bind="$attrs">
        <el-button type="primary" @click="stableOrders" :disabled="actionDisabled" v-loading="data.loading">
            {{ $t('finance.eod.operations.completeOrders') }}
        </el-button>
    </StepButtons>
</template>