import { GET, POST, PUT, DEL, PAT } from "../methods";

const path = 'settings/'

// Holidays
export const getSettingHolidays = (params) => GET(path + 'holidays/', params)
export const getAllHolidays = (params) => GET(path + 'holidays/all/', params)
export const addSettingHoliday = (params) => POST(path + 'holidays/', params)
export const updateSettingHoliday = (params) => PUT(path + 'holidays/', params)
export const updateSettingHolidayAvailability = (params) => PAT(path + 'holidays/', params)
export const deleteSettingHolidays = (params) => DEL(path + 'holidays/', params)

// Delivery Destinations
export const getDeliveryDestinations = () => GET(path + 'delivery-destinations/')
export const addDeliveryDestination = params => POST(path + 'delivery-destinations/', params)
export const updateDeliveryDestination = params => PUT(path + 'delivery-destinations/', params)
export const updateDeliveryDestinationPriority = params => PAT(path + 'delivery-destinations/', params)
export const deleteDeliveryDestination = params => DEL(path + 'delivery-destinations/', params)

// System Settings
export const getSystemSettings = () => GET(path + 'system-settings/');
export const updateSystemSettings = (params) => PUT(path + 'system-settings/', params);

// Company Infos
export const getCompanyInfo = () => GET(path + 'company-info/');
export const updateCompanyInfo = (params) => PUT(path + 'company-info/', params);
