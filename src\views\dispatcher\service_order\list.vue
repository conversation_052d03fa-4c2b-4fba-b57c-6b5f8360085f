<script setup name="DispatcherServiceOrderList">
    import { Delete, Upload, Download, CopyDocument } from '@element-plus/icons-vue'
    import eventBus from '@/utils/eventBus'
    import { usePagination } from '@/utils/composables'
    import { getServiceOrder, getServiceOrders, deleteServiceOrder, uploadServiceOrder, removeServiceOrderFromRoute, duplicateServiceOrder } from '@/api/modules/dispatcher'
    import EmployeeSelector from '@/views/staffs/components/employee_selector.vue'
    import WorkShiftSelector from '@/views/dispatcher/work_shifts/components/shift_selector.vue'
    import OrderStatusStyles from './status'

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            name__icontains: null,
        },
        // 批量操作
        batch: {
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            name__icontains: null,
        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
        eventBus.on('get-data-list', () => {
            getDataList()
        })
    })

    onBeforeUnmount(() => {
        eventBus.off('get-data-list')
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getServiceOrders(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.order_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        router.push({
            name: 'dispatcherServiceOrderDetail'
        })
    }

    function onEdit(row) {
        router.push({
            name: 'dispatcherServiceOrderDetail',
            query: {
                id: row.id
            }
        })
    }
    const duplicateDialogVisible = ref(false)
    const orderToDuplicate = ref({
        id: null,
        location_name: null,
        driver: null,
        shift: null,
        date: null,
        time_window_from: null,
        time_window_to: null,
        duration: 1,
        upload_at: null,
        notes: null,
    })


    async function onDuplicate(row) {
        duplicateDialogVisible.value = true
        let res = await getServiceOrder(row.id);
        orderToDuplicate.value.id = res.data.id;
        orderToDuplicate.value.location_name = res.data.location_name;
        orderToDuplicate.value.driver = res.data.driver;
        orderToDuplicate.value.shift = res.data.shift;
        orderToDuplicate.value.date = res.data.date;
        orderToDuplicate.value.time_window_from = res.data.time_window_from;
        orderToDuplicate.value.time_window_to = res.data.time_window_to;
        orderToDuplicate.value.duration = res.data.duration;
        orderToDuplicate.value.upload_at = res.data.upload_at;
        orderToDuplicate.value.notes = res.data.notes;
    }

    function onCancelDuplicate() {
        duplicateDialogVisible.value = false;
    }

    function onConfirmDuplicate() {
        let params = JSON.parse(JSON.stringify(orderToDuplicate.value))
        duplicateServiceOrder(orderToDuplicate.value.id, params).then(res => {
            if (res.data.errCode == 365) {
                getDataList();
                onCancelDuplicate();
            }
        })
    }


    function onDel(row) {
        ElMessageBox.confirm(`确认删除「${row.location_name}」吗？`, '确认信息').then(() => {
            deleteServiceOrder(row.id).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }


    function onUpload(row) {
        let params = { ids: [row.id] };
        uploadServiceOrder(params).then(res => {
            if (res.errCode == 365) {
                getDataList();
            }
        })
    }

    function onBatchUpload() {
        let params = { ids: data.value.batch.selectionDataList.map(e => e.id) };
        uploadServiceOrder(params).then(res => {
            if (res.errCode == 365) {
                getDataList();
            }
        })
    }

    function onRemove(row) {
        let params = { ids: [row.id] }
        removeServiceOrderFromRoute(params).then(res => {
            if (res.errCode == 365) {
                getDataList();
            }
        })
    }

    function onBatchRemove() {
        let params = { ids: data.value.batch.selectionDataList.map(e => e.id) }
        removeServiceOrderFromRoute(params).then(res => {
            if (res.errCode == 365) {
                getDataList();
            }
        })
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!['SCHEDULED', 'CREATED'].includes(row.status)) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-header title="服务订单" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button size="default" @click="onBatchUpload" type="success">
                        {{ $t('operations.batch', { op: $t('operations.upload') }) }}
                    </el-button>
                    <el-button-group>
                        <!-- <el-button size="default" type="danger" plain>
                            {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                        </el-button> -->
                        <el-button size="default" type="danger" @click="onBatchRemove">
                            {{ $t('operations.batch', { op: $t('operations.removeFromRemote') }) }}
                        </el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }} 服务订单
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column type="index" width="40" align="center" fixed />
                <el-table-column width="40" align="center" fixed>
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.duplicate')" placement="top-start">
                            <el-button type="success" :icon="CopyDocument" text size="small"
                                @click="onDuplicate(scope.row)" />
                        </el-tooltip>
                    </template>

                </el-table-column>
                <el-table-column prop="no" :label="$t('fields.no')" />
                <el-table-column prop="location_name" :label="$t('dispatcher.orders.fields.location')" />
                <el-table-column prop="time_window_from" :label="$t('dispatcher.orders.fields.timeWindow')">
                    <template #default="scope">
                        {{ scope.row.time_window_from }} ~ {{ scope.row.time_window_to }}
                    </template>

                </el-table-column>
                <el-table-column prop="driver" :label="$t('staffs.fields.driver')" />
                <el-table-column prop="eta" :label="$t('dispatcher.orders.fields.eta')" />
                <el-table-column prop="distance" :label="$t('dispatcher.orders.fields.distance')">
                    <template #default="scope">
                        {{ scope.row.distance != null ? (scope.row.distance / 1000).toFixed(1) : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" :label="$t('fields.status')">
                    <template #default="scope">
                        <el-tag :type="OrderStatusStyles[scope.row.status]" round size="small">
                            {{ $t(`dispatcher.serviceOrders.selections.status.${scope.row.status}`) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="150" align="right" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.upload')" placement="top-start">
                            <el-button type="success" :icon="Upload" circle size="small" @click="onUpload(scope.row)"
                                v-if="['CREATED', 'PENDING'].includes(scope.row.status)" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.removeFromRemote')" placement="top-start">
                            <el-button type="danger" plain :icon="Download" circle size="small"
                                @click="onRemove(scope.row)" v-if="scope.row.status == 'SCHEDULED'" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)"
                                v-if="scope.row.status != 'SCHEDULED'" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <!-- Duplication Dialog -->
        <el-dialog v-model="duplicateDialogVisible" title="Duplicate Service Order" width="700">
            <el-form :model="orderToDuplicate" label-width="100">
                <el-form-item :label="$t('dispatcher.orders.fields.location')" prop="location_name">
                    {{ orderToDuplicate.location_name }}
                </el-form-item>
                <el-form-item :label="$t('delivery.worksheets.fields.driver')" prop="driver">
                    <EmployeeSelector v-model="orderToDuplicate.driver" />
                </el-form-item>
                <el-form-item :label="$t('fields.date')">
                    <el-date-picker v-model="orderToDuplicate.date" type="date" placeholder="Pick a day"
                        value-format="YYYY-MM-DD" :shortcuts="shortcuts" :size="size" />
                </el-form-item>
                <el-form-item :label="$t('dispatcher.orders.fields.timeWindow')" prop="time_window_from">
                    <el-space>
                        <el-time-picker v-model="orderToDuplicate.time_window_from" arrow-control
                            placeholder="Arbitrary time" value-format="HH:MM" />
                        ~
                        <el-time-picker v-model="orderToDuplicate.time_window_to" arrow-control
                            placeholder="Arbitrary time" value-format="HH:MM" />
                    </el-space>
                </el-form-item>
                <el-form-item :label="$t('staffs.worksheet.fields.shift')" prop="shift">
                    <WorkShiftSelector v-model="orderToDuplicate.shift" />
                </el-form-item>
                <el-form-item :label="$t('dispatcher.orders.fields.uploadAt')" prop="upload_at">
                    <el-date-picker v-model="orderToDuplicate.upload_at" type="datetime"
                        placeholder="Select date and time" />
                </el-form-item>
                <el-form-item :label="$t('dispatcher.orders.fields.duration')" prop="duration">
                    <el-input-number v-model="orderToDuplicate.duration" />
                </el-form-item>
                <el-form-item :label="$t('fields.notes')" prop="notes">
                    <el-input v-model="orderToDuplicate.notes" placeholder="请输入标题" type="textarea" />
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="onCancelDuplicate">Cancel</el-button>
                    <el-button type="primary" @click="onConfirmDuplicate">
                        Confirm
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>
