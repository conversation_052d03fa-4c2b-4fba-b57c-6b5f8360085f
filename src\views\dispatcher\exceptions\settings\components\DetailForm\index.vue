<script setup>
import api from '@/api'
import { getExceptions, getNotifyMethods, getNotifyUsers, getExceptionTypes, addException, updateException } from '@/api/modules/dispatcher';
import { getCategoryList } from '@/api/modules/messenger'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        type_of: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

onMounted(() => {
    getUtils()
    if (data.value.form.id != '') {
        getInfo()
    }
})

const notifyUsers = ref([])
const notifyMethods = ref([])
const types = ref([])
const categories = ref([])


function getUtils() {
    getNotifyUsers().then(res => {
        notifyUsers.value = res.data
    });
    getNotifyMethods().then(res => {
        notifyMethods.value = res.data
    });
    getExceptionTypes().then(res => {
        types.value = res.data
    });
    getCategoryList().then(res => {
        categories.value = res.data;
    })
}

function getInfo() {
    data.value.loading = true
    getExceptions({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addException(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateException(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="160px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.type')" prop="type_of">
                <el-select v-model="data.form.type_of">
                    <el-option v-for="item of types" :label="item" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('dispatcher.exceptions.fields.notifyUser')">
                <el-select v-model="data.form.notify_user" multiple>
                    <el-option v-for="item of notifyUsers" :label="item" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('dispatcher.exceptions.fields.notifyMethod')">
                <el-select v-model="data.form.notify_method" multiple>
                    <el-option v-for="item of notifyMethods" :label="item" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('dispatcher.exceptions.fields.messageCategory')" prop="category">
                <el-select v-model="data.form.message_category" value-key="id" placeholder="Select">
                    <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('dispatcher.exceptions.fields.needAck')">
                <el-switch v-model="data.form.need_ack" />
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')">
                <el-switch v-model="data.form.is_available" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>