<script setup>
import { getSystemSettings, updateSystemSettings, getCompanyInfo, updateCompanyInfo } from '@/api/modules/settings';


const formRef = ref()
const data = ref({
    loading: false,
    form: {
        title: ''
    },
    rules: {
        copyright: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        current_version: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getSystemSettings().then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function onUpdate() {
    formRef.value.validate(valid => {
        if (valid) {
            updateSystemSettings(data.value.form).then((res) => {
                if (res.data.errCode == 365) { callback && callback() }
            })
        }
    })

}
</script>

<template>
    <div>
        <page-header title="版本设置" />
        <el-row>
            <el-col :span="16">
                <div v-loading="data.loading">
                    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="220px">
                        <page-main title="版权文字">
                            <el-form-item label="Copyright" prop="copyright">
                                <el-input v-model="data.form.copyright" placeholder="请输入标题" />
                            </el-form-item>
                        </page-main>
                        <page-main title="版本设置">
                            <el-form-item label="Current Version" prop="current_version">
                                <el-input v-model="data.form.current_version" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="Required Update" prop="version_requires_update">
                                <el-switch v-model="data.form.version_requires_update" placeholder="请输入标题" />
                            </el-form-item>
                        </page-main>
                        <page-main title="App 市场设置">
                            <el-form-item label="App Store" prop="app_store_uri">
                                <el-input v-model="data.form.app_store_uri" placeholder="请输入标题" />
                            </el-form-item>
                            <el-form-item label="Google Play" prop="google_play_store_uri">
                                <el-input v-model="data.form.google_play_store_uri" placeholder="请输入标题" />
                            </el-form-item>
                        </page-main>
                    </el-form>
                </div>

            </el-col>
        </el-row>


        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onUpdate">{{ $t('operations.submit') }}</el-button>
            <el-button size="large" @click="getInfo">{{ $t('operations.cancel') }}</el-button>
        </fixed-action-bar>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>