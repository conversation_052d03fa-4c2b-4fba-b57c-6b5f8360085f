<script setup name="ChatNowGreetingsList">
import { Delete } from '@element-plus/icons-vue'
import { usePagination } from '@/utils/composables'
import FormMode from './components/FormMode/index.vue'
import { getChatGreetings, deleteChatGreetings, updateChatGreetingAvailabilityPriority } from '@/api/modules/chat_now'


const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const data = ref({
    loading: false,
    /**
     * 详情展示模式
     * router 路由跳转
     * dialog 对话框
     * drawer 抽屉
     */
    // 详情
    formModeProps: {
        visible: false,
        id: '',
        text: ''
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})



onMounted(() => {
    getDataList()
})


function getDataList() {
    data.value.loading = true
    let params = getParams()
    getChatGreetings(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.greeting_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    data.value.formModeProps.id = ''
    data.value.formModeProps.text = ''
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.text = row.text
    data.value.formModeProps.visible = true
}

function onDel(row) {
    ElMessageBox.confirm(`确认删除「${row.text}」吗？`, '确认信息').then(() => {
        deleteChatGreetings({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onBatchDel() {
    ElMessageBox.confirm(`确认删除吗？`, '确认信息').then(() => {
        const ids = data.value.batch.selectionDataList.map(e => e.id);
        deleteChatGreetings({ id: ids.join(',') }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onUpdateAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    updateChatGreetingAvailabilityPriority(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}
function onUpdatePriority(row) {
    let params = {
        id: row.id,
        priority: row.priority
    }
    updateChatGreetingAvailabilityPriority(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}



const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('cms.chatNow.greetings.title')" />
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button type="danger" size="default" @click="onBatchDel">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }} {{ $t('cms.chatNow.greetings.title') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="priority" :label="$t('fields.priority')" align="center" width="80">
                    <template #default="scope">
                        <el-input v-model="scope.row.priority" :input-style="{ 'text-align': 'center' }"
                            @blur="onUpdatePriority(scope.row)" @keyup.enter="onUpdatePriority(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="text" :label="$t('cms.chatNow.greetings.fields.text')" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" :text="data.formModeProps.text" v-model="data.formModeProps.visible"
            @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: #bbb;
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
