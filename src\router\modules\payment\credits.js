/*
* Author: <EMAIL>'
* Date: '2024-01-12 03:13:36'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/payment/credits.js'
* File: 'credits.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/payment/credits',
    component: Layout,
    redirect: '/payment/invoices/list',
    name: 'userInvoices',
    meta: {
        title: '积分管理',
        icon: 'entypo:credit',
        auth: ['super'],
        i18n: 'route.finance.userCredits.title'
    },
    children: [
        {
            path: 'direct',
            name: 'userCreditsDirect',
            component: () => import('@/views/finance/credits/direct/index.vue'),
            meta: {
                title: '积分直付',
                icon: 'mingcute:refund-dollar-line',
                i18n: 'route.finance.userCredits.direct',
                activeMenu: '/payment/credits/direct',
                auth: ['super'],
                // cache: ['userInvoiceDetail']

            }
        },
        {
            path: 'logs/list',
            name: 'userCreditsLogList',
            component: () => import('@/views/finance/credits/logs/list.vue'),
            meta: {
                title: '积分日志',
                icon: 'mingcute:diary-line',
                i18n: 'route.finance.userCredits.logs',
                activeMenu: '/payment/credits/logs/list',
                auth: ['super'],
                // cache: ['userInvoiceDetail']

            }
        },
    ]
}
