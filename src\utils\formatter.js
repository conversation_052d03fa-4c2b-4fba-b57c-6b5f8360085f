/*
* Author: <EMAIL>'
* Date: '2024-09-25 18:43:02'
* Project: 'FleetNowV3'
* Path: 'src/utils/formatter.js'
* File: 'formatter.js'
* Version: '1.0.0'
*/


function phoneNumberFormatter(num) {
    if (num) {
        num = num.replace(/\D/g, '')
        if (num.length === 10) {
            return `(${num.substr(0, 3)}) ${num.substr(3, 3)}-${num.substr(6)}`
        } else if (num.length === 11) {
            return `+${num[0]} (${num.substr(1, 3)}) ${num.substr(4, 3)}-${num.substr(7)}`
        } else {
            return num
        }
    } else {
        return ''
    }

}


function currencyFormatter(row, column, cellValue, index) {
    if (cellValue != null) {
        return (cellValue / 100).toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.))/g, (match) => {
            return match + ',';
        })
    } else {
        return '0.00'
    }
}

function countFormatter(row, column, cellValue, index) {
    if (cellValue) return cellValue
    return 0
}


function percentageFormatter(row, column, cellValue, index) {
    if (Math.abs(cellValue) < 1) {
        return (cellValue * 100).toFixed(1) + '%'
    }
    return currencyFormatter(row, column, cellValue, index)
}

function userNameFormatter(row, column, cellValue, index) {
    return (row?.user?.user_type ?? row?.extended_info?.user_type) === 'personal'
        ? row?.user?.name ?? row.name
        : (
            row?.user.company_name
                ? row.user.company_name
                : row?.user?.name ?? row.name
        )
}

function orderNoFormatter(row, column, cellValue, index) {
    const p = /^(\w{2})[\w-]+(\w{6})$/
    return cellValue.replace(p, (match, p1, p2) => {
        return `${p1}‧${p2}`
    })
}

function snakeToCamel(s) {
    return s.replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase())
}

function replaceNonLatinCharacters(s) {
    return s.replace(/[^a-zA-Z0-9_]/g)

}


export {
    phoneNumberFormatter,
    currencyFormatter,
    percentageFormatter,
    userNameFormatter,
    orderNoFormatter,
    snakeToCamel,
    countFormatter,
    replaceNonLatinCharacters,
}
