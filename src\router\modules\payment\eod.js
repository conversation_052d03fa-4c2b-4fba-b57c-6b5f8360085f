/*
* Author: <EMAIL>'
* Date: '2023-06-29 20:53:47'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/payment/eod.js'
* File: 'eod.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/payment/eod',
    component: Layout,
    redirect: '/payment/eod/reportList',
    name: 'eod',
    meta: {
        title: '结算&统计',
        icon: 'tabler:home-stats',
        auth: ['super'],
        i18n: 'route.finance.endOfDay.title'
    },
    children: [
        {
            path: 'index',
            name: 'financeEOD',
            component: () => import('@/views/finance/eod/index.vue'),
            meta: {
                title: '营业日结',
                icon: 'mdi:book-play',
                i18n: 'route.finance.endOfDay.eod',
                activeMenu: '/payment/eod/index',
                auth: ['super'],
            }
        },
        {
            path: 'reports',
            name: 'reports',
            redirect: '/payment/eod/reportList',
            meta: {
                title: '报告',
                icon: 'mdi:file-report',
                i18n: 'route.finance.endOfDay.reports',
                activeMenu: '/payment/eod/reportList',
                auth: ['super'],
            },
            children: [
                {
                    path: 'reportList',
                    name: 'financeEODReportList',
                    component: () => import('@/views/finance/eod/components/report/list.vue'),
                    meta: {
                        title: '日结报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.eodReports',
                        activeMenu: '/payment/eod/reportList',
                        auth: ['super'],
                    }
                },
                {
                    path: 'reportDetail',
                    name: 'financeEODReportDetail',
                    component: () => import('@/views/finance/eod/components/report/detail.vue'),
                    meta: {
                        title: '日结报告',
                        icon: 'mdi:file-report',
                        i18n: 'route.finance.endOfDay.eodReports',
                        activeMenu: '/payment/eod/reportList',
                        sidebar: false,
                        auth: ['super'],
                    }
                },
                {
                    path: 'deliveryReportList',
                    name: 'financeEODDeliveryReportList',
                    component: () => import('@/views/finance/eod/components/delivery/list.vue'),
                    meta: {
                        title: '订单报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.deliveryReports',
                        activeMenu: '/payment/eod/deliveryReportList',
                        auth: ['super'],
                    }
                },
                {
                    path: 'deliveryReportDetail',
                    name: 'financeEODDeliveryReportDetail',
                    component: () => import('@/views/finance/eod/components/delivery/detail.vue'),
                    meta: {
                        title: '订单报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.deliveryReportDetail',
                        activeMenu: '/payment/eod/deliveryReportDetail',
                        sidebar: false,
                        auth: ['super'],
                    }
                },

                {
                    path: 'salaryList',
                    name: 'financeEODSalaryList',
                    component: () => import('@/views/finance/eod/components/salaries/list.vue'),
                    meta: {
                        title: '工资报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.salaries',
                        activeMenu: '/payment/eod/salaryList',
                        auth: ['super'],
                    }
                },
                {
                    path: 'salaryDetail',
                    name: 'financeEODSalaryDetail',
                    component: () => import('@/views/finance/eod/components/salaries/detail.vue'),
                    meta: {
                        title: '工资报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.salaryDetail',
                        activeMenu: '/payment/eod/salaryList',
                        sidebar: false,
                        auth: ['super'],
                    }
                },
                {
                    path: 'costList',
                    name: 'financeEODCostList',
                    component: () => import('@/views/finance/eod/components/costs/list.vue'),
                    meta: {
                        title: '费用报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.costs',
                        activeMenu: '/payment/eod/costList',
                        auth: ['super'],
                    }
                },
                {
                    path: 'costDetail',
                    name: 'financeEODCostDetail',
                    component: () => import('@/views/finance/eod/components/costs/detail.vue'),
                    meta: {
                        title: '费用报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.costDetail',
                        activeMenu: '/payment/eod/costList',
                        sidebar: false,
                        auth: ['super'],
                    }
                },
            ]
        },
        {
            path: 'payment-reports',
            name: 'paymentReports',
            redirect: '/payment/eod/reportList',
            meta: {
                title: '财务报告',
                icon: 'mdi:file-report',
                i18n: 'route.finance.endOfDay.paymentReports',
                activeMenu: '/payment/eod/paymentReportList',
                auth: ['super'],
            },
            children: [
                {
                    path: 'paymentReportList',
                    name: 'financeEODPaymentReportList',
                    component: () => import('@/views/finance/eod/components/payment/list.vue'),
                    meta: {
                        title: '财务报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.paymentReports',
                        activeMenu: '/payment/eod/paymentReportList',
                        auth: ['super'],
                    }
                },
                {
                    path: 'paymentReportDetail',
                    name: 'financeEODPaymentReportDetail',
                    component: () => import('@/views/finance/eod/components/payment/detail.vue'),
                    meta: {
                        title: '财务报告',
                        icon: 'carbon:report-data',
                        i18n: 'route.finance.endOfDay.paymentReportDetail',
                        activeMenu: '/payment/eod/paymentReportList',
                        sidebar: false,
                        auth: ['super'],
                    }
                },
            ]
        },
    ]
}
