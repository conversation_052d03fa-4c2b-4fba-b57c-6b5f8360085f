const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/suppliers',
    component: Layout,
    redirect: '/dispatcher/suppliers/list',
    name: 'dispatcherSuppliers',
    meta: {
        title: '供应商管理',
        icon: 'carbon:image-service',
        auth: ['super'],
        i18n: 'route.dispatcher.suppliers.title'
    },
    children: [
        {
            path: 'list',
            name: 'dispatcherSupplierList',
            component: () => import('@/views/dispatcher/suppliers/list.vue'),
            meta: {
                title: '供应商列表',
                icon: 'material-symbols:home-repair-service-outline',
                activeMenu: '/dispatcher/suppliers/list',
                i18n: 'route.dispatcher.suppliers.title',
                auth: ['super']
            }
        },

    ]
}
