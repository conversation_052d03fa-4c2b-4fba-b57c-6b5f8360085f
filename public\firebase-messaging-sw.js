importScripts('https://www.gstatic.com/firebasejs/10.12.3/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.3/firebase-messaging-compat.js');

const firebaseConfig = {
    apiKey: "AIzaSyAi2AVP-S-5paRYMlServgkg7sLuKgoCKc",
    authDomain: "fleetnow-v3.firebaseapp.com",
    projectId: "fleetnow-v3",
    storageBucket: "fleetnow-v3.appspot.com",
    messagingSenderId: "378868034319",
    appId: "1:378868034319:web:a824d77978ad8735d26c7a",
    measurementId: "G-D28SD103S0"
};

firebase.initializeApp(firebaseConfig)
const messaging = firebase.messaging();


let db;
// Let us open our database
const DBOpenRequest = self.indexedDB.open('fcmBackgroundMessages', 3);
// Register two event handlers to act on the database being opened successfully, or not
DBOpenRequest.onerror = (event) => {
    console.error('DB created failed');
};
DBOpenRequest.onsuccess = (event) => {
    // Store the result of opening the database in the db variable. This is used a lot below
    console.log('DB Opened 1')
    db = event.target.result;
}

messaging.onBackgroundMessage(function (payload) {
    console.log('[firebase-messaging-sw.js] Received background message ', payload);
    const channel = new BroadcastChannel('sw-messages');
    channel.postMessage(payload);
    // Customize notification here
    const notificationTitle = 'Background Message Title';
    let notificationBody = 'Background Message body.'; // Default value
    let notificationOptions = {
        body: notificationBody,
        icon: '/firebase-logo.png',
        action: 'http://localhost:9000/users/messenger/cs-messages/chat'
    };

    try {
        // 尝试访问可能不存在的 notification.body
        if (payload && payload.notification && payload.notification.body) {
             notificationBody = payload.notification.body;
             notificationOptions.body = notificationBody; // 更新选项中的 body
        } else {
            console.warn('[firebase-messaging-sw.js] Payload does not contain notification.body. Using default.');
            // 如果需要，可以在这里处理没有 body 的情况，比如使用 payload.data 中的信息
            if (payload && payload.data && payload.data.title) {
                notificationOptions.body = payload.data.title; // 示例：使用 data.title 作为 body
            }
        }

        // Grab the values entered into the form fields and store them in an object ready for being inserted into the IndexedDB
        const newItem = [
            {
                id: payload.data.id,
                type: payload.data.type,
                from_token: payload.data.from_token,
                user_id: payload.data.user_id,
                user_avatar: payload.data.user_avatar,
                user_name: payload.data.user_name,
                // 使用从 payload 中获取的 notificationBody 或默认值
                content: notificationBody,
                created_at: payload.data.created_at,
            },
        ];
        // Open a read/write DB transaction, ready for adding the data
        const transaction = db.transaction(['fcmBackgroundMessages'], 'readwrite');
        // Report on the success of the transaction completing, when everything is done
        transaction.oncomplete = () => {
            console.log('Transaction completed: database modification finished.');
        };
        // Handler for any unexpected error
        transaction.onerror = () => {
            console.error(`Transaction not opened due to error: ${transaction.error}`);
        };
        const objectStore = transaction.objectStore('fcmBackgroundMessages');
        // Make a request to add our newItem object to the object store
        const objectStoreRequest = objectStore.add(newItem[0]);
        objectStoreRequest.onsuccess = (event) => {
            // Report the success of our request
            // (to detect whether it has been successfully
            // added to the database, you'd look at transaction.oncomplete)
            console.log('Request successful.');
        }
        objectStoreRequest.oncomplete = (event) => {
            console.log('Added successful.');
        }
        // addBackgroundMessage(payload);
        self.registration.showNotification(notificationTitle,
            notificationOptions);

    } catch (error) {
        console.error(`[firebase-messaging-sw.js] Error processing background message. Error: ${error.message}. Payload causing error:`, JSON.stringify(payload, null, 2));
        // 即使出错，也尝试显示一个通用通知，或者根据需要采取其他错误处理措施
        self.registration.showNotification('Background Message Error', {
            body: 'Failed to process background message content.',
            icon: '/firebase-logo.png'
        });
    }
});

function addBackgroundMessage(payload) {

}
