<script setup>
import ListVue from './components/list.vue'
import { getAddedServices, deleteAddedService, updateAddedServiceAvailabilityPriority, batchAlterServiceAvailability, addAddedService, updateAddedService } from '@/api/modules/delivery'

</script>
<template>
    <ListVue v-bind="$attrs" :get-data="getAddedServices" :on-delete="deleteAddedService"
        :on-update-availability-priority="updateAddedServiceAvailabilityPriority"
        :on-batch-alter-availability="batchAlterServiceAvailability" :on-add="addAddedService"
        :on-update="updateAddedService" title="addedService" module="as" />
</template>
