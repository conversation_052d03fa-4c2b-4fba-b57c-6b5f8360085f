<script setup>
import { getNoReportDates } from '@/api/modules/statistics'

const emit = defineEmits(['start'])

const date = ref(null)

const availableDates = ref([])

onMounted(() => {
    getDates()
})



function getDates() {
    getNoReportDates().then(res => {
        availableDates.value = res.data
    })
}
function disabledDate(time) {
    return !availableDates.value.includes(time.toLocaleDateString('en-CA'));
}

function start() {
    emit('start', date.value)
}


</script>
<template>
    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
        <div style="margin-bottom: 30px;">
            <el-date-picker v-model="date" type="date" :placeholder="$t('finance.eod.pickADay')" :size="size"
                :disabled-date="disabledDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </div>
        <el-button type="primary" @click="start" :disabled="date == null">
            {{ $t('finance.eod.operations.start') }}
        </el-button>
    </div>
</template>