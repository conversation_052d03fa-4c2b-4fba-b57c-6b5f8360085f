<script setup>
import Detail from '../eod/step8.vue'
const router = useRouter()

function goBack() {
    router.push({ name: 'financeEODReportList' })
}

</script>
<template>
    <div>
        <page-header :title="$t('finance.eod.s8')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>

        <page-main>
            <Detail />
        </page-main>
    </div>
</template>