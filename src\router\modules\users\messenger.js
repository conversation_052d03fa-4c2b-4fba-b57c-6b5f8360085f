const Layout = () => import('@/layout/index.vue')

export default {
    path: '/users/messenger',
    component: Layout,
    redirect: '/users/messenger/messages/list',
    name: 'messenger',
    meta: {
        title: '消息系统',
        icon: 'pajamas:messages',
        i18n: 'route.users.messenger.title',

    },
    children: [
        {
            path: 'messages/list',
            name: 'messageList',
            component: () => import('@/views/messenger/messages/list.vue'),
            meta: {
                title: '推送消息',
                icon: 'streamline:mail-chat-bubble-square-warning-bubble-square-messages-notification-chat-message-warning-alert',
                i18n: 'route.users.messenger.notifications',
                activeMenu: '/users/messenger/messages/list',
                cache: ['messageDetail']
            }
        },
        {
            path: 'messages/detail',
            name: 'messageDetail',
            component: () => import('@/views/messenger/messages/detail.vue'),
            meta: {
                title: '消息详情',
                i18n: 'route.users.messenger.notifications',
                activeMenu: '/users/messenger/messages/list',
                sidebar: false,
            }
        },
        {
            path: 'fcm-users/list',
            name: 'fcmUserList',
            component: () => import('@/views/messenger/fcm_users/list.vue'),
            meta: {
                title: '消息终端',
                icon: 'solar:devices-line-duotone',
                i18n: 'route.users.messenger.fcmUsers',
                activeMenu: '/users/messenger/fcm-users/list',
            }
        },
        {
            path: 'fcm-users/detail',
            name: 'fcmUserDetail',
            component: () => import('@/views/messenger/fcm_users/detail.vue'),
            meta: {
                title: '消息终端',
                i18n: 'route.users.messenger.fcmUser',
                activeMenu: '/users/messenger/fcm-users/list',
                sidebar: false,
            },
        },
        {
            path: 'categories/list',
            name: 'messageCategoryList',
            component: () => import('@/views/messenger/categories/list.vue'),
            meta: {
                title: '类别管理',
                icon: 'iconamoon:category',
                i18n: 'route.users.messenger.categories',
                activeMenu: '/users/messenger/categories/list',
                cache: ['messageDetail']
            }
        },
        {
            path: 'email-templates/list',
            name: 'emailTemplatesList',
            component: () => import('@/views/messenger/email_templates/list.vue'),
            meta: {
                title: '邮件模版',
                icon: 'eos-icons:templates-outlined',
                i18n: 'route.users.messenger.emailTemplates',
                activeMenu: '/users/messenger/email-templates/list',
            }
        },
        {
            path: 'email-templates/detail/',
            name: 'emailTemplatesDetail',
            component: () => import('@/views/messenger/email_templates/detail.vue'),
            meta: {
                title: '邮件模版',
                i18n: 'route.users.messenger.emailTemplates',
                activeMenu: '/users/messenger/email-templates/list',
                sidebar: false
            }
        },
        {
            path: 'sms-templates/list',
            name: 'smsTemplatesList',
            component: () => import('@/views/messenger/sms_templates/list.vue'),
            meta: {
                title: '短信模版',
                icon: 'eos-icons:templates-outlined',
                i18n: 'route.users.messenger.smsTemplates',
                activeMenu: '/users/messenger/sms-templates/list',
            }
        },
        {
            path: 'sms-templates/detail/',
            name: 'smsTemplatesDetail',
            component: () => import('@/views/messenger/sms_templates/detail.vue'),
            meta: {
                title: '短信模版',
                i18n: 'route.users.messenger.smsTemplates',
                activeMenu: '/users/messenger/sms-templates/list',
                sidebar: false
            }
        },
        {
            path: 'chat-now-greetings/list',
            name: 'chatNowGreetingsList',
            component: () => import('@/views/cms/chat_now/greetings/list.vue'),
            meta: {
                title: '客服问候语',
                icon: 'fluent-mdl2:greeting-card',
                i18n: 'route.cms.chatNow.greetings',
                activeMenu: '/users/messenger/chat-now-greetings/list',
            }
        },

        {
            path: 'unsubscribing',
            name: 'unsubscribing',
            component: () => import('@/views/messenger/unsubscribing/list.vue'),
            meta: {
                title: '退订列表',
                icon: 'material-symbols-light:unsubscribe-outline',
                i18n: 'route.users.messenger.unsubscribing',
                activeMenu: '/users/messenger/unsubscribing',
            }
        },
        {
            path: 'unsubscribing/logs',
            name: 'unsubscribingLogs',
            component: () => import('@/views/messenger/unsubscribing/logs.vue'),
            meta: {
                title: '退订日志',
                icon: 'tabler:logs',
                i18n: 'route.users.messenger.unsubscribing',
                activeMenu: '/users/messenger/unsubscribing/logs',
            }
        },


    ]
}
