<script setup>
import api from '@/api'
import { getEliteTemplates, updateEliteTemplate, addEliteTemplate, getEliteParams } from '@/api/modules/dispatcher'
import senderPositions from '@/assets/images/sender.jpg';
import receiverPositions from '@/assets/images/receiver.jpg';
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const senderPos = ref(senderPositions)
const receiverPos = ref(receiverPositions)

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const vars = ref([])

onMounted(() => {
    getAvailableVars()
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getEliteTemplates({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function getAvailableVars() {
    getEliteParams().then(res => {
        vars.value = res.data
    })

}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addEliteTemplate(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateEliteTemplate(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-row>
            <el-col :span="8">
                <page-main>
                    <el-descriptions title="Available Vars" :column="1" border direction="vertical">
                        <el-descriptions-item v-for="item in vars" :label="`{${item}}`">
                            {{ $t(`dispatcher.elite.vars.${item}`) }}
                        </el-descriptions-item>
                    </el-descriptions>
                </page-main>
            </el-col>
            <el-col :span="8">
                <page-main>
                    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                        <el-form-item :label="$t('fields.name')" prop="name">
                            <el-input v-model="data.form.name" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.desc')" prop="description">
                            <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" :rows="5" />
                        </el-form-item>
                        <el-divider />
                        <el-form-item label="POS 0" prop="pos_0">
                            <el-input v-model="data.form.pos_0" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS 1" prop="pos_1">
                            <el-input v-model="data.form.pos_1" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS 2" prop="pos_2">
                            <el-input v-model="data.form.pos_2" placeholder="请输入标题" />
                        </el-form-item>
                        <el-divider />
                        <el-form-item label="POS S0" prop="pos_s_0">
                            <el-input v-model="data.form.pos_s_0" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS S1" prop="pos_s_1">
                            <el-input v-model="data.form.pos_s_1" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS S2" prop="pos_s_2">
                            <el-input v-model="data.form.pos_s_2" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS S3" prop="pos_s_3">
                            <el-input v-model="data.form.pos_s_3" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS S4" prop="pos_s_4">
                            <el-input v-model="data.form.pos_s_4" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS S5" prop="pos_s_5">
                            <el-input v-model="data.form.pos_s_5" placeholder="请输入标题" />
                        </el-form-item>
                        <el-divider />
                        <el-form-item label="POS R0" prop="pos_r_0">
                            <el-input v-model="data.form.pos_r_0" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS R1" prop="pos_r_1">
                            <el-input v-model="data.form.pos_r_1" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS R2" prop="pos_r_2">
                            <el-input v-model="data.form.pos_r_2" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS R3" prop="pos_r_3">
                            <el-input v-model="data.form.pos_r_3" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS R4" prop="pos_r_4">
                            <el-input v-model="data.form.pos_r_4" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="POS R5" prop="pos_r_5">
                            <el-input v-model="data.form.pos_r_5" placeholder="请输入标题" />
                        </el-form-item>
                    </el-form>
                </page-main>
            </el-col>
            <el-col :span="8">
                <page-main>
                    <el-space direction="vertical" :size="30">
                        <el-image :src="senderPos" />
                        <el-image :src="receiverPos" />
                    </el-space>
                </page-main>
            </el-col>
        </el-row>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
