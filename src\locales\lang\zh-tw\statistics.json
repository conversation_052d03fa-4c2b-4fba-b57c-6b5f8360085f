{"title": "EOD Reports", "fields": {"totalOrders": "Total Orders", "totalPackages": "Total Packages", "productionValue": "Production Value", "note": "Note", "totalWithoutTax": "Income w/o hst", "hst": "HST", "total": "Total Income", "profit": "Profit", "profitRate": "Profit %", "costs": "Costs", "salaries": "Salaries"}, "operations": {"start": "Start", "next": "Next", "previous": "Previous", "generateReport": "Generate Report"}, "drivers": {"temporaryDrivers": "Temporary Drivers", "fulltimeDrivers": "Fulltime Drivers", "totalDrivers": "Total Drivers", "ordersPerDriver": "Orders/Driver"}, "salaries": {"title": "Salaries", "fields": {"baseSalary": "Base Salary", "fuelAllowance": "Fuel Allowance", "services": "Services", "bonus": "Bonus", "extraBonus": "Extra Bonus", "compensation": "Compensation", "deduction": "Deduction", "tip": "Tip", "note": "Note", "totalWithoutTax": "Total w/o hst", "hst": "Salaries HST", "total": "Total"}}, "payment": {"title": "Payments", "fields": {"paymentMethods": "Payment methods", "refundMethods": "Refund  methods", "ap": "AP", "cc": "CC", "cr": "Credits", "netIncome": "Net Income", "commission": "Commission"}}, "delivery": {"title": "Deliveries", "fields": {"totalAmount": "Total amount", "groupCount": "Groups count", "averagePU": "Average PU"}}}