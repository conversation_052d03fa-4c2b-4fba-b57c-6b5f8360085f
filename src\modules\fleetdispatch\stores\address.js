import { defineStore } from 'pinia'
import { WAREHOUSE } from '../config'
import { useDriverStore } from './driver'

/**
 * 地址存储 - 管理仓库和司机家庭地址
 */
export const useAddressStore = defineStore('address', {
    state: () => ({
        // 仓库地址信息
        warehouse: {
            id: 'CA|CP|B|10440098', // 仓库地址ID
            address: '1-110 Torbay Rd',
            city: 'Markham',
            province: 'ON',
            postal_code: 'L3R 1G6',
            country: 'Canada',
            // 注意：这里存储的是 [lat, lng] 格式，与 Driver 模型保持一致
            coordinates: [43.8188751, -79.3452168],
            // 原始坐标，用于调试
            original_coordinates: [-79.3452168, 43.8188751],
            type: 'warehouse'
        },

        // 司机家庭地址缓存 - 从 driverStore 中提取
        driverAddresses: new Map(),

        // 地址缓存 - 用于快速查找
        addressCache: new Map()
    }),

    getters: {
        /**
         * 获取仓库地址
         */
        getWarehouse: (state) => {
            return state.warehouse
        },

        /**
         * 获取所有地址（仓库 + 司机家庭）
         */
        getAllAddresses: (state) => {
            const addresses = [state.warehouse]

            // 添加司机家庭地址
            state.driverAddresses.forEach(address => {
                addresses.push(address)
            })

            return addresses
        },

        /**
         * 根据地址ID获取地址
         */
        getAddressById: (state) => (addressId) => {
            // 处理复杂地址对象
            if (addressId && typeof addressId === 'object') {
                console.log('AddressStore: 处理复杂地址对象:', addressId);

                // 如果对象有 Id 属性，尝试使用它
                if (addressId.Id) {
                    console.log(`AddressStore: 使用复杂地址对象的 Id 属性: ${addressId.Id}`);

                    // 检查是否是仓库地址
                    if (state.warehouse.id === addressId.Id) {
                        console.log('AddressStore: 匹配到仓库地址');
                        return state.warehouse;
                    }

                    // 检查司机地址
                    if (state.driverAddresses.has(addressId.Id)) {
                        console.log(`AddressStore: 匹配到司机地址: ${addressId.Id}`);
                        return state.driverAddresses.get(addressId.Id);
                    }

                    // 检查缓存
                    if (state.addressCache.has(addressId.Id)) {
                        console.log(`AddressStore: 从缓存中匹配到地址: ${addressId.Id}`);
                        return state.addressCache.get(addressId.Id);
                    }
                }

                // 如果对象有坐标信息，创建一个临时地址对象
                if (addressId.Position && addressId.Position.Latitude && addressId.Position.Longitude) {
                    console.log('AddressStore: 从复杂地址对象中提取坐标信息');

                    const tempAddress = {
                        id: addressId.Id || 'temp_address',
                        coordinates: [
                            parseFloat(addressId.Position.Latitude),
                            parseFloat(addressId.Position.Longitude)
                        ],
                        original_coordinates: [
                            parseFloat(addressId.Position.Longitude),
                            parseFloat(addressId.Position.Latitude)
                        ],
                        type: 'temp_address'
                    };

                    // 添加到缓存
                    state.addressCache.set(tempAddress.id, tempAddress);
                    console.log('AddressStore: 创建临时地址对象:', tempAddress);

                    return tempAddress;
                }

                console.log('AddressStore: 无法处理复杂地址对象');
                return null;
            }

            // 处理字符串地址ID

            // 首先检查缓存
            if (state.addressCache.has(addressId)) {
                return state.addressCache.get(addressId);
            }

            // 检查是否是仓库地址
            if (state.warehouse.id === addressId) {
                return state.warehouse;
            }

            // 检查司机地址
            if (state.driverAddresses.has(addressId)) {
                return state.driverAddresses.get(addressId);
            }

            return null;
        },

        /**
         * 根据地址ID获取坐标 [lat, lng]
         */
        getCoordinatesByAddressId: (state) => (addressId) => {
            const address = state.getAddressById(addressId)
            return address ? address.coordinates : null
        }
    },

    actions: {
        /**
         * 初始化地址存储
         * 从配置和司机存储中加载地址
         */
        initialize() {
            console.log('AddressStore: 初始化地址存储')

            // 更新仓库信息（如果配置中有更新）
            if (WAREHOUSE) {
                // 确保坐标格式正确 [lat, lng]
                const warehouseCoordinates = [
                    WAREHOUSE.latitude || 43.8188751,
                    WAREHOUSE.longitude || -79.3452168
                ]

                // 更新仓库信息
                this.warehouse = {
                    ...this.warehouse,
                    coordinates: warehouseCoordinates,
                    original_coordinates: [WAREHOUSE.longitude || -79.3452168, WAREHOUSE.latitude || 43.8188751],
                    address: WAREHOUSE.address || this.warehouse.address,
                    city: WAREHOUSE.city || this.warehouse.city,
                    province: WAREHOUSE.province || this.warehouse.province,
                    postal_code: WAREHOUSE.postalCode || this.warehouse.postal_code
                }
            }

            // 添加仓库地址到缓存
            this.addressCache.set(this.warehouse.id, this.warehouse)

            // 加载司机地址
            this.loadDriverAddresses()

            console.log('AddressStore: 初始化完成，仓库地址:', this.warehouse)
        },

        /**
         * 从司机存储中加载司机地址
         */
        loadDriverAddresses() {
            const driverStore = useDriverStore()
            const drivers = driverStore.drivers

            if (!drivers || drivers.length === 0) {
                console.log('AddressStore: 没有可用的司机数据')
                return
            }

            console.log(`AddressStore: 加载 ${drivers.length} 个司机的地址信息`)

            // 清除现有司机地址
            this.driverAddresses.clear()

            // 处理每个司机的地址
            drivers.forEach(driver => {
                if (driver.address_id && driver.address_lng_lat) {
                    const driverAddress = {
                        id: driver.address_id,
                        driver_id: driver.id,
                        driver_name: driver.name,
                        driver_color: driver.color,
                        coordinates: driver.address_lng_lat, // 已经是 [lat, lng] 格式
                        original_coordinates: driver.original_address_lng_lat, // 原始 [lng, lat] 格式
                        address_label: driver.address_label || null,
                        type: 'driver_home'
                    }

                    // 添加到司机地址集合
                    this.driverAddresses.set(driver.address_id, driverAddress)

                    // 添加到地址缓存
                    this.addressCache.set(driver.address_id, driverAddress)
                }
            })

            console.log(`AddressStore: 已加载 ${this.driverAddresses.size} 个司机地址`)
        },

        /**
         * 更新司机地址
         * 当司机数据更新时调用
         */
        updateDriverAddresses() {
            this.loadDriverAddresses()
        }
    }
})
