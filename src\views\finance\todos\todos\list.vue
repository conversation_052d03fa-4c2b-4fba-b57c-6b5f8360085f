<script setup name="FinanceTodosTodosList">
    import { Delete } from '@element-plus/icons-vue'
    import { usePagination } from '@/utils/composables'
    import FormMode from './components/FormMode/index.vue'
    import { alterTodoStatus, deleteTodos, executeTodos, getTodos } from '@/api/modules/todos'
    import { todoStatusStyles as statusStyle } from '../constants'
    import { useI18n } from 'vue-i18n'
    import { CloseBold, Watch, Flag } from '@element-plus/icons-vue'

    import useTodosStore from '@/store/modules/todos'
    const todosStore = useTodosStore()

    const { t } = useI18n()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const route = useRoute()

    const data = ref({
        loading: false,
        formModeProps: {
            visible: false,
            id: ''
        },
        // 搜索
        search: {
            module__name__icontains: null,
            notes__icontains: null,
        },
        // 批量操作
        batch: {
            enable: false,
            selectionDataList: []
        },
        // 列表数据
        dataList: [],
        opName: route.query.opName,
        moduleName: route.query.module
    })
    const searchBarCollapsed = ref(0)

    // Filters
    function resetFilters() {
        data.value.search =
        {
            module__name__icontains: null,
            notes__icontains: null,
        }
        currentChange()
    }


    watch(() => route.query.module, (val) => {
        if (val) {
            getDataList();
        }
    }, { immediate: true })


    watch(
        () => todosStore[data.value.moduleName || route.query.module].length,
        (val) => { if (val > 0) getDataList(); }
    );

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                opName: data.value.opName,
                moduleName: data.value.moduleName || route.query.module,
                filters: JSON.stringify(data.value.search)
            }
        )
        getTodos(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.todo_list
            pagination.value.total = res.data.total
            todosStore[data.value.moduleName || route.query.module].length = 0;
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        data.value.formModeProps.id = ''
        data.value.formModeProps.visible = true
    }

    function onEdit(row) {
        data.value.formModeProps.id = row.id
        data.value.formModeProps.visible = true
        row.is_new = false
    }

    function onExecute(row) {
        data.value.loading = true
        let params = {
            ids: [row.id],
        }
        executeTodos(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
            data.value.loading = false
        })
    }

    function onBatchExecute() {
        data.value.loading = true
        let params = {
            ids: data.value.batch.selectionDataList.map(x => x.id),
        }
        executeTodos(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
            data.value.loading = false
        })
    }

    function onAlterStatus(row, status) {
        data.value.loading = true
        let params = {
            id: row.id,
            status: status
        }
        alterTodoStatus(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
            data.value.loading = false
        })
    }

    function onBatchAlterStatus(status) {
        data.value.loading = true
        let params = {
            id: data.value.batch.selectionDataList.map(x => x.id),
            status: status
        }
        alterTodoStatus(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
            data.value.loading = false
        })

    }

    function onDel(row) {
        ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.module.name }), t('dialog.titles.confirmation')).then(() => {
            data.value.loading = true
            let params = { id: JSON.stringify([row.id]) }
            deleteTodos(params).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
                data.value.loading = false
            })
        }).catch(() => { })
    }

    function onBatchDel() {
        ElMessageBox.confirm(t('dialog.messages.batchDeletion', { module: t('todo.title') }), t('dialog.titles.confirmation')).then(() => {
            data.value.loading = true

            let ids = data.value.batch.selectionDataList.map(x => x.id)
            let params = {
                id: JSON.stringify(ids)
            }
            deleteTodos(params).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
                data.value.loading = false
            })
        }).catch(() => {
            data.value.loading = false
        })
    }


    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (['voided', 'expired'].includes(row.status)) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-header :title="$t('todo.list')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('todo.fields.opName')">
                                        <el-input v-model="data.search.module__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('todo.fields.opName') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('todo.fields.notes')">
                                        <el-input v-model="data.search.notes__icontains"
                                            :placeholder="$t('placeholder', { field: $t('todo.fields.notes') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button size="default" type="success" @click="onBatchExecute">
                        {{ $t('operations.batch', { op: $t('todo.operations.execute') }) }}
                    </el-button>
                    <el-button-group>
                        <el-button size="default" type="danger" @click="onBatchAlterStatus('voided')" plain>
                            {{ $t('operations.batch', { op: $t('todo.operations.void') }) }}
                        </el-button>

                        <el-button size="default" type="warning" plain @click="onBatchAlterStatus('pending')">
                            {{ $t('operations.batch', { op: $t('todo.operations.pend') }) }}
                        </el-button>
                        <el-button size="default" type="success" plain @click="onBatchAlterStatus('scheduled')">
                            {{ $t('operations.batch', { op: $t('todo.operations.schedule') }) }}
                        </el-button>
                    </el-button-group>
                    <el-button size="default" type="danger" @click="onBatchDel">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>

                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column type="index" align="center" fixed />
                <el-table-column prop="module.name" :label="$t('todo.fields.opName')" width="100">
                    <template #default="scope">
                        <el-space>
                            {{ scope.row.module.name }}
                            <span v-if="scope.row.is_new" style="color: #f56c6c;">●</span>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="notes" :label="$t('todo.fields.notes')" />
                <el-table-column prop="status" :label="$t('fields.status')" align="center" width="130">
                    <template #default="scope">
                        <el-tag :type="statusStyle[scope.row.status]" round size="small">
                            •&ThickSpace;{{ $t('todo.selections.status.' + scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="right" fixed="right">
                    <template #default="scope">
                        <el-space :size="2">

                            <el-tooltip :content="$t('todo.operations.execute')" placement="top-start">
                                <span>
                                    <el-button type="success" v-if="scope.row.can_execute" circle size="small"
                                        :icon="Flag" @click="onExecute(scope.row)" />
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('todo.operations.void')" placement="top-start">
                                <span>
                                    <el-button type="danger" plain v-if="scope.row.can_void" circle size="small"
                                        :icon="CloseBold" @click="onAlterStatus(scope.row, 'voided')" />
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('delivery.orders.operations.pend')" placement="top-start">
                                <span>
                                    <el-button type="warning" plain v-if="scope.row.can_pend" circle size="small"
                                        @click="onAlterStatus(scope.row, 'pending')">
                                        <svg-icon name="material-symbols:pause" />
                                    </el-button>
                                </span>
                            </el-tooltip>
                            <el-tooltip :content="$t('todo.operations.schedule')" placement="top-start">
                                <span>
                                    <el-button type="success" plain v-if="scope.row.can_schedule" circle size="small"
                                        :icon="Watch" @click="onAlterStatus(scope.row, 'scheduled')" />
                                </span>
                            </el-tooltip>

                            <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                                <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                            </el-tooltip>
                        </el-space>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" @success="getDataList" />
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: var(--g-unavailable-color);
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>
