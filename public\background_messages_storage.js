window.onload = () => {
    console.log('Window Loaded')
    // Hold an instance of a db object for us to store the IndexedDB data in
    let db;
    // Let us open our database
    const DBOpenRequest = window.indexedDB.open('fcmBackgroundMessages', 3);
    // Register two event handlers to act on the database being opened successfully, or not
    DBOpenRequest.onerror = (event) => {
        console.error(`DB created failed`);
    };
    DBOpenRequest.onsuccess = (event) => {
        // Store the result of opening the database in the db variable. This is used a lot below
        db = event.target.result;
        console.log(`DB created 2`);
    }
    // This event handles the event whereby a new version of the database needs to be created
    // Either one has not been created before, or a new version number has been submitted via the
    // window.indexedDB.open line above
    //it is only implemented in recent browsers
    DBOpenRequest.onupgradeneeded = (event) => {
        db = event.target.result;
        db.onerror = (event) => {
            console.error(`Database error: ${event.target.errorCode}`);
        };

        // Create an objectStore for this database
        const objectStore = db.createObjectStore('fcmBackgroundMessages', { keyPath: 'id' });

        // Define what data items the objectStore will contain
        objectStore.createIndex('type', 'type', { unique: false });
        objectStore.createIndex('from_token', 'from_token', { unique: false });
        objectStore.createIndex('user_id', 'user_id', { unique: false });
        objectStore.createIndex('user_avatar', 'user_avatar', { unique: false });
        objectStore.createIndex('user_name', 'user_name', { unique: false });
        objectStore.createIndex('content', 'content', { unique: false });
        objectStore.createIndex('created_at', 'created_at', { unique: false });
        console.log('Object store created.');
    };
}
