{"title": "EOD Reports", "fields": {"costs": "Costs", "count": "Count", "hst": "HST", "income": "Income", "note": "Note", "orders": "Orders", "packages": "Packages", "payable": "Payable", "preTax": "Pre-tax", "profit": "Profit", "profitRate": "Profit %", "salaries": "Salaries", "total": "Total", "cash": "Cash", "refund": "Refund"}, "operations": {"start": "Start", "next": "Next", "previous": "Previous", "generateReport": "Generate Report"}, "drivers": {"temporaryDrivers": "Temporary Drivers", "fulltimeDrivers": "Fulltime Drivers", "totalDrivers": "Total Drivers", "ordersPerDriver": "Orders/Driver"}, "salary": {"title": "Salaries", "fields": {"baseSalary": "Base Salary", "fuelAllowance": "Fuel Allowance", "services": "Services", "bonus": "Bonus", "extraBonus": "Extra Bonus", "compensation": "Compensation", "deduction": "Deduction", "tip": "Tip", "note": "Note", "hours": "Hours", "salaryPerHour": "AVG / Hour", "travelKm": "Travel KM", "fuelAllowancePerKm": "AVG / KM", "employeeCount": "Employees", "salaryPerEmployee": "AVG / Employee"}}, "payment": {"title": "Payments", "fields": {"paymentMethods": "Payment methods", "refundMethods": "Refund  methods", "ap": "AP", "cc": "CC", "cr": "Credits", "netIncome": "Net Income", "commission": "Commission", "cashTotal": "Cash Total", "cashCount": "Cash Count", "cashRefund": "Cash Refund", "creditTotal": "Credit Total", "creditCount": "Credit Count", "creditRefund": "Credit Refund"}}, "delivery": {"title": "Deliveries", "fields": {"totalAmount": "Total amount", "groupCount": "Groups count", "averagePU": "Average PU", "paidAmount": "<PERSON><PERSON>", "unpaidAmount": "Unpaid Amount", "refundAmount": "Refund Amount", "tip": "Tip"}}}