import { defineStore } from 'pinia'
import { DriverService } from '../services/DriverService'

export const useDriverStore = defineStore('driver', {
    state: () => ({
        // 创建DriverService实例
        driverService: new DriverService(),
        // 添加司机位置集合
        driverPositions: new Map()
    }),

    getters: {
        // 根据ID获取司机
        getDriverById: state => id => {
            return state.driverService.getDriverById(id)
        },

        // 获取所有司机
        drivers: state => {
            return state.driverService.getDrivers()
        },

        // 获取所有司机数组
        allDrivers: state => {
            return state.driverService.allDrivers
        },

        // 获取选中的司机
        selectedDriver: state => {
            return state.driverService.selectedDriver
        },

        // 获取所有带位置信息的司机
        driversWithLocation: state => {
            return state.driverService.getDriversWithLocation()
        },

        // 获取司机位置信息
        getDriverPosition: state => id => {
            return state.driverPositions.get(id) || null
        },

        // 获取所有司机位置
        allDriverPositions: state => {
            return Array.from(state.driverPositions.values())
        }
    },

    actions: {
        // 加载司机数据（如果还未加载）
        async loadDrivers(params = {}) {
            // 检查是否已有司机数据
            if (this.allDrivers && this.allDrivers.length > 0 && !params.forceRefresh) {
                console.log('DriverStore: 使用缓存的司机数据');
                return this.allDrivers;
            }

            // 没有数据则加载
            return await this.fetchDrivers(params);
        },

        // 获取司机数据
        async fetchDrivers(params = {}) {
            try {
                console.log('DriverStore: 获取司机列表，参数:', params);

                // 构建查询参数
                const queryParams = { ...params };

                // 如果有 timeStore.selectedShift，添加 shift_id 参数
                if (params.shift && params.shift.id) {
                    queryParams.shift_id = params.shift.id;
                    console.log(`DriverStore: 使用 shift_id=${queryParams.shift_id} 获取司机`);
                }

                const drivers = await this.driverService.fetchDrivers(queryParams);
                console.log(`DriverStore: 成功获取 ${drivers.length} 个司机`);
                return drivers;
            } catch (error) {
                console.error('DriverStore: 获取司机数据失败:', error);
                throw error;
            }
        },

        // 更新司机状态
        updateDriverStatuses(activeDriverIds) {
            return this.driverService.updateDriverStatuses(activeDriverIds)
        },

        // 更新司机位置
        updateDriverLocation(driverId, locationData) {
            // 保存到位置集合
            this.driverPositions.set(driverId, {
                id: driverId,
                ...locationData,
                lastUpdated: new Date()
            })

            // 同时更新到服务层
            return this.driverService.updateDriverLocation(driverId, locationData)
        },

        // 批量更新多个司机位置
        updateMultipleDriverLocations(driversLocations) {
            if (!Array.isArray(driversLocations)) {
                console.warn('批量更新司机位置失败: 参数不是数组')
                return false
            }

            let updateCount = 0

            driversLocations.forEach(driverData => {
                const driverId = driverData.driver?.id

                if (!driverId) {
                    console.warn('批量更新司机位置失败: 缺少司机ID', driverData)
                    return
                }

                // 获取所有位置
                const positions = driverData.positions || []

                // 如果没有位置数据，跳过
                if (positions.length === 0) {
                    return
                }

                // 取最新的位置数据（通常是数组中的第一个）
                const latestPosition = positions[0]

                // 保存到位置集合
                this.driverPositions.set(driverId, {
                    id: driverId,
                    latitude: latestPosition.latitude,
                    longitude: latestPosition.longitude,
                    timestamp: latestPosition.timestamp,
                    speed: latestPosition.speed,
                    accuracy: latestPosition.accuracy,
                    lastUpdated: new Date()
                })

                // 更新司机位置
                const success = this.driverService.updateDriverLocation(driverId, {
                    latitude: latestPosition.latitude,
                    longitude: latestPosition.longitude,
                    timestamp: latestPosition.timestamp,
                    speed: latestPosition.speed,
                    accuracy: latestPosition.accuracy
                })

                if (success) {
                    updateCount++
                }
            })

            console.log(`批量更新司机位置完成: ${updateCount}/${driversLocations.length} 个司机位置已更新`)
            return updateCount > 0
        },

        // 清理过期的司机位置数据
        cleanupDriverLocations() {
            // 清理本地位置集合
            const fiveMinutesAgo = new Date()
            fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5)

            // 删除过期位置数据
            this.driverPositions.forEach((position, driverId) => {
                if (position.lastUpdated < fiveMinutesAgo) {
                    this.driverPositions.delete(driverId)
                }
            })

            // 同时清理服务层数据
            this.driverService.cleanupDriverLocations()
        },

        // 设置选中的司机
        setSelectedDriver(driver) {
            return this.driverService.setSelectedDriver(driver)
        },

        // 清除选中的司机
        clearSelectedDriver() {
            this.driverService.clearSelectedDriver()
        },

        // 选择司机
        selectDriver(driver) {
            return this.driverService.selectDriver(driver)
        },

        // 为司机分配路线
        async assignRouteToDriver(driverId, routeId) {
            return this.driverService.assignRouteToDriver(driverId, routeId)
        },

        // 取消司机的路线分配
        async unassignDriver(driverId) {
            return this.driverService.unassignDriver(driverId)
        },

        // 直接更新司机数据（用于切换全部/在线模式）
        updateDrivers(drivers) {
            return this.driverService.updateDrivers(drivers)
        }
    }
})