<script setup name="FinanceEodList">
import { Delete } from '@element-plus/icons-vue'
import { usePagination } from '@/utils/composables'
import { getReports, deleteReport } from '@/api/modules/statistics'
import { currencyFormatter, percentageFormatter } from '@/utils/formatter'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        date__range: [],
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        date__range: [],
    }
    currentChange()
}


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getReports(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.report_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'financeEOD'
    })
}

function onEdit(row) {
    console.log(row.id)
    router.push({
        name: 'financeEODReportDetail',
        query: {
            id: row.id
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
        deleteReport({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}


const getSummaries = ({ columns, data }) => {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = 'Total'
            return
        } else if (index < 14) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)
            if ([1, 2].includes(index)) {
                sums[index] = sum
            }
            else {
                sums[index] = currencyFormatter(null, null, sum, null)
            }

        }
    })
    sums[columns.length - 2] = (Number(sums[columns.length - 3]) / Number(sums[3]?.replace(',', ''))).toFixed(2) + '%'
    return sums
}


</script>

<template>
    <div>
        <page-header :title="$t('statistics.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.date')">
                                        <el-date-picker v-model="data.search.date__range" type="daterange"
                                            range-separator="~" start-placeholder="Start date"
                                            end-placeholder="End date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    Reset
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    Filter
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe border
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit" show-summary
                :summary-method="getSummaries" @sort-change="sortChange">
                <el-table-column prop="date" :label="$t('fields.date')" fixed sortable align="center" />
                <el-table-column :label="$t('statistics.fields.count')" align="center">
                    <el-table-column prop="total_orders" :label="$t('statistics.fields.orders')" align="center"
                        sortable />
                    <el-table-column prop="total_packages" :label="$t('statistics.fields.packages')" align="center"
                        class-name="bold" sortable />
                </el-table-column>
                <el-table-column :label="$t('statistics.fields.income')" align="center">
                    <el-table-column :label="$t('statistics.fields.preTax')" align="center">
                        <el-table-column prop="income" label="①" :formatter="currencyFormatter" align="center" />
                    </el-table-column>
                    <el-table-column align="center" :label="$t('statistics.fields.hst')">
                        <el-table-column prop="hst" :formatter="currencyFormatter" label="②" align="center" />
                    </el-table-column>
                    <el-table-column align="center" :label="$t('statistics.fields.total')" prop="total_income" sortable>
                        <el-table-column prop="total_income" class-name="bold" label="③ = ① + ②"
                            :formatter="currencyFormatter" align="center" />
                    </el-table-column>

                </el-table-column>
                <el-table-column :label="$t('statistics.fields.payable')" align="center">
                    <el-table-column :label="$t('statistics.fields.costs')" align="center">
                        <el-table-column :label="$t('statistics.fields.preTax')" align="center" prop="cost" sortable>
                            <el-table-column prop="cost" label="④" :formatter="currencyFormatter" align="center" />
                        </el-table-column>
                        <el-table-column :label="$t('statistics.fields.hst')" align="center">
                            <el-table-column prop="cost_hst" label="⑤" align="center" :formatter="currencyFormatter" />
                        </el-table-column>
                    </el-table-column>
                    <el-table-column :label="$t('statistics.fields.salaries')" align="center">
                        <el-table-column :label="$t('statistics.fields.preTax')" align="center" prop="salaries"
                            sortable>
                            <el-table-column prop="salaries" label="⑥" align="center" :formatter="currencyFormatter" />
                        </el-table-column>
                        <el-table-column :label="$t('statistics.fields.hst')" align="center" prop="salary_hst" sortable>
                            <el-table-column prop="salary_hst" label="⑦" align="center"
                                :formatter="currencyFormatter" />
                        </el-table-column>
                        <el-table-column :label="$t('statistics.salary.fields.tip')" align="center" prop="tip" sortable>
                            <el-table-column prop="tip" label="⑧" align="center" :formatter="currencyFormatter" />
                        </el-table-column>
                    </el-table-column>
                    <el-table-column :label="$t('statistics.fields.hst')" align="center" prop="hst_payable" sortable>
                        <el-table-column prop="hst_payable" label="⑨ = ② - ⑤ - ⑦" align="center"
                            :formatter="currencyFormatter" />
                    </el-table-column>

                    <el-table-column :label="$t('statistics.fields.total')" align="center" prop="total_payable"
                        sortable>
                        <el-table-column prop="total_payable" label="①⓪ = ④ + ⑥ + ⑧ + ⑨" align="center"
                            :formatter="currencyFormatter" class-name="bold" />
                    </el-table-column>

                </el-table-column>
                <el-table-column :label="$t('statistics.fields.profit')" align="center" prop="profit" sortable>
                    <el-table-column prop="profit" label="①① = ① - ④ - ⑥ - ⑧" :formatter="currencyFormatter"
                        align="center" class-name="bold" />
                </el-table-column>
                <el-table-column :label="$t('statistics.fields.profitRate')" align="center" prop="profit_rate" sortable>
                    <el-table-column prop="profit_rate" label="①② = ①① ÷ ①" align="center"
                        :formatter="percentageFormatter" />
                </el-table-column>
                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.75em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }

    .bold {
        font-weight: bolder;
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>