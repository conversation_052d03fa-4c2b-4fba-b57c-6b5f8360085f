<script setup>
    import { currencyFormatter, orderNoFormatter, phoneNumberFormatter } from '@/utils/formatter'
    import { removeAssignments, getRoutes } from '@/api/modules/dispatcher'
    import { orderStatusStyles } from '@/utils/constants'
    import { CloseBold } from '@element-plus/icons-vue'

    const props = defineProps({
        id: {
            type: String,
            default: null,
        },
    })

    const data = ref({
        loading: false,
        dataList: []
    })

    onMounted(() => {
        getData();
    })

    function getData() {
        let params = {
            id: props.id,
        }
        data.value.loading = true;
        getRoutes(params).then(res => {
            data.value.dataList = res.data.order_list;
            data.value.loading = false;
        })
    }


    function onRemoveAssignment(row, all) {
        let params = {
            id: row.id,

        }
        if (!all) {
            params.type = row.type
        }
        removeAssignments(params).then(res => {
            if (res.data.errCode == 365) {
                getData()
            }
        })
    }



    function packageIndexClass(row, index) {
        if (row.sorted_packages.length === 0) {
            return 'uncompleted';
        }

        const sortedPackage = row.sorted_packages.find(e => e.index === index);

        if (!sortedPackage) {
            return 'uncompleted';
        }

        if (row.type === 'pickup' || sortedPackage.is_departed) {
            return 'completed';
        }

        if (row.type === 'delivery') {
            return 'unsorted';
        }

        return '';
    }
    const getSummaries = (param) => {
        const { columns, data } = param
        const sums = []
        columns.forEach((column, index) => {

            if (index === 0) {
                const values = data.map((item) => Number(item.travel_time))
                sums[index] = h('div', { style: { 'font-weight': 'bolder', 'font-size': '20px', } }, [
                    values.filter((e) => e > 0).length,
                ])
                return
            } else if ([2, 7, 8].includes(index)) {
                const values = data.map((item) => Number(item[column.property]))
                if (!values.every((value) => Number.isNaN(value))) {
                    var sum = values.reduce((prev, curr) => {
                        const value = Number(curr)
                        if (!Number.isNaN(value)) {
                            return prev + curr
                        } else {
                            return prev
                        }
                    }, 0)
                    if (index === 7) {
                        sums[index] = Math.round(sum / 60)
                    } else if (index === 8) {
                        sums[index] = (sum / 1000).toFixed(2)
                    } else {
                        sums[index] = sum
                    }

                } else {
                    sums[index] = 'N/A'
                }

            }

        })

        return sums
    }

</script>
<template>
    <el-table ref="table" v-loading="data.loading" :data="data.dataList" border stripe style=" width: 100%;"
        highlight-current-row :summary-method="getSummaries" show-summary>
        <el-table-column :label="$t('dispatcher.orders.fields.stop')" prop="stop_no" width="55" align="center" fixed
            :resizable="false">
            <template #default="scope">
                <el-tag type="success" style="width: 30px;" :effect="scope.row.type == 'pickup' ? 'plain' : 'dark'">{{
                    scope.row.stop_no
                }}</el-tag>
            </template>

        </el-table-column>
        <el-table-column prop="no" :label="$t('fields.no')" width="110" :resizable="false">
            <template #default="scope">
                {{ orderNoFormatter(_, _, scope.row.no) }}
            </template>
        </el-table-column>
        <el-table-column prop="quantity" :label="$t('fields.quantity')" width="150" align="center">
            <template #default="scope">
                <div style="display: flex; justify-content: center;">
                    <div class="package-index" :class="packageIndexClass(scope.row, i)"
                        v-for="i in Array.from(Array(scope.row.quantity)).map((e, i) => i + 1)">
                        {{ i }}
                    </div>
                </div>

            </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('delivery.orders.fields.status')" :resizable="false" align="center"
            width="110">

            <template #default="scope">
                <el-tag :type="orderStatusStyles[scope.row.status]" round size="small">
                    {{ $t(`delivery.orders.selections.status.${scope.row.status}`) }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('fields.name')" show-overflow-tooltip />
        <el-table-column prop="address" :label="$t('dispatcher.orders.fields.address')" show-overflow-tooltip />
        <el-table-column prop="eta" :label="$t('dispatcher.orders.fields.eta')" width="150" :resizable="false" />
        <el-table-column prop="travel_time" :label="$t('dispatcher.orders.fields.travelTime')" width="100"
            :resizable="false" align="center">
            <template #default="scope">
                {{ scope.row.travel_time ? Math.round(scope.row.travel_time / 60) : '-' }}
            </template>
        </el-table-column>
        <el-table-column prop="distance" :label="$t('dispatcher.orders.fields.distance')" width="100" :resizable="false"
            align="center"><template #default="scope">
                {{ scope.row.distance > 0 ? (scope.row.distance / 1000).toFixed(2) : '-' }}
            </template></el-table-column>
        <el-table-column prop="dispatched_at" :label="$t('dispatcher.orders.fields.dispatchedAt')" width="160"
            :resizable="false" />
        <el-table-column prop="completed_at" :label="$t('dispatcher.orders.fields.completedAt')" width="160"
            :resizable="false" />
        <el-table-column :label="$t('fields.operations')" align="center" fixed="right" width="50">
            <template #default="scope">
                <el-tooltip class="box-item" :content="$t('dispatcher.orders.operations.remove')" placement="top-start"
                    v-if="!scope.row.completed_at">
                    <el-button type="danger" :icon="CloseBold" circle size="small"
                        @click="onRemoveAssignment(scope.row)" />
                </el-tooltip>
            </template>
        </el-table-column>
    </el-table>
</template>
<style lang="scss">
    .el-table {

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .package-index {
        width: 20px;
        text-align: center;
        font-weight: bolder;
    }

    .completed {
        background-color: #79bbff;
        color: white;
    }

    .unsorted {
        background-color: #E6A23C;
        color: white;
    }

    .uncompleted {
        background-color: #F56C6C;
        color: white;
    }

</style>