<script setup name="FinanceCreditsList">
import { getAllUserCreditLogs } from '@/api/modules/payment'
import { usePagination } from '@/utils/composables'
import { currencyFormatter } from '@/utils/formatter';

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const data = ref({
    loading: false,
    // 搜索
    search: {
        name__icontains: null,
        delta__lte: null,
        delta__gte: null,
        type: null,
        record__contains: null,
        created_at__lte: null,
        created_at__gte: null,
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)
const typeList = ['consumed', 'returned', 'promoted', 'directed']

// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
        name__icontains: null,
        delta__lte: null,
        delta__gte: null,
        type: null,
        record__contains: null,
        created_at__lte: null,
        created_at__gte: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getAllUserCreditLogs(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.log_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

</script>

<template>
    <div>
        <page-header title="用户积分" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>

                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('fields.createdAtMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('fields.createdAtMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>

                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('user.log.delta')">
                                        <el-input-number v-model="data.search.delta__gte" :min="0"
                                            :placeholder="$t('user.log.deltaMin')" controls-position="right" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.delta__lte" :min="0"
                                            :placeholder="$t('user.log.deltaMax')" controls-position="right" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.log.type')">
                                        <el-select v-model="data.search.type"
                                            :placeholder="$t('statSelectHolder', { field: $t('user.log.type') })"
                                            size="large" clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in typeList" :key="item"
                                                :label="$t(`user.log.creditType.${item}`)" :value="item" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="index" width="50" fixed />
                <el-table-column prop="user.name" :label="$t('fields.user')" />
                <el-table-column prop="delta" :label="$t('user.log.delta')" align="right">
                    <template #default="scope">
                        <el-text :type="scope.row.type == 'consumed' ? 'success' : 'danger'" tag="b">
                            {{ `${scope.row.type == 'consumed' ? '-' : '+'} ${currencyFormatter(_, _, scope.row.delta)}` }}
                        </el-text>
                    </template>

                </el-table-column>
                <el-table-column prop="type" :label="$t('user.log.type')" align="center">
                    <template #default="scope">
                        {{ $t(`user.log.creditType.${scope.row.type}`) }}
                    </template>
                </el-table-column>
                <el-table-column prop="record" :label="$t('user.log.record')" show-overflow-tooltip min-width="300">
                    <template #default="scope">
                        {{ scope.row.record.join(', ') }}
                    </template>
                </el-table-column>
                <el-table-column prop="operator" :label="$t('fields.operator')" align="center" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

}
</style>
