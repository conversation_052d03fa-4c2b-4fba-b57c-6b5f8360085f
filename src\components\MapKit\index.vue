/* stylelint-disable rule-empty-line-before */
<script setup name="MapKit">
const props = defineProps({
    jwt: null,
    driverPositions: []

})

const emit = defineEmits(['update:lnglat'])

const search = ref('')

const realHeight = computed(() => {
    return typeof props.height == 'string' ? props.height : `${props.height}px`
})

let map;

onMounted(() => {
    let script = document.createElement('script')
    script.src = `https://cdn.apple-mapkit.com/mk/5.x.x/mapkit.core.js`
    script.setAttribute('crossorigin', 'anonymous')
    script.setAttribute('async', 'true')
    script.setAttribute('data-callback', 'initMapKit')
    script.setAttribute('data-libraries', 'map,annotations,services')
    script.setAttribute('data-initial-token', '')
    document.head.appendChild(script)
    script.onload = () => {
        init()
    }
})

const setupMapKitJs = async () => {
    if (!window.mapkit || window.mapkit.loadedLibraries.length === 0) {
        await new Promise(resolve => { window.initMapKit = resolve });
        delete window.initMapKit;
    }
    // TODO: For production use, the JWT should not be hard-coded into JS.
    mapkit.init({
        authorizationCallback: done => { done(props.jwt); }
    });
};

async function init() {
    await setupMapKitJs()
    map = new mapkit.Map("map");
    addMarker();

    const cupertino = new mapkit.CoordinateRegion(
        new mapkit.Coordinate(37.3316850890998, -122.030067374026),
        new mapkit.CoordinateSpan(0.167647972, 0.354985255)
    );
    // Create a map in the element whose ID is "map-container"
    // const map = new mapkit.Map("map-container");
    // map.region = cupertino;
    // const geocoder = new mapkit.Geocoder({ language: "en-US" });

    // Create the "Event" annotation, setting properties in the constructor.
    // const event = new mapkit.Coordinate(37.7831, -122.4041);
    // const eventAnnotation = new mapkit.MarkerAnnotation(event, {
    //     color: "#4eabe9",
    //     title: "Event",
    //     glyphText: "\u{1F37F}" // Popcorn Emoji
    // });

    // // Create the "Work" annotation, setting properties after construction.
    // const work = new mapkit.Coordinate(37.3349, -122.0090);
    // const workAnnotation = new mapkit.MarkerAnnotation(work);
    // workAnnotation.color = "#969696";
    // workAnnotation.title = "Work";
    // workAnnotation.subtitle = "Apple Park";
    // workAnnotation.selected = "true";
    // workAnnotation.glyphText = "\u{F8FF}"; // Apple Symbol

    // // Add and show both annotations on the map
    // map.showItems([eventAnnotation, workAnnotation]);

    // // This will contain the user-set single-tap annotation.
    // let clickAnnotation = null;

    // // Add or move an annotation when a user single-taps an empty space
    // map.addEventListener("single-tap", event => {
    //     if (clickAnnotation) {
    //         map.removeAnnotation(clickAnnotation);
    //     }

    //     // Get the clicked coordinate and add an annotation there
    //     const point = event.pointOnPage;
    //     const coordinate = map.convertPointOnPageToCoordinate(point);

    //     clickAnnotation = new mapkit.MarkerAnnotation(coordinate, {
    //         title: "Loading...",
    //         color: "#c969e0"
    //     });

    //     map.addAnnotation(clickAnnotation);

    //     // Look up the address with the Geocoder's Reverse Lookup Function
    //     geocoder.reverseLookup(coordinate, (error, data) => {
    //         const first = (!error && data.results) ? data.results[0] : null;
    //         clickAnnotation.title = (first && first.name) || "";
    //     });
    // });
    // Landmarks data
    const sanFranciscoLandmarks = [
        {
            coordinate: new mapkit.Coordinate(37.7951315, -122.402986),
            title: "Transamerica Pyramid",
            phone: "******-983-5420",
            url: "http://www.transamericapyramidcenter.com/"
        },
        {
            coordinate: new mapkit.Coordinate(37.7954201, -122.39352),
            title: "Ferry Building",
            phone: "+****************",
            url: "http://www.ferrybuildingmarketplace.com"
        },
        {
            coordinate: new mapkit.Coordinate(37.8083396, -122.415727),
            title: "Fisherman's Wharf",
            phone: "+****************", url: "http://visitfishermanswharf.com"
        },
        {
            coordinate: new mapkit.Coordinate(37.8023553, -122.405742),
            title: "Coit Tower",
            phone: "+****************",
            url: "http://sfrecpark.org/destination/telegraph-hill-pioneer-park/coit-tower/"
        },
        {
            coordinate: new mapkit.Coordinate(37.7552305, -122.452624),
            title: "Sutro Tower",
            phone: "+****************",
            url: "http://www.sutrotower.com"
        },
        {
            coordinate: new mapkit.Coordinate(37.779267, -122.419269),
            title: "City Hall",
            phone: "+****************",
            url: "http://sfgsa.org/index.aspx?page=1085"
        },
        {
            coordinate: new mapkit.Coordinate(37.8184493, -122.478409),
            title: "Golden Gate Bridge",
            phone: "+****************",
            url: "http://www.goldengatebridge.org"
        },
        {
            coordinate: new mapkit.Coordinate(37.7785538, -122.514035),
            title: "Cliff House",
            phone: "+****************",
            url: "http://www.cliffhouse.com/"
        }
    ];
}

watch(() => props.driverPositions, () => { addMarker() }, { deep: true });

function addMarker() {
    // Offset between the callout and the associated annotation marker
    const offset = new DOMPoint(-148, -78);

    // Maps each annotation (key) to the landmark data describing it (value)
    const annotationsToLandmark = new Map();

    // Each annotation will use these functions to present a custom callout
    const landmarkAnnotationCallout = {

        calloutElementForAnnotation: annotation => {
            const landmark = annotationsToLandmark.get(annotation);

            const div = document.createElement("div");
            div.className = "landmark";

            const title = div.appendChild(document.createElement("h1"));
            title.textContent = landmark.title;

            const section = div.appendChild(document.createElement("section"));

            const phone = section.appendChild(document.createElement("p"));
            phone.className = "phone";
            phone.textContent = landmark.phone;

            const link = section.appendChild(document.createElement("p"));
            link.className = "homepage";

            const a = link.appendChild(document.createElement("a"));
            a.href = landmark.url;
            a.textContent = "website";

            return div;
        },

        calloutAnchorOffsetForAnnotation: (annotation, element) => offset,

        calloutAppearanceAnimationForAnnotation: annotation =>
            ".4s cubic-bezier(0.4, 0, 0, 1.5) " +
            "0s 1 normal scale-and-fadein"
    };

    for (const landmark of props.driverPositions) {
        const annotation = new mapkit.MarkerAnnotation(new mapkit.Coordinate(...landmark.current_position), {
            callout: landmarkAnnotationCallout,
            color: "#c969e1",
            title: landmark.name,
            glyphText: landmark.name[0],
            glyphImage: { 1: landmark.avatar }
        });

        annotationsToLandmark.set(annotation, landmark);
    }
    map.removeAnnotations(map.annotations)
    map.showItems(Array.from(annotationsToLandmark.keys()));

}
function onSearch(queryString, cb) {
}
function onSelect(item) {
}
</script>

<template>
    <div class="map">
        <div id="map" :style="`height:${realHeight};`" />
    </div>
</template>

<style>
#map {
    height: 600px;
}

a:link,
a:visited {
    color: #2aaef5;
    outline: none;
    text-decoration: none;
}

.landmark {
    width: 250px;
    padding: 7px 0 0;
    background: rgb(247 247 247 / 75%);
    border-radius: 5px;
    box-shadow: 10px 10px 50px rgb(0 0 0 / 29%);
    font-family: Helvetica, Arial, sans-serif;
    transform-origin: 0 10px;
}

.landmark h1 {
    margin-top: 0;
    padding: 5px 15px;
    background: #2aaef5;
    color: rgb(255 255 255 / 90%);
    font-size: 16px;
    font-weight: 300;
}

.landmark section {
    padding: 0 15px 5px;
    font-size: 14px;
}

.landmark section p {
    margin: 5px 0;
}

.landmark::after {
    content: "";
    position: absolute;
    top: 7px;
    left: -13px;
    width: 0;
    height: 0;
    margin-bottom: -13px;
    border-right: 13px solid #2aaef5;
    border-top: 13px solid rgb(0 0 0 / 0%);
    border-bottom: 13px solid rgb(0 0 0 / 0%);
}

@keyframes scale-and-fadein {

    0% {
        transform: scale(0.2);
        opacity: 0;
    }


    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scale-and-fadein {

    0% {
        transform: scale(0.2);
        opacity: 0;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}
</style>
