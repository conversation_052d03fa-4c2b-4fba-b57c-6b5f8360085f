import axios from 'axios'

// 创建axios实例
const api = axios.create({
    // 开发环境配置
    // baseURL: '/api/management',

    // 生产环境配置
    baseURL: '/api/management',
    timeout: 200000000,
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})

// 登录相关的API
export const authAPI = {
    // 管理员登录
    async login(username, password) {
        try {
            console.log('开始登录请求...')
            const response = await api.post('/administrators/login/', {
                username,
                password
            })
            // headers: {
            //   'X-Requested-With': 'XMLHttpRequest'
            // }

            // console.log('响应头中的 Set-Cookie:', response.headers['set-cookie']);
            // 检查响应头
            // const allHeaders = response.headers;
            // console.log('所有响应头:', allHeaders);

            // 检查响应状态
            // console.log('响应状态:', response.status);
            // console.log('响应数据:', response.data);

            if (response.data?.errCode === 365) {
                // 登录成功后立即检查 cookie
                // const cookies = document.cookie.split(';').map(cookie => cookie.trim());
                // console.log('登录后的 cookies:', cookies);

                return {
                    success: true,
                    data: response.data
                }
            }
            return {
                success: false,
                message: '登录失败'
            }
        } catch (error) {
            console.error('登录错误详情:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                headers: error.response?.headers,
                data: error.response?.data,
                error: error.message
            })
            return {
                success: false,
                message: error.response?.data?.message || '登录失败'
            }
        }
    },
    async checkloginstate() {
        try {
            const response = await api.get('/administrators/login-state/')
            return {
                success: true,
                data: response.data
            }
        } catch (error) {
            return {
                success: false,
                message: error.response?.data || '检查登录状态失败'
            }
        }
    },
    // 登出
    logout() {
        return api.post('/administrators/logout/')
    }
}

// 请求拦截器
api.interceptors.request.use(config => {
    console.log('发送请求:', config.url, config.headers)
    return config
})

// 响应拦截器
api.interceptors.response.use(
    response => {
        console.log('收到响应:', response.config.url, response.data)
        if (response.data?.errCode === 365) {
            return {
                status: 365,
                data: response.data
            }
        }
        return response.data
    },
    error => {
        console.error('请求失败:', error.config?.url, error.response?.data)
        if (error.response?.status === 401) {
            console.error('未授权或登录已过期')
        }
        return Promise.reject(error)
    }
)
// 班次相关的API
export const shiftAPI = {
    // 获取班次列表
    async getShifts() {
        try {
            console.log('开始获取班次列表...');
            const response = await api.get('/dispatcher/app/shifts/');
            console.log('班次API响应:', response);

            // 检查响应格式
            if (Array.isArray(response)) {
                console.log(`成功获取 ${response.length} 个班次`);
                return response;
            } else if (response && response.data && Array.isArray(response.data)) {
                console.log(`成功获取 ${response.data.length} 个班次 (从 response.data)`);
                return response.data;
            } else if (response && response.shifts && Array.isArray(response.shifts)) {
                console.log(`成功获取 ${response.shifts.length} 个班次 (从 response.shifts)`);
                return response.shifts;
            } else {
                console.error('班次数据格式不正确:', response);

                // 如果无法获取真实数据，返回硬编码的默认班次
                const defaultShifts = [
                    {
                        id: 1,
                        name: 'AM',
                        label: 'AM (08:00-12:00)',
                        start_at: '08:00',
                        end_at: '12:00',
                        is_available: true
                    },
                    {
                        id: 2,
                        name: 'PM',
                        label: 'PM (12:00-18:00)',
                        start_at: '12:00',
                        end_at: '18:00',
                        is_available: true
                    },
                    {
                        id: 3,
                        name: 'NT',
                        label: 'NT (18:00-22:00)',
                        start_at: '18:00',
                        end_at: '22:00',
                        is_available: true
                    }
                ];

                console.log('使用默认班次数据:', defaultShifts);
                return defaultShifts;
            }
        } catch (error) {
            console.error('获取班次列表失败:', error);

            // 如果API调用失败，返回硬编码的默认班次
            const defaultShifts = [
                {
                    id: 1,
                    name: 'AM',
                    label: 'AM (08:00-12:00)',
                    start_at: '08:00',
                    end_at: '12:00',
                    is_available: true
                },
                {
                    id: 2,
                    name: 'PM',
                    label: 'PM (12:00-18:00)',
                    start_at: '12:00',
                    end_at: '18:00',
                    is_available: true
                },
                {
                    id: 3,
                    name: 'NT',
                    label: 'NT (18:00-22:00)',
                    start_at: '18:00',
                    end_at: '22:00',
                    is_available: true
                }
            ];

            console.log('API调用失败，使用默认班次数据:', defaultShifts);
            return defaultShifts;
        }
    }
}

// 司机相关的API
export const driverAPI = {
    // 获取所有可用司机列表
    getDrivers(params = {}) {
        console.log('获取司机列表，原始参数:', params);

        // 构建查询参数，只提取需要的字段
        const queryParams = {};

        // 只添加 date 和 shift_id 参数
        if (params.date) {
            queryParams.date = params.date;
        }

        // 使用 shift_id 作为参数名称
        if (params.shiftId) {
            queryParams.shift_id = params.shiftId;
        } else if (params.shift_id) {
            queryParams.shift_id = params.shift_id;
        }

        console.log('获取司机列表，精简后的参数:', queryParams);

        return api.get('/dispatcher/app/drivers/', {
            params: queryParams
        })
            .then(response => {
                console.log('获取司机列表成功:', response);
                // 直接返回数据数组，不需要包装
                return response;
            })
            .catch(error => {
                console.error('获取司机列表失败:', error);
                return [];  // 出错时返回空数组
            });
    },

    // 获取单个司机信息
    getDriver(driverId) {
        return api.get(`/dispatcher/app/drivers/${driverId}/`)
    }

    //   // 更新司机状态
    //   updateDriverStatus(id, status) {
    //     return api.patch(`/drivers/${id}/status`, { status })
    //   },

    //   // 创建新司机
    //   createDriver(driverData) {
    //     return api.post('/drivers', driverData)
    //   },

    //   // 更新司机信息
    //   updateDriver(id, driverData) {
    //     return api.put(`/drivers/${id}`, driverData)
    //   },

//   // 删除司机
//   deleteDriver(id) {
//     return api.delete(`/drivers/${id}`)
//   }
}

// 订单相关的API
export const orderAPI = {
    // 获取订单列表
    async getOrders(params) {
        try {
            let orderResponse = []
            try {
                console.log('发送订单查询请求:', params)

                // 如果params中有useLocalFile标记，则从本地文件加载数据
                if (params && params.useLocalFile) {
                    console.log('从本地文件加载订单数据')
                    try {
                        // 在浏览器环境中，使用fetch API加载本地文件
                        const response = await fetch('/src/services/exampleorders.json')
                        const data = await response.json()
                        orderResponse = data.orders || []
                        console.log(`从本地文件加载了 ${orderResponse.length} 个订单`)
                    } catch (fileError) {
                        console.error('从本地文件加载订单数据失败:', fileError)
                    }
                } else {
                    // 正常调用API
                    console.log('使用参数调用订单API，原始参数:', params);

                    // 构建查询参数，只提取需要的字段
                    const queryParams = {};

                    // 只添加 date 和 shift_id 参数
                    if (params.date) {
                        queryParams.date = params.date;
                    }

                    // 使用 shiftId 作为参数名称
                    if (params.shiftId) {
                        queryParams.shiftId = params.shiftId;
                        console.log(`使用 shiftId=${params.shiftId} 获取订单`);
                    } else if (params.shift_id) {
                        queryParams.shiftId = params.shift_id;
                        console.log(`使用 shiftId=${params.shift_id} 获取订单`);
                    } else if (params.shift && params.shift.id) {
                        // 如果传入的是 shift 对象，提取 id
                        queryParams.shiftId = params.shift.id;
                        console.log(`从 shift 对象提取 shiftId=${queryParams.shiftId}`);
                    }

                    console.log('使用参数调用订单API，精简后的参数:', queryParams);

                    const response = await api.get('/dispatcher/app/orders/', {
                        params: queryParams
                    })

                    // 提取订单列表数据
                    if (response && response.order_list) {
                        orderResponse = response.order_list
                    } else if (response && response.data && response.data.order_list) {
                        orderResponse = response.data.order_list
                    } else {
                        console.log('原始API响应:', response)
                        orderResponse = response // 直接使用响应数据
                    }
                }

                // 处理坐标数据
                if (Array.isArray(orderResponse)) {
                    orderResponse = orderResponse.map(order => {
                        // 确保每个订单都有lng_lat字段
                        // 检查可能的坐标字段
                        if (!order.lng_lat) {
                            if (order.latitude && order.longitude) {
                                order.lng_lat = [parseFloat(order.latitude), parseFloat(order.longitude)]
                            } else if (order.pickup_lat && order.pickup_lng) {
                                order.lng_lat = [parseFloat(order.pickup_lat), parseFloat(order.pickup_lng)]
                            } else if (order.lat && order.lng) {
                                order.lng_lat = [parseFloat(order.lat), parseFloat(order.lng)]
                            }

                        }
                        return order
                    })
                }
            } catch (error) {
                console.error('获取订单列表失败:', error)
            }

            return {
                orders: {
                    order_list: orderResponse
                }
            }
        } catch (error) {
            console.error('获取订单列表失败:', error)
            return {
                orders: {
                    order_list: []
                }
            }
        }
    },

    // 获取单个订单详情
    getOrder(id, type) {
        return api.get(`/dispatcher/app/orders/${id}/?type=${type}`)
    },
    getorderpickuptime(id) {
        return api.get(`/delivery/orders/?id=${id}`)
    },

    // 批量获取多个订单的详细信息
    async getOrdersBatch(orderIds, types = null) {
        if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
            console.warn('getOrdersBatch: 订单ID列表为空');
            return [];
        }

        try {
            // 创建获取每个订单详情的Promise数组
            const promises = orderIds.map(orderId => {
                // 如果提供了types映射，使用对应的type，否则不指定type
                const type = types && types[orderId] ? `?type=${types[orderId]}` : '';
                return api.get(`/dispatcher/app/orders/${orderId}/${type}`);
            });

            // 并行获取所有订单详情
            const results = await Promise.all(promises);

            // 处理结果
            const orders = [];
            results.forEach((response, index) => {
                if (response && response.order) {
                    orders.push(response.order);
                } else if (response) {
                    // 根据API响应结构提取订单数据
                    const orderData = response.data ? response.data.order || response.data : response;
                    if (orderData) {
                        // 确保数据中有ID
                        if (!orderData.id) {
                            orderData.id = orderIds[index];
                        }
                        orders.push(orderData);
                    }
                }
            });

            console.log(`[orderAPI] 批量获取了 ${orders.length}/${orderIds.length} 个订单详情`);
            return orders;

        } catch (error) {
            console.error('批量获取订单详情失败:', error);
            return [];
        }
    },

    // 分配订单到路线
    // orderAPI.assignOrder(orderId, routeId, 'PICKUP');

    // // 取消订单分配
    // orderAPI.assignOrder(orderId, null, 'PICKUP');
    assignOrder(orderId, routeId, type) {
        return api.put(`/dispatcher/app/orders/${orderId}/`, {
            route_id: routeId,
            type: type // PICKUP, DELIVERY, SERVICE
        })
    },
    // 更新停靠点
    updateStopNumber(orderId, stopNumber, type) {
        return api.patch(`/dispatcher/app/orders/${orderId}/`, {
            stop_no: stopNumber,
            type: type
        })
    }

}

// 路线相关的API
let allroutes = []
export const routeAPI = {
    routepickups: [],
    routedeliveries: [],
    // 获取所有当前日期时间段路线规划
    async getRoutes(params) {
        try {
            // 检查并处理参数
            if (!params) {
                console.error('getRoutes: params 参数为空')
                return []
            }

            console.log('getRoutes: 原始参数:', params);

            // 构建查询参数，只提取需要的字段
            const queryParams = {};

            // 添加 date 参数
            if (params.date) {
                queryParams.date = params.date;
            } else {
                console.error('getRoutes: date 参数为空')
                return []
            }

            // 处理 priority 参数
            let priority;

            // 如果直接提供了 priority 参数
            if (params.priority !== undefined) {
                priority = params.priority;
            }
            // 如果提供了 shift 对象，从中提取 priority
            else if (params.shift && params.shift.name) {
                const shiftName = params.shift.name;

                // 根据班次名称设置 priority: AM=2, PM=4, NT=6
                if (shiftName.includes('AM')) {
                    priority = 2;
                    console.log('根据班次名称设置 priority=2 (AM)');
                } else if (shiftName.includes('PM')) {
                    priority = 4;
                    console.log('根据班次名称设置 priority=4 (PM)');
                } else if (shiftName.includes('NT')) {
                    priority = 6;
                    console.log('根据班次名称设置 priority=6 (NT)');
                }
            }

            // 如果成功确定了 priority，添加到 filters 对象中
            if (priority !== undefined) {
                // 创建 filters 对象并添加 priority
                const filters = { priority: priority };

                // 将 filters 对象转换为 JSON 字符串
                queryParams.filters = JSON.stringify(filters);

                console.log(`已将 priority=${priority} 添加到 filters 对象中: ${queryParams.filters}`);
            } else {
                console.warn('警告: 无法确定 priority 参数，请求将只按日期筛选');
            }

            // 记录最终请求参数
            console.log('路线API最终请求参数:', queryParams)

            // 发送 GET 请求
            allroutes = await api.get('/dispatcher/app/routes/', {
                params: queryParams
            })

            console.log('获取到路线数据:', allroutes)

            // 检查返回数据结构并提取路线列表
            if (allroutes && allroutes.route_list) {
                return allroutes.route_list
            } else if (allroutes && allroutes.data && Array.isArray(allroutes.data.route_list)) {
                // 适配可能的包装层
                return allroutes.data.route_list
            } else {
                console.warn('路线数据格式不符合预期:', allroutes)
                return []
            }
        } catch (error) {
            console.error('获取路线数据失败:', error)
            return []
        }
    },
    // 创建新路线
    createRoute(routeData) {
        return api.post('/dispatcher/app/routes/', routeData)
    },
    // 更新路线
    updateRoute(routeId, routeData) {
        return api.put(`/dispatcher/app/routes/${routeId}/`, routeData)
    },
    // 获取单个路线详情
    getRouteById(routeId) {
        return api.get(`/dispatcher/app/routes/${routeId}/`)
    },
    // 分配路线
    //   //unassign的时候driver_id为""
    // {
    //     "driver_id":""
    // }
    assignRoute(routeId, driverId) {
        return api.patch(`/dispatcher/app/routes/${routeId}/`, {
            driver_id: driverId
        })
    },

    // 更新路线状态
    updateRouteStatus(routeId, status) {
        return api.patch(`/routes/${routeId}/status`, { status })
    }
}

export default api