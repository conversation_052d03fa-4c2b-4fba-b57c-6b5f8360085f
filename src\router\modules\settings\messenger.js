/*
* Author: <EMAIL>'
* Date: '2023-06-19 20:34:18'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/settings/messenger.js'
* File: 'messenger.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/settings/messenger',
    component: Layout,
    redirect: '/settings/messenger/index',
    name: 'settingMessenger',
    meta: {
        title: '消息系统',
        icon: 'tabler:message-cog',
        auth: ['super'],
        i18n: 'route.system.messengerSettings.title'
    },
    children: [
        {
            path: 'index',
            name: 'settingMessengerIndex',
            component: () => import('@/views/settings/messenger/index.vue'),
            meta: {
                title: '设置',
                icon: 'gala:settings',
                activeMenu: '/settings/messenger/index',
                i18n: 'route.system.messengerSettings.settings',
                auth: ['super']
            }
        },
        {
            path: 'email',
            name: 'settingMessengerEmail',
            component: () => import('@/views/settings/email/list.vue'),
            meta: {
                title: 'Email设置',
                icon: 'fluent:table-offset-settings-20-filled',
                activeMenu: '/settings/messenger/email',
                i18n: 'route.system.messengerSettings.email',
                auth: ['super']
            }
        },
        {
            path: 'operation-hours',
            name: 'settingMessengerOperationHours',
            component: () => import('@/views/settings/operation_hours/list.vue'),
            meta: {
                title: '客服时间设置',
                icon: 'mdi:shop-hours',
                activeMenu: '/settings/messenger/operation-hours',
                i18n: 'route.system.messengerSettings.operationalHours',
                auth: ['super']
            }
        },
        {
            path: 'supplier-list',
            name: 'settingMessengerSupplierList',
            component: () => import('@/views/settings/messenger/suppliers/list.vue'),
            meta: {
                title: '供应商',
                icon: 'mdi:shop-hours',
                activeMenu: '/settings/messenger/supplier-list',
                i18n: 'route.system.messengerSettings.suppliers',
                auth: ['super']
            }
        }
    ]
}
