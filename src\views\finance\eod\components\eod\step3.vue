<script setup>
import { getEOPPromotionOrders, executeEOPPromotions } from '@/api/modules/promotions'
import StepButtons from './step_buttons.vue'

import { userNameFormatter, currencyFormatter, orderNoFormatter } from '@/utils/formatter';


const props = defineProps({
    date: {
        type: String,
        default: ''
    }

})

const data = ref({
    loading: false,
    promotionResult: {}

})

const ordersToPromote = ref([])

const selectedPromotedOrders = ref([])

onMounted(() => {
    getPromotionOrders()
})

function getPromotionOrders() {
    let params = { date: props.date }

    getEOPPromotionOrders(params).then(res => {
        ordersToPromote.value = res.data
        selectedPromotedOrders.value = res.data.map(e => e.key)
    })

}
function getSummaries({ columns, data }) {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = 'Total'
            return
        } else if (index >= 3) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)
            if (index == 3) {
                sums[index] = sum
            } else {
                sums[index] = currencyFormatter(null, null, sum, null)
            }
        }
    })
    return sums
}

function executePromotions() {
    let params = {
        keys: selectedPromotedOrders.value
    }
    executeEOPPromotions(params).then(res => {
        if (res.data.errCode == null) {
            getPromotionOrders();
            data.value.promotionResult = res.data;
        }
    })
}
</script>
<template>
    <el-checkbox-group v-model="selectedPromotedOrders" v-if="ordersToPromote.length > 0">
        <template v-for="order of ordersToPromote" :key="order.key">
            <el-space size="large">
                <el-checkbox :label="order.key">
                    {{ userNameFormatter(order) }}
                </el-checkbox>
                <el-text tag="b">{{ order.promo[0].name }}</el-text>
                <el-text>{{ order.expected_pickup_start_at }}</el-text>
            </el-space>
            <el-table v-loading="data.loading" :data="order.orders" stripe highlight-current-row show-summary
                :summary-method="getSummaries">
                <el-table-column prop="no" :label="$t('fields.no')" width="200" :formatter="orderNoFormatter" />
                <el-table-column prop="sender_name" :label="$t('delivery.orders.fields.sender')" />
                <el-table-column prop="receiver_name" :label="$t('delivery.orders.fields.receiver')" />
                <el-table-column prop="quantity" :label="$t('delivery.orders.fields.quantity')" width="100"
                    align="center" />
                <el-table-column prop="total" :label="$t('delivery.orders.fields.total')" :formatter="currencyFormatter"
                    width="100" align="center" />
                <el-table-column prop="to_return" :label="$t('delivery.orders.fields.return')"
                    :formatter="currencyFormatter" width="100" align="center" />
            </el-table>
        </template>
    </el-checkbox-group>
    <el-empty v-else />
    <StepButtons v-bind="$attrs">
        <el-button type="primary" @click="executePromotions" :disabled="selectedPromotedOrders.length <= 0">
            {{ $t('finance.eod.operations.executePromotions') }}
        </el-button>
    </StepButtons>
    <slot name="buttons" />
</template>