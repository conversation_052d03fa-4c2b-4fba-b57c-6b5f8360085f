/*
* Author: <EMAIL>'
* Date: '2023-08-31 21:49:30'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/topics.js'
* File: 'topics.js'
* Version: '1.0.0'
*/

import { GET, PAT, POST, PUT, DEL, UPL, POSTFormData } from "../methods";

const path = 'knowledge-lib/'

export const getTopicCategories = (params) => GET(path + 'categories/', params)
export const getAvailableTopicCategories = () => GET(path + 'categories/available/')
export const addTopicCategory = (params) => POST(path + 'categories/', params)
export const updateTopicCategory = (params) => PUT(path + 'categories/', params)
export const updateTopicCategoryAvailability = (params) => PAT(path + 'categories/', params)
export const deleteTopicCategory = (params) => DEL(path + 'categories/', params)


export const getTopics = (params) => GET(path + 'topics/', params)
export const addTopic = (params) => POST(path + 'topics/', params)
export const updateTopic = (params) => PUT(path + 'topics/', params)
export const updateTopicAvailability = (params) => PAT(path + 'topics/', params)
export const deleteTopic = (params) => DEL(path + 'topics/', params)

export const getPrivacyPolicies = (params) => GET(path + 'topics/privacy-policies/', params)
export const getTermsConditions = (params) => GET(path + 'topics/terms-and-conditions/', params)
export const getAboutUs = () => GET(path + 'topics/about-us/')

// Videos
const videoPath = 'files/videos/'
export const getVideos = (params) => GET(videoPath, params)
export const addVideo = (params) => POSTFormData(videoPath, params)
export const updateVideo = (params) => PUT(videoPath, params)
export const deleteVideo = (params) => DEL(videoPath, params)
