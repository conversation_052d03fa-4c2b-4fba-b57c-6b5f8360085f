/*
* Author: <EMAIL>'
* Date: '2023-01-04 21:23:42'
* Project: 'Admin-UI'
* Path: 'src/router/modules/delivery/orders.js'
* File: 'orders.js'
* Version: '1.0.0'
*/

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/delivery/orders',
    component: Layout,
    redirect: '/delivery/orders/list',
    name: 'deliveryOrders',
    meta: {
        title: '订单管理',
        icon: 'icon-park-solid:order',
        auth: ['super'],
        i18n: 'route.delivery.orders.title'
    },
    children: [
        {
            path: 'newOrderForm',
            name: 'newDeliveryOrderForm',
            component: () => import('@/views/delivery/admin_orders/detail.vue'),
            meta: {
                title: '订单详情',
                icon: 'fluent:notepad-edit-16-filled',
                activeMenu: '/delivery/orders/admin-orders/list',
                i18n: 'route.delivery.orders.newOrder',
                auth: ['super'],
                sidebar: false
            }
        },
        {
            path: 'list',
            name: 'deliveryOrdersList',
            component: () => import('@/views/delivery/orders/list.vue'),
            meta: {
                title: '订单管理',
                icon: 'fluent:notepad-24-regular',
                activeMenu: '/delivery/orders/list',
                i18n: 'route.delivery.orders.orders',
                auth: ['super'],
                cache: ['deliveryOrderDetail']
            }
        },
        {
            path: 'detail',
            name: 'deliveryOrderDetail',
            component: () => import('@/views/delivery/orders/detail.vue'),
            meta: {
                title: '订单详情',
                activeMenu: '/delivery/orders/list',
                i18n: 'route.delivery.orders.orders',
                sidebar: false,
                auth: ['super']
            }
        },
        {
            path: 'admin-orders/list',
            name: 'deliveryAdminOrdersList',
            component: () => import('@/views/delivery/admin_orders/list.vue'),
            meta: {
                title: '后台订单',
                icon: 'fluent:notepad-person-20-regular',
                activeMenu: '/delivery/orders/admin-orders/list',
                i18n: 'route.delivery.orders.adminOrders',
                auth: ['super'],
                cache: ['deliveryAdminOrderDetail']
            }
        },
        {
            path: 'admin-orders/detail',
            name: 'deliveryAdminOrderDetail',
            component: () => import('@/views/delivery/admin_orders/detail.vue'),
            meta: {
                title: '订单详情',
                icon: 'fluent:notepad-person-20-regular',
                activeMenu: '/delivery/orders/admin-orders/list',
                i18n: 'route.delivery.orders.adminOrders',
                auth: ['super'],
                sidebar: false,
                cache: ['deliveryOrderDetail']
            }
        },
        {
            path: 'open-orders/list',
            name: 'deliveryOpenOrderList',
            component: () => import('@/views/open/delivery/orders/list.vue'),
            meta: {
                title: '平台订单',
                icon: 'icon-park-outline:notepad',
                activeMenu: '/delivery/orders/open-orders/list',
                i18n: 'route.delivery.orders.openOrders',
                auth: ['super'],
                cache: ['deliveryOrderDetail']
            }
        },
        {
            path: 'extended-orders/list',
            name: 'deliveryExtendedOrderList',
            component: () => import('@/views/delivery/extended_orders/list.vue'),
            meta: {
                title: '扩展订单',
                icon: 'carbon:task-asset-view',
                activeMenu: '/delivery/orders/extended-orders/list',
                i18n: 'route.delivery.extendedOrders.title',
                auth: ['super'],
                cache: ['deliveryExtendedOrderDetail']
            }
        },
        {
            path: 'extended-orders/detail',
            name: 'deliveryExtendedOrderDetail',
            component: () => import('@/views/delivery/extended_orders/detail.vue'),
            meta: {
                title: '扩展订单',
                icon: 'carbon:task-asset-view',
                activeMenu: '/delivery/orders/extended-orders/list',
                i18n: 'route.delivery.extendedOrder.title',
                auth: ['super'],
                sidebar: false,
            }
        },
        {
            path: 'user-orders',
            name: 'deliveryUserOrdersList',
            component: () => import('@/views/delivery/user_orders/list.vue'),
            meta: {
                title: '用户订单',
                icon: 'fluent:notepad-person-20-regular',
                activeMenu: '/delivery/user-orders',
                i18n: 'route.delivery.orders.userOrders',
                auth: ['super'],
            }
        },

    ]
}
