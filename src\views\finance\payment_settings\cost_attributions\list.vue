<script setup name="FinancePaymentSettingsCostAttributionsList">
import api from '@/api'
import { Delete } from '@element-plus/icons-vue'
import FormMode from './components/FormMode/index.vue'

import { getCostAttributions, updateCostAttributionAvailability, deleteCostAttribution } from '@/api/modules/statistics'
import { useI18n } from 'vue-i18n'
import { currencyFormatter } from '@/utils/formatter'

const { t } = useI18n()

const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        id: '',
        name: '',
        defaultAmount: 0,
        defaultHst: 0,
        isAvailable: true,
    },
    // 批量操作
    batch: {
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})


onMounted(() => {
    getDataList()
})


function getDataList() {
    data.value.loading = true
    getCostAttributions().then(res => {
        data.value.loading = false
        data.value.dataList = res.data
    })
}


function onCreate() {
    data.value.formModeProps.id = ''
    data.value.formModeProps.name = ''
    data.value.formModeProps.isArray = true
    data.value.formModeProps.defaultAmount = 0
    data.value.formModeProps.defaultHst = 0
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.name = row.name
    data.value.formModeProps.isAvailable = row.is_available
    data.value.formModeProps.defaultAmount = row.default_amount
    data.value.formModeProps.defaultHst = row.default_hst
    data.value.formModeProps.visible = true
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        let params = {
            id: row.id,
        }
        deleteCostAttribution(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onBatchDel() {
    ElMessageBox.confirm(t('dialog.messages.batchDeletion', { module: t('delivery.services.sizeWeight') }), t('dialog.titles.confirmation')).then(() => {
        let ids = data.value.batch.selectionDataList.map(i => i.id)
        let params = {
            id: ids.join(',')
        }
        deleteCostAttribution(params).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onUpdateAvailability(row) {
    let params = {
        id: row.id,
        is_available: !row.is_available
    }
    updateCostAttributionAvailability(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}

function onBatchAlterAvailability(isAvailable) {
    let ids = data.value.batch.selectionDataList.map(i => i.id)

    let params = {
        id: ids,
        isAvailable: isAvailable,
    }
    updateCostAttributionAvailability(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}


</script>

<template>
    <div>
        <page-header title="Cost Items" />
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button size="default" @click="onBatchDel" type="danger">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>
                    <el-button-group>
                        <el-button type="success" @click="onBatchAlterAvailability(true)">
                            {{ $t('operations.batch', { op: t('operations.enable') }) }}
                        </el-button>
                        <el-button type="warning" @click="onBatchAlterAvailability(false)">
                            {{ $t('operations.batch', { op: t('operations.disable') }) }}
                        </el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit"
                :row-class-name="tableRowClassName" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="default_amount" :label="$t('fields.fee')" :formatter="currencyFormatter" />
                <el-table-column prop="default_hst" :label="$t('fields.tax')" :formatter="currencyFormatter" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon
                                    :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
        <FormMode :id="data.formModeProps.id" :name="data.formModeProps.name"
            :is-available="data.formModeProps.isAvailable" :default-amount="data.formModeProps.defaultAmount"
            :default-hst="data.formModeProps.defaultHst" v-model="data.formModeProps.visible" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-table {
    font-size: 0.8em;

    .not-available-row {
        color: var(--g-unavailable-color);
    }

}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
