# MapView组件重构说明

为了提高组件的可维护性，我们将原先的单个大型MapView.vue文件（4000多行代码）拆分成了多个较小的组件和可组合式函数(composables)。这种模块化的方法使得代码更加易于理解、测试和维护。

## 目录结构

```
src/modules/fleetdispatch/components/MapView/
├── MapView.vue            # 原始组件(不再使用)
├── MapViewNew.vue         # 新的主组件，使用了所有composables
├── Marker.vue             # 标记组件
├── MarkerPopup.vue        # 标记弹窗组件
├── index.js               # 导出所有组件和composables
├── components/            # 子组件目录
│   ├── DriverMarker.vue        # 司机标记组件
│   └── DriverMarkerPopup.vue   # 司机标记弹窗组件
└── composables/           # 可组合式函数目录
    ├── useMapInitialization.js # 地图初始化功能
    ├── useMarkerManagement.js  # 标记管理功能
    ├── useDriverMarkers.js     # 司机标记管理功能
    ├── useRouteManagement.js   # 路线管理功能
    ├── useMapInteractions.js   # 地图交互功能
    ├── useTrafficLayer.js      # 交通图层功能
    └── usePerformanceMonitor.js # 性能监控功能
```

## 功能模块说明

### 1. useMapInitialization

负责地图的初始化、配置和销毁。

```js
const { map, isMapLoaded, initializeMap, removeMap } = useMapInitialization(mapContainer)
```

### 2. useMarkerManagement

管理地图上的订单标记，包括添加、更新和删除标记。

```js
const { markers, isUpdatingMarkers, clearMarkers, updateMarkers } = useMarkerManagement(map)
```

### 3. useDriverMarkers

管理地图上的司机标记，包括添加、更新和删除司机标记。

```js
const { driverMarkers, showDrivers, updateDriverMarkers, toggleDrivers } = useDriverMarkers(map)
```

### 4. useRouteManagement

管理地图上的路线，包括路线的绘制和更新。

```js
const { routeType, isUpdatingRoutes, clearAllRoutes, redrawRoutes, drawDirectRoute } = useRouteManagement(map)
```

### 5. useMapInteractions

处理地图的交互功能，如点击、选择、键盘事件等。

```js
const { isDrawingSelectionBox, selectionBox, showSelectionTooltip, bindMapEventHandlers } = useMapInteractions(map, mapContainer)
```

### 6. useTrafficLayer

管理地图上的实时交通图层。

```js
const { showTraffic, initTrafficLayer, toggleTraffic, removeTrafficLayer } = useTrafficLayer(map)
```

### 7. usePerformanceMonitor

监控和显示地图性能指标。

```js
const { performanceStats, updatePerformanceStats } = usePerformanceMonitor()
```

## 如何使用

### 1. 替换原有组件

在项目中，使用`MapViewNew.vue`替换原有的`MapView.vue`组件：

```js
// 修改前
import MapView from 'path/to/MapView.vue'

// 修改后
import { MapView } from 'path/to/MapView' // 使用index.js中导出的组件
```

### 2. 自定义地图功能

如果需要在其他组件中使用地图功能，可以直接导入相应的composable：

```js
// 在其他组件中使用地图初始化功能
import { useMapInitialization } from 'path/to/MapView'

const MyComponent = {
  setup() {
    const mapContainer = ref(null)
    const { map, isMapLoaded, initializeMap } = useMapInitialization(mapContainer)
    
    // 使用地图功能...
    
    return { mapContainer }
  }
}
```

### 3. 扩展功能

如果需要添加新功能，可以创建新的composable或扩展现有composable，而不需要修改主组件。

## 性能优化

使用模块化架构可以带来以下性能优势：

1. 按需加载：只在需要时导入相应的功能模块
2. 更好的缓存：较小的文件更有利于浏览器缓存
3. 代码分割：在构建过程中可以更容易实现代码分割

## 注意事项

1. 原始的`MapView.vue`文件保留在项目中，可以作为参考，但新代码应该使用`MapViewNew.vue`。
2. 所有composables都是独立的，但它们通常需要接收map实例作为参数。
3. 确保在组件卸载时清理资源，如移除事件监听器和地图实例。 