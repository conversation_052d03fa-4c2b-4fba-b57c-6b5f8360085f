<script setup name="DispatcherZonesList">
import api from '@/api'
import { Delete } from '@element-plus/icons-vue'
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { getZones, updateZoneAvailability } from '@/api/modules/dispatcher'
import { currencyFormatter } from '@/utils/formatter'
import { useI18n } from 'vue-i18n'


const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        name__icontains: null,
        descriptions__icontains: null,
        fee__lte: null,
        fee__gte: 0.0,
        postal_codes__code: null,
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
        descriptions__icontains: null,
        fee__lte: null,
        fee__gte: 0.0,
        postal_codes__code: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getZones(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.zone_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'dispatcherZoneDetail'
    })
}

function onEdit(row) {
    router.push({
        name: 'dispatcherZoneDetail',
        query: {
            id: row.id
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        api.post({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function alterAvailability(row) {
    var params = {
        id: row.id,
        is_available: !row.is_available
    }
    updateZoneAvailability(params).then(res => {
        if (res.data.errCode == 365) {
            getDataList()
        }
    })
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('dispatcher.zones.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.name')">
                                        <el-input v-model="data.search.name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.name') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.desc')">
                                        <el-input v-model="data.search.descriptions__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.desc') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.postalCode')">
                                        <el-input v-model="data.search.postal_codes__code"
                                            :placeholder="$t('placeholder', { field: $t('user.fields.postalCode') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.fee')">
                                        <el-space>
                                            <el-input-number v-model="data.search.fee__gte" controls-position="right"
                                                :min="0" :step="0.1" :precision="2" :max="data.search.fee__lte ?? 100"
                                                :placeholder="$t('placeholder', { field: $t('fields.feeMin') }) + ', ' + $t('fuzzySupported')"
                                                clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                            ~
                                            <el-input-number v-model="data.search.fee__lte" controls-position="right"
                                                :min="data.search.fee__gte" :step="0.1" :precision="2"
                                                :placeholder="$t('placeholder', { field: $t('fields.feeMax') }) + ', ' + $t('fuzzySupported')"
                                                clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange">
                <el-table-column width="50" type="index" />
                <el-table-column prop="name" :label="$t('fields.name')" width="180" />
                <el-table-column prop="description" :label="$t('fields.desc')" />
                <el-table-column prop="postal_codes_count" :label="$t('user.fields.postalCode')" width="80"
                    align="center" />
                <el-table-column prop="fee" :label="$t('fields.fee')" :formatter="currencyFormatter" width="80" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="150" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="alterAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
