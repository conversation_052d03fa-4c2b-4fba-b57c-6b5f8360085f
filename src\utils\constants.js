export const userTypes = ['personal', 'business']
export const weekdays = [0, 1, 2, 3, 4, 5, 6]
export const languages = ['all', 'zh-cn', 'zh-tw', 'en-us', 'fr-fr', 'es-es']
export const messageMethods = ['fcm', 'email', 'sms']
export const appPlatforms = ['iOS', 'Android', 'Web']
export const sexes = ['m', 'f', 'x']


export const orderStatusStyles = {
    'closed': 'info',
    'created': 'info',
    'pending': 'warning',
    'stillToPay': 'danger',
    'inQueue': 'warning',
    'processing': 'warning',
    'pickedUp': 'warning',
    'sorting': 'warning',
    'transporting': 'warning',
    'delivered': 'success',
    'completed': '',
    'exceptional': 'danger'
}


export const reimbursementStatusStyles = {
    'created': 'success',
    'approved': 'danger',
    'rejected': 'info',
    'reimbursed': 'primary',
}

export const deliveryOptions = [
    {
        'name': 'Bring it back',
        'value': 'bringItBack',
    },
    {
        'name': 'Leave it',
        'value': 'leaveIt',
    },
]
