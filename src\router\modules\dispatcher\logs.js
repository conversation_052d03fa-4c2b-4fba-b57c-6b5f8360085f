const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/logs',
    component: Layout,
    redirect: '/dispatcher/logs/list',
    name: 'dispatcherLogs',
    meta: {
        title: '日志',
        icon: 'icon-park-outline:upload-logs',
        auth: ['super'],
        i18n: 'route.dispatcher.logs.title'
    },
    children: [
        {
            path: 'list',
            name: 'dispatcherLogs',
            component: () => import('@/views/dispatcher/logs/list.vue'),
            meta: {
                title: '地址列表',
                icon: 'icon-park-outline:upload-logs',
                activeMenu: '/dispatcher/logs/list',
                i18n: 'route.dispatcher.logs.title',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },
    ]
}
