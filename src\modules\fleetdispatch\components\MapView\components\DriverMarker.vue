<!-- 司机标记组件 - 用于在地图上显示司机位置 -->
<template>
  <div 
    class="driver-marker" 
    :class="{ 'selected': isSelected }"
    @click="onClick"
  >
    <div class="car-icon" :style="carStyle">
      <svg viewBox="0 0 24 24" width="24" height="24">
        <!-- 小车SVG图形 -->
        <g :fill="driver.color || '#757575'">
          <!-- 车身 -->
          <rect x="5" y="8" width="14" height="10" rx="2" />
          <!-- 车顶 -->
          <rect x="8" y="4" width="8" height="6" rx="2" />
        </g>
        <!-- 车轮 -->
        <circle cx="7" cy="18" r="2" fill="#333" />
        <circle cx="17" cy="18" r="2" fill="#333" />
      </svg>
    </div>
    
    <div v-if="showDriverInfo" class="driver-info">
      <div class="driver-name">{{ driver.name || `司机 ${driver.id}` }}</div>
      <div v-if="driver.route_number" class="driver-route">{{ driver.route_number }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 组件属性
const props = defineProps({
  driver: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  showDriverInfo: {
    type: Boolean,
    default: false
  },
  bearing: {
    type: Number,
    default: 0
  }
});

// 事件
const emit = defineEmits(['click']);

// 点击处理
const onClick = () => {
  emit('click', props.driver);
};

// 计算样式
const carStyle = computed(() => {
  const rotation = props.bearing || 0;
  
  return {
    transform: `rotate(${rotation}deg)`,
    backgroundColor: props.isSelected ? '#E3F2FD' : 'transparent',
    borderColor: props.isSelected ? '#2196F3' : 'transparent'
  };
});
</script>

<style scoped>
.driver-marker {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 1;
}

.car-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease;
  padding: 2px;
}

.driver-marker.selected .car-icon {
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.5);
  transform: scale(1.1);
}

.driver-info {
  margin-top: 4px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.driver-name {
  font-weight: bold;
}

.driver-route {
  font-size: 10px;
  color: #666;
}
</style> 