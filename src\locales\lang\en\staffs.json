{"fields": {"employee": "Employee", "employees": "Employees", "driver": "Driver", "stub": "<PERSON><PERSON>", "stubs": "<PERSON><PERSON><PERSON>", "reimbursement": "Reimbursement", "reimbursements": "Reimbursements", "paymentMethod": "Payment Method", "period": "Period", "worksheet": "Worksheet", "worksheets": "Worksheets"}, "stub": {"fields": {"salary": "Salary", "tip": "Tip", "tax": "HST", "baseSalary": "Base", "fuelAllowance": "Fuel Allowance", "bonus": "Bonus", "extraBonus": "Extra Bonus", "compensation": "Compensation", "deduction": "Deduction"}}, "reimbursement": {"fields": {"purpose": "Purpose", "receiptNumber": "Receipt Number", "vendor": "<PERSON><PERSON><PERSON>", "reviewedAt": "Reviewed at", "reviewedBy": "Reviewer"}, "selections": {"status": {"created": "Created", "approved": "Approved", "rejected": "Rejected", "reimbursed": "Reimbursed"}}}, "paymentStatement": {"fields": {"paidAmount": "<PERSON><PERSON>", "paidAt": "<PERSON><PERSON>", "paymentNo": "Payment No.", "isCompleted": "Completed"}}, "worksheet": {"fields": {"shift": "Shift"}, "selections": {"photoCategories": {"punchingIn": "Punching In", "punchingOut": "Punching Out", "pickup": "Pickup", "delivery": "Delivery", "exceptions": "Exceptions"}}}}