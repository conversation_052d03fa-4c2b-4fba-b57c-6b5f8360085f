<script setup name="DispatcherOrdersList">
    import { Refresh, DocumentCopy, ChromeFilled, Plus } from '@element-plus/icons-vue'
    import { usePagination } from '@/utils/composables'
    import {
        startOrderSync, getSynToken, queryOrderSync, stopOrderSync,
        getRoutes, completeReviseRoute, deleteRoute
    } from '@/api/modules/dispatcher'
    import { Delete, Check, Close } from '@element-plus/icons-vue'
    import { useClipboard } from '@vueuse/core'
    import Orders from './components/driver_orders.vue'
    import EmployeeSelector from '@/views/staffs/components/employee_selector.vue';
    import { useI18n } from 'vue-i18n'

    const { t } = useI18n()

    const { text, copy, copied, isSupported } = useClipboard()
    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            completed_at__isnull: true,
            name__endswith: ' ',
            driver__pk: null,
        },
        // 列表数据
        dataList: [],
    })
    const searchBarCollapsed = ref(0)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            completed_at__isnull: true,
            name__endswith: ' ',
            driver__pk: null,
        }
        currentChange()
    }

    onMounted(() => {
        var today = new Date()
        selectedDate.value = `${today.getFullYear()}-${('0' + (today.getMonth() + 1)).slice(-2)}-${('0' + today.getDate()).slice(-2)}`
        getDataList();
    })

    const selectedDate = ref()


    function getDataList() {
        data.value.loading = true
        let filters = JSON.parse(JSON.stringify(data.value.search))
        if (filters.name__endswith == ' ') {
            filters.name__endswith = null
        }
        let params = getParams({
            filters: JSON.stringify(filters),
            date: selectedDate.value,
        });
        getRoutes(params).then(res => {
            data.value.loading = false
            data.value.data = res.data.route_list;
            pagination.value.total = res.data.total
            data.value.loading = false
        })
    }

    function completeRevise(row) {
        let params = {
            id: row.id,
        }
        data.value.loading = true
        completeReviseRoute(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
            data.value.loading = false

        })
    }


    function onDel(row) {

        ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
            let params = {
                id: row.id
            }
            data.value.loading = true
            deleteRoute(params).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
                data.value.loading = false
            })
        }).catch(() => { })
    }
    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // Synchronizer Dialog
    // OR Loader
    const syncDialogVisible = ref(false);
    const syncStatus = ref({
        status: null,
        progress: 0,
        total: 0,
    });

    const syncProgress = computed(() => {
        // Round to 2 decimal places
        return Math.round((syncStatus.value.progress / syncStatus.value.total) * 100);
    })

    const syncSwitch = ref(false);

    const resetBucketSn = ref(true);

    let syncInterval;

    function onReload() {
        syncDialogVisible.value = true;
        getSyncStatus();
    }

    function getSyncStatus() {
        syncSwitch.value = true;
        const checkStatus = () => {
            if (!syncSwitch.value) {
                clearInterval(syncInterval);
                return;
            }

            queryOrderSync().then(res => {
                syncStatus.value = res.data;
                if (res.data.status !== 'IN_PROGRESS') {
                    syncSwitch.value = false;
                    clearInterval(syncInterval);
                }
            });
        };

        // Initial check
        checkStatus();
        // Set up interval
        syncInterval = setInterval(checkStatus, 1234);
    }

    function onConfirmReload() {
        data.value.loading = true
        let params = {
            date: selectedDate.value,
            reset_bucket_sn: resetBucketSn.value,
        }
        startOrderSync(params).then(res => {
            if ((res.data.data.total ?? 0) > 0) {
                getSyncStatus();
            } else {
                onCloseSyncDialog();
                ElMessageBox.alert('No order to Sync', '', {
                    center: true,
                }).then(() => {
                }).catch(() => { })

            }
            data.value.loading = false
        })
    }

    function stopSynchronization() {
        stopOrderSync().then(res => {
            getSyncStatus();
        })
    }

    function onCloseSyncDialog() {
        syncDialogVisible.value = false;
        syncSwitch.value = false;
    }

    const syncOneDialogVisible = ref(false);
    const syncOneParams = ref({
        driver_id: null,
        date: null,
        reset_bucket_sn: false,
    });

    const syncOneDriverName = ref(null);


    function onReloadOne(row) {
        syncOneDialogVisible.value = true;
        syncOneParams.value.driver_id = row.driver.id;
        syncOneParams.value.date = row.date;
        syncOneDriverName.value = row.driver.name;
        getSyncStatus();
    }

    const newRouteDialogVisible = ref(false)
    const newRouteDriver = ref(null)

    function onLoadNewOne() {
        newRouteDialogVisible.value = true;
        syncOneParams.value.date = selectedDate.value;
        getSyncStatus();
    }
    function onConfirmLoadNewOne() {
        newRouteDialogVisible.value = false;
        syncOneParams.value.driver_id = newRouteDriver.value.id;
        syncOneDriverName.value = newRouteDriver.value.name;
        onConfirmReloadOne();
    }

    function onCloseLoadNew() {
        newRouteDialogVisible.value = false;
        newRouteDriver.value = null;
    }


    function onConfirmReloadOne() {
        let params = JSON.parse(JSON.stringify(syncOneParams.value));
        startOrderSync(params).then(res => {
            if ((res.data.data.total ?? 0) > 0) {
                getSyncStatus();
            } else {
                onCloseReloadOne();
                ElMessageBox.alert('No order to Sync', '', {
                    center: true,
                }).then(() => {
                }).catch(() => { })
            }
        })
    }

    function onCloseReloadOne() {
        syncOneDialogVisible.value = false;
        syncSwitch.value = false;
    }



    // Token Dialog
    const tokenDialogVisible = ref(false)
    const token = ref(null)

    function getToken() {
        getSynToken().then(res => {
            if (res.data.token) {
                token.value = res.data.token;
                tokenDialogVisible.value = true;
            }

        })
    }

    function closeTokenDialog() {
        token.value = null;
        tokenDialogVisible.value = false;
    }

    watch(() => data.value.search.driver__pk, (_) => { getDataList() })


    const shifts = [
        {
            label: 'All',
            value: ' ',
        },
        {
            label: 'AM',
            value: 'A',
        },
        {
            label: 'PM',
            value: 'P',
        },
        {
            label: 'NT',
            value: 'N',
        },
    ]


    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (row.completed_at != null) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>
<template>
    <div>
        <page-header :title="$t('dispatcher.orders.title')" />
        <page-main>
            <div class="top-buttons">
                <el-descriptions border size="small" :column="4">
                    <el-descriptions-item :label="$t('fields.date')">
                        <el-date-picker v-model="selectedDate" type="date"
                            :placeholder="$t('selectPlaceHolder', { field: $t('fields.date') })"
                            value-format="YYYY-MM-DD" @change="getDataList" />
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('fields.status')">
                        <el-switch v-model="data.search.completed_at__isnull" @change="getDataList"
                            :active-text="$t('operations.uncompletedRoutes')"
                            :inactive-text="$t('operations.showAllRoutes')" :inactive-value="null" />
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('fields.shifts')">
                        <el-select v-model="data.search.name__endswith" @change="getDataList" style="width: 130px">
                            <el-option v-for="s of shifts" :value="s.value" :label="s.label" />
                        </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('staffs.fields.employee')">
                        <EmployeeSelector v-model="data.search.driver__pk" />
                    </el-descriptions-item>
                </el-descriptions>
                <el-space>
                    <el-button type="primary" @click="onReload" :icon="Refresh">
                        {{ $t('operations.load') }}
                    </el-button>
                    <el-button type="primary" @click="getToken" :icon="ChromeFilled" plain circle />
                    <el-button type="success" @click="onLoadNewOne" :icon="Plus" plain circle />
                </el-space>
            </div>
            <el-table :data="data.data" @expand-change="onRouteExpanded" stripe highlight-current-row
                v-loading="data.loading" :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName">
                <el-table-column type="expand">
                    <template #default="scope">
                        <div style="padding-right: 40px; padding-left: 40px;">
                            <Orders :id="scope.row.id" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="name" :label="$t('dispatcher.routes.fields.name')" width="200" />
                <el-table-column prop="driver.name" :label="$t('dispatcher.routes.fields.driver')" />
                <el-table-column prop="pickups" :label="$t('dispatcher.routes.fields.pickups')" align="center" />
                <el-table-column prop="deliveries" :label="$t('dispatcher.routes.fields.deliveries')" align="center" />
                <el-table-column prop="start_at" :label="$t('dispatcher.routes.fields.startAt')" />
                <el-table-column prop="completed_at" :label="$t('dispatcher.routes.fields.completedAt')" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" />
                <el-table-column :label="$t('fields.operations')" width="120" align="right" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.load')" placement="top-start">
                            <el-button type="success" plain :icon="Refresh" circle size="small"
                                @click="onReloadOne(scope.row)" />
                        </el-tooltip>

                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.completed_at != null ? 'warning' : 'success'" circle
                                size="small" v-if="scope.row.start_at != null"
                                :icon="scope.row.completed_at != null ? Close : Check"
                                @click="completeRevise(scope.row)">
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" v-if="scope.row.completed_at == null"
                            :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>

        <!-- Token Dialog -->
        <el-dialog v-model="tokenDialogVisible" title="Sync Token" width="500" :before-close="handleClose">
            <el-space>
                <el-text size="large" tag="b">{{ token }}</el-text>
                <el-button @click.stop="copy(token)" size="small" type="primary" plain circle :icon="DocumentCopy" />
            </el-space>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="closeTokenDialog">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- Sync Dialog -->
        <el-dialog v-model="syncDialogVisible" title="Synchronize Orders" width="500" :before-close="onCloseSyncDialog">
            <template v-if="syncStatus.status == 'IN_PROGRESS'">
                <el-progress :percentage="syncProgress" />
                <el-space>
                    <el-text tag="b">{{ syncStatus.progress }}/{{ syncStatus.total }}</el-text>
                    <el-text>Synchronization in progress</el-text>
                    <el-link type="danger" @click="stopSynchronization">{{ $t('operations.cancel') }}</el-link>
                </el-space>
            </template>
            <el-checkbox v-else v-model="resetBucketSn" label="Reset Bucket SN" size="large" />
            <template #footer>
                <div class="dialog-footer" v-if="syncStatus.status !== 'IN_PROGRESS'">
                    <el-button type="primary" @click="onConfirmReload">
                        {{ $t('operations.load') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- Sync One Dialog -->
        <el-dialog v-model="syncOneDialogVisible" :title="`Synchronize ${syncOneDriverName}'s Orders`" width="500"
            :before-close="onCloseReloadOne">
            <template v-if="syncStatus.status == 'IN_PROGRESS'">
                <el-progress :percentage="syncProgress" />
                <el-space>
                    <el-text tag="b">{{ syncStatus.progress }}/{{ syncStatus.total }}</el-text>
                    <el-text>Synchronization in progress</el-text>
                    <el-link type="danger" @click="stopSynchronization">{{ $t('operations.cancel') }}</el-link>
                </el-space>
            </template>
            <template #footer>
                <div class="dialog-footer" v-if="syncStatus.status !== 'IN_PROGRESS'">
                    <el-button type="primary" @click="onConfirmReloadOne">
                        {{ $t('operations.load') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- New Route Dialog -->
        <el-dialog v-model="newRouteDialogVisible" title="Add a route from remote" width="500"
            :before-close="onCloseLoadNew">
            <EmployeeSelector v-model="newRouteDriver" :id-only="false" />
            <template #footer>
                <div class="dialog-footer" v-if="syncStatus.status !== 'IN_PROGRESS'">
                    <el-button type="primary" @click="onConfirmLoadNewOne">
                        {{ $t('operations.load') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
        margin-bottom: 24px;
    }

    .package-index {
        width: 20px;
        text-align: center;
        font-weight: bolder;
    }

    .completed {
        background-color: #79bbff;
        color: white;
    }

    .unsorted {
        background-color: #E6A23C;
        color: white;
    }

    .uncompleted {
        background-color: #F56C6C;
        color: white;
    }
</style>
