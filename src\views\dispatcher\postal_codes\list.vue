<script setup name="DispatcherPostalCodesList">
    import { Delete } from '@element-plus/icons-vue'
    import { usePagination } from '@/utils/composables'
    import FormMode from './components/FormMode/index.vue'
    import { getPostalCodes, alterPostalCodesAvailabilityFee, deletePostalCodes } from '@/api/modules/dispatcher'
    import { currencyFormatter } from '@/utils/formatter'
    import { getZones } from '@/api/modules/delivery'
    import { useI18n } from 'vue-i18n'

    const { t } = useI18n()

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        formModeProps: {
            visible: false,
            row: {}
        },
        // 搜索
        search: {
            code__icontains: null,
            code1__icontains: null,
            city__icontains: null,
            province__icontains: 'Ontario',
            fee__lte: null,
            fee__gte: 0,
            is_available: null,
            zone: null
        },
        // 批量操作
        batch: {
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)
    const zones = ref([])
    const fee = ref(0.0)
    const showBatchEditDialog = ref(false)


    // Filters
    function resetFilters() {
        data.value.search =
        {
            code__icontains: null,
            code1__icontains: null,
            city__icontains: null,
            province__icontains: 'Ontario',
            fee__lte: null,
            fee__gte: 0,
            is_available: null,
            zone: null

        }
        currentChange()
    }


    onMounted(() => {
        getDataList()
        getZoneList()
    })


    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getPostalCodes(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.postal_code_list
            pagination.value.total = res.data.total
        })
    }

    function getZoneList() {
        getZones().then(res => zones.value = res.data)
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onCreate() {
        data.value.formModeProps.row = { id: '', fee: 0.0 }
        data.value.formModeProps.visible = true
    }

    function onEdit(row) {
        data.value.formModeProps.row = JSON.parse(JSON.stringify(row))
        data.value.formModeProps.visible = true
    }

    function onDel(row) {
        ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.code }), t('dialog.titles.confirmation')).then(() => {
            deletePostalCodes({ id: row.id }).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }


    function onBatchDel() {
        ElMessageBox.confirm(t('dialog.messages.batchDeletion', { module: t('dispatcher.postalCodes.title') }), t('dialog.titles.confirmation')).then(() => {
            let ids = []
            data.value.batch.selectionDataList.forEach(i => ids.push(i.id))

            let params = {
                id: JSON.stringify(ids)
            }
            deletePostalCodes(params).then((res) => {
                if (res.data.errCode == 365) {
                    getDataList()
                }
            })
        }).catch(() => { })
    }


    function onUpdateAvailability(row) {
        let params = { id: row.id, is_available: !row.is_available }
        alterPostalCodesAvailabilityFee(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }

    function _showBatchEditDialog() {
        showBatchEditDialog.value = true
        fee.value = 0.0
    }

    function _hideBatchEditDialog() {
        showBatchEditDialog.value = false
        fee.value = 0.0
    }


    function onBatchUpdateFee() {

        let params = {
            id: data.value.batch.selectionDataList.map(x => x.id),
            fee: Math.round(fee.value * 100)
        }
        alterPostalCodesAvailabilityFee(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
                _hideBatchEditDialog()
            }
        })


    }
    function onBatchAlterAvailability(isAvailable) {

        let params = {
            id: data.value.batch.selectionDataList.map(x => x.id),
            is_available: isAvailable
        }
        alterPostalCodesAvailabilityFee(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })


    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.is_available) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-header :title="$t('dispatcher.postalCodes.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.addressBook.fields.postalCode')">
                                        <el-input v-model="data.search.code__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.postalCode') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('dispatcher.postalCodes.fields.pc1')">
                                        <el-input v-model="data.search.code1__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.postalCode') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.addressBook.fields.city')">
                                        <el-input v-model="data.search.city__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.city') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.addressBook.fields.province')">
                                        <el-input v-model="data.search.province__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.province') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.isAvailable')">
                                        <el-select v-model="data.search.is_available" @change="currentChange">
                                            <el-option :value="null" :label="$t('selection.availability.all')" />
                                            <el-option :value="true"
                                                :label="$t('selection.availability.isAvailable')" />
                                            <el-option :value="false"
                                                :label="$t('selection.availability.notAvailable')" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('delivery.services.fields.zone')">
                                        <el-select v-model="data.search.zone" @change="currentChange">
                                            <el-option v-for="item in zones" :key="item.id" :value="item.id"
                                                :label="item.name" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.fee')">
                                        <el-space>
                                            <el-input-number v-model="data.search.fee__gte" controls-position="right"
                                                :placeholder="$t('placeholder', { field: $t('fields.feeMin') })"
                                                clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                            ~
                                            <el-input-number v-model="data.search.fee__lte" controls-position="right"
                                                :placeholder="$t('placeholder', { field: $t('fields.feeMax') })"
                                                clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button size="default" type="primary" @click="_showBatchEditDialog">
                        {{ $t('operations.batch', { op: $t('operations.edit') }) }}
                    </el-button>
                    <el-button-group>
                        <el-button type="success" @click="onBatchAlterAvailability(true)">
                            {{ $t('operations.batch', { op: t('operations.enable') }) }}
                        </el-button>
                        <el-button type="warning" @click="onBatchAlterAvailability(false)">
                            {{ $t('operations.batch', { op: t('operations.disable') }) }}
                        </el-button>
                    </el-button-group>

                    <el-button size="default" type="danger" @click="onBatchDel">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange"
                @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="id" align="center" fixed width="90" />
                <el-table-column prop="code1" :label="$t('user.fields.postalCode')" class-name="bold-pc" align="center"
                    sortable="custom">
                    <template #default="scope">
                        <el-space>
                            {{ scope.row.code1 }} {{ scope.row.code2 }}
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="city" :label="$t('user.fields.city')" width="150" align="center"
                    sortable="custom" />
                <el-table-column prop="province" :label="$t('user.fields.province')" width="150" align="center"
                    sortable="custom" />
                <el-table-column prop="fee" :label="$t('fields.fee')" :formatter="currencyFormatter" width="100"
                    align="center" sortable="custom" />
                <el-table-column prop="zone.name" :label="$t('delivery.services.fields.zone')" width="100"
                    align="center" />
                <el-table-column :label="$t('fields.operations')" width="150" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onUpdateAvailability(scope.row)">
                                <svg-icon
                                    :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>

                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :row="data.formModeProps.row" :zones="zones" v-model="data.formModeProps.visible"
            @success="getDataList" />
        <el-dialog v-model="showBatchEditDialog" title="Warning" width="30%" center>
            <el-row align="center">
                <el-col>
                    <el-space>
                        <span>Fee</span>
                        <el-input-number v-model="fee" :min="0.0" :step="0.1" :precision="2" />
                    </el-space>
                </el-col></el-row>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="centerDialogVisible = false">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onBatchUpdateFee">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: var(--g-unavailable-color);
        }

        .bold-pc {
            font-weight: bolder;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>
