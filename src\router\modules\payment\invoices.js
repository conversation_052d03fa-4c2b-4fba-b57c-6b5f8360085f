/*
* Author: <EMAIL>'
* Date: '2022-12-07 00:40:48'
* Project: 'Admin-UI'
* Path: 'src/router/modules/payment/invoices.js'
* File: 'invoices.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/payment/invoices',
    component: Layout,
    redirect: '/payment/invoices/list',
    name: 'invoices',
    meta: {
        title: '发票管理',
        icon: 'fa-solid:file-invoice-dollar',
        auth: ['super'],
        i18n: 'route.finance.invoices.title'
    },
    children: [
        {
            path: 'invoices/list',
            name: 'invoiceList',
            component: () => import('@/views/users/user_invoices/list.vue'),
            meta: {
                title: '用户发票',
                icon: 'teenyicons:invoice-outline',
                i18n: 'route.finance.invoices.userInvoice',
                activeMenu: '/payment/invoices/list',
                auth: ['super'],
                cache: ['userInvoiceDetail']

            }
        },
        {
            path: 'invoices/detail',
            name: 'invoiceDetail',
            component: () => import('@/views/users/user_invoices/detail.vue'),
            meta: {
                title: '支付订单',
                icon: 'icon-park-outline:transaction-order',
                i18n: 'route.finance.invoices.detail',
                activeMenu: '/payment/invoices/list',
                auth: ['super'],
                sidebar: false,
            }
        },
        {
            path: 'bills/list',
            name: 'billList',
            component: () => import('@/views/open/delivery/bills/list.vue'),
            meta: {
                title: '账单管理',
                icon: 'la:file-invoice-dollar',
                i18n: 'route.finance.bills.title',
                activeMenu: '/payment/invoices/bills/list',
                auth: ['super'],
            }
        },
        {
            path: 'bills/detail',
            name: 'billDetail',
            component: () => import('@/views/open/delivery/bills/detail.vue'),
            meta: {
                title: '账单管理',
                icon: 'la:file-invoice-dollar',
                i18n: 'route.finance.bills.title',
                activeMenu: '/payment/invoices/bills/list',
                auth: ['super'],
                sidebar: false,
            }
        },

    ]
}
