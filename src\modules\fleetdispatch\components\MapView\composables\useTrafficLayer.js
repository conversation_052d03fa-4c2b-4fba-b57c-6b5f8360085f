import { ref } from 'vue';

export function useTrafficLayer(map, isMapLoaded) {
    // 交通图层控制
    const showTraffic = ref(false); // 默认关闭

    // 添加TomTom API密钥
    const TOMTOM_API_KEY = '********************************';

    // 添加交通图层
    const addTrafficLayer = () => {
        if (!map.value) return;

        // 检查图层是否已存在
        if (map.value.getSource('trafficSource')) {
            return;
        }

        // 添加TomTom交通图层
        try {
            // 添加交通数据源
            map.value.addSource('trafficSource', {
                type: 'raster',
                tiles: [
                    `https://api.tomtom.com/traffic/map/4/tile/flow/relative/{z}/{x}/{y}.png?key=${TOMTOM_API_KEY}&style=s3`
                ],
                tileSize: 256,
                attribution: '© TomTom'
            });

            // 添加交通流量图层
            map.value.addLayer({
                id: 'traffic-flow',
                type: 'raster',
                source: 'trafficSource',
                layout: {
                    visibility: showTraffic.value ? 'visible' : 'none' // 根据控制状态设置初始可见性
                },
                paint: {
                    'raster-opacity': 0.75
                }
            });

            console.log('TomTom交通图层添加完成');
        } catch (error) {
            console.error('添加TomTom交通图层失败:', error);
        }
    };

    // 切换交通图层显示/隐藏
    const toggleTraffic = () => {
        if (!map.value || !isMapLoaded.value) return;

        const visibility = showTraffic.value ? 'visible' : 'none';

        // 检查图层是否存在
        if (map.value.getLayer('traffic-flow')) {
            map.value.setLayoutProperty('traffic-flow', 'visibility', visibility);
            console.log(`TomTom交通图层已${showTraffic.value ? '显示' : '隐藏'}`);
        } else if (showTraffic.value) {
            // 如果图层不存在但需要显示，则添加图层
            addTrafficLayer();
        }
    };

    // 移除交通图层
    const removeTrafficLayer = () => {
        if (!map.value) return;

        try {
            // 移除图层和源
            if (map.value.getLayer('traffic-flow')) {
                map.value.removeLayer('traffic-flow');
            }

            if (map.value.getSource('trafficSource')) {
                map.value.removeSource('trafficSource');
            }
        } catch (error) {
            console.error('移除交通图层出错:', error);
        }
    };

    return {
        showTraffic,
        addTrafficLayer,
        toggleTraffic,
        removeTrafficLayer
    };
}