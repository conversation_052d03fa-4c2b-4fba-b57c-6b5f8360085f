<script setup name="UserInvoicesList">
    import eventBus from '@/utils/eventBus'
    import { usePagination } from '@/utils/composables'
    import { getUserInvoices } from '@/api/modules/payment'
    import { currencyFormatter, userNameFormatter } from '@/utils/formatter'

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            title: ''
        },
        // 列表数据
        dataList: []
    })

    onMounted(() => {
        getDataList()
        if (data.value.formMode === 'router') {
            eventBus.on('get-data-list', () => {
                getDataList()
            })
        }
    })

    onBeforeUnmount(() => {
        if (data.value.formMode === 'router') {
            eventBus.off('get-data-list')
        }
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams()
        getUserInvoices(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.invoice_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function onEdit(row) {
        router.push({
            name: 'invoiceDetail',
            query: {
                id: row.id
            }
        })
    }
</script>

<template>
    <div>
        <page-header :title="$t('finance.userInvoice.title')" />
        <page-main>
            <search-bar>
                <el-form :model="data.search" size="default" label-width="100px" label-suffix="：">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="标题">
                                <el-input v-model="data.search.title" placeholder="请输入标题，支持模糊查询" clearable
                                    @keydown.enter="currentChange()" @clear="currentChange()" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item>
                        <el-button type="primary" @click="currentChange()">
                            <template #icon>
                                <el-icon>
                                    <svg-icon name="ep:search" />
                                </el-icon>
                            </template>
                            筛选
                        </el-button>
                    </el-form-item>
                </el-form>
            </search-bar>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit"
                @sort-change="sortChange">
                <el-table-column prop="no" :label="$t('fields.no')" width="200" />
                <el-table-column prop="user" :label="$t('fields.user')" sortable="custom" min-width="260"
                    :show-overflow-tooltip="true">
                    <template #default="scope">
                        <el-space>
                            <el-tag v-if="scope.row.user?.user_code" size="small" type="info" effect="plain">{{
                                scope.row.user?.user_code
                                }}</el-tag>
                            <span>{{ userNameFormatter(scope.row) }}</span>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter"
                    align="right" />
                <el-table-column prop="period_start" :label="$t('finance.invoice.fields.periodStart')" align="center" />
                <el-table-column prop="period_end" :label="$t('finance.invoice.fields.periodEnd')" align="center" />
                <el-table-column prop="downloaded_count" :label="$t('finance.invoice.fields.downloaded')"
                    align="center" />
                <el-table-column prop="created_at" width="160" :label="$t('fields.createdAt')" sortable="custom" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;
    }
</style>
