import pinia from '@/store'
import useSettingsStore from '@/store/modules/settings'
import useMessagesStore from '@/store/modules/messages'
import useTodosStore from '@/store/modules/todos'

const messageStore = useMessagesStore(pinia)
const todosStore = useTodosStore(pinia)

// 固定路由（默认路由），
let constantRoutes = [
    {
        path: '/',
        redirect: '/dashboard'
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login.vue'),
        meta: {
            whiteList: true,
            title: '登录',
            i18n: 'route.login'
        }
    },
    {
        path: '/:all(.*)*',
        name: 'notFound',
        component: () => import('@/views/[...all].vue'),
        meta: {
            title: '找不到页面'
        }
    }
]

// 系统路由
let systemRoutes = [
    {
        path: '/dashboard',
        component: () => import('@/layout/index.vue'),
        meta: {
            title: () => {
                return useSettingsStore().dashboard.title
            },
            breadcrumb: false
        },
        children: [
            {
                path: '',
                name: 'dashboard',
                component: () => import('@/views/index.vue'),
                meta: {
                    title: () => {
                        return useSettingsStore().dashboard.title
                    },
                    i18n: 'route.dashboard',
                    breadcrumb: false
                }
            }
        ]
    },
    {
        path: '/personal',
        component: () => import('@/layout/index.vue'),
        redirect: '/personal/setting',
        meta: {
            title: '个人中心',
            breadcrumb: false
        },
        children: [
            {
                path: 'setting',
                name: 'personalSetting',
                component: () => import('@/views/personal/setting.vue'),
                meta: {
                    title: '个人设置',
                    i18n: 'route.personal.setting',
                    cache: 'personalEditPassword'
                }
            },
            {
                path: 'edit/password',
                name: 'personalEditPassword',
                component: () => import('@/views/personal/edit.password.vue'),
                meta: {
                    title: '修改密码',
                    i18n: 'route.personal.editpassword'
                }
            }
            // {
            //     path: 'notification',
            //     name: 'personalNotification',
            //     component: () => import('@/views/personal/notification.vue'),
            //     meta: {
            //         title: '通知中心',
            //         i18n: 'route.personal.notification'
            //     }
            // }
        ]
    },
    {
        path: '/reload',
        component: () => import('@/layout/index.vue'),
        meta: {
            title: '重新加载',
            breadcrumb: false
        },
        children: [
            {
                path: '',
                name: 'reload',
                component: () => import('@/views/reload.vue'),
                meta: {
                    title: '重新加载',
                    breadcrumb: false
                }
            }
        ]
    },
    {
        path: '/dispatcher/fleetdispatch',
        component: () => import('@/layout/index.vue'),
        meta: {
            title: '车队调度',
            i18n: 'route.dispatcher.fleetdispatch',
            breadcrumb: false
        },
        children: [
            {
                path: '',
                name: 'fleetDispatch',
                component: () => import('@/modules/fleetdispatch/FleetDispatchApp.vue'),
                meta: {
                    title: '车队调度',
                    i18n: 'route.dispatcher.fleetdispatch',
                    breadcrumb: false
                }
            }
        ]
    }
]

import Administrators from './modules/settings/administrators'
import SettingsRoutes from './modules/settings'
import UsersRoutes from './modules/users'
import PaymentRoutes from './modules/payment'
import DeliveryRoutes from './modules/delivery'
import Dispatcher, { FleetDispatch } from './modules/dispatcher'
import Statistics from './modules/statistics'
import WebsiteRoutes from './modules/website'
import Staffs from './modules/staffs'

// 动态路由（异步路由、导航栏路由）
let asyncRoutes = [
    {
        meta: {
            title: '调度系统',
            i18n: 'route.dispatcher.title',
            icon: 'solar:map-arrow-square-broken',
            auth: 'dispatcher',
            badge: () => todosStore.dispatcher.length
        },
        children: Dispatcher
    },
    {
        meta: {
            title: '人力资源',
            i18n: 'route.staffs.title',
            icon: 'mdi:human-male-board',
            auth: 'staffs'
            // badge: () => todosStore.staffs.length
        },
        children: Staffs
    },
    {
        meta: {
            title: '送货服务',
            i18n: 'route.delivery.title',
            icon: 'carbon:delivery-parcel',
            badge: () => todosStore.delivery.length
        },
        children: DeliveryRoutes
    },
    {
        meta: {
            title: '财务管理',
            i18n: 'route.finance.title',
            icon: 'tabler:home-dollar',
            auth: 'payment',
            badge: () => todosStore.payment.length
        },
        children: PaymentRoutes
    },
    {
        meta: {
            title: '用户管理',
            i18n: 'route.users.title',
            icon: 'fa6-solid:users-gear',
            auth: 'users',
            badge: () => messageStore.newMessagesCount
        },
        children: UsersRoutes
    },
    {
        meta: {
            title: '系统设置',
            i18n: 'route.system.title',
            icon: 'material-symbols:settings-suggest',
            auth: 'super'
        },
        children: SettingsRoutes
    },
    // {
    //     meta: {
    //         title: '数据统计',
    //         i18n: 'route.statistics.title',
    //         icon: 'solar:graph-new-outline',
    //     },
    //     children: Statistics
    // },
    // {
    //     meta: {
    //         title: '商城管理',
    //         i18n: 'route.mall.title',
    //         icon: 'ic:twotone-store-mall-directory',
    //     },
    //     children: []
    // },
    {
        meta: {
            title: '网站管理',
            i18n: 'route.website.title',
            icon: 'mdi:web-box'
        },
        children: WebsiteRoutes
    }
]

import { setupLayouts } from 'virtual:generated-layouts'
import generatedRoutes from 'virtual:generated-pages'

if (useSettingsStore(pinia).app.routeBaseOn === 'filesystem') {
    constantRoutes = generatedRoutes.filter(item => {
        return item.meta?.enabled !== false && item.meta?.constant === true
    })
    asyncRoutes = setupLayouts(
        generatedRoutes.filter(item => {
            return (
                item.meta?.enabled !== false &&
                item.meta?.constant !== true &&
                item.meta?.layout !== false
            )
        })
    )
}

export { constantRoutes, systemRoutes, asyncRoutes }
