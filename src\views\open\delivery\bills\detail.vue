<script setup name="OpenDeliveryBillsDetail">
import useSettingsStore from '@/store/modules/settings'
import DetailForm from './components/DetailForm/index.vue'
import eventBus from '@/utils/eventBus'
import { useTabbar } from '@/utils/composables'
const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()


// 返回列表页
function goBack() {
    eventBus.emit('get-data-list');
    if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
        useTabbar().close({ name: 'billList' })
    } else {
        router.push({ name: 'billList' })
    }
}
</script>

<template>
    <div>
        <page-header :title="route.query.id == null ? $t('operations.add') : $t('operations.add')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>
        <DetailForm :id="route.query.id" />
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
