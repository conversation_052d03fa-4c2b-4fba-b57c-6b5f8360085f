<script setup name="DispatcherLogsList">
import { usePagination } from '@/utils/composables'
import { getEliteLogs } from '@/api/modules/dispatcher'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        order__no: null,
        valid: null,
        order__user__name__icontains: null,
        tracking__isnull: null,
    },

    dataList: []
})
const searchBarCollapsed = ref(0)

// Filters
function resetFilters() {
    data.value.search =
    {
        order__no: null,
        valid: null,
        order__user__name__icontains: null,
        tracking__isnull: null,
    }
    currentChange()
}

const validStatus = [
    {
        'name': 'valid',
        'value': true,
    },
    {
        'name': 'Not Valid',
        'value': false,
    },
]

const trackingStatus = [
    {
        'name': 'Empty',
        'value': true,
    },
    {
        'name': 'Not Empty',
        'value': false,
    },
]


onMounted(() => {
    getDataList()
})


function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getEliteLogs(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.log_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

function openInNewTab(url) {
    window.open(url, '_target', 'noreferrer');
}

function pushToOrderDetail(id) {
    router.push({
        name: 'deliveryOrderDetail',
        query: {
            id: id
        }
    }

    )
}

</script>

<template>
    <div>
        <page-header :title="$t('dispatcher.logs.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.no')">
                                        <el-input v-model="data.search.order__no__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.no') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange" @clear="onSearch" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.orders.fields.user')">
                                        <el-input v-model="data.search.order__user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.orders.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange" @clear="onSearch" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.status')">
                                        <el-select v-model="data.search.valid" value-key="name"
                                            :placeholder="$t('statSelectHolder', { field: $t('fields.status') })" clearable
                                            @change="currentChange" @clear="currentChange">
                                            <el-option v-for="item in validStatus" :key="item.name" :label="item.name"
                                                :value="item.value" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('dispatcher.logs.fields.tracking')">
                                        <el-select v-model="data.search.tracking__isnull" value-key="name"
                                            :placeholder="$t('statSelectHolder', { field: $t('dispatcher.logs.fields.tracking') })"
                                            clearable @change="currentChange" @clear="currentChange">
                                            <el-option v-for="item in trackingStatus" :key="item.name" :label="item.name"
                                                :value="item.value" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>

                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>

        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row>
                <el-table-column type="index" align="center" fixed />
                <el-table-column prop="order.no" :label="$t('dispatcher.logs.fields.orderNo')" width="200">
                    <template #default="scope">
                        <el-link @click="pushToOrderDetail(scope.row.order.id)">{{ scope.row.order.no }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="user.contact" :label="$t('dispatcher.logs.fields.userName')" width="100"
                    show-overflow-tooltip />
                <el-table-column prop="user.name" width="150" show-overflow-tooltip />
                <el-table-column prop="details" :label="$t('dispatcher.logs.fields.exceptions')" show-overflow-tooltip />
                <el-table-column prop="tracking" :label="$t('dispatcher.logs.fields.tracking')" width="200" />
                <el-table-column prop="is_valid" :label="$t('fields.status')" width="80">
                    <template #default="scope">
                        <el-tag type="success" v-if="scope.row.valid">
                            {{ $t('dispatcher.logs.selections.status.success') }}
                        </el-tag>
                        <el-tag type="danger" v-else>{{ $t('dispatcher.logs.selections.status.failed') }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('dispatcher.logs.fields.doc')" align="center" width="80">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.filename" placement="top-start">
                            <el-button circle type="primary" plain @click="openInNewTab(scope.row.file)"
                                :disabled="scope.row.file == null || scope.row.file.length == 0">
                                <svg-icon name="bi:filetype-xml" :size="30" />
                            </el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
