<script setup>
    import { getPickupTimeByPc, getDeliveryTimeByPc, updateDeliveryOrder } from '@/api/modules/delivery';

    const props = defineProps({
        id: {
            type: String,
            default: null
        },
        cardShadow: {
            type: String,
            default: 'always'
        },
        data: {
            type: Object,
            default: {},
        },
        postalCode: {
            type: String,
            default: null,
        },
        part: {
            type: String,
            default: null
        }


    })

    defineExpose({
        submit() {
            updateDateTime()
        },
    })
    const formRef = ref()

    const emit = defineEmits(['success'])

    onMounted(() => {
        getTimes();
    })

    const data = ref({
        date: props.data.date,
        time: props.data.time,
    })

    const formRules = {
        date: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        time: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }


    function updateDateTime() {
        formRef.value.validate(valid => {
            if (valid) {
                let params = {
                    id: props.id,
                    part: props.part,
                    ...data.value,
                }
                updateDeliveryOrder(params).then(res => {
                    if (res.data.errCode === 365) {
                        emit('success')
                    }
                })
            }
        })
    }

    const times = ref([])

    function disabledDates(date) {
        const _date = new Date();
        return date.getTime() < _date.setDate(_date.getDate() - 1)
    }
    // API
    function getTimes() {
        let params = {
            date: data.value.date,
            postalCode: props.postalCode,
        }
        if (props.part == 'pickup') {
            getPickupTimeByPc(params).then(res => {
                times.value = res.data
                console.log(!times.value.some(e => e.id == props.time))
                if (!times.value.some(e => e.id == props.time)) {
                    data.value.time = null
                }
            })
        } else {
            getDeliveryTimeByPc(params).then(res => {
                times.value = res.data
                if (!times.value.some(e => e.id == props.time)) {
                    data.value.time = null
                }

            })

        }
    }

</script>
<template>

    <el-card :header="$t('delivery.orders.fields.expectedPickupTime')"
        :class="props.part == 'pickup' ? 'sender-card' : 'receiver-card'" :shadow="cardShadow">
        <el-form :rules="formRules" :model="data" ref="formRef">
            <el-form-item :label="$t('delivery.orders.fields.expectedPickupDate')" prop="date">
                <el-date-picker v-model="data.date" type="date" placeholder="Pick a day" @change="getTimes"
                    :disabled-date="disabledDates" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item :label="$t('delivery.orders.fields.expectedPickupTime')" prop="time">
                <el-select v-model="data.time" placeholder="Select">
                    <el-option v-for="item in times" :key="item.id" :value="item.id"
                        :label="`${item.start_at} ~ ${item.end_at}`" />
                </el-select>
            </el-form-item>
        </el-form>
    </el-card>

</template>
<style scoped lang="scss">
    :deep(.el-card__header) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-weight: bolder;
        color: white;
    }

    .el-alert {
        margin: 20px 0 0;

        :deep(.el-alert__description) {
            margin: 0;
            padding: 0;
            line-height: normal;
        }
    }

    .el-alert:first-child {
        margin: 0;
    }

    .sender-card {

        :deep(.el-card__header) {
            background-color: #eebe77 !important;
        }
    }

    .receiver-card {

        :deep(.el-card__header) {
            background-color: #b3e19d !important;
        }
    }

    .address-not-available {
        display: block;
        text-align: right;
        font-size: 0.8em;
        color: red;
        line-height: 0.8em;
    }

    .address-auto-span {
        font-weight: bolder;
        text-align: center;
        width: 100%;
        margin-top: -20px;
        margin-bottom: -20px;
        padding-right: 10px;
    }
</style>