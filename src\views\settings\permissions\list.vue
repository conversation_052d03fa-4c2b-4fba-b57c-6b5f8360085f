<script setup name="PermissionsList">
import { deletePermission, getPermissions, updatePermission } from '@/api/modules/permissions'
import { usePagination } from '@/utils/composables'
import eventBus from '@/utils/eventBus'
import { Delete, RefreshLeft, WarningFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination()
// const router = useRouter()

const data = ref({
    loading: false,
    search: {
        title: ''
    },
    batch: {
        enable: false,
        selectionDataList: []
    },
    dataList: []
})

onMounted(() => {
    getDataList()
    if (data.value.formMode === 'router') {
        eventBus.on('get-data-list', () => {
            getDataList()
        })
    }
})

onBeforeUnmount(() => {
    if (data.value.formMode === 'router') {
        eventBus.off('get-data-list')
    }
})

function getDataList() {
    data.value.loading = true
    let params = getParams()
    getPermissions(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.permission_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

function onDel(row) {
    ElMessageBox.confirm(
        t('dialog.messages.deletion', { name: t(`${row.module.name}.title`) + ' - ' + t(`operations.${row.operation.name}`) }),
        t('dialog.titles.confirmation')
    ).then(() => {
        deletePermission({ ids: JSON.stringify([row.id]) }).then(res => {
            if (res.data.errCode === 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onBatchDel() {
    ElMessageBox.confirm(
        t('dialog.messages.deletion', { name: 'Selected' }),
        t('dialog.titles.confirmation')
    ).then(() => {
        let ids = []
        data.value.batch.selectionDataList.forEach(e => {
            ids.push(e.id)
        })
        deletePermission({ ids: JSON.stringify(ids) }).then(res => {
            if (res.data.errCode === 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function reset() {
    updatePermission().then(() => {
        getDataList()
    })
}
</script>

<template>
    <div>
        <page-header :title="$t('settings.permission.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-suffix="：">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="标题">
                                        <el-input v-model="data.search.title" placeholder="请输入标题，支持模糊查询" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item></el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList"
                    @check-all="$refs.table.toggleAllSelection()" @check-null="$refs.table.clearSelection()">
                    <el-button size="default" type="danger" plain @click="onBatchDel">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>
                </batch-action-bar>
                <el-button type="danger" @click="reset" :icon="WarningFilled">
                    {{ $t('operations.reset') }}
                </el-button>

            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="module" :label="$t('settings.permission.fields.module')">
                    <template #default="scope">
                        <div class="permission-item-flex">
                            {{ $t(`modules.${scope.row.module.name}`) }}
                            <el-icon :color="scope.row.module.is_available ? '#67C23A' : '#F56C6C'">
                                <svg-icon
                                    :name="scope.row.module.is_available ? 'i-ep:success-filled' : 'i-ep:circle-close'" />
                            </el-icon>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="operation" :label="$t('settings.permission.fields.operation')">
                    <template #default="scope">
                        <div class="permission-item-flex">
                            {{ $t(`operations.${scope.row.operation.name}`) }}
                            <el-icon :color="scope.row.operation.is_available ? '#67C23A' : '#F56C6C'">
                                <svg-icon
                                    :name="scope.row.operation.is_available ? 'i-ep:success-filled' : 'i-ep:circle-close'" />
                            </el-icon>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.permission-item-flex {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 5px;
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
