<script setup name="WebsiteManagementTopicsPrivacyPolicies">
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'

import { updateTopicAvailability, getAboutUs } from '@/api/modules/topics'
import { useI18n } from 'vue-i18n';

const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        name__icontains: null,
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getAboutUs(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.topic_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'websiteTopicDetail',
        query: {
            category: 'aboutUs',
        }
    })
}

function onEdit(row) {
    router.push({
        name: 'websiteTopicDetail',
        query: {
            id: row.id,
            category: 'aboutUs',
        }
    })
}
function onAlterAvailability(row) {
    updateTopicAvailability({ id: row.id, is_available: !row.is_available }).then(res => {
        if (res.data.errCode === 365) {
            getDataList()
        }
    })
}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('topics.title')" />
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column prop="title" :label="$t('topics.fields.title')" />
                <el-table-column prop="lang_code" :label="$t('fields.language')">
                    <template #default="scope">
                        <ElTag size="small" type="warning" v-if="scope.row.lang_code">
                            {{ $t(`user.selection.language.${scope.row.lang_code}`) }}
                        </ElTag>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.status ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onAlterAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: #bbb;
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
