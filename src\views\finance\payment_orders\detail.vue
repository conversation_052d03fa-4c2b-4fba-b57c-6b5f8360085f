<script setup name="PaymentOrdersDetail">
import useSettingsStore from '@/store/modules/settings'
import PaymentOrderDetail from './components/DetailForm/payment_order.vue'
import eventBus from '@/utils/eventBus'
import { useTabbar } from '@/utils/composables'

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()


// 返回列表页
function goBack() {
    eventBus.emit('get-data-list')
    if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
        useTabbar().close({ name: 'paymentOrderList' })
    } else {
        router.push({ name: 'paymentOrderList' })
    }
}
</script>

<template>
    <div>
        <page-header :title="$t('finance.paymentOrder.detailPage')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>
        <PaymentOrderDetail :id="route.query.id" />
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
