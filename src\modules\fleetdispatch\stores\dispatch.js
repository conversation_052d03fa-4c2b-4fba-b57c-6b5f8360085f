import { defineStore } from 'pinia'
import { DispatchCoordinator } from '../services/DispatchCoordinator'

export const useDispatchStore = defineStore('dispatch', {
    state: () => ({
        isLoading: false,
        error: null,
        coordinator: new DispatchCoordinator()
    }),

    getters: {
    // 获取所有订单
        allOrders: state => {
            return state.coordinator.orderService.getAllOrders()
        },
    
        // 获取未分配订单
        unassignedOrders: state => {
            return state.coordinator.orderService.orders
        },
    
        // 获取已分配订单
        assignedOrders: state => {
            return state.coordinator.orderService.assignedOrders
        },
    
        // 获取选中的订单
        selectedOrders: state => {
            return state.coordinator.orderService.selectedOrders
        },
    
        // 获取所有路线
        routes: state => {
            return state.coordinator.routeService.routes
        },
    
        // 获取活动路线
        activeRoutes: state => {
            return state.coordinator.routeService.getActiveRoutes()
        },
    
        // 获取所有司机
        drivers: state => {
            return state.coordinator.driverService.allDrivers
        },
    
        // 获取选中的司机
        selectedDriver: state => {
            return state.coordinator.driverService.selectedDriver
        }
    },

    actions: {
    // 加载所有数据
        async loadAllData() {
            this.isLoading = true
            this.error = null
      
            try {
                const result = await this.coordinator.loadAllData()
                this.isLoading = false
                return result
            } catch (error) {
                console.error('加载数据失败:', error)
                this.error = error.message
                this.isLoading = false
                throw error
            }
        },
    
        // 选择订单
        selectOrder(order) {
            return this.coordinator.orderService.selectOrder(order)
        },
    
        // 切换订单选择状态
        toggleOrderSelection(order) {
            return this.coordinator.orderService.toggleOrderSelection(order)
        },
    
        // 批量选择订单
        selectOrders(orderIds) {
            return this.coordinator.orderService.selectOrders(orderIds)
        },
    
        // 清除订单选择
        clearOrderSelection() {
            this.coordinator.orderService.clearSelection()
        },
    
        // 选择司机
        selectDriver(driver) {
            return this.coordinator.driverService.selectDriver(driver)
        },
    
        // 清除司机选择
        clearDriverSelection() {
            this.coordinator.driverService.clearSelectedDriver()
        },
    
        // 分配订单给司机
        async assignOrdersToDriver(orderIds, driverId) {
            this.isLoading = true
            this.error = null
      
            try {
                const result = await this.coordinator.assignOrdersToDriver(orderIds, driverId)
                // 清除订单选择
                this.coordinator.orderService.clearSelection()
                this.isLoading = false
                return result
            } catch (error) {
                console.error('分配订单失败:', error)
                this.error = error.message
                this.isLoading = false
                throw error
            }
        },
    
        // 从路线中移除订单
        async removeOrderFromRoute(orderId, driverId) {
            this.isLoading = true
            this.error = null
      
            try {
                const result = await this.coordinator.removeOrderFromRoute(orderId, driverId)
                this.isLoading = false
                return result
            } catch (error) {
                console.error('移除订单失败:', error)
                this.error = error.message
                this.isLoading = false
                throw error
            }
        },
    
        // 重新排序路线停靠点
        async reorderRouteStops(routeNumber, orderedOrderIds) {
            this.isLoading = true
            this.error = null
      
            try {
                const result = await this.coordinator.reorderRouteStops(routeNumber, orderedOrderIds)
                this.isLoading = false
                return result
            } catch (error) {
                console.error('重新排序路线失败:', error)
                this.error = error.message
                this.isLoading = false
                throw error
            }
        },
    
        // 完成路线
        async completeRoute(routeNumber) {
            this.isLoading = true
            this.error = null
      
            try {
                const result = await this.coordinator.completeRoute(routeNumber)
                this.isLoading = false
                return result
            } catch (error) {
                console.error('完成路线失败:', error)
                this.error = error.message
                this.isLoading = false
                throw error
            }
        },
    
        // 取消路线
        async cancelRoute(routeNumber) {
            this.isLoading = true
            this.error = null
      
            try {
                const result = await this.coordinator.cancelRoute(routeNumber)
                this.isLoading = false
                return result
            } catch (error) {
                console.error('取消路线失败:', error)
                this.error = error.message
                this.isLoading = false
                throw error
            }
        }
    }
}) 