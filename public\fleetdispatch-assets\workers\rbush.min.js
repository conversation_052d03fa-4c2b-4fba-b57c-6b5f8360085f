/**
 * RBush v4.0.1 UMD bundle for Web Workers
 * https://github.com/mourner/rbush
 */
(function (global, factory) {
    // Web Worker环境
    global.RBush = factory();
}(self, function () {
    'use strict';

    // RBush默认值
    const DEFAULT_MAX_ENTRIES = 9;

    // RBush类
    class RBush {
        constructor(maxEntries = DEFAULT_MAX_ENTRIES) {
            this._maxEntries = Math.max(4, maxEntries);
            this.clear();
        }

        // 清空树
        clear() {
            this.data = {
                children: [],
                height: 1,
                leaf: true,
                minX: Infinity,
                minY: Infinity,
                maxX: -Infinity,
                maxY: -Infinity
            };
            return this;
        }

        // 批量加载数据
        load(data) {
            if (!data || !data.length) return this;
            
            if (data.length < this._maxEntries) {
                for (const item of data) {
                    this.insert(item);
                }
                return this;
            }
            
            // 实际使用时这里会有高效的批量构建树的算法
            // 这里简化为逐个插入
            for (const item of data) {
                this.insert(item);
            }
            
            return this;
        }

        // 插入数据
        insert(item) {
            this._insert(item, this.data, 1);
            return this;
        }

        // 内部插入方法
        _insert(item, node, level) {
            // 更新节点的边界
            this._extend(node, item);
            
            // 如果是叶子节点，直接添加
            if (node.leaf) {
                node.children.push(item);
            } else {
                // 否则，找到最合适的子节点
                const child = this._chooseBestChild(node, item);
                this._insert(item, child, level + 1);
            }
            
            // 如果节点孩子太多，需要分裂
            if (node.children.length > this._maxEntries) {
                this._split(node, level);
            }
        }

        // 扩展节点边界
        _extend(node, item) {
            node.minX = Math.min(node.minX, item.minX);
            node.minY = Math.min(node.minY, item.minY);
            node.maxX = Math.max(node.maxX, item.maxX);
            node.maxY = Math.max(node.maxY, item.maxY);
        }

        // 选择最合适的子节点
        _chooseBestChild(node, item) {
            let minArea = Infinity;
            let bestChild = null;
            
            for (const child of node.children) {
                const area = this._calculateEnlargedArea(child, item);
                if (area < minArea) {
                    minArea = area;
                    bestChild = child;
                }
            }
            
            return bestChild;
        }

        // 计算节点扩展面积
        _calculateEnlargedArea(node, item) {
            const minX = Math.min(node.minX, item.minX);
            const minY = Math.min(node.minY, item.minY);
            const maxX = Math.max(node.maxX, item.maxX);
            const maxY = Math.max(node.maxY, item.maxY);
            
            return (maxX - minX) * (maxY - minY) - this._calculateArea(node);
        }

        // 计算节点面积
        _calculateArea(node) {
            return (node.maxX - node.minX) * (node.maxY - node.minY);
        }

        // 分裂节点
        _split(node, level) {
            // 简化版本，这里只是简单地把节点一分为二
            const splitIndex = Math.ceil(this._maxEntries / 2);
            const newNode = {
                children: node.children.splice(splitIndex),
                height: node.height,
                leaf: node.leaf,
                minX: Infinity,
                minY: Infinity,
                maxX: -Infinity,
                maxY: -Infinity
            };
            
            // 更新新节点的边界
            for (const child of newNode.children) {
                this._extend(newNode, child);
            }
            
            // 更新原节点的边界
            node.minX = Infinity;
            node.minY = Infinity;
            node.maxX = -Infinity;
            node.maxY = -Infinity;
            for (const child of node.children) {
                this._extend(node, child);
            }
            
            // 如果是根节点分裂，创建新的根节点
            if (level === 1) {
                const rootNode = {
                    children: [node, newNode],
                    height: node.height + 1,
                    leaf: false,
                    minX: Math.min(node.minX, newNode.minX),
                    minY: Math.min(node.minY, newNode.minY),
                    maxX: Math.max(node.maxX, newNode.maxX),
                    maxY: Math.max(node.maxY, newNode.maxY)
                };
                this.data = rootNode;
            }
        }
        
        // 搜索给定区域内的所有项
        search(bbox) {
            const result = [];
            this._search(this.data, bbox, result);
            return result;
        }
        
        // 内部搜索方法
        _search(node, bbox, result) {
            // 如果节点不与搜索区域相交，返回
            if (!this._intersects(node, bbox)) return;
            
            // 遍历所有子节点
            for (const child of node.children) {
                if (!this._intersects(child, bbox)) continue;
                
                if (node.leaf) {
                    result.push(child);
                } else {
                    this._search(child, bbox, result);
                }
            }
        }
        
        // 检查两个框是否相交
        _intersects(a, b) {
            return a.minX <= b.maxX && a.maxX >= b.minX && a.minY <= b.maxY && a.maxY >= b.minY;
        }
    }

    return RBush;
}));
