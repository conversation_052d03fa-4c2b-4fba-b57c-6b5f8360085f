const Layout = () => import('@/layout/index.vue')

export default {
    path: '/delivery/services',
    component: Layout,
    redirect: '/delivery/services',
    name: 'deliveries',
    meta: {
        title: '服务设置',
        icon: 'carbon:cloud-satellite-services',
        auth: ['super'],
        i18n: 'route.delivery.services.title'
    },
    children: [
        {
            path: 'package-types',
            name: 'deliveryPackageTypes',
            component: () => import('@/views/delivery/services/package_types.vue'),
            meta: {
                title: '包裹类型',
                icon: 'icon-park-outline:multi-ring',
                i18n: 'route.delivery.services.packageTypes',
                activeMenu: '/delivery/services/package-types',
                auth: ['super']
            }
        },
        {
            path: 'size-weights',
            name: 'deliveryPackageSizeWeights',
            component: () => import('@/views/delivery/services/size_weights.vue'),
            meta: {
                title: '体积重量',
                icon: 'tabler:ruler-measure',
                i18n: 'route.delivery.services.sizeWeights',
                activeMenu: '/delivery/services/size-weights',
                auth: ['super']
            }
        },
        {
            path: 'added-services',
            name: 'deliveryAddedServices',
            component: () => import('@/views/delivery/services/added_services.vue'),
            meta: {
                title: '增值服务',
                icon: 'icon-park-outline:resting',
                i18n: 'route.delivery.services.addedServices',
                activeMenu: '/delivery/service-packs/added-services',
                auth: ['super']
            }
        },
        // {
        //     path: 'packaging',
        //     name: 'deliveryPackaging',
        //     component: () => import('@/views/delivery/services/packaging.vue'),
        //     meta: {
        //         title: '包装材料',
        //         icon: 'icon-park-outline:reverse-lens-one',
        //         i18n: 'route.delivery.services.packaging',
        //         activeMenu: '/delivery/services/packaging',
        //         auth: ['super']
        //     }
        // },
    ]
}
