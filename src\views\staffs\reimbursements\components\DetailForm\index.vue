<script setup>
    import { getPaymentMethods } from '@/api/modules/open';
    import { getReimbursements, getDrivers, addReimbursement, updateReimbursement, deleteReimbursement, reviewReimbursement } from '@/api/modules/staffs'
    import EmployeeSelector from '../../../components/employee_selector.vue'

    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            date: null,
            employee: null,
            purpose: null,
            amount: 0,
            receipt_number: null,
            payment_method: null,
            vendor: null,
            can_update: true
        },
        rules: {
            date: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            employee: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            amount: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            payment_method: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
        }
    })

    const drivers = ref([])
    const paymentMethods = ref([])
    const amount = ref(0.0)
    const hst = ref(0.0)
    const total = computed(() => amount.value + hst.value)

    onMounted(() => {
        getDriverList()
        getPaymentMethodList()
        if (data.value.form.id != '') {
            getInfo()
        }
    })

    function getInfo() {
        data.value.loading = true
        getReimbursements({ id: data.value.form.id }).then(res => {
            data.value.loading = false
            data.value.form = res.data
            amount.value = Number((res.data.amount / 100).toFixed(2))
            hst.value = Number((res.data.hst / 100).toFixed(2))
        })
    }
    function getDriverList() {
        getDrivers().then(res => {
            drivers.value = res.data;
        })
    }
    function getPaymentMethodList() {
        getPaymentMethods().then(res => {
            paymentMethods.value = res.data;
        })
    }

    defineExpose({
        submit(callback) {
            let params = JSON.parse(JSON.stringify(data.value.form))
            params.amount = Math.round(amount.value * 100)
            params.hst = Math.round(hst.value * 100)
            if (data.value.form.id == '') {
                formRef.value.validate(valid => {
                    if (valid) {
                        addReimbursement(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            } else {
                formRef.value.validate(valid => {
                    if (valid) {
                        updateReimbursement(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            }
        },
        reset() {
            getInfo();
        },
        async approve(callBack) {
            await onReview('approve');
            callBack && callBack();
        },
        async reject(callBack) {
            await onReview('reject');
            callBack && callBack();
        },
    })

    async function onReview(operation) {
        let params = {
            id: data.value.form.id,
            operation: operation
        }
        reviewReimbursement(params).then(res => {
            if (res.data.errCode == 365) {
                getInfo();
            }
        })
    }
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="150px">
            <el-form-item :label="$t('fields.date')" prop="date">
                <el-date-picker v-model="data.form.date" type="date" placeholder="Pick a day" value-format="YYYY-MM-DD"
                    :disabled="!data.form.can_update" />
            </el-form-item>
            <el-form-item :label="$t('staffs.fields.employee')" prop="employee">
                <EmployeeSelector v-model="data.form.employee" :disabled="data.form.id != ''" />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" :disabled="!data.form.can_update" />
            </el-form-item>
            <el-form-item :label="$t('staffs.reimbursement.fields.purpose')" prop="purpose">
                <el-input v-model="data.form.purpose" placeholder="请输入标题" :disabled="!data.form.can_update" />
            </el-form-item>
            <el-form-item :label="$t('fields.preTax')" prop="amount">
                <el-input-number v-model="amount" placeholder="请输入标题" :min="0" :precision="2"
                    :disabled="!data.form.can_update" />
            </el-form-item>
            <el-form-item :label="$t('fields.tax')" prop="hst">
                <el-input-number v-model="hst" placeholder="请输入标题" :min="0" :precision="2"
                    :disabled="!data.form.can_update" />
            </el-form-item>
            <el-form-item :label="$t('fields.total')" prop="total">
                <el-text tag="b">{{ total.toFixed(2) }}</el-text>
            </el-form-item>
            <el-form-item :label="$t('staffs.reimbursement.fields.receiptNumber')" prop="receipt_number">
                <el-input v-model="data.form.receipt_number" placeholder="请输入标题" :disabled="!data.form.can_update" />
            </el-form-item>
            <el-form-item :label="$t('staffs.fields.paymentMethod')" prop="payment_method">
                <el-select v-model="data.form.payment_method" placeholder="Select" :disabled="!data.form.can_update">
                    <el-option v-for="item in paymentMethods" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('staffs.reimbursement.fields.vendor')" prop="vendor">
                <el-input v-model="data.form.vendor" placeholder="请输入标题" :disabled="!data.form.can_update" />
            </el-form-item>
            <el-form-item :label="$t('fields.photos')" prop="photos">
                <el-space>
                    <el-image v-for="photo of data.form.photos" style="width: 100px; height: 100px" :src="photo.url"
                        :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                        :preview-src-list="data.form.photos.map(e => e.url)" :initial-index="4" fit="cover" />
                </el-space>
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
    // scss</style>