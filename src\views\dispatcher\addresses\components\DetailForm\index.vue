<script setup>
import { updateAddressFee } from '@/api/modules/dispatcher';
const props = defineProps({
    row: {
        type: Object,
        default: { Id: '' }
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: props.row,
    fee: props.row.fee / 100,
    rules: {
        fee: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})


defineExpose({
    submit(callback) {
        formRef.value.validate(valid => {
            if (valid) {
                let params = {
                    id: data.value.form.Id,
                    fee: Math.round(data.value.fee * 100)
                }
                updateAddressFee(params).then((res) => {
                    if (res.data.errCode == 365) { callback && callback() }
                })
            }
        })
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('user.fields.address')" prop="Line1">
                <el-input v-model="data.form.Line1" placeholder="请输入标题" disabled />
            </el-form-item>
            <el-form-item :label="$t('user.fields.postalCode')" prop="PostalCode">
                <el-input v-model="data.form.PostalCode" placeholder="请输入标题" disabled />
            </el-form-item>
            <el-form-item :label="$t('user.fields.city')" prop="City">
                <el-input v-model="data.form.City" placeholder="请输入标题" disabled />
            </el-form-item>
            <el-form-item :label="$t('user.fields.province')" prop="ProvinceName">
                <el-input v-model="data.form.ProvinceName" placeholder="请输入标题" disabled />
            </el-form-item>
            <el-form-item :label="$t('fields.fee')" prop="fee">
                <el-input-number v-model="data.fee" placeholder="请输入标题" :min="0" :step="0.1" :precision="2" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
