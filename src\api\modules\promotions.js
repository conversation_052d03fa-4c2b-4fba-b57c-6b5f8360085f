/*
* Author: <EMAIL>'
* Date: '2023-06-30 21:05:02'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/promotions.js'
* File: 'promotions.js'
* Version: '1.0.0'
*/

import { DEL, GET, PAT, POST, PUT, DL } from "../methods";

const path = 'promotions/'

export const getEOPPromotionOrders = params => POST(path + 'same-patch/', params)
export const executeEOPPromotions = params => POST(path + 'same-patch/execute/', params)

export const getDiscounts = params => GET(path + 'discounts/', params)
export const createUpdateDiscount = params => POST(path + 'discounts/', params)
export const updateDiscountAvailability = params => PAT(path + 'discounts/', params)
export const deleteDiscount = params => DEL(path + 'discounts/', params)
export const getCouponExclusiveDiscounts = () => GET(path + 'discounts/coupon-exclusive/')


const couponPath = path + 'coupons/'
export const getCoupons = params => GET(couponPath, params)
export const createCoupon = params => POST(couponPath, params)
export const updateCoupon = params => PUT(couponPath, params)
export const updateCouponsAvailability = params => PAT(couponPath, params)
export const deleteCoupons = params => DEL(couponPath, params)
export const batchCreateCoupons = params => POST(couponPath + 'batch-add/', params)

export const closeCoupon = params => GET(couponPath + `close/${params.id}/`)

export const getAllTags = () => GET(couponPath + 'tags/')
export const batchAddTag = params => POST(couponPath + 'tags/batch-add/', params)
export const exportCouponsToExcel = params => POST(couponPath + 'export/excel/', params)

// Logs
const logPath = path + 'logs/'
export const getDiscountLogs = (params) => GET(logPath + 'discount/', params);
export const getDiscountUsageLogs = (params) => GET(logPath + 'discount/usage/', params);
export const getCouponUsageLogs = (params) => GET(logPath + 'coupon/usage/', params);
export const exportExcelCouponUsageLogs = (params) => POST(logPath + 'coupon/usage/export/excel/', params);
