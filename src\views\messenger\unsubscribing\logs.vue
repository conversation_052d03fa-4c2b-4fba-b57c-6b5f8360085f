<script setup name="MessengerUnsubscribingLogs">
import { usePagination } from '@/utils/composables'
import { getUnsubscribingLogs } from '@/api/modules/messenger'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        email__icontains: null,
        phone_number__icontains: null,
        category__name__icontains: null,
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        email__icontains: null,
        phone_number__icontains: null,
        category__name__icontains: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: data.value.search
        }
    )
    getUnsubscribingLogs(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.log_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

</script>

<template>
    <div>
        <page-header title="默认模块" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.email')">
                                        <el-input v-model="data.search.email__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.email') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.phone')">
                                        <el-input v-model="data.search.phone_number__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.phone') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.category')">
                                        <el-input v-model="data.search.category__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.category') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @sort-change="sortChange">
                <el-table-column type="index" align="center" fixed />
                <el-table-column prop="email" :label="$t('delivery.addressBook.fields.email')" />
                <el-table-column prop="phone_number" :label="$t('delivery.addressBook.fields.phone')" />
                <el-table-column prop="category" :label="$t('fields.category')" />
                <el-table-column prop="operator" :label="$t('fields.operator')" />
                <el-table-column prop="operation" :label="$t('fields.operation')" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: #bbb;
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>