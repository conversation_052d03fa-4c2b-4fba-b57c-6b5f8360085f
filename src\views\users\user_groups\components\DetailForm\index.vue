<script setup>
import { getUserGroups, addUserGroup, updateUserGroup } from '@/api/modules/users';
import { currencyFormatter } from '@/utils/formatter'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: null,
        fee: null,
        fee_deduction_rate: 0,
    },
    rules: {
        name: [
            { required: true, message: t('placeholder', { field: t('fields.name') }), trigger: 'blur' }
        ],
        fee: [
            { required: true, message: t('placeholder', { field: t('user.userGroup.fields.fee') }), trigger: 'blur' }
        ],
    }
})

const tempData = ref({
    fee: 0.0,
    rate: 0.0
})

watch(() => tempData.value.fee, val => {
    data.value.form.fee = Math.round(val * 100)
})

watch(() => tempData.value.rate, val => {
    data.value.form.fee_deduction_rate = Math.round(val * 100)
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getUserGroups({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        tempData.value.fee = Number(currencyFormatter(null, null, res.data.fee))
        tempData.value.rate = Number(currencyFormatter(null, null, res.data.fee_deduction_rate))
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addUserGroup(data.value.form).then((res) => {
                        if (res.data.errCode === 365) {
                            callback && callback()
                        }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateUserGroup(data.value.form).then((res) => {
                        if (res.data.errCode === 365) {
                            callback && callback()
                        }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" :placeholder="$t('placeholder', { field: $t('fields.name') })" />
            </el-form-item>
            <el-form-item :label="$t('user.userGroup.fields.fee')" prop="fee">
                <el-input-number v-model="tempData.fee"
                    :placeholder="$t('placeholder', { field: $t('user.userGroup.fields.fee') })" :min="0" :step="0.1"
                    :precision="2" />
            </el-form-item>
            <el-form-item :label="$t('user.userGroup.fields.deductionRate')" prop="fee_deduction_rate">
                <el-input-number v-model="tempData.rate"
                    :placeholder="$t('placeholder', { field: $t('user.userGroup.fields.deductionRate') })" :min="0"
                    :step="1" />
                &ThickSpace; %
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
