<script setup>
    import { usePagination } from '@/utils/composables'
    import { getDeliveryStubs } from '@/api/modules/staffs'
    import { currencyFormatter } from '@/utils/formatter';

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

    const props = defineProps({
        employee: {
            type: String,
            default: null
        },
        dateMin: {
            type: String,
            default: null
        },
        dateMax: {
            type: String,
            default: null
        },
        modelValue: {
            type: Array,
            default: []
        },
        all: {
            type: Boolean,
            default: false,
        },
        defaultSelected: {
            type: Array,
            default: []
        }
    })

    defineExpose({
        fetch(callback) { if (props.employee) sizeChange(defaultPageSize); },
        reset(callBack) { onReset() },
    })

    const table = ref()


    const emit = defineEmits(['update:modelValue'])

    const defaultPageSize = 20
    const pageSizes = [20, 50, 100]

    onMounted(() => {
        pagination.value.size = defaultPageSize
    })

    watch(() => props.employee, (val) => data.value.search.worksheet__employee__pk = val)
    watch(() => props.dateMin, (val) => data.value.search.date__gte = val)
    watch(() => props.dateMax, (val) => data.value.search.date__lte = val)
    const data = ref({
        loading: false,
        dataList: [],
        search: {
            worksheet__employee__pk: props.employee,
            date__gte: props.dateMin,
            date__lte: props.dateMax,
            statements__isnull: props.all ? null : true,
        },
    })

    function onReset() {
        data.value.search = {
            worksheet__employee__pk: null,
            date__gte: null,
            date__lte: null,
            statements__isnull: props.all ? null : true,
        }
        data.value.dataList.length = 0;
        selectionDataList.length = 0;
    }

    let selectionDataList = computed({
        get: function () {
            return props.modelValue
        },
        set: function (val) {
            emit('update:modelValue', val)
        }
    })



    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getDeliveryStubs(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.stub_list
            pagination.value.total = res.data.total
            nextTick().then(() => {
                if (props.defaultSelected.length > 0) {
                    for (let s of props.defaultSelected) {
                        let found = table.value.data.find(e => e.id == s.id)
                        if (found != undefined) { table.value.toggleRowSelection(found, true) }
                    }
                }
            })

        })
    }

    function setSelected() {

    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

</script>

<template>
    <el-table ref="table" v-loading="data.loading" :data="data.dataList" stripe highlight-current-row
        @sort-change="sortChange" @selection-change="selectionDataList = $event">
        <el-table-column type="selection" align="center" fixed />
        <el-table-column type="index" align="center" fixed width="50" />
        <el-table-column prop="employee" :label="$t('staffs.fields.employee')" />
        <el-table-column prop="date" :label="$t('fields.date')" />
        <el-table-column prop="hst" :label="$t('staffs.stub.fields.tax')" :formatter="currencyFormatter" />
        <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter" />
        <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
        <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
    </el-table>
    <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="defaultPageSize"
        :page-sizes="pageSizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background
        @size-change="sizeChange" @current-change="currentChange" />
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }

    .bold {
        font-weight: bolder;
    }
</style>