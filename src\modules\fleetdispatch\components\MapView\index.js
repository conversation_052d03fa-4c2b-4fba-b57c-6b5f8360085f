// src/modules/fleetdispatch/components/MapView/index.js

// 导出主组件
export { default as MapView } from './MapViewNew.vue';
export { default as Marker } from './Marker.vue';
export { default as MarkerPopup } from './MarkerPopup.vue';

// 导出子组件
export { default as DriverMarker } from './components/DriverMarker.vue';
export { default as DriverMarkerPopup } from './components/DriverMarkerPopup.vue';

// 导出可组合式函数
export { useMapInitialization } from './composables/useMapInitialization';
export { useMarkerManagement } from './composables/useMarkerManagement';
export { useDriverMarkers } from './composables/useDriverMarkers';
export { useRouteManagement } from './composables/useRouteManagement';
export { useMapInteractions } from './composables/useMapInteractions';
export { useTrafficLayer } from './composables/useTrafficLayer';
export { usePerformanceMonitor } from './composables/usePerformanceMonitor'; 