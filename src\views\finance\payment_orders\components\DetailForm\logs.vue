<script setup>
import { usePagination } from '@/utils/composables'
import { getPaymentOrderLogs } from '@/api/modules/payment';
import { currencyFormatter, snakeToCamel } from '@/utils/formatter'
import { useI18n } from 'vue-i18n';
const { te } = useI18n()

const { pagination, getParams, onCurrentChange } = usePagination()

const props = defineProps({
    orderId: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
    dataList: [],
})

const logStyles = {
    created: {
        type: 'success'
    },
    updated: {
        type: 'primary',
        hollow: true
    },
    closed: {
        type: 'warning',
        hollow: true
    },
    paying: {
        type: 'success',
        hollow: true
    },
    paid: {
        type: 'success',
    },
    refunding: {
        type: 'warning',
    },
    partiallyRefunded: {
        type: 'warning',
        hollow: true
    },
    refunded: {
        type: 'warning',
    },
    completed: {
        type: 'primary',
    },
}

defineExpose({
    reload() {
        getDataList()
    }
})


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams({ paymentOrderId: props.orderId })
    getPaymentOrderLogs(params).then(res => {
        pagination.value.total = res.data.total
        data.value.dataList = res.data.log_list
        data.value.loading = false
    })
}

function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

</script>
<template>
    <el-timeline v-if="data.dataList.length">
        <el-timeline-item v-for="(item, index) in data.dataList" :key="index" :timestamp="item.created_at"
            :type="logStyles[item.operation]?.type" :hollow="logStyles[item.operation]?.hollow" placement="top">
            <el-space direction="vertical" alignment="start" size="large" class="logs">
                <el-space>
                    <el-tag :type="logStyles[item.operation]?.type" size="small" round>
                        {{ $t(`finance.paymentOrder.selections.logStatus.${item.operation}`) }}
                    </el-tag>
                    <span class="operator">{{ item.operator }}</span>
                </el-space>
                <el-descriptions :column="1" v-if="item.record">
                    <el-descriptions-item :label="$t('fields.status')" label-align="right" label-class-name="log-label"
                        class-name="log-content" v-if="item.record.status">
                        <el-space>
                            <span>
                                {{
                                    te(`finance.paymentOrder.selections.status.${item.record.status.prev}`) ?
                                    $t(`finance.paymentOrder.selections.status.${item.record.status.prev}`) :
                                    item.record.status.prev
                                }}
                            </span>
                            <el-icon>
                                <svg-icon name="ep:right" />
                            </el-icon>
                            <span>
                                {{
                                    $t(`finance.paymentOrder.selections.status.${item.record.status.curr}`)
                                }}
                            </span>
                        </el-space>
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('finance.paymentOrder.fields.refunded')" label-align="right"
                        label-class-name="log-label" v-if="item.record.refunded_amount" class-name="log-content">
                        <el-space>
                            <span>{{ currencyFormatter(_, _, item.record.refunded_amount.prev) }}</span>
                            <el-icon>
                                <svg-icon name="ep:right" />
                            </el-icon>
                            <span>{{ currencyFormatter(_, _, item.record.refunded_amount.curr) }}</span>
                        </el-space>
                    </el-descriptions-item>
                    <template v-for="(value, key) of item.record">
                        <el-descriptions-item :label="$t(`finance.paymentOrder.fields.${snakeToCamel(key)}`)"
                            label-align="right" label-class-name="log-label" class-name="log-content"
                            v-if="!['status', 'refunded_amount'].includes(key)">
                            <el-space>
                                <span>{{ value.prev }}</span>
                                <el-icon>
                                    <svg-icon name="ep:right" />
                                </el-icon>
                                <span>{{ value.curr }}</span>
                            </el-space>
                        </el-descriptions-item>
                    </template>
                </el-descriptions>
            </el-space>
        </el-timeline-item>
    </el-timeline>
    <el-empty v-else :description="$t('noData')" />
    <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
        :layout="pagination.layoutM" :hide-on-single-page="true" class="pagination" @current-change="currentChange" />
</template>
