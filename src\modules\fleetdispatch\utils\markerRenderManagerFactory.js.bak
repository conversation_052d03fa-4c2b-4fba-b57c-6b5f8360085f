/**
 * 标记渲染管理器工厂
 * 用于创建统一配置的标记渲染管理器实例
 */

import { MarkerRenderManager } from './markerRenderManager';

/**
 * 创建标记渲染管理器实例
 * @param {Object} map MapLibre地图对象 
 * @param {Object} eventBus 事件总线
 * @param {Object} options 可选配置项
 * @param {Object} options.driverStore 驱动数据存储
 * @returns {MarkerRenderManager} 标记渲染管理器实例
 */
export function createMarkerRenderManager(map, eventBus, options = {}) {
  if (!map) {
    console.error('创建标记渲染管理器失败: 缺少地图对象');
    return null;
  }
  
  if (!eventBus) {
    console.error('创建标记渲染管理器失败: 缺少事件总线');
    return null;
  }
  
  // 创建标记渲染管理器实例
  const renderManager = new MarkerRenderManager(map, eventBus, options);
  
  // 确保驱动数据存储被正确传递
  if (options.driverStore) {
    renderManager.setDriverStore(options.driverStore);
  }
  
  console.log('标记渲染管理器已创建');
  return renderManager;
}

// 导出渲染模式，便于使用
export { RENDER_MODES } from './markerRenderManager'; 