import { GET, POST, PUT, PAT, DEL, UPL } from "../methods"



const path = 'staffs/'
const commonPath = path + 'common/';
const deliveryPath = path + 'delivery/';
// Employees
const employeePath = commonPath + 'employees/'
const driverPath = deliveryPath + 'drivers/'
export const getDriverList = params => GET(employeePath, params)
export const addDriver = params => POST(employeePath, params)
export const updateDriver = params => PUT(employeePath, params)
export const updateDriverStatus = params => PAT(employeePath, params)
export const deleteDriver = params => DEL(employeePath, params)
export const validateDriver = id => GET(employeePath + `validate/${id}/`)
export const invalidateDriver = id => GET(employeePath + `invalidate/${id}/`)
export const suspendDriver = id => GET(employeePath + `suspend/${id}/`)
export const resetDriverPassword = params => POST(employeePath + `reset-password/${params.id}/`, params)
export const getDrivers = (params = {}) => GET(employeePath + 'all/', params)
export const getValidDrivers = () => GET(employeePath + 'valid/')
export const getAvailableDrivers = () => GET(driverPath + 'available/')
export const clearDriverBuckets = () => GET(driverPath + 'clear-buckets/')


// Worksheets
const worksheetPath = commonPath + 'worksheets/'
export const getWorksheets = params => GET(worksheetPath, params)
export const addWorksheet = params => POST(worksheetPath, params)
export const updateWorksheet = params => PUT(worksheetPath, params)
export const deleteWorksheet = params => DEL(worksheetPath, params)
export const uploadWorksheetPhoto = (params, data) => UPL(worksheetPath + 'photos/upload/', params, data)
export const removeWorksheetPhoto = params => DEL(worksheetPath + 'photos/remove/', params)

// Stubs
const stubPath = commonPath + 'stubs/'
export const getDeliveryStubs = params => GET(stubPath, params)
export const addDeliveryStub = params => POST(stubPath, params)
export const updateDeliveryStub = params => PUT(stubPath, params)
export const deleteDeliveryStub = params => DEL(stubPath, params)
export const getUnreportedSalaries = () => GET(stubPath + 'unreported/')


// Reimbursements
const reimbursementPath = commonPath + 'reimbursement/'
export const getReimbursements = params => GET(reimbursementPath, params);
export const getReportableReimbursements = () => GET(reimbursementPath + 'reportable/');
export const addReimbursement = params => POST(reimbursementPath, params);
export const updateReimbursement = params => PUT(reimbursementPath, params);
export const deleteReimbursement = params => DEL(reimbursementPath, params);
export const reviewReimbursement = params => GET(reimbursementPath + `review/${params.id}/`, params);

// Payment Statements
const statementPath = commonPath + 'payment-statements/'
export const getStatements = params => GET(statementPath, params)
export const addStatement = params => POST(statementPath, params)
export const updateStatement = params => PUT(statementPath, params)
export const updateStatementPaymentInfo = params => PAT(statementPath, params)
export const deleteStatement = params => DEL(statementPath, params)
export const calculateStatement = id => GET(statementPath + `calculate/${id}/`)
export const completeStatement = id => GET(statementPath + `complete/${id}/`)
export const downloadStatement = id => GET(statementPath + `download/${id}/`)
export const sendStatement = id => GET(statementPath + `send/${id}/`)
