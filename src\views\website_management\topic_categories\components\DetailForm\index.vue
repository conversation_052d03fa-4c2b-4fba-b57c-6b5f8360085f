<script setup>
import api from '@/api'
import { getTopicCategories, addTopicCategory, updateTopicCategory } from '@/api/modules/topics'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getTopicCategories({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addTopicCategory(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateTopicCategory(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('topics.categories.fields.isPrivacyPolicy')" prop="is_privacy_policy">
                <el-switch v-model="data.form.is_privacy_policy" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('topics.categories.fields.isTermsConditions')" prop="is_terms_and_conditions">
                <el-switch v-model="data.form.is_terms_and_conditions" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.isAvailable')" prop="is_available">
                <el-switch v-model="data.form.is_available" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
