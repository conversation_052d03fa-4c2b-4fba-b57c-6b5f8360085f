const Layout = () => import('@/layout/index.vue')

export default {
    path: '/payment/orders',
    component: Layout,
    redirect: '/payment/orders/index',
    name: 'paymentOrders',
    meta: {
        title: '订单管理',
        icon: 'icon-park-solid:transaction-order',
        auth: ['super'],
        i18n: 'route.finance.orders.title'
    },
    children: [
        {
            path: 'index',
            name: 'paymentOrderStatistics',
            component: () => import('@/views/finance/payment_orders/index.vue'),
            meta: {
                title: '订单统计',
                icon: 'material-symbols:bar-chart-4-bars-rounded',
                i18n: 'route.finance.orders.statistics',
                activeMenu: '/payment/orders/index',
                auth: ['super']
            }
        },
        {
            path: 'list',
            name: 'paymentOrderList',
            component: () => import('@/views/finance/payment_orders/list.vue'),
            meta: {
                title: '支付订单',
                icon: 'icon-park-outline:transaction-order',
                i18n: 'route.finance.orders.orders',
                activeMenu: '/payment/orders/list',
                auth: ['super'],
                cache: ['paymentOrderDetail']

            }
        },
        {
            path: 'detail',
            name: 'paymentOrderDetail',
            component: () => import('@/views/finance/payment_orders/detail.vue'),
            meta: {
                title: '支付订单',
                icon: 'icon-park-outline:transaction-order',
                i18n: 'route.finance.orders.orders',
                activeMenu: '/payment/orders/list',
                auth: ['super'],
                sidebar: false,
            }
        },

        {
            path: 'cc',
            name: 'ccOrders',
            component: () => import('@/views/finance/payment_orders/cc_list.vue'),
            meta: {
                title: '信用卡订单',
                icon: 'bx:credit-card-alt',
                i18n: 'route.finance.orders.cc',
                activeMenu: '/payment/orders/cc',
                auth: ['super']
            }
        },
        {
            path: 'ap',
            name: 'apOrders',
            component: () => import('@/views/finance/payment_orders/ap_list.vue'),
            meta: {
                title: 'Alphapay订单',
                icon: 'icon-park-outline:pay-code-two',
                i18n: 'route.finance.orders.ap',
                activeMenu: '/payment/orders/ap',
                auth: ['super']
            }
        },

    ]
}
