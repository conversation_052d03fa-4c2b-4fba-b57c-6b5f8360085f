<script setup>
import { getSettingHolidays, addSettingHoliday, updateSettingHoliday } from '@/api/modules/settings';
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        name: [
            { required: true, message: t('placeholder', { field: t('fields.name') }), trigger: 'blur' }
        ],
        start_at: [
            { required: true, message: t('placeholder', { field: t('settings.holiday.fields.startAt') }), trigger: 'blur' }
        ],
        end_at: [
            { required: true, message: t('placeholder', { field: t('settings.holiday.fields.endAt') }), trigger: 'blur' }
        ],
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getSettingHolidays({ id: data.value.form.id }
    ).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addSettingHoliday(data.value.form).then((res) => {
                        if (res.data.errCode == 365) {
                            callback && callback()
                        }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateSettingHoliday(data.value.form).then((res) => {
                        if (res.data.errCode == 365) {
                            callback && callback()
                        }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" show-word-limit maxlength="50" />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" show-word-limit
                    maxlength="200" />
            </el-form-item>
            <el-form-item :label="$t('settings.holiday.fields.startAt')" prop="start_at">
                <el-date-picker v-model="data.form.start_at" type="date" placeholder="Pick a day"
                    value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item :label="$t('settings.holiday.fields.endAt')" prop="end_at">
                <el-date-picker v-model="data.form.end_at" type="date" placeholder="Pick a day"
                    value-format="YYYY-MM-DD" />
            </el-form-item>
        </el-form>
    </div>
</template>
