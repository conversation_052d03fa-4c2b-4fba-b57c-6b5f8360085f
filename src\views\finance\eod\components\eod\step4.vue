<script setup>
import { addDeliveryReport, getDeliveryReports } from '@/api/modules/statistics'
import { currencyFormatter } from '@/utils/formatter'
import StepButtons from './step_buttons.vue'
const route = useRoute()


const props = defineProps({
    date: {
        type: String,
        default: ''
    }

})
const emit = defineEmits(['setDeliveryReport'])

const deliveryReport = ref({})

onMounted(() => {
    if (props.date != '') { createDeliveryReport() } else { getData() }
})

async function createDeliveryReport() {
    let params = { date: props.date }
    let res = await addDeliveryReport(params)
    if (res.data.errCode == null || res.data.errCode == 365) {
        emit('setDeliveryReport', res.data.id)
        getDeliveryReports({ id: res.data.id }).then(res => {
            deliveryReport.value = res.data
        })
    }
}

function getData() {
    getDeliveryReports({ id: route.query.id }).then(res => {
        deliveryReport.value = res.data
    })
}
</script>
<template>
    <el-descriptions :title="$t('finance.eod.s4')" :column="3" border style="margin-bottom: 20px;">
        <el-descriptions-item :label="$t('fields.date')" label-align="left" align="center" :span="3">
            {{ deliveryReport.date }}
        </el-descriptions-item>

        <el-descriptions-item :label="$t('delivery.orders.fields.deliveryFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.delivery_fee) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('delivery.orders.fields.quantity')" label-align="left" align="center">
            {{ deliveryReport.packages }}
        </el-descriptions-item>

        <el-descriptions-item :label="$t('statistics.delivery.fields.averagePU')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.delivery_fee / deliveryReport.packages) }}
        </el-descriptions-item>

        <el-descriptions-item :label="$t('delivery.orders.fields.typeFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.type_fee) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('delivery.orders.fields.sizeFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.size_fee) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('delivery.orders.fields.expressFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.express_fee) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('delivery.orders.fields.timeFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.time_fee) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('delivery.orders.fields.weekdayFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.weekday_fee) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('delivery.orders.fields.holidayFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.holiday_fee) }}
        </el-descriptions-item>

        <el-descriptions-item :label="$t('delivery.orders.fields.zoneFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport?.zone_fee ?? 0 + delivery?.pc_fee ?? 0 + delivery?.address_fee ??
        0)
            }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('delivery.orders.fields.addedServicesFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.added_services_fee) }}
        </el-descriptions-item>
        <!-- <el-descriptions-item :label="$t('delivery.orders.fields.insuranceFee')" label-align="left" align="center">
            {{ currencyFormatter(_, _, deliveryReport.insurance_fee) }}
        </el-descriptions-item> -->
        <el-descriptions-item :label="$t('delivery.orders.fields.promotionalDeduction')" label-align="left"
            align="center">
            {{ currencyFormatter(_, _, deliveryReport.promotional_deduction) }}
        </el-descriptions-item>
    </el-descriptions>
    <el-descriptions :column="5" border style="margin-bottom: 20px;">
        <el-descriptions-item label-align="left" align="center" :label="$t('statistics.delivery.fields.paidAmount')">
            {{ currencyFormatter(_, _, deliveryReport.total_paid_amount) }}
        </el-descriptions-item>
        <el-descriptions-item label-align="left" align="center" :label="$t('statistics.delivery.fields.unpaidAmount')">
            {{ currencyFormatter(_, _, deliveryReport.total_unpaid_amount) }}
        </el-descriptions-item>
        <el-descriptions-item label-align="left" align="center" :label="$t('statistics.delivery.fields.refundAmount')">
            {{ currencyFormatter(_, _, deliveryReport.total_refund_amount) }}
        </el-descriptions-item>
        <el-descriptions-item label-align="left" align="center" :label="$t('statistics.delivery.fields.tip')">
            {{ currencyFormatter(_, _, deliveryReport.total_tip) }}
        </el-descriptions-item>
        <el-descriptions-item label-align="left" align="center" :label="$t('fields.total')">
            <el-text tag="b">{{ currencyFormatter(_, _, deliveryReport.total_income) }}</el-text>
        </el-descriptions-item>
    </el-descriptions>
    <el-descriptions :column="3" border style="margin-bottom: 20px;">
        <el-descriptions-item v-for="service in deliveryReport.services" :span="1" :label="service.services"
            label-align="left" align="center">
            <el-space>
                <el-text tag="b">
                    {{ service.count }}
                </el-text>
                <el-text>
                    ({{ service.packages }})
                </el-text>
            </el-space>
        </el-descriptions-item>
        <el-descriptions-item label-align="left" align="center" />
    </el-descriptions>
    <StepButtons v-bind="$attrs" />
</template>