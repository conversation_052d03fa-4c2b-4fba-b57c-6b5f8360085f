import { GET, POST, PUT, PAT, DEL } from "../methods";

const path = 'messenger/'


export const getCategories = (params) => GET(path + 'categories/', params);
export const getCategoryList = () => GET(path + 'categories/list/');

// FCM
const messagesPath = path + 'messages/'
export const getMessages = (params) => GET(messagesPath, params);

export const addMessage = (params) => POST(messagesPath, params);
export const updateMessage = (params) => PUT(messagesPath, params);

export const approveMessage = (params) => GET(messagesPath + 'approve/', params)
export const abandonMessage = (params) => GET(messagesPath + 'abandon/', params)
export const sendMessage = (params) => POST(messagesPath + 'send/', params)

// Users
const usersPath = path + 'fcm-users/'
export const getFCMUsers = (params) => GET(usersPath, params)
export const removeFCMUsers = (params) => DEL(usersPath, params)

// Administrators
const adminPath = path + 'fcm-administrators/'
export const storeToken = (params) => POST(adminPath, params)
export const getOtherActiveAdministrators = () => GET(adminPath + 'active/others/')


// CS Messages
const csPath = path + 'cs-messages/';
export const getCSMessages = (params) => GET(csPath, params);
export const getOfflineMessages = () => GET(csPath + 'offline-messages/')
export const sendCSMessage = (params) => POST(csPath, params);
export const resendCSMessage = (params) => PAT(csPath, params);
export const readCSMessages = (params) => PUT(csPath, params)
export const respondOfflineMessages = (params) => POST(csPath + 'offline-messages/respond/', params)
export const confirmConversationRequest = (params) => POST(csPath + 'conversation/requests/confirm/', params)
export const endConversation = (params) => POST(csPath + 'conversation/end/', params)
export const transferConversation = (params) => POST(csPath + 'conversation/transfer/', params)
export const confirmTransferConversation = (params) => POST(csPath + 'conversation/transfer/confirm/', params)

// Work Sheets
const workSheetPath = path + 'working-sheets/'
export const getWorkSheets = (params) => GET(workSheetPath, params);
export const adminPunchIn = () => POST(workSheetPath);
export const adminPunchOut = () => PUT(workSheetPath);
export const getAdminWorkStatus = () => GET(workSheetPath + 'admin-status/');

// Settings
const settingPath = path + 'settings/'
export const getMessengerSettings = params => GET(settingPath, params);
export const updateMessengerSettings = params => PUT(settingPath, params);

export const getEmailSettings = params => GET(settingPath + 'email/', params)
export const getAllEmailSettings = params => GET(settingPath + 'email/all/', params)
export const addEmailSettings = params => POST(settingPath + 'email/', params)
export const updateEmailSettings = params => PUT(settingPath + 'email/', params)
export const deleteEmailSettings = params => DEL(settingPath + 'email/', params)

export const getOperationHours = params => GET(settingPath + 'operation-hours/', params)
export const getAllOperationHours = params => GET(settingPath + 'operation-hours/all/', params)
export const addOperationHours = params => POST(settingPath + 'operation-hours/', params)
export const updateOperationHours = params => PUT(settingPath + 'operation-hours/', params)
export const deleteOperationHours = params => DEL(settingPath + 'operation-hours/', params)

// Suppliers
const supplierPath = settingPath + 'suppliers/'
export const getMessengerSuppliers = params => GET(supplierPath, params)
export const addMessengerSupplier = params => POST(supplierPath, params)
export const updateMessengerSupplier = params => PUT(supplierPath, params)
export const updateMessengerSupplierAvailability = params => PAT(supplierPath, params)
export const deleteMessengerSupplier = params => DEL(supplierPath, params)

// Emails
const emailPath = path + 'emails/'

export const getEmails = params => GET(emailPath, params);
export const addEmail = params => POST(emailPath, params);
export const updateEmail = params => PUT(emailPath, params);
export const approveEmail = params => GET(emailPath + `approve/${params.id}/`);
export const abandonEmail = params => GET(emailPath + `abandon/${params.id}/`, params);
export const sendEmail = params => GET(emailPath + `send/${params.id}/`);

// Email Templates
export const getEmailTemplates = params => GET(emailPath + 'templates/', params)
export const addEmailTemplate = params => POST(emailPath + 'templates/', params)
export const updateEmailTemplate = params => PUT(emailPath + 'templates/', params)
export const updateEmailTemplatePriority = params => PAT(emailPath + 'templates/', params)
export const deleteEmailTemplate = params => DEL(emailPath + 'templates/', params)
export const copyEmailTemplate = params => POST(emailPath + 'templates/copy/', params)
export const previewEmailTemplate = params => POST(emailPath + 'templates/preview/', params)

// SMS
const smsPath = path + 'sms/'
export const getSMS = params => GET(smsPath, params);
// SMS Templates
export const getSMSTemplates = params => GET(smsPath + 'templates/', params);
export const addSMSTemplate = params => POST(smsPath + 'templates/', params);
export const updateSMSTemplate = params => PUT(smsPath + 'templates/', params);
export const updateSMSTemplatePriority = params => PAT(smsPath + 'templates/', params);
export const deleteSMSTemplate = params => DEL(smsPath + 'templates/', params);



// Subscriptions
const subscriptionPath = path + 'subscriptions/'
export const getUnsubscribing = params => GET(subscriptionPath + 'unsubscribing/', params);
export const removeUnsubscribing = params => DEL(subscriptionPath + 'unsubscribing/', params);
export const getUnsubscribingLogs = params => GET(subscriptionPath + 'unsubscribing/logs/', params);
