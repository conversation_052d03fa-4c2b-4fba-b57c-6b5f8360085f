import { Order } from '../models/Order'
import { orderAPI } from '../api'
import api from '../api'

export class OrderRepository {
    // 获取订单列表
    async getOrders(params) {
        try {
            const response = await orderAPI.getOrders(params)
            console.log('API返回的订单数据:', response)
      
            // 修改数据检查逻辑
            let orderResponse = []
            if (response && response.orders && response.orders.order_list) {
                orderResponse = response.orders.order_list
            } else if (response && response.data && response.data.order_list) {
                orderResponse = response.data.order_list
            } else if (Array.isArray(response)) {
                orderResponse = response
            } else {
                console.warn('API返回的订单数据格式不正确:', response)
                return []
            }
      
            // 将API返回的普通对象转换为Order对象
            const orders = Array.isArray(orderResponse) 
                ? orderResponse.map(order => {
                    // 检查并添加坐标信息
                    if (!order.lng_lat && order.location) {
                        // 如果有location对象但没有lng_lat
                        if (order.location.latitude && order.location.longitude) {
                            order.lng_lat = [order.location.latitude, order.location.longitude]
                        }
                    } else if (!order.lng_lat && order.pickup_lat && order.pickup_lng) {
                        // 使用pickup坐标
                        order.lng_lat = [order.pickup_lat, order.pickup_lng]
                    } else if (!order.lng_lat && order.latitude && order.longitude) {
                        // 使用独立的latitude和longitude字段
                        order.lng_lat = [order.latitude, order.longitude]
                    } else if (!order.lng_lat && order.pickup_address) {
                        // 记录没有坐标的订单
                        console.warn(`订单 ${order.id} 没有坐标信息，但有地址: ${order.pickup_address}`)
                    }
            
                    // 创建Order对象 - 保留原始字段名
                    const orderObj = new Order(order)
            
                    // 确保保留原始字段名
                    orderObj.name = order.name || ''
                    orderObj.company = order.company || ''
                    orderObj.phone = order.phone || ''
                    orderObj.address = order.address || ''
            
                    return orderObj
                }) 
                : []
      
            console.log('处理后的订单数据:', orders.map(o => ({
                id: o.id,
                type: o.type,
                name: o.name,
                company: o.company,
                phone: o.phone,
                address: o.address,
                lng_lat: o.lng_lat
            })))
      
            return orders
        } catch (error) {
            console.error('获取订单失败:', error)
            throw error
        }
    }

    // 获取单个订单
    async getOrder(id) {
        try {
            const response = await orderAPI.getOrder(id)
            if (!response || !response.order) {
                return null
            }
      
            return new Order(response.order)
        } catch (error) {
            console.error(`获取订单 ${id} 失败:`, error)
            throw error
        }
    }

    // 获取指定路线的所有订单
    async getOrdersByRouteNumber(routeNumber) {
        try {
            const response = await orderAPI.getOrdersByRouteNumber(routeNumber)
            if (!response || !response.orders) {
                return []
            }
      
            return response.orders.map(order => new Order(order))
        } catch (error) {
            console.error(`获取路线 ${routeNumber} 的订单失败:`, error)
            throw error
        }
    }

    // 分配订单给司机
    async assignOrderToDriver(order, driverId, routeId) {
    // 这里可能需要调用API来实际更新服务器数据
    // 目前我们只在本地进行更新
        order.assignDriver(driverId)
        if (routeId) {
            order.assignRoute(routeId)
        }
        return order
    }
  
    // 使用API分配订单
    async assignOrderWithAPI(orderId, routeId, type) {
        try {
            const response = await orderAPI.assignOrder(orderId, routeId, type)
            console.log('API分配订单返回数据:', response)
      
            if (!response) {
                console.warn('API分配订单返回数据异常')
                return null
            }
            
            // 检查返回数据结构并获取订单信息
            // 由于API返回的是 {status: 365, data: {...}} 格式
            // 而不是直接返回订单对象，需要修复这个问题
            
            // 获取真正的订单数据
            let orderData;
            if (response.data && response.data.order) {
                // 如果API返回了正确的订单数据结构
                orderData = response.data.order;
            } else {
                // 如果没有获取到正确的数据结构，则需要主动去获取最新的订单信息
                console.log(`未从响应中获取到订单数据，主动获取订单 ${orderId} 的最新信息`);
                
                // 调用获取订单详情API
                try {
                    const orderDetail = await orderAPI.getOrder(orderId, type);
                    if (orderDetail && orderDetail.order) {
                        orderData = orderDetail.order;
                    } else {
                        // 仍然没有获取到订单数据，使用本地构建简单的订单对象
                        console.warn(`无法获取订单 ${orderId} 的详细信息，使用简单订单对象`);
                        orderData = {
                            id: orderId,
                            route_id: routeId,
                            type: type
                        };
                        
                        // 尝试从路线获取driver_id
                        try {
                            const routeInfo = await this.getRouteInfo(routeId);
                            if (routeInfo && routeInfo.driver_id) {
                                orderData.driver_id = routeInfo.driver_id;
                                console.log(`从路线 ${routeId} 获取司机ID: ${routeInfo.driver_id}`);
                            }
                        } catch (routeError) {
                            console.error(`获取路线 ${routeId} 信息失败:`, routeError);
                        }
                    }
                } catch (fetchError) {
                    console.error(`获取订单 ${orderId} 详情失败:`, fetchError);
                    // 使用简单的订单对象
                    orderData = {
                        id: orderId,
                        route_id: routeId,
                        type: type
                    };
                    
                    // 尝试从路线获取driver_id
                    try {
                        const routeInfo = await this.getRouteInfo(routeId);
                        if (routeInfo && routeInfo.driver_id) {
                            orderData.driver_id = routeInfo.driver_id;
                            console.log(`从路线 ${routeId} 获取司机ID: ${routeInfo.driver_id}`);
                        }
                    } catch (routeError) {
                        console.error(`获取路线 ${routeId} 信息失败:`, routeError);
                    }
                }
            }
            
            // 确保订单数据至少包含必要的ID和路线ID
            orderData.id = orderData.id || orderId;
            orderData.route_id = orderData.route_id || routeId;
            orderData.type = orderData.type || type;
      
            console.log('处理后的订单数据:', orderData);
            
            // 返回更新后的订单
            return new Order(orderData);
        } catch (error) {
            console.error(`使用API分配订单 ${orderId} 到路线 ${routeId} 失败:`, error)
            throw error
        }
    }
  
    // 获取路线信息
    async getRouteInfo(routeId) {
        try {
            const response = await api.get(`/dispatcher/app/routes/${routeId}/`);
            if (response && response.route) {
                return response.route;
            } else if (response && response.data && response.data.route) {
                return response.data.route;
            } else {
                console.warn(`未能从API获取路线 ${routeId} 数据`);
                return null;
            }
        } catch (error) {
            console.error(`获取路线 ${routeId} 数据失败:`, error);
            return null;
        }
    }
  
    // 使用API批量分配订单
    async assignOrdersWithAPI(orderIds, routeId, type) {
        try {
            const results = []
            for (const orderId of orderIds) {
                const result = await this.assignOrderWithAPI(orderId, routeId, type)
                if (result) {
                    results.push(result)
                }
            }
            return results
        } catch (error) {
            console.error('批量分配订单失败:', error)
            throw error
        }
    }

    // 批量分配订单
    async assignOrdersToDriver(orders, driverId, routeId) {
    // 将多个订单分配给同一个司机
        return orders.map(order => {
            return this.assignOrderToDriver(order, driverId, routeId)
        })
    }

    // 取消订单分配
    async unassignOrder(order) {
    // 这里可能需要调用API来实际更新服务器数据
    // 目前我们只在本地进行更新
        return order.unassign()
    }
  
    // 使用API取消订单分配
    async unassignOrderWithAPI(orderId, type) {
        try {
            // 调用API取消分配订单，传入null作为routeId
            const response = await orderAPI.assignOrder(orderId, null, type)
            console.log('API取消分配订单返回数据:', response)
      
            if (!response) {
                console.warn('API取消分配订单返回数据异常')
                return null
            }
      
            // 返回更新后的订单
            return new Order(response)
        } catch (error) {
            console.error(`使用API取消分配订单 ${orderId} 失败:`, error)
            throw error
        }
    }
  
    // 使用API批量取消分配订单
    async unassignOrdersWithAPI(orderIds, type) {
        try {
            const results = []
            for (const orderId of orderIds) {
                const result = await this.unassignOrderWithAPI(orderId, type)
                if (result) {
                    results.push(result)
                }
            }
            return results
        } catch (error) {
            console.error('批量取消分配订单失败:', error)
            throw error
        }
    }

    // 批量获取多个订单的详细信息
    async getOrdersBatch(orderIds, typeMap = null) {
        try {
            console.log(`[OrderRepository] 批量获取 ${orderIds.length} 个订单的详细信息`);
            const apiOrders = await orderAPI.getOrdersBatch(orderIds, typeMap);
            
            // 转换为Order对象
            const orderObjects = apiOrders.map(orderData => new Order(orderData));
            console.log(`[OrderRepository] 成功获取 ${orderObjects.length} 个订单对象`);
            
            return orderObjects;
        } catch (error) {
            console.error('批量获取订单详情失败:', error);
            return [];
        }
    }

    // 获取订单取货时间
    async getOrderPickupTime(orderId) {
        try {
            console.log(`[OrderRepository] 获取订单 ${orderId} 的取货时间信息`);
            
            const response = await orderAPI.getorderpickuptime(orderId);
            
            if (!response) {
                console.warn(`[OrderRepository] 获取订单 ${orderId} 取货时间返回空数据`);
                return null;
            }
            
            // API可能返回数组或单个对象，需要处理不同情况
            let orderData;
            
            if (Array.isArray(response) && response.length > 0) {
                // 如果返回数组，取第一个
                orderData = response[0];
            } else if (response.data && Array.isArray(response.data) && response.data.length > 0) {
                // 如果是包装在data属性中的数组
                orderData = response.data[0];
            } else if (response.data && response.data.order) {
                // 如果是包装在data.order中的对象
                orderData = response.data.order;
            } else if (response.order) {
                // 如果是包装在order属性中的对象
                orderData = response.order;
            } else {
                // 可能直接就是订单对象
                orderData = response;
            }
            
            console.log(`[OrderRepository] 获取到订单 ${orderId} 取货时间信息:`, orderData);
            
            return orderData;
        } catch (error) {
            console.error(`[OrderRepository] 获取订单 ${orderId} 取货时间失败:`, error);
            throw error;
        }
    }

    // 更新订单的停靠点编号
    async updateOrderStopNumber(orderId, stopNumber, type) {
        try {
            console.log(`[OrderRepository] 更新订单 ${orderId} 的停靠点编号为 ${stopNumber}`);
            
            // 调用API更新停靠点编号
            const response = await orderAPI.updateStopNumber(orderId, stopNumber, type);
            
            // 检查API响应
            if (!response) {
                console.warn(`[OrderRepository] 更新订单 ${orderId} 停靠点编号API返回数据异常`);
                return false;
            }
            
            return true;
        } catch (error) {
            console.error(`[OrderRepository] 更新订单 ${orderId} 停靠点编号失败:`, error);
            throw error;
        }
    }
    
    // 批量更新订单的停靠点编号
    async updateOrdersStopNumbers(orders) {
        try {
            const results = [];
            
            // 遍历订单列表，更新每个订单的停靠点编号
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                const stopNumber = i + 1; // 从1开始的序号
                const type = order.type || 'PICKUP'; // 默认类型为PICKUP
                
                const success = await this.updateOrderStopNumber(order.id, stopNumber, type);
                if (success) {
                    // 更新本地订单对象的停靠点编号
                    order.stop_no = stopNumber;
                    results.push(order);
                }
            }
            
            return results;
        } catch (error) {
            console.error('[OrderRepository] 批量更新订单停靠点编号失败:', error);
            throw error;
        }
    }
} 