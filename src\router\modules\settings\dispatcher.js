/*
* Author: <EMAIL>'
* Date: '2024-08-19 17:03:31'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/settings/dispatcher.js'
* File: 'dispatcher.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')
export default {
    path: '/dispatcher/settings',
    component: Layout,
    redirect: '/dispatcher/settings/settings',
    name: 'dispatcherSettings',
    meta: {
        title: '调度系统设置',
        icon: 'mdi:wrench-settings-outline',
        auth: ['super'],
        i18n: 'route.system.dispatcherSettings.title'
    },
    children: [
        {
            path: 'suppliers',
            name: 'dispatcherSupplierList',
            component: () => import('@/views/dispatcher/suppliers/list.vue'),
            meta: {
                title: '供应商列表',
                icon: 'material-symbols:home-repair-service-outline',
                activeMenu: '/dispatcher/suppliers/list',
                i18n: 'route.system.dispatcherSettings.suppliers',
                auth: ['super']
            }
        },
        {
            path: 'settings/list',
            name: 'dispatcherExceptionSettingList',
            component: () => import('@/views/dispatcher/exceptions/settings/list.vue'),
            meta: {
                title: '异常设置',
                icon: 'bx:key',
                activeMenu: '/dispatcher/exceptions/settings',
                i18n: 'route.system.dispatcherSettings.exceptions',
                auth: ['super']
            }
        },
        {
            path: 'settings/detail',
            name: 'dispatcherExceptionSettingDetail',
            component: () => import('@/views/dispatcher/exceptions/settings/detail.vue'),
            meta: {
                title: '异常设置',
                icon: 'bx:key',
                activeMenu: '/dispatcher/exceptions/settings',
                i18n: 'route.system.dispatcherSettings.exceptions',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },
        {
            path: 'settings',
            name: 'dispatcherSettingsSettings',
            component: () => import('@/views/delivery/settings/settings.vue'),
            meta: {
                title: '系统',
                icon: 'carbon:settings-adjust',
                activeMenu: '/delivery/settings/settings',
                i18n: 'route.system.dispatcherSettings.settings',
                auth: ['super']
            }
        },
    ]
}
