<script setup name="UsersList">
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { getUsers, getUserGroups, updateUserStatus, deleteUser } from '@/api/modules/users'
import { Delete } from '@element-plus/icons-vue'
import { currencyFormatter, phoneNumberFormatter } from '@/utils/formatter'

import { useI18n } from 'vue-i18n'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const { t } = useI18n()

const data = ref({
    loading: false,
    search: {
        name__icontains: null,
        extendedinfo__city__icontains: null,
        phone_number__icontains: null,
        email__icontains: null,
        credits__gte: 0,
        credits__lte: null,
        extendedinfo__user_type__in: ['personal', 'business'],
        extendedinfo__user_group: null,
        language: null,
        status: null,
        created_at__lte: null,
        created_at__gte: null,
        updated_at__lte: null,
        updated_at__gte: null,
    },
    dataList: []
})

onMounted(() => {
    getDataList()
    getUserGroups().then(res => {
        userGroups.value = res.data.group_list
    })

    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getUsers(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.user_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
        extendedinfo__city__icontains: null,
        phone_number__icontains: null,
        email__icontains: null,
        credits__gte: 0,
        credits__lte: null,
        extendedinfo__user_type__in: ['personal', 'business'],
        extendedinfo__user_group: null,
        language: null,
        status: null,
        created_at__lte: null,
        created_at__gte: null,
        updated_at__lte: null,
        updated_at__gte: null,
    }
    currentChange()
}

const searchBarCollapsed = ref('0')
const shortcuts = [
    {
        text: 'Today',
        value: new Date(),
    },
    {
        text: 'Yesterday',
        value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24)
            return date
        },
    },
    {
        text: 'A week ago',
        value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
            return date
        },
    },
    {
        text: 'A Month ago',
        value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 30)
            return date
        },
    },
]

const disabledDate = (time) => {
    return time.getTime() > Date.now()
}
const userGroups = ref([])
const languages = ref([
    'zh-cn', 'zh-tw', 'en-us', 'fr-fr', 'es-es'
])

//  API
function onEdit(row) {
    router.push({
        name: 'userDetail',
        query: {
            id: row.id,
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deleteUser({ id: row.id }).then((res) => {
            if (res.data.errCode === 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function alterStatus(row) {
    let params = {
        id: row.id,
        status: row.status != 'disabled' ? 'disabled' : 'created'
    }
    ElMessageBox.confirm(
        t(row.status != 'disabled' ? 'dialog.messages.disable' : 'dialog.messages.enable', { name: row.name }),
        t('dialog.titles.confirmation')
    ).then(() => {
        data.value.loading = true
        updateUserStatus(params).then(res => {
            if (res.data.errCode === 365) {
                getDataList()
            }
            data.value.loading = true
        })
    }).catch(() => { })

}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (row.status === 'disabled') {
        return 'banned-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('user.pages.list')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.name')">
                                        <el-input v-model="data.search.name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.name') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.city')">
                                        <el-input v-model="data.search.extendedinfo__city__icontains"
                                            :placeholder="$t('placeholder', { field: $t('user.fields.city') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.phoneNumber')">
                                        <el-input v-model="data.search.phone_number__icontains"
                                            :placeholder="$t('placeholder', { field: $t('user.fields.phoneNumber') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.email')">
                                        <el-input v-model="data.search.email__icontains"
                                            :placeholder="$t('placeholder', { field: $t('user.fields.email') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('user.fields.credits')">
                                        <el-input-number v-model="data.search.credits__gte" :min="0"
                                            :placeholder="$t('user.fields.creditsMin')" controls-position="right" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.credits__lte" :min="0"
                                            :placeholder="$t('user.fields.creditsMax')" controls-position="right" clearable
                                            @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>

                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.language')">
                                        <el-select v-model="data.search.language"
                                            :placeholder="$t('selectHolder', { field: $t('user.fields.language') })"
                                            @change="currentChange()">
                                            <el-option v-for="item in languages" :key="item"
                                                :label="$t(`user.selection.languageSelections.${item}`)" :value="item" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('user.fields.group')">
                                        <el-select v-model="data.search.extendedinfo__user_group"
                                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })"
                                            @change="currentChange()">
                                            <el-option v-for="item in userGroups" :key="item.id" :label="item.name"
                                                :value="item.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="6">
                                    <el-form-item :label="$t('user.fields.type')">
                                        <el-checkbox-group v-model="data.search.extendedinfo__user_type__in"
                                            @change="currentChange()">
                                            <el-checkbox label="business">{{ $t('user.selection.userType.business')
                                            }}</el-checkbox>
                                            <el-checkbox label="personal">{{ $t('user.selection.userType.personal')
                                            }}</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item :label="$t('user.fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('user.fields.createdAtMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('user.fields.createdAtMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('user.fields.updatedAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.updated_at__gte" type="date"
                                                :placeholder="$t('user.fields.updatedAtMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.updated_at__lte" type="date"
                                                :placeholder="$t('user.fields.updatedAtMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-class-name="tableRowClassName" :row-style="{ cursor: 'pointer' }"
                @row-dblclick="onEdit" @sort-change="sortChange">
                <el-table-column width="120" align="right" class-name="sign-row">
                    <template #default="scope">
                        <el-space size="small">
                            <ElTag size="small" type="warning" v-if="scope.row.language">
                                {{ $t(`user.selection.language.${scope.row.language}`) }}
                            </ElTag>
                            <ElTag v-if="scope.row.user_group" size="small" type="success">
                                {{ scope.row.user_group.name }}
                            </ElTag>
                            <ElTag v-if="scope.row.user_type === 'business'" size="small">
                                B
                            </ElTag>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="avatar" width="55" show-overflow-tooltip>
                    <template #default="scope">
                        <el-avatar size="small">
                            <img :src="scope.row.avatar" v-if="scope.row.avatar">
                            <span v-else style="font-size: 0.7em;">
                                {{
                                    scope.row.user_code
                                    ? scope.row.user_code
                                    : scope.row.name?.toUpperCase()[0]
                                }}
                            </span>
                        </el-avatar>
                    </template>
                </el-table-column>
                <el-table-column prop="name" min-width="160" :label="$t('fields.name')" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tooltip placement="top-start" effect="light">
                            {{ scope.row.name }}
                            <template #content>
                                <el-descriptions :column="1">
                                    <el-descriptions-item :label="$t('user.fields.companyName')"
                                        v-if="scope.row.company_name">
                                        {{ scope.row.company_name }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.name')" v-if="scope.row.name">
                                        {{ scope.row.name }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.username')">
                                        {{ scope.row.username }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="city" :label="$t('user.fields.city')" width="100">
                    <template #default="scope">
                        <el-tooltip placement="top-start" effect="light">
                            {{ scope.row.city ? scope.row.city : '-' }}
                            <template #content>
                                <el-descriptions :column="1">
                                    <el-descriptions-item :label="$t('user.fields.address')">
                                        {{ scope.row.address }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.postalCode')">
                                        {{ scope.row.postal_code }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.city')">
                                        {{ scope.row.city }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.province')">
                                        {{ scope.row.province }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="phone_number" :label="$t('user.fields.phoneNumber')" align="right" width="150">
                    <template #default="scope">
                        <el-tooltip placement="top-start" effect="light">
                            {{ phoneNumberFormatter(scope.row.phone_number) }}
                            <template #content>
                                <el-descriptions :column="1">
                                    <el-descriptions-item :label="$t('user.fields.phoneNumber')">
                                        {{ phoneNumberFormatter(scope.row.phone_number) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.secondaryPhone')"
                                        v-if="scope.row.sec_phone_number">
                                        {{ phoneNumberFormatter(scope.row.sec_phone_number) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('user.fields.extensionPhone')"
                                        v-if="scope.row.ext_phone_number">
                                        {{ scope.row.ext_phone_number }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="email" :label="$t('user.fields.email')" :show-overflow-tooltip="true" width="200">
                    <template #default="scope">
                        <el-link :href="`mailto:{{scope.row.email}}`" :type="scope.row.status == 'disabled' ? 'info' : ''">
                            {{ scope.row.email }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="credits" :label="$t('user.fields.credits')" align="center" width="100"
                    sortable="custom" :formatter="currencyFormatter" />
                <el-table-column prop="created_at" width="160" :label="$t('user.fields.createdAt')" sortable="custom" />
                <el-table-column prop="updated_at" width="160" :label="$t('user.fields.updatedAt')" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.status ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.status != 'disabled' ? 'warning' : 'success'" circle size="small"
                                @click="alterStatus(scope.row)">
                                <svg-icon
                                    :name="scope.row.status != 'disabled' ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .banned-row {
        color: #bbb !important;
    }
}

.el-pagination {
    margin-top: 20px;
}
</style>

