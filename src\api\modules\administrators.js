/*
 * Author: <EMAIL>'
 * Date: '2022-10-31 21:48:25'
 * Project: 'Admin-UI'
 * Path: 'src/api/apis/administrators.js'
 * File: 'administrators.js'
 * Version: '1.0.0'
 */

import { DEL, GET, PAT, POST, PUT } from "../methods";

const path = "administrators/";

export const getAdministrators = (params) => GET(path, params);
export const addAdministrator = (params) => POST(path, params);
export const updateAdministrator = (params) => PUT(path, params);
export const resetAdministratorPassword = (params) => PAT(path, params);
export const deleteAdministrator = (params) => DEL(path, params);
