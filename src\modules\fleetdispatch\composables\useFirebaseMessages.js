/**
 * Firebase消息组合式API
 * 用于在Vue组件中订阅和处理Firebase消息
 */

import { onMounted, onUnmounted, ref } from 'vue'
import { eventBus, EVENT_TYPES } from '../utils/eventBus'
import { firebaseMessagingService } from '../services/FirebaseMessagingService'

export function useFirebaseMessages() {
    // 存储最近接收的消息
    const recentMessages = ref([])
    // 最多保存10条消息历史
    const MAX_MESSAGES = 10
    
    // 处理新订单消息的回调函数
    const onNewOrder = (message) => {
        console.log('组件收到新订单消息:', message)
        
        // 在这里添加组件级别的处理逻辑
        // 例如：显示提示、更新UI等
        
        // 将消息添加到历史记录
        addMessageToHistory(message)
    }
    
    // 处理司机位置更新消息的回调函数
    const onDriverLocationUpdate = (message) => {
        console.log('组件收到司机位置更新消息:', message)
        
        // 在这里添加组件级别的处理逻辑
        
        // 将消息添加到历史记录
        addMessageToHistory(message)
    }
    
    // 处理订单状态变更消息的回调函数
    const onOrderStatusChange = (message) => {
        console.log('组件收到订单状态变更消息:', message)
        
        // 在这里添加组件级别的处理逻辑
        
        // 将消息添加到历史记录
        addMessageToHistory(message)
    }
    
    // 处理所有Firebase消息的通用回调函数
    const onMessage = (message) => {
        console.log('组件收到Firebase消息:', message)
        
        // 根据消息类型分发到具体的处理函数
        switch (message.type) {
            case 'new_order':
                onNewOrder(message)
                break
            case 'driver_location_update':
                onDriverLocationUpdate(message)
                break
            case 'order_status_change':
                onOrderStatusChange(message)
                break
            default:
                // 将消息添加到历史记录
                addMessageToHistory(message)
                break
        }
    }
    
    // 将消息添加到历史记录
    const addMessageToHistory = (message) => {
        // 添加时间戳
        const messageWithTimestamp = {
            ...message,
            receivedAt: new Date().toISOString()
        }
        
        // 在数组开头添加新消息
        recentMessages.value.unshift(messageWithTimestamp)
        
        // 如果超过最大数量，移除最旧的消息
        if (recentMessages.value.length > MAX_MESSAGES) {
            recentMessages.value = recentMessages.value.slice(0, MAX_MESSAGES)
        }
    }
    
    // 清空消息历史
    const clearMessageHistory = () => {
        recentMessages.value = []
    }
    
    // 组件挂载时注册事件监听
    onMounted(() => {
        // 确保Firebase消息服务已初始化
        if (!firebaseMessagingService.initialized) {
            firebaseMessagingService.initialize()
        }
        
        // 订阅消息事件
        eventBus.on(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, onMessage)
        eventBus.on(EVENT_TYPES.FIREBASE_NEW_ORDER, onNewOrder)
        eventBus.on(EVENT_TYPES.FIREBASE_DRIVER_LOCATION_UPDATE, onDriverLocationUpdate)
        eventBus.on(EVENT_TYPES.FIREBASE_ORDER_STATUS_CHANGE, onOrderStatusChange)
    })
    
    // 组件卸载时移除事件监听
    onUnmounted(() => {
        eventBus.off(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, onMessage)
        eventBus.off(EVENT_TYPES.FIREBASE_NEW_ORDER, onNewOrder)
        eventBus.off(EVENT_TYPES.FIREBASE_DRIVER_LOCATION_UPDATE, onDriverLocationUpdate)
        eventBus.off(EVENT_TYPES.FIREBASE_ORDER_STATUS_CHANGE, onOrderStatusChange)
    })
    
    return {
        recentMessages,
        clearMessageHistory
    }
} 