module.exports = {
    root: true,
    env: {
        browser: true,
        es6: true
    },
    globals: {
        __dirname: true,
        process: true,
        require: true,
        module: true,
        AMap: true,
        // element-plus
        ElMessage: true,
        ElMessageBox: true,
        ElNotification: true,
        ElLoading: true
    },
    extends: [
        // 'plugin:vue/vue3-recommended',
        // 'plugin:vue/vue3-strongly-recommended',
        // 'eslint:recommended',
        // './.eslintrc-auto-import.json'
    ],
    parser: 'vue-eslint-parser',
    parserOptions: {
        ecmaVersion: '2020',
        ecmaFeatures: {
            jsx: true
        }
    },
    rules: {
        'no-unused-vars': 'off',
        // 禁用所有规则
        // 代码风格
        'block-spacing': 'off',
        'brace-style': 'off',
        'comma-spacing': 'off',
        'comma-dangle': 'off',
        'comma-style': 'off',
        'computed-property-spacing': 'off',
        'indent': 'off',
        'key-spacing': 'off',
        'keyword-spacing': 'off',
        'linebreak-style': 'off',
        'multiline-ternary': 'off',
        'no-multiple-empty-lines': 'off',
        'no-unneeded-ternary': 'off',
        'quotes': 'off',
        'semi': 'off',
        'space-before-blocks': 'off',
        'space-before-function-paren': 'off',
        'space-in-parens': 'off',
        'space-infix-ops': 'off',
        'space-unary-ops': 'off',
        'spaced-comment': 'off',
        'switch-colon-spacing': 'off',
        'object-curly-spacing': 'off',
        // ES6
        'arrow-parens': 'off',
        'arrow-spacing': 'off',
        // Vue - https://github.com/vuejs/eslint-plugin-vue
        'vue/multi-word-component-names': 'off',
        'vue/html-indent': 'off',
        'vue/no-v-html': 'off',
        'vue/max-attributes-per-line': 'off',
        'vue/require-default-prop': 'off',
        'vue/singleline-html-element-content-newline': 'off',
        'vue/require-explicit-emits': 'off',
        'vue/script-indent': 'off'
    }
};
