
<script setup name="Column<PERSON>hart">
import { Column } from '@antv/g2plot';
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

let columnPlot = undefined

const props = defineProps({
    action: {
        type: Function,
        default: undefined
    },
    title: {
        type: String,
        default: '柱形图'
    },
    color: {
        type: String,
        default: '#5B8FF9'
    }

})

const config = {
    xField: 'date',
    yField: 'num',
    label: {
        position: 'middle',
        style: {
            fill: '#FFFFFF',
            opacity: 0.6,
        },
    },
    xAxis: {
        label: {
            autoHide: true,
            autoRotate: true,
        },
    },
    color: props.color,
    meta: {
        date: {
            alias: t('fields.date'),
        },
        num: {
            alias: t('fields.quantity'),
        },
    },
}

const noData = ref(false)
const total = ref(0)


const years = Array.from({ length: new Date().getFullYear() - 2021 + 1 }, (_, i) => 2021 + i)
const months = computed(
    () => Array.from(
        { length: selectedYear.value && selectedYear.value == new Date().getFullYear() ? new Date().getMonth() + 1 : 12 }, (_, i) => 1 + i
    )
)
const selectedYear = ref(null)
const selectedMonth = ref(null)

watch(() => selectedYear.value, val => {
    let now = new Date()
    if (val && val == now.getFullYear() && selectedMonth.value && selectedMonth.value > now.getMonth() + 1) {
        selectedMonth.value = null
    }
})


onMounted(() => {
    getData()
})

function getData() {
    let params = {
        year: selectedYear.value,
        month: selectedMonth.value
    }
    noData.value = false

    props.action && props.action(params).then(res => {
        if (res.data.data.length) {
            total.value = res.data.total
            columnPlot?.destroy()
            columnPlot = new Column('chart', {
                data: res.data.data,
                ...config
            })
            columnPlot.render()
        } else {
            noData.value = true
        }
    })
}

onUnmounted(() => {
    columnPlot?.destroy()
})

function resetParams() {
    selectedYear.value = null
    selectedMonth.value = null
    getData()
}

</script>

<template>
    <page-main>
        <template #title>
            <div class="title">
                <el-space>
                    <span>{{ props.title }}</span>
                    <span>
                        <span v-if="selectedYear">{{ selectedYear }}</span>
                        <span v-if="!selectedYear && selectedMonth">{{ new Date().getFullYear() }}</span>
                        <span v-if="selectedMonth">-{{ selectedMonth }}</span>
                    </span>
                </el-space>
                <span>{{ total }}</span>
            </div>
        </template>
        <el-space class="selection">
            <el-select v-model="selectedYear" :placeholder="$t('statSelectHolder', { field: $t('fields.year') })">
                <el-option v-for="(item, idx) in years" :key="idx" :label="item" :value="item" />
            </el-select>
            <el-select v-model="selectedMonth" :placeholder="$t('statSelectHolder', { field: $t('fields.month') })">
                <el-option v-for="(item, idx) in months" :key="idx" :label="item" :value="item" />
            </el-select>
            <el-button type="warning" plain @click="resetParams">{{ $t('operations.reset') }}
            </el-button>
            <el-button type="success" @click="getData">{{ $t('operations.query') }}</el-button>
        </el-space>
        <div v-if="!noData" id="chart" style="width: 100%; height: 200px;" />
        <el-empty v-else />
    </page-main>
</template >

<style scoped>
.title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.selection {
    margin-bottom: 30px;
}
</style>

