const Layout = () => import('@/layout/index.vue')

export default {
    path: '/administrators',
    component: Layout,
    redirect: '/administrators/list',
    name: 'administrators',
    meta: {
        title: '职员管理',
        icon: 'clarity:administrator-solid',
        auth: ['super'],
        i18n: 'route.system.staffs.title'
    },
    children: [
        {
            path: 'list',
            name: 'administratorList',
            component: () => import('@/views/administrators/list.vue'),
            meta: {
                title: '列表',
                icon: 'clarity:administrator-solid',
                i18n: 'route.system.staffs.administrators',
                activeMenu: '/administrators/list',
                auth: ['super']
            }
        },
        {
            path: 'create',
            name: 'administratorCreate',
            component: () => import('@/views/administrators/detail.vue'),
            meta: {
                title: '新增',
                activeMenu: '/administrators/list',
                sidebar: false,
                auth: ['super']
            }
        },
        {
            path: 'edit/:id',
            name: 'administratorEdit',
            component: () => import('@/views/administrators/detail.vue'),
            meta: {
                title: '编辑',
                activeMenu: '/administrators/list',
                sidebar: false,
                auth: ['super']
            }
        },
        {
            path: 'roles/list',
            name: 'roleList',
            component: () => import('@/views/roles/list.vue'),
            meta: {
                title: '列表',
                icon: 'eos-icons:role-binding-outlined',
                i18n: 'route.system.staffs.roles',
                activeMenu: '/administrators/roles/list',
                auth: ['super']
            }
        },
        {
            path: 'roles/create',
            name: 'roleCreate',
            component: () => import('@/views/roles/detail.vue'),
            meta: {
                title: '新增',
                activeMenu: '/administrators/roles/list',
                sidebar: false,
                auth: ['super']
            }
        },
        {
            path: 'roles/edit/',
            name: 'roleEdit',
            component: () => import('@/views/roles/detail.vue'),
            meta: {
                title: '编辑',
                activeMenu: '/administrators/roles/list',
                sidebar: false,
                auth: ['super']
            }
        }
    ]
}
