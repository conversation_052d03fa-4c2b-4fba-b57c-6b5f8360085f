<script setup>
import { currencyFormatter } from '@/utils/formatter'

import StepButtons from './step_buttons.vue'
import { getSalaryReports } from '@/api/modules/statistics';
const route = useRoute()

const props = defineProps({
    date: {
        type: String,
        default: ''
    },

})
const emit = defineEmits(['setSalary'])


const salaryReport = ref({
    id: route.query.id,
    date: props.date,

})




onMounted(() => {
    getData()
})

function getData() {
    let params = {
        id: salaryReport.value.id,
        date: salaryReport.value.date
    }
    getSalaryReports(params).then(res => {
        salaryReport.value = res.data
        emit('setSalary', res.data.id)
    })

}
const getSalarySummaries = ({ columns, data }) => {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = 'Total'
            return
        } else if (index > 2 && index < 5) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)
            sums[index] = currencyFormatter(null, null, sum, null)
        }
    })
    return sums
}


</script>
<template>
    <el-descriptions :title="$t('finance.eod.s6')" :column="3" border style="margin-bottom: 40px;">
        <el-descriptions-item :label="$t('fields.date')" label-align="left" align="center" :span="3">
            {{ salaryReport.date }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.baseSalary')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.base_salary) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.hours')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.working_hours) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.salaryPerHour')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.average_salary_per_hour) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.fuelAllowance')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.fuel_allowance) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.travelKm')" label-align="left" align="center">
            {{ salaryReport.travel_km }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.fuelAllowancePerKm')" label-align="left"
            align="center">
            {{ currencyFormatter(_, _, salaryReport.average_fuel_allowance_per_km) }}
        </el-descriptions-item>

        <el-descriptions-item :label="$t('statistics.salary.fields.bonus')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.bonus) }}
        </el-descriptions-item>
        <el-descriptions-item />
        <el-descriptions-item />
        <el-descriptions-item :label="$t('statistics.salary.fields.extraBonus')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.extra_bonus) }}
        </el-descriptions-item>
        <el-descriptions-item />
        <el-descriptions-item />

        <el-descriptions-item :label="$t('statistics.salary.fields.compensation')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.compensation) }}
        </el-descriptions-item>
        <el-descriptions-item />
        <el-descriptions-item />

        <el-descriptions-item :label="$t('statistics.salary.fields.deduction')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.deduction) }}
        </el-descriptions-item>
        <el-descriptions-item />
        <el-descriptions-item />
        <el-descriptions-item :label="$t('statistics.salary.fields.tip')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.tip) }}
        </el-descriptions-item>
        <el-descriptions-item />
        <el-descriptions-item />
        <el-descriptions-item :label="$t('fields.tax')" label-align="left" align="center">
            {{ currencyFormatter(_, _, salaryReport.tax) }}
        </el-descriptions-item>
        <el-descriptions-item />
        <el-descriptions-item />

        <el-descriptions-item :label="$t('fields.total')" label-align="left" align="center">
            <el-text tag='b'>{{ currencyFormatter(_, _, salaryReport.total) }}</el-text>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.employeeCount')" label-align="left" align="center">
            {{ salaryReport.employee_count }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('statistics.salary.fields.salaryPerEmployee')" label-align="left"
            align="center">
            <el-text tag='b'>{{ currencyFormatter(_, _, salaryReport.average_salary_per_employee) }}</el-text>
        </el-descriptions-item>
    </el-descriptions>
    <el-text size="large" tag="b">{{ $t('staffs.fields.stubs') }}</el-text>
    <el-table :data="salaryReport.stubs" stripe highlight-current-row border show-summary
        :summary-method="getSalarySummaries" style="margin-top: 20px;">
        <el-table-column type="index" align="center" fixed width="50" />
        <el-table-column prop="employee" :label="$t('staffs.fields.employee')" />
        <el-table-column prop="date" :label="$t('fields.date')" />
        <el-table-column prop="hst" :label="$t('staffs.stub.fields.tax')" :formatter="currencyFormatter" />
        <el-table-column prop="total" :label="$t('fields.total')" :formatter="currencyFormatter" />
        <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
        <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
    </el-table>


    <StepButtons v-bind="$attrs" />
</template>