<script setup>
import { getDeliveryStubs } from '@/api/modules/staffs'
import StubTable from '@/views/staffs/components/stub_table.vue'
import WorksheetTable from '@/views/staffs/components/worksheet_table.vue'
import eventBus from '@/utils/eventBus'
const router = useRouter()

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const data = ref({
    loading: false,
    form: {
        id: props.id,
    },
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getDeliveryStubs({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function onDelete() {
    console.log('deleted');
    eventBus.emit('get-data-list')
    router.push({ name: 'deliveryStubs' })
}

</script>

<template>
    <div v-loading="data.loading">
        <el-row>
            <el-col :span="12">
                <StubTable :stub="data.form" :on-update="getInfo" :on-delete="onDelete"
                    v-if="data.form.base_salary > 0" />
            </el-col>
            <el-col :span="12" v-if="data.form.worksheet">
                <WorksheetTable :worksheet="data.form.worksheet" />
            </el-col>
        </el-row>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>