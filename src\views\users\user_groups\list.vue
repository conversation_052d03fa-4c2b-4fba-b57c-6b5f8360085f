<script setup name="UserGroupsList">
import { usePagination } from '@/utils/composables'
import FormMode from './components/FormMode/index.vue'
import { getUserGroups, updateUserGroupAvailability, deleteUserGroup, bulkUpdateUserGroupAvailability } from '@/api/modules/users';
import { Delete } from '@element-plus/icons-vue'
import { currencyFormatter, percentageFormatter } from '@/utils/formatter'
import { useI18n } from 'vue-i18n';

const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        id: ''
    },
    search: {
        name__icontains: null,
        fee__gte: null,
        fee__lte: null,
        fee_deduction_rate__gte: null,
        fee_deduction_rate__lte: null,
        is_available: null,
        created_at__lte: null,
        created_at__gte: null,
        updated_at__lte: null,
        updated_at__gte: null,
    },
    selectionDataList: [],
    dataList: []
})

onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true

    let filters = JSON.parse(JSON.stringify(data.value.search))
    if (filters.fee__gte) {
        filters.fee__gte = Math.round(filters.fee__gte * 100)
    }
    if (filters.fee__lte) {
        filters.fee__lte = Math.round(filters.fee__lte * 100)
    }
    if (filters.fee_deduction_rate__gte) {
        filters.fee_deduction_rate__gte = Math.round(filters.fee_deduction_rate__gte * 100)
    }
    if (filters.fee_deduction_rate__lte) {
        filters.fee_deduction_rate__lte = Math.round(filters.fee_deduction_rate__lte * 100)
    }

    let params = getParams(
        {
            filters: JSON.stringify(filters)
        }
    )
    getUserGroups(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.group_list
        pagination.value.total = res.data.total
    })
}
// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}


// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}
// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
        fee__gte: null,
        fee__lte: null,
        fee_deduction_rate__gte: null,
        fee_deduction_rate__lte: null,
        is_available: null,
        created_at__lte: null,
        created_at__gte: null,
        updated_at__lte: null,
        updated_at__gte: null,
    }
    currentChange()
}

const searchBarCollapsed = ref('0')
const shortcuts = [
    {
        text: 'Today',
        value: new Date(),
    },
    {
        text: 'Yesterday',
        value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24)
            return date
        },
    },
    {
        text: 'A week ago',
        value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
            return date
        },
    },
    {
        text: 'A Month ago',
        value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 30)
            return date
        },
    },
]

const disabledDate = (time) => {
    return time.getTime() > Date.now()
}



function onCreate() {
    data.value.formModeProps.id = ''
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.visible = true
}

function onAlterAvailability(row) {
    updateUserGroupAvailability({ id: row.id, isAvailable: !row.is_available }).then(res => {
        if (res.data.errCode === 365) {
            getDataList()
        }
    })
}

function onBulkAlterAvailability(isAvailable) {
    let ids = []
    data.value.selectionDataList.forEach(g => {
        if (g.is_available != isAvailable) {
            ids.push(g.id)
        }
    });

    if (ids.length) {
        let params = {
            ids: ids,
            isAvailable: isAvailable
        }
        bulkUpdateUserGroupAvailability(params).then(res => {
            if (res.data.errCode === 365) {
                getDataList()
            }
        })
    } else {
        ElMessage.warning('noGroupToUpdate')
    }

}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deleteUserGroup({ ids: JSON.stringify([row.id]) }).then(res => {
            if (res.data.errCode === 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onBulkDelete() {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: 'All' }), t('dialog.titles.confirmation')).then(() => {
        let ids = []
        data.value.selectionDataList.forEach(g => {
            ids.push(g.id)
        })
        deleteUserGroup({ ids: JSON.stringify(ids) }).then(res => {
            if (res.data.errCode === 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'banned-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('user.userGroup.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="24">
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.name')">
                                        <el-input v-model="data.search.name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.name') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('user.userGroup.fields.fee')">
                                        <el-input-number v-model="data.search.fee__gte" :min="0" :step="0.1" :precision="2"
                                            :placeholder="$t('user.userGroup.fields.feeMin')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.fee__lte" :min="0" :step="0.1" :precision="2"
                                            :placeholder="$t('user.userGroup.fields.feeMax')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('user.userGroup.fields.deductionRate')">
                                        <el-input-number v-model="data.search.fee_deduction_rate__gte" :min="0" :step="1"
                                            :placeholder="$t('user.userGroup.fields.rateMin')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.fee_deduction_rate__lte" :min="0" :step="1"
                                            :placeholder="$t('user.userGroup.fields.rateMax')" controls-position="right"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('fields.createdAtMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('fields.createdAtMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('fields.updatedAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.updated_at__gte" type="date"
                                                :placeholder="$t('fields.updatedAtMin')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.updated_at__lte" type="date"
                                                :placeholder="$t('fields.updatedAtMax')" :disabled-date="disabledDate"
                                                :shortcuts="shortcuts" clearable format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD" @keydown.enter="currentChange()"
                                                @clear="currentChange()" @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>

        <page-main>
            <div class="top-buttons">

                <batch-action-bar :data="data.dataList" :selection-data="data.selectionDataList">
                    <el-button size="default" @click="onBulkDelete" type="danger">
                        {{ $t('operations.batch', { op: $t('operations.delete') }) }}
                    </el-button>
                    <el-button-group>
                        <el-button size="default" @click="onBulkAlterAvailability(true)" type="success">
                            {{ $t('operations.batch', { op: $t('operations.enable') }) }}
                        </el-button>
                        <el-button size="default" @click="onBulkAlterAvailability(false)" type="warning">
                            {{ $t('operations.batch', { op: $t('operations.disable') }) }}
                        </el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" size="large" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" border stripe
                :row-class-name="tableRowClassName" highlight-current-row :row-style="{ cursor: 'pointer' }"
                @row-dblclick="onEdit" @sort-change="sortChange" @selection-change="data.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="fee" :label="$t('user.userGroup.fields.fee')" :formatter="currencyFormatter"
                    align="center" sortable="custom" />
                <el-table-column prop="fee_deduction_rate" :label="$t('user.userGroup.fields.deductionRate')"
                    :formatter="percentageFormatter" align="center" sortable="custom" />
                <el-table-column prop="created_at" width="160" :label="$t('user.fields.createdAt')" sortable="custom" />
                <el-table-column prop="updated_at" width="160" :label="$t('user.fields.updatedAt')" sortable="custom" />

                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.status ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="onAlterAvailability(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>

            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="true" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-table {
    font-size: 0.8em;
}

.el-table .banned-row {
    color: var(--g-unavailable-color);
}

.el-pagination {
    margin-top: 20px;
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
