<script setup name="FinanceCreditsDirect">
    import { getAllUserCreditLogs, getCreditsTotalBalance, directChargeUserCredits } from '@/api/modules/payment';
    import { usePagination } from '@/utils/composables'
    import { currencyFormatter, phoneNumberFormatter } from '@/utils/formatter'
    import { getUserSimpleList } from '@/api/modules/users'
    import UserSelector from '@/views/components/UserSelector/index.vue'
    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

    const data = ref({
        loading: false,
        // 搜索
        search: {
            name__icontains: null,
            delta__lte: null,
            delta__gte: null,
            type: 'directed',
            created_at__lte: null,
            created_at__gte: null,
        },
        // 列表数据
        dataList: []
    })

    const totalBalance = ref(0)
    const userList = ref([])

    const directParams = ref({
        amount: 0,
        user: null,
        password: null,
        notes: null,
    })

    const rules = {
        password: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }



    // Filters
    function resetFilters() {
        data.value.search =
        {
            name__icontains: null,
            delta__lte: null,
            delta__gte: null,
            type: 'directed',
            created_at__lte: null,
            created_at__gte: null,
        }
        currentChange()
    }


    onMounted(() => {
        getDataList();
        getTotalBalance();
    })


    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getAllUserCreditLogs(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.log_list
            pagination.value.total = res.data.total
        })
    }

    function getTotalBalance() {
        getCreditsTotalBalance().then(res => {
            totalBalance.value = res.data.balance;
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    function getUsers(query) {
        if (query && query.length > 3) {
            getUserSimpleList({ terms: query }).then(res => {
                userList.value = res.data
            })
        } else {
            userList.value = []
        }
    }

    const directDialogVisible = ref(false)

    function onDirect() {
        directDialogVisible.value = true;
    }

    function onConfirmDirect() {

        if (directParams.value.password == null || directParams.value.password.length <= 0) return;
        let params = {
            amount: Math.round(directParams.value.amount * 100),
            user_id: directParams.value.user.id,
            password: directParams.value.password,
            notes: directParams.value.notes
        }
        directChargeUserCredits(params).then(res => {
            if (res.data.errCode == 365) {
                getDataList();
                getTotalBalance();
                onCancelDirect();
            }
        })
    }
    function onCancelDirect() {
        directParams.value = {
            amount: 0,
            user: null,
            password: null,
            notes: null,
        }
        directDialogVisible.value = false;
    }



</script>

<template>
    <div>
        <page-header title="用户积分" />
        <page-main>
            <div style="display: flex; justify-content: space-between;">
                <el-space>
                    <el-text tag='b'>{{ $t('user.log.operation.direct') }}</el-text>
                    <el-input-number :min="0" :max="999" v-model="directParams.amount" :precision="2" :step="0.1" />
                    <div style="width: 100%;">
                        <UserSelector v-model="directParams.user" />
                    </div>
                    <el-button type="primary" @click="onDirect"
                        :disabled="directParams.amount <= 0 || directParams.user == null">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </el-space>
                <el-space>
                    <el-text tag='b'>{{ $t('user.userStatistics.fields.creditBalance') }}</el-text>
                    <el-text>
                        {{ currencyFormatter(_, _, totalBalance) }}
                    </el-text>
                </el-space>
            </div>
        </page-main>
        <page-main>
            <search-bar>
                <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                    <el-row :gutter="20" align="bottom">
                        <el-col :span="6">
                            <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                <el-input v-model="data.search.user__name__icontains"
                                    :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                    clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                            </el-form-item>

                        </el-col>
                        <el-col :span="6">
                            <el-form-item :label="$t('fields.createdAt')">
                                <el-space>
                                    <el-date-picker v-model="data.search.created_at__gte" type="date"
                                        :placeholder="$t('fields.createdAtMin')" :disabled-date="disabledDate"
                                        :shortcuts="shortcuts" clearable format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                        @keydown.enter="currentChange()" @clear="currentChange()"
                                        @change="currentChange()" />
                                    <span>~</span>
                                    <el-date-picker v-model="data.search.created_at__lte" type="date"
                                        :placeholder="$t('fields.createdAtMax')" :disabled-date="disabledDate"
                                        :shortcuts="shortcuts" clearable format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                        @keydown.enter="currentChange()" @clear="currentChange()"
                                        @change="currentChange()" />
                                </el-space>

                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item :label="$t('user.log.delta')">
                                <el-input-number v-model="data.search.delta__gte" :min="0"
                                    :placeholder="$t('user.log.deltaMin')" controls-position="right" clearable
                                    @keydown.enter="currentChange()" @clear="currentChange()" />
                                <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                <el-input-number v-model="data.search.delta__lte" :min="0"
                                    :placeholder="$t('user.log.deltaMax')" controls-position="right" clearable
                                    @keydown.enter="currentChange()" @clear="currentChange()" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </el-form>
            </search-bar>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @sort-change="sortChange">
                <el-table-column type="index" width="50" fixed />
                <el-table-column prop="user.name" :label="$t('fields.user')" />
                <el-table-column prop="delta" :label="$t('user.log.delta')" align="right"
                    :formatter="currencyFormatter" />
                <el-table-column prop="type" :label="$t('user.log.type')" align="center">
                    <template #default="scope">
                        {{ $t(`user.log.creditType.${scope.row.type}`) }}
                    </template>
                </el-table-column>
                <el-table-column prop="record" :label="$t('user.log.record')" show-overflow-tooltip min-width="300">
                    <template #default="scope">
                        {{ scope.row.record.join(', ') }}
                    </template>
                </el-table-column>
                <el-table-column prop="operator" :label="$t('fields.operator')" align="center" />
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <el-dialog v-model="directDialogVisible" title="Confirm & Input password" width="30%" draggable>
            <el-descriptions title="Check the detail" :column="1" border>
                <el-descriptions-item :label="$t('fields.amount')" label-align="right" align="left">
                    {{ currencyFormatter(_, _, directParams.amount * 100) }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('fields.user')" label-align="right" align="left">
                    <el-space direction="vertical" alignment="start">
                        <el-space>
                            <el-text tag='b' type="primary">{{ directParams.user?.name }}</el-text>
                            <el-text tag='b' type="info" size="small" v-if="directParams.user?.code">
                                {{ `(${directParams.user?.code})` }}
                            </el-text>
                        </el-space>
                        <el-space alignment="start">
                            {{ directParams.user?.email }}
                            {{ phoneNumberFormatter(directParams.user?.phone) }}
                        </el-space>

                    </el-space>
                </el-descriptions-item>
            </el-descriptions>
            <br />
            <el-form :model="directParams" :rules="rules" label-width="80px">
                <el-form-item :label="$t('fields.password')" prop="password">
                    <el-input v-model="directParams.password" show-password />
                </el-form-item>
                <el-form-item :label="$t('fields.notes')">
                    <el-input v-model="directParams.notes" type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelDirect">
                        {{ $t('operations.cancel') }}
                    </el-button>
                    <el-button type="primary" @click="onConfirmDirect">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;
    }
</style>