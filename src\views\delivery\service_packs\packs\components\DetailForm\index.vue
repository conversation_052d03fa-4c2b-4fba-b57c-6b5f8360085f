<script setup>
import { getServicePacks, updateServiePack, createServicePack } from '@/api/modules/delivery'

import ServicePackItemSelector from '@/views/components/ServicePackItems/selector.vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        names: { 'en-us': '', 'zh-cn': '', 'zh-tw': '', 'fr-fr': '', 'es-es': '' },
        descriptions: { 'en-us': '', 'zh-cn': '', 'zh-tw': '', 'fr-fr': '', 'es-es': '' },
        priority: 10,
    },
    rules: {
        'names.en-us': [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        priority: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }
})


onMounted(async () => {
    if (data.value.form.id != '') {
        getInfo()
    }
})


const selectedItems = ref([])

function getInfo() {
    data.value.loading = true
    getServicePacks({ id: data.value.form.id }).then(res => {
        data.value.form = res.data
        selectedItems.value = JSON.parse(JSON.stringify(res.data.items))
    })
    data.value.loading = false
}

defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.items = selectedItems.value;
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    createServicePack(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateServiePack(params).then((res) => {
                        if (res.data.errCode < 400) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                <el-row>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.name')" prop="names.en-us">
                            <el-input v-model="data.form.names['en-us']"
                                :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.desc')">
                            <el-input v-model="data.form.descriptions['en-us']" style="width: 100%;"
                                :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.nameZh')">
                            <el-input v-model="data.form.names['zh-cn']"
                                :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.descZh')">
                            <el-input v-model="data.form.descriptions['zh-cn']"
                                :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.nameTw')">
                            <el-input v-model="data.form.names['zh-tw']"
                                :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.descTw')">
                            <el-input v-model="data.form.descriptions['zh-tw']"
                                :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.nameFr')">
                            <el-input v-model="data.form.names['fr-fr']"
                                :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.descFr')">
                            <el-input v-model="data.form.descriptions['fr-fr']"
                                :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.nameEs')">
                            <el-input v-model="data.form.names['es-es']"
                                :placeholder="$t('placeholder', { field: $t('fields.name') })" maxlength="50"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item :label="$t('fields.descEs')">
                            <el-input v-model="data.form.descriptions['es-es']"
                                :placeholder="$t('placeholder', { field: $t('fields.desc') })" maxlength="200"
                                show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-divider />
                <el-form-item :label="$t('fields.priority')" prop="priority">
                    <el-input-number v-model="data.form.priority"
                        :placeholder="$t('placeholder', { field: $t('fields.priority') })" />
                </el-form-item>
                <el-form-item :label="$t('delivery.services.fields.items')">
                    <ServicePackItemSelector v-model="selectedItems" />
                </el-form-item>
            </el-form>
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
// Scss
</style>
