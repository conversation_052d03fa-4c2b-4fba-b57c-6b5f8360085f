const Layout = () => import('@/layout/index.vue')

export default {
    path: '/delivery/address-books',
    component: Layout,
    redirect: '/delivery/address-books',
    name: 'deliveryAddressBooks',
    meta: {
        title: '用户地址簿',
        icon: 'icon-park-outline:address-book',
        auth: ['super'],
        i18n: 'route.delivery.addressBooks'
    },
    children: [
        {
            path: '',
            name: 'deliveryAddressBooksList',
            component: () => import('@/views/delivery/address_books/list.vue'),
            meta: {
                title: '列表',
                icon: 'bx:key',
                activeMenu: '/delivery/address-books',
                i18n: 'route.delivery.addressBooks',
                sidebar: false,
                auth: ['super']
            }
        },
    ]
}
