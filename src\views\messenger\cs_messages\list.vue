<script setup name="MessengerCsMessagesList">
import { getCSMessages } from '@/api/modules/messenger'
import MessagesTab from './components/TabContent/messages.vue'


const data = ref({
    loading: false,
    dataList: []
})


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    getCSMessages().then(res => {
        data.value.loading = false
        data.value.dataList = res.data
    })
}


</script>

<template>
    <div>
        <page-header title="默认模块" />
        <page-main>
            <el-tabs tab-position="left" stretch>
                <el-tab-pane v-for="item in data.dataList" :label="item.name">
                    <MessagesTab :id="item.id" :name="item.name" />
                </el-tab-pane>
            </el-tabs>
        </page-main>
    </div>
</template>

