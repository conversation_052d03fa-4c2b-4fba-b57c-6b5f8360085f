
<script setup name="<PERSON><PERSON><PERSON>">
import { Bar } from '@antv/g2plot';
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

let barPlot = undefined

const props = defineProps({
    action: {
        type: Function,
        default: undefined
    },
    title: {
        type: String,
        default: '柱形图'
    }

})

const config = {
    xField: 'num',
    yField: 'extendedinfo__city',
    seriesField: 'extendedinfo__city',
    legend: {
        position: 'top-left',
        visible: false
    },

}

const total = ref(0)

const years = Array.from({ length: new Date().getFullYear() - 2021 + 1 }, (_, i) => 2021 + i)
const months = computed(
    () => Array.from(
        { length: selectedYear.value && selectedYear.value == new Date().getFullYear() ? new Date().getMonth() + 1 : 12 }, (_, i) => 1 + i
    )
)
const selectedYear = ref(null)
const selectedMonth = ref(null)

watch(() => selectedYear.value, val => {
    let now = new Date()
    if (val && val == now.getFullYear() && selectedMonth.value && selectedMonth.value > now.getMonth() + 1) {
        selectedMonth.value = null
    }
})


onMounted(() => {
    getData()
})

function getData() {
    let params = {
        year: selectedYear.value,
        month: selectedMonth.value
    }
    props.action && props.action(params).then(res => {
        total.value = res.data.total
        barPlot?.destroy()
        barPlot = new Bar('bar', {
            data: res.data.data,
            ...config
        })
        barPlot.render()
    })
}

onUnmounted(() => {
    barPlot?.destroy()
})

</script>

<template>
    <page-main :title="props.title">
        <template #title>
            <div class="title">
                <el-space>
                    <span>{{ props.title }}</span>
                    <span>{{ total }}</span>
                </el-space>
                <el-button type="success" @click="getData">{{ $t('operations.query') }}</el-button>
            </div>
        </template>
        <div id="bar" />
    </page-main>
</template >

<style scoped>
.title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

#bar {
    height: 1000px;
}
</style>

