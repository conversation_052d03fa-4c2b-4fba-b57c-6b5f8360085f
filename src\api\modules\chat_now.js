/*
* Author: <EMAIL>'
* Date: '2024-01-11 10:41:58'
* Project: 'FleetNowV3'
* Path: 'src/api/modules/cms.js'
* File: 'cms.js'
* Version: '1.0.0'
*/

import { DEL, GET, PAT, POST, PUT } from "../methods";

const path = 'cms/chat-now/'

export const getChatGreetings = (params) => GET(path + 'greetings/', params);
export const createChatGreeting = (params) => POST(path + 'greetings/', params);
export const updateChatGreeting = (params) => PUT(path + 'greetings/', params);
export const updateChatGreetingAvailabilityPriority = (params) => PAT(path + 'greetings/', params);
export const deleteChatGreetings = (params) => DEL(path + 'greetings/', params);


export const getVisitors = (params) => GET(path + 'visitors/', params);
export const deleteVisitor = (params) => DEL(path + 'visitors/', params);

export const getVisitorMessages = (params) => GET(path + 'messages/', params)
export const addVisitorMessage = (params) => POST(path + 'messages/', params);
export const readVisitorMessage = (params) => PUT(path + 'messages/', params);
