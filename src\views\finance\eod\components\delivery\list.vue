<script setup name="FinanceEodList">
import api from '@/api'
import { Delete } from '@element-plus/icons-vue'
import { usePagination } from '@/utils/composables'
import { getDeliveryReports, deleteDeliveryReport } from '@/api/modules/statistics'
import { currencyFormatter } from '@/utils/formatter'
import { getUserGroups } from '@/api/modules/users'

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 搜索
    search: {
        name__icontains: null,
    },
    // 批量操作
    batch: {
        enable: false,
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)

const userGroups = ref([])


// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
    getGroups()
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getDeliveryReports(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.report_list
        pagination.value.total = res.data.total
    })
}

function getGroups() {
    getUserGroups().then(res => {
        userGroups.value = res.data.group_list

    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'financeEOD'
    })
}
function onEdit(row) {
    console.log(row.id)
    router.push({
        name: 'financeEODDeliveryReportDetail',
        query: {
            id: row.id
        }
    })
}


function onDel(row) {
    ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
        deleteDeliveryReport({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

const getSummaries = ({ columns, data }) => {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = 'Total'
            return
        } else if (index != 8 && index < columns.length - 1) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)
            if (index > 2) { sums[index] = currencyFormatter(null, null, sum, null) }
            else {
                sums[index] = sum
            }
        }
    })
    sums[8] = (Number(sums[3]?.replace(',', '')) / sums[2]).toFixed(2)
    return sums
}


</script>

<template>
    <div>
        <page-header :title="$t('statistics.delivery.title')" />
        <!-- <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
Reset
</el-button>
<el-button type="primary" @click="currentChange()">
    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
    Filter
</el-button>
</el-form-item>

</el-form>
</search-bar>
</el-collapse-item>
</el-collapse>
</page-main> -->
        <page-main>
            <div class="top-buttons">
                <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                    :selection-data="data.batch.selectionDataList">
                    <el-button size="default">单个批量操作按钮</el-button>
                    <el-button-group>
                        <el-button size="default">批量操作按钮组1</el-button>
                        <el-button size="default">批量操作按钮组2</el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe show-summary
                :summary-method="getSummaries" highlight-current-row @sort-change="sortChange" @row-dblclick="onEdit">
                <el-table-column prop="date" :label="$t('fields.date')" fixed width="120" />
                <el-table-column prop="count" :label="$t('statistics.fields.orders')" align="center">
                    <template #default="scope">
                        <el-tooltip placement="bottom" effect="light"
                            :disabled="!scope.row.services || scope.row.services.length == 0">
                            <el-link :disabled="!scope.row.services || scope.row.services.length == 0">
                                {{ scope.row.count }}
                            </el-link>
                            <template #content>
                                <el-descriptions :column="1">
                                    <el-descriptions-item v-for="g in scope.row?.services" :label="g.services">
                                        {{ g.count }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="packages" :label="$t('statistics.fields.packages')" align="center">
                    <template #default="scope">
                        <el-tooltip placement="bottom" effect="light"
                            :disabled="!scope.row.services || scope.row.services.length == 0">
                            <el-link :disabled="!scope.row.services || scope.row.services.length == 0">
                                {{ scope.row.packages }}
                            </el-link>
                            <template #content>
                                <el-descriptions :column="1">
                                    <el-descriptions-item v-for="g in scope.row?.services" :label="g.services">
                                        {{ g.packages }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="delivery_fee" :label="$t('delivery.orders.fields.deliveryFee')" align="center"
                    width="120" :formatter="currencyFormatter" />
                <el-table-column prop="added_services_fee" :label="$t('delivery.orders.fields.addedServicesFee')"
                    width="150" align="center" :formatter="currencyFormatter" />
                <el-table-column prop="type_fee" :label="$t('delivery.orders.fields.typeFee')" align="center"
                    width="140" :formatter="currencyFormatter" />
                <!-- <el-table-column prop="size_fee" :label="$t('delivery.orders.fields.sizeFee')" align="center"
                    :formatter="currencyFormatter" /> -->
                <!-- <el-table-column :label="$t('delivery.orders.fields.timeFee')" align="center" >
                    <template #default="scope">
                        {{ currencyFormatter(_, _, scope.row.time_fee + scope.row.weekday_fee + scope.row.holiday_fee) }}
                    </template>
                </el-table-column> -->
                <el-table-column :label="$t('delivery.orders.fields.addressFee')" align="center">
                    <template #default="scope">
                        {{ currencyFormatter(_, _, scope.row.address_fee + scope.row.pc_fee + scope.row.zone_fee) }}
                    </template>
                </el-table-column>
                <!-- <el-table-column :label="$t('delivery.orders.fields.expressFee')" align="center" >
                    <template #default="scope">
                        {{ currencyFormatter(_, _, scope.row.express_fee) }}
                    </template>
                </el-table-column> -->
                <el-table-column prop="total_total" :label="$t('statistics.delivery.fields.totalAmount')" align="center"
                    :formatter="currencyFormatter" />
                <el-table-column prop="average_dfu" :label="$t('statistics.delivery.fields.averagePU')" align="center"
                    width="120" :formatter="currencyFormatter" />
                <el-table-column prop="total_refund_amount" :label="$t('delivery.orders.fields.refunded')"
                    align="center">
                    <template #default="scope">
                        {{ currencyFormatter(_, _, scope.row.total_refund_amount) }}
                    </template>
                </el-table-column>
                <el-table-column prop="total_tip" :label="$t('delivery.orders.fields.tip')" align="center">
                    <template #default="scope">
                        {{ currencyFormatter(_, _, scope.row.total_tip) }}
                    </template>
                </el-table-column>
                <el-table-column prop="total_income" :label="$t('fields.total')" align="center">
                    <template #default="scope">
                        {{ currencyFormatter(_, _, scope.row.total_income) }}
                    </template>
                </el-table-column>

                <el-table-column align="center" fixed="right" width="100">
                    <template #default="scope">
                        <el-button type="danger" size="small" @click="onDel(scope.row)" v-if="scope.row.can_update">
                            {{ $t('operations.delete') }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>