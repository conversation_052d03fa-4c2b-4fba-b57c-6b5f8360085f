/*
* Author: <EMAIL>'
* Date: '2023-12-11 12:09:11'
* Project: 'FleetNowV3'
* Path: 'src/store/modules/todos.js'
* File: 'todos.js'
* Version: '1.0.0'
*/

const useTodosStore = defineStore(
    'todos',
    {
        state: () => ({
            payment: [],
            delivery: [],
            dispatcher: [],
        }),
        getters: {
            newTodoCount: (state) => state.payment.length + state.delivery.length + state.dispatcher.length,
        },
        actions: {
            onReceiveTodo(todo) {
                console.log(todo);
                switch (todo.module) {
                    case 'payment':
                        if (!this.payment.some(t => t.id == todo.id)) {
                            this.payment.push(todo);
                        }
                        break;
                    case 'delivery':
                        if (!this.delivery.some(t => t.id == todo.id)) {
                            this.delivery.push(todo);
                        }
                        break;
                    case 'dispatcher':
                        if (!this.dispatcher.some(t => t.id == todo.id)) {
                            this.dispatcher.push(todo);
                        }
                        break;
                    default: return;
                }

            },
        },
    });

export default useTodosStore;
