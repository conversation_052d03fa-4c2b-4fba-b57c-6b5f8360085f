<script setup>
import { getUnreportedSalaries, deleteDeliveryStub } from '@/api/modules/staffs'
import { currencyFormatter } from '@/utils/formatter'

import StepButtons from './step_buttons.vue'
import { addSalaryReport } from '@/api/modules/statistics';

const props = defineProps({
    date: {
        type: String,
        default: ''
    }

})

const emit = defineEmits(['setSalary'])

const salaries = ref([])
const selectedStubs = ref([])



onMounted(() => {
    getSalaries();

})

function getSalaries() {
    let params = { date: props.date }
    getUnreportedSalaries(params).then(res => {
        salaries.value = res.data
    })
}


function onDeleteSalary(row) {
    let params = { id: row.id }
    deleteDeliveryStub(params).then(res => {
        if (res.data.errCode == 365) {
            getSalaries()
        }
    })
}

function onCreateSalaryReport() {
    let params = {
        date: props.date,
        stubs: selectedStubs.value.map(e => e.id)
    }
    addSalaryReport(params).then(res => {
        if (res.data.errCode == 365) {
            emit('setSalary', res.data.id)
            getSalaries();
        }
    })
}

const getSalarySummaries = ({ columns, data }) => {
    const sums = []
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = 'Total'
            return
        } else if (index > 2 && index < 11) {
            const values = data.map((item) => Number(item[column.property]))
            const sum = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)
            sums[index] = currencyFormatter(null, null, sum, null)
        }
    })
    return sums
}
</script>

<template>
    <h2>{{ $t('statistics.salary.title') }}</h2>
    <el-table :data="salaries" border stripe style="width: 100%;" show-summary :summary-method="getSalarySummaries"
        :row-style="{ cursor: 'pointer' }" @selection-change="selectedStubs = $event">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column prop="employee" :label="$t('fields.name')" show-overflow-tooltip sortable width="140" />
        <el-table-column prop="date" :label="$t('fields.date')" sortable width="120" />
        <el-table-column prop="base_salary" :label="$t('statistics.salary.fields.baseSalary')" sortable align="center"
            :formatter="currencyFormatter" />
        <el-table-column prop="fuel_allowance" :label="$t('statistics.salary.fields.fuelAllowance')" sortable
            align="center" :formatter="currencyFormatter" />
        <el-table-column prop="bonus" :label="$t('statistics.salary.fields.bonus')" width="100" align="center" sortable
            :formatter="currencyFormatter" />
        <el-table-column prop="extra_bonus" :label="$t('statistics.salary.fields.extraBonus')" sortable align="center"
            :formatter="currencyFormatter" />
        <el-table-column prop="compensation" :label="$t('statistics.salary.fields.compensation')" align="center"
            :formatter="currencyFormatter" />
        <el-table-column prop="deduction" :label="$t('statistics.salary.fields.deduction')" align="center"
            :formatter="currencyFormatter" />
        <el-table-column prop="hst" :label="$t('fields.tax')" align="center" :formatter="currencyFormatter" />
        <el-table-column prop="tip" :label="$t('statistics.salary.fields.tip')" align="center" sortable
            :formatter="currencyFormatter" />
        <el-table-column prop="total" :label="$t('fields.total')" align="center" sortable class-name="bold"
            label-class-nam="bold" :formatter="currencyFormatter" />
        <el-table-column align="center" fixed="right" width="80">
            <template #default="scope">
                <el-button type="danger" size="small" @click="onDeleteSalary(scope.row)" v-if="scope.row.can_update">
                    {{ $t('operations.delete') }}
                </el-button>
            </template>
        </el-table-column>


    </el-table>
    <StepButtons v-bind="$attrs">
        <el-button type="primary" @click="onCreateSalaryReport" :disabled="selectedStubs.length <= 0">
            {{ $t('finance.eod.operations.generalReport') }}
        </el-button>

    </StepButtons>
</template>