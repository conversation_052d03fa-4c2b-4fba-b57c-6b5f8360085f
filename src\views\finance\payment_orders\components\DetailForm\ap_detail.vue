<script setup>
import { getAlphapayOrders } from '@/api/modules/payment';
import { currencyFormatter, userNameFormatter } from '@/utils/formatter'


const props = defineProps({
    id: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
    form: {
        id: props.id
    },
})

const logStyles = {
    PAY_SUCCESS: {
        type: 'success'
    },
    PAYING: {
        type: 'success',
        hollow: true
    },
    CREATE_FAIL: {
        type: 'info',
        hollow: true
    },
    CLOSED: {
        type: 'info'
    },
    PAY_FAIL: {
        type: 'info',
        hollow: true
    },
    FULL_REFUND: {
        type: 'danger'
    },
    PARTIAL_REFUND: {
        type: 'danger',
        hollow: true
    },
}


onMounted(() => {
    getInfo()
})

function getInfo() {
    data.value.loading = true
    getAlphapayOrders({ id: data.value.form.id }).then(res => {
        data.value.form = res.data
        data.value.loading = false
    })
}

</script>
<template>
    <el-space direction="vertical" alignment="start" size="large" class="logs">
        <el-space>
            <el-tag :type="logStyles[data.form.result_code]?.type" size="small" round>
                {{ data.form.result_code }}
            </el-tag>
            <span class="operator">{{ data.form.operator }}</span>
        </el-space>
        <el-descriptions :column="1">
            <template v-if="data.form.payment_order">
                <el-descriptions-item :label="$t('finance.paymentOrder.fields.no')" label-class-name="log-label">
                    {{ data.form.payment_order.no }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('fields.user')" label-class-name="log-label">
                    <el-space>
                        <el-tag v-if="data.form.user.user_code" size="small" type="info" effect="plain">{{
                            data.form.user.user_code
                        }}</el-tag>
                        <span>{{ userNameFormatter(data.form) }}</span>
                    </el-space>
                </el-descriptions-item>
            </template>
            <el-descriptions-item :label="$t('finance.apLog.fields.inputFee')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ currencyFormatter(_, _, data.form.input_fee) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.totalFee')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ currencyFormatter(_, _, data.form.total_fee) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.realFee')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ currencyFormatter(_, _, data.form.real_fee) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.currency')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.currency }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.rate')" label-align="right" label-class-name="log-label"
                class-name="log-content">
                {{ data.form.rate }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.desc')" label-align="right" label-class-name="log-label"
                class-name="log-content">
                {{ data.form.description }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.resultCode')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.result_code }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.orderId')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.order_id }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.customerId')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.customer_id }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.createTime')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.create_time }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.payTime')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.pay_time }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.returnCode')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.return_code }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.returnedMsg')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.return_message }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.channel')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.channel }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.channelErrCode')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.channel_error_code }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('finance.apLog.fields.channelErrMsg')" label-align="right"
                label-class-name="log-label" class-name="log-content">
                {{ data.form.channel_error_message }}
            </el-descriptions-item>
        </el-descriptions>
    </el-space>
</template>

