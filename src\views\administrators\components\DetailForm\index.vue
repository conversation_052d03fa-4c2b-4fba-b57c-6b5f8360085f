<script setup>
import { addAdministrator, getAdministrators, resetAdministratorPassword, updateAdministrator } from '@/api/modules/administrators'
import { getRoles } from '@/api/modules/permissions'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: '',
        username: '',
        role: null,
        is_super: false,
        is_available: true
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const roles = ref([])

const selectedRole = computed(() => {
    if (data.value.form.role) {
        return roles.value.find(r => r.id === data.value.form.role)
    }
})

const password = ref(null)

onMounted(() => {
    getRoleList()
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getAdministrators({
        id: data.value.form.id
    }
    ).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

function getRoleList() {
    getRoles().then(res => {
        roles.value = res.data
    })
}

function clearRole() {
    data.value.form.role = null
}

// Reset Password
const resetPasswordDialogVisible = ref(false)

function resetPassword() {
    resetPasswordDialogVisible.value = true
}

function submitNewPassword() {
    if (password.value) {
        let params = {
            id: data.value.form.id,
            newPassword: password.value
        }
        resetAdministratorPassword(params).then(res => {
            if (res.data.errCode === 365) {
                cancelNewPassword()
            }
        })
    } else {
        ElMessage.error('emptyPassword')
    }
}

function cancelNewPassword() {
    resetPasswordDialogVisible.value = false
    password.value = null
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            let params = {
                name: data.value.form.name,
                username: data.value.form.username,
                password: password.value,
                isSuper: data.value.form.is_super,
                isAvailable: data.value.form.is_available,
                roleId: data.value.form.role
            }
            if (password.value) {
                params['password'] = password.value
            }
            formRef.value.validate(valid => {
                if (valid) {
                    addAdministrator(params).then(res => {
                        if (res.data.errCode === 365) {
                            callback && callback()
                        }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    let params = {
                        id: data.value.form.id,
                        name: data.value.form.name,
                        isSuper: data.value.form.is_super,
                        isAvailable: data.value.form.is_available,
                        roleId: data.value.form.role
                    }
                    updateAdministrator(params).then(res => {
                        if (res.data.errCode === 365) {
                            callback && callback()
                        }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item v-if="data.form.avatar" :label="$t('administrator.fields.avatar')">
                <el-avatar>
                    <img :src="data.form.avatar">
                </el-avatar>
            </el-form-item>
            <el-form-item :label="$t('administrator.fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('administrator.fields.username')" prop="username">
                <el-input v-model="data.form.username" placeholder="请输入标题" :disabled="props.id != ''" />
            </el-form-item>
            <template v-if="data.form.username != 'automator'">
                <el-form-item :label="$t('administrator.fields.password')" prop="password">
                    <el-button v-if="props.id" type="warning" plain @click="resetPassword">
                        {{ $t('administrator.operations.resetPassword') }}
                    </el-button>
                    <el-input v-else v-model="password" placeholder="请输入标题" />
                </el-form-item>
                <template v-if="!data.form.is_super">
                    <el-form-item :label="$t('administrator.fields.role')" prop="role">
                        <div class="selector-role">
                            <el-select v-model="data.form.role" placeholder="Select">
                                <el-option v-for="item in roles" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                            <el-button text type="primary" @click="clearRole">{{ $t('operations.clear') }}</el-button>
                        </div>
                    </el-form-item>
                    <el-form-item :label="$t('administrator.fields.permissions')">
                        <div v-if="selectedRole" class="permission-items">
                            <span v-for="(item, index) in selectedRole.permissions" :key="index">
                                {{ $t(`${item.module.name}.title`) }} - {{ $t(`operations.${item.operation.name}`) }}
                            </span>
                        </div>
                    </el-form-item>
                </template>
                <el-form-item :label="$t('administrator.fields.isSuper')">
                    <el-switch v-model="data.form.is_super" />
                </el-form-item>
                <el-form-item :label="$t('fields.isAvailable')">
                    <el-switch v-model="data.form.is_available" class="availability-switch" />
                </el-form-item>
            </template>
            <template v-if="props.id">
                <el-form-item :label="$t('fields.createdAt')">
                    {{ data.form.created_at }}
                </el-form-item>
                <el-form-item :label="$t('fields.updatedAt')">
                    {{ data.form.updated_at }}
                </el-form-item>
            </template>
        </el-form>
    </div>
    <el-dialog v-model="resetPasswordDialogVisible" :title="$t('dialog.titles.resetPassword')" width="30%" align-center>
        <div class="password-dialog-body">
            <p>{{ $t('administrator.fields.newPassword') }}</p>
            <el-input v-model="password" size="small" clearable :show-password="true" type="password" />
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancelNewPassword">{{ $t('operations.cancel') }}</el-button>
                <el-button type="primary" @click="submitNewPassword">
                    {{ $t('operations.confirm') }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
.selector-role {
    display: flex;
    flex-direction: row;
    gap: 20px;
}

.permission-items {
    display: flex;
    flex-direction: column;
}

.password-dialog-body {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 20px;

    p {
        white-space: nowrap;
    }
}
</style>
