import {
    adminLogin,
    adminLogout,
    getAdminLoginState,
    getAdminPermissions,
    updateAdminPassword,
} from "@/api/modules/profile";

import useMenuStore from "./menu";
import useRouteStore from "./route";
import useTabbarStore from "./tabbar";
import { getToken, onMessage, deleteToken } from "firebase/messaging";
import { storeToken } from "@/api/modules/messenger";
import { useFirebaseMessaging } from '@/utils/composables/firebase_messaging'
import useMessagesStore from './messages'

import { vapidKey } from "@/firebase/index";

const messageStore = useMessagesStore()
const useUserStore = defineStore(
    // 唯一ID
    "user",
    {
        state: () => ({
            adminName: null,
            avatar: null,
            isSuper: false,
            permissions: [],
            fcmToken: null,
            tokenLastUpdated: null,
            stopReceivingMessages: false,
        }),
        getters: {
            isLogin: (state) => {
                return Boolean(state.adminName);
            },
            // 检查FCM token是否过期（token有效期通常为60天）
            isTokenExpired: (state) => {
                if (!state.tokenLastUpdated) return true;
                // token有效期为60天，这里设为58天检查，预留时间
                const tokenLifetime = 58 * 24 * 60 * 60 * 1000; // 58天的毫秒数
                return Date.now() - state.tokenLastUpdated > tokenLifetime;
            },
            // 获取消息接收状态
            isMessageReceivingStopped: (state) => {
                return state.stopReceivingMessages;
            }
        },
        actions: {
            checkLoginState() {
                return new Promise((resolve, reject) => {
                    getAdminLoginState()
                        .then((res) => {
                            this.adminName = res.data.name;
                            this.avatar = res.data.avatar;
                            this.isSuper = res.data.is_super;
                            resolve();
                        })
                        .catch((error) => {
                            resolve();
                            this.clearLoginState();
                        });
                });
            },
            login(data) {
                return new Promise((resolve, reject) => {
                    adminLogin(data)
                        .then((res) => {
                            if (res.data.errCode === 365) {
                                this.adminName = res.data.name;
                                this.avatar = res.data.avatar;
                                this.isSuper = res.data.is_super;
                            }
                            resolve();
                        })
                        .catch((error) => {
                            alert(error);
                            reject();
                        });
                });
            },
            async requestFCMPermission() {
                const messaging = useFirebaseMessaging()
                
                // 确保消息处理程序只注册一次
                try {
                    // 注册消息接收处理程序
                    const messageStore = useMessagesStore();
                    console.log('注册Firebase消息接收处理程序');
                    
                    // 前台消息处理程序 - 确保即使在页面前台也能正确接收和处理消息
                    onMessage(messaging, (payload) => {
                        console.log('Firebase前台消息接收:', payload);
                        // 将消息转发给messageStore处理
                        messageStore.onReceiveMessage(payload);
                    });
                    
                    console.log('Firebase消息接收处理程序注册成功');
                } catch (err) {
                    console.error('注册Firebase消息接收处理程序失败:', err);
                }
                
                // 请求通知权限并获取令牌
                try {
                    const permission = await Notification.requestPermission();
                    if (permission === 'granted') {
                        try {
                            // 检查现有token是否过期
                            if (this.isTokenExpired && this.fcmToken) {
                                console.log('FCM token已过期，尝试删除旧token');
                                await deleteToken(messaging);
                                this.fcmToken = null;
                            }
                            
                            // 获取新token
                            const currentToken = await getToken(messaging, { vapidKey: vapidKey });
                            if (currentToken) {
                                console.log('Firebase token已获取:', currentToken);
                                // 保存token到store
                                this.fcmToken = currentToken;
                                this.tokenLastUpdated = Date.now();
                                
                                // 发送token到服务器
                                await storeToken({ token: currentToken });
                                console.log('Firebase token已发送到服务器');
                                return currentToken;
                            } else {
                                console.warn('无法获取Firebase token，请检查通知权限');
                                return null;
                            }
                        } catch (err) {
                            console.error('获取Firebase token时发生错误:', err);
                            return null;
                        }
                    } else {
                        console.warn('通知权限未授予:', permission);
                        return null;
                    }
                } catch (err) {
                    console.error('请求通知权限时发生错误:', err);
                    return null;
                }
            },
            async logout() {
                await adminLogout()
                this.clearLoginState();
            },

            clearLoginState() {
                this.adminName = null;
                this.isSuper = false
                this.permissions = [];

                const tabbarStore = useTabbarStore();
                const routeStore = useRouteStore();
                const menuStore = useMenuStore();
                tabbarStore.clean();
                routeStore.removeRoutes();
                menuStore.setActived(0);
            },
            // 获取我的权限
            getPermissions() {
                return new Promise((resolve) => {
                    // 通过 mock 获取权限
                    getAdminPermissions().then((res) => {
                        this.permissions = res.data;
                        resolve(res.data);
                    });
                });
            },
            editPassword(data) {
                return new Promise((resolve) => {
                    updateAdminPassword({
                        password: data.password,
                        newPassword: data.newPassword,
                    }).then(() => {
                        resolve();
                    });
                });
            },
            // 切换消息接收状态
            toggleMessageReceiving() {
                this.stopReceivingMessages = !this.stopReceivingMessages;
                console.log(`消息接收已${this.stopReceivingMessages ? '停止' : '开启'}`);
                return this.stopReceivingMessages;
            },
            
            // 设置消息接收状态
            setMessageReceivingStatus(status) {
                this.stopReceivingMessages = status;
                console.log(`消息接收已${this.stopReceivingMessages ? '停止' : '开启'}`);
                return this.stopReceivingMessages;
            },
        },
    }
);

export default useUserStore;
