<script setup>
    import { updateDeliveryOrder } from '@/api/modules/delivery';
    import { phoneNumberFormatter } from '@/utils/formatter'

    const props = defineProps({
        id: {
            type: String,
            default: null
        },
        cardShadow: {
            type: String,
            default: 'always'
        },
        data: {
            type: Object,
            default: {},
        },
        part: {
            type: String,
            default: null
        }
    })


    defineExpose({
        submit() {
            updateAddresses()
        },
    })

    const formRef = ref()

    const emit = defineEmits(['success'])

    const data = ref({
        name: props.data.name,
        company: props.data.company,
        phone: props.data.phone,
        email: props.data.email,
        secondaryPhone: props.data.secondaryPhone,
        buzzerCode: props.data.buzzerCode
    })

    const addressesFormRules = {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        phone: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        email: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }


    function updateAddresses() {
        formRef.value.validate(valid => {
            if (valid) {
                let params = {
                    id: props.id,
                    part: props.part,
                    ...data.value,
                }
                updateDeliveryOrder(params).then(res => {
                    if (res.data.errCode === 365) {
                        emit('success')
                    }
                })
            }
        })
    }
</script>
<template>
    <el-card :header="$t('delivery.orders.fields.sender')"
        :class="props.part == 'pickup' ? 'sender-card' : 'receiver-card'" :shadow="cardShadow">
        <el-form ref="formRef" :rules="addressesFormRules" :model="data" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.name" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.companyName')" prop="company">
                <el-input v-model="data.company" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.phoneNumber')" prop="phone">
                <el-input v-model="data.phone" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.secondaryPhone')">
                <el-input v-model="data.secondaryPhone" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.email')" prop="email">
                <el-input v-model="data.email" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.buzzerCode')">
                <el-input v-model="data.buzzerCode" />
            </el-form-item>
        </el-form>
    </el-card>

</template>
<style scoped lang="scss">
    :deep(.el-card__header) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-weight: bolder;
        color: white;
    }

    .el-alert {
        margin: 20px 0 0;

        :deep(.el-alert__description) {
            margin: 0;
            padding: 0;
            line-height: normal;
        }
    }

    .el-alert:first-child {
        margin: 0;
    }

    .sender-card {

        :deep(.el-card__header) {
            background-color: #eebe77 !important;
        }
    }

    .receiver-card {

        :deep(.el-card__header) {
            background-color: #b3e19d !important;
        }
    }
</style>