<script setup>
import ListVue from './components/list.vue'
import { getPackaging, deletePackaging, updatePackagingAvailabilityPriority, batchAlterServiceAvailability, addPackaging, updatePackaging } from '@/api/modules/delivery'

</script>
<template>
    <ListVue v-bind="$attrs" :get-data="getPackaging" :on-delete="deletePackaging"
        :on-update-availability-priority="updatePackagingAvailabilityPriority"
        :on-batch-alter-availability="batchAlterServiceAvailability" :on-add="addPackaging" :on-update="updatePackaging"
        title="packaging" module="pk" />
</template>
