<script setup>
import DetailForm from '../DetailForm/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    // eslint-disable-next-line vue/valid-define-props
    ...DetailForm.props,
    modelValue: {
        type: Boolean,
        default: false
    },
})

const formRef = ref()

const emit = defineEmits(['update:modelValue', 'success'])

let myVisible = computed({
    get: function () {
        return props.modelValue
    },
    set: function (val) {
        emit('update:modelValue', val)
    }
})

const title = computed(() => props.id == '' ? '新增默认模块' : '编辑默认模块')

function onSubmit() {
    formRef.value.submit(() => {
        emit('success')
        onCancel()
    })
}

function onExecute() {
    formRef.value.execute(() => {
        emit('success')
        onCancel()
    })

}

function onAlterStatus(status) {
    formRef.value.alterStatus(status, () => emit('success'))
}

function onDelete() {
    formRef.value.delete(() => {
        emit('success')
        onCancel()
    })
}

function onCancel() {
    myVisible.value = false
}
</script>

<template>
    <div>
        <el-dialog v-model="myVisible" :title="title" width="600px" :close-on-click-modal="false" append-to-body
            destroy-on-close :show-close="false">
            <template #header>
                <div style="display: flex; flex-direction: row; justify-content: space-between;">
                    <div>ABC</div>
                    <el-button size="large" @click="onDelete" type="danger">
                        {{ $t('operations.delete') }}
                    </el-button>
                </div>
            </template>
            <DetailForm ref="formRef" v-bind="$props" />
            <template #footer>
                <div style="display: flex; flex-direction: row; justify-content: space-between;">
                    <div>
                        <el-button size="large" @click="onAlterStatus('voided')" v-if="formRef?.canVoid" type="danger"
                            plain>
                            {{ $t('todo.operations.void') }}
                        </el-button>
                        <el-button size="large" @click="onAlterStatus('pending')" v-if="formRef?.canPend" type="warning"
                            plain>
                            {{ $t('todo.operations.pend') }}
                        </el-button>
                        <el-button size="large" @click="onAlterStatus('scheduled')" v-if="formRef?.canSchedule"
                            type="success" plain>
                            {{ $t('todo.operations.schedule') }}
                        </el-button>
                    </div>
                    <el-button size="large" @click="onExecute" v-if="formRef?.canExecute" type="success">
                        {{ $t('todo.operations.executeNow') }}
                    </el-button>

                    <div>
                        <el-button size="large" @click="onCancel">{{ $t('operations.cancel') }}</el-button>
                        <el-button type="primary" size="large" @click="onSubmit" :disabled="!formRef?.canUpdate">
                            {{ $t('operations.save') }}
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
