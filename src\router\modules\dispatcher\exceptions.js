const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/exceptions',
    component: Layout,
    redirect: '/dispatcher/exceptions/logs',
    name: 'dispatcherExceptions',
    meta: {
        title: '异常处理',
        icon: 'mdi:address-marker-outline',
        auth: ['super'],
        i18n: 'route.dispatcher.exceptions.title'
    },
    children: [
        {
            path: 'logs',
            name: 'dispatcherExceptionLogs',
            component: () => import('@/views/dispatcher/exceptions/logs/list.vue'),
            meta: {
                title: '异常日志',
                icon: 'bx:key',
                activeMenu: '/dispatcher/exceptions/logs',
                i18n: 'route.dispatcher.exceptions.logs.title',
                auth: ['super']
            }
        },
        {
            path: 'log',
            name: 'dispatcherExceptionLog',
            component: () => import('@/views/dispatcher/exceptions/logs/detail.vue'),
            meta: {
                title: '异常日志',
                icon: 'bx:key',
                activeMenu: '/dispatcher/exceptions/logs',
                i18n: 'route.dispatcher.exceptions.logs.title',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },

    ]
}
