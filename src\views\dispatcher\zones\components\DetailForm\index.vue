<script setup>
import { addZone, getZones, updateZone, getZonePC, removeZonePc, getNewPostalCodes, addZonePcs } from '@/api/modules/dispatcher';
import { CircleCloseFilled, Search } from '@element-plus/icons-vue'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        fee: 0.0,
        postal_codes: []
    },
    fee: 0.0,
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

const activePC = ref('')

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getZones({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
        data.value.fee = res.data.fee / 100
    })
}

defineExpose({
    submit(callback) {
        let params = JSON.parse(JSON.stringify(data.value.form))
        params.fee = Math.round(data.value.fee * 100)
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addZone(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateZone(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})

const postalCodes = ref({})

function getPostalCodes(v) {
    activePC.value = v;

    let lastPC = v[v.length - 1]
    if (lastPC && !Object.keys(postalCodes.value).includes(lastPC)) getPCs(lastPC);
}

async function getPCs(code1) {
    let params = { id: data.value.form.id, code1: code1 }
    let res = await getZonePC(params)
    postalCodes.value[code1] = res.data;
    if (res.data.length == 0) {
        let p = data.value.form.postal_codes.find(x => x.code1 == code1)
        let idx = data.value.form.postal_codes.indexOf(p)
        if (idx >= 0) data.value.form.postal_codes.splice(idx, 1)
    } else {
        data.value.form.postal_codes.find(x => x.code1 == code1).count = res.data.length;
    }
}

async function removePostalCode(id, code1) {
    let params = { id: data.value.form.id, pcId: id }
    let res = await removeZonePc(params)
    if (res.data.errCode == 365) {
        getPCs(code1);
    }
}

// New Postal codes
const addPCDialogVisible = ref(false)
const newCode1 = ref(null)
const newPostalCodes = ref([])

function getNewPCs() {
    getNewPostalCodes({ code1: newCode1.value }).then(res => {
        newPostalCodes.value = res.data
    })
}

function removeFromNewPostalCodes(id) {
    let p = newPostalCodes.value.find(x => x.id == id)
    let idx = newPostalCodes.value.indexOf(p)
    if (idx >= 0) {
        newPostalCodes.value.splice(idx, 1)
    }
}

function hideAddDialog() {
    newCode1.value = null
    newPostalCodes.value.length = 0
    addPCDialogVisible.value = false
}

function addPostalCodes() {
    let params = {
        id: data.value.form.id,
        newPostalCodes: newPostalCodes.value.map(x => x.id)
    }
    addZonePcs(params).then(res => {
        if (res.data.errCode == 365) {
            getInfo()
        }
    })
    hideAddDialog()

}


</script>

<template>
    <div v-loading="data.loading">
        <page-main>
            <el-row>
                <el-col :md="24" :lg="16">
                    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                        <el-form-item :label="$t('fields.name')" prop="name">
                            <el-input v-model="data.form.name" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.desc')" prop="description">
                            <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" :rows="5" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.fee')" prop="fee">
                            <el-input-number v-model="data.fee" placeholder="请输入标题" :min="0.0" :step="0.1" :precision="2" />
                        </el-form-item>
                        <el-form-item :label="$t('fields.isAvailable')" prop="is_available">
                            <el-switch v-model="data.form.is_available" />
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
        </page-main>
        <page-main :title="$t('dispatcher.postalCodes.title')">
            <template #extra>
                <el-button type="primary" @click="addPCDialogVisible = true">{{ $t('operations.add') }}</el-button>
            </template>
            <el-row>
                <el-col :md="24" :lg="16">
                    <el-collapse v-model="activePC" @change="getPostalCodes">
                        <el-collapse-item v-for="pc in data.form.postal_codes" :title="pc.code1" :name="pc.code1">
                            <template #title>
                                {{ pc.code1 }}&thinsp; ({{ pc.count }})
                            </template>
                            <el-space v-for="p in postalCodes[pc.code1]">
                                <span>{{ p.code2 }}</span>
                                <el-icon>
                                    <CircleCloseFilled color="#F56C6C" style="cursor: pointer;"
                                        @click="removePostalCode(p.id, pc.code1)" />
                                </el-icon>
                                &ThickSpace;
                            </el-space>
                        </el-collapse-item>
                    </el-collapse>
                </el-col>
            </el-row>
        </page-main>
        <el-dialog v-model="addPCDialogVisible" :title="`Add postal codes to ${data.form.name}`" width="60%"
            :before-close="hideAddDialog">
            <div style="margin-bottom: 20px; width: 50%;">
                <el-input v-model="newCode1" placeholder="Input Postal code first 3 characters" maxlength="3"
                    @keyup.enter="getNewPCs">
                    <template #append>
                        <el-button :icon="Search" :disable="newCode1?.length ?? 0 < 3" @click="getNewPCs" />
                    </template>
                </el-input>
            </div>

            <template v-if="newPostalCodes.length > 0">
                <el-space v-for="p in newPostalCodes">
                    <span>{{ p.code2 }}</span>
                    <el-icon>
                        <CircleCloseFilled color="#F56C6C" style="cursor: pointer;"
                            @click="removeFromNewPostalCodes(p.id)" />
                    </el-icon>
                    &ThickSpace;
                </el-space>
            </template>
            <el-empty v-else />

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="hideAddDialog">Cancel</el-button>
                    <el-button type="primary" @click="addPostalCodes" :disabled="newPostalCodes.length == 0">
                        Confirm
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
