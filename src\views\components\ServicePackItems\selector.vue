<script setup>
import { getAllServicePackItems } from '@/api/modules/delivery'

const props = defineProps({
    modelValue: {
        type: Array,
        default: []
    }
})

const emit = defineEmits(['update:modelValue', 'onLoading'])

const items = ref([]);

onMounted(() => {
    nextTick(getItems);
})

let selectedItems = computed({
    get: function () {
        return props.modelValue.map(e => e.id)
    },
    set: function (val) {
        let res = [];
        for (let i of val) {
            let selectedItem = items.value.find(e => e.id == i)
            res.push({ id: selectedItem.id, descriptions: selectedItem.descriptions })
        }
        emit('update:modelValue', res)
    }
})

function getItems() {
    emit('onLoading', true)
    getAllServicePackItems().then(res => {
        items.value = res.data;
    })
    emit('onLoading', false)
}

watch(() => props.modelValue.length, (val) => {
    if (val) {
        for (let i of props.modelValue) {
            let item = items.value.find(e => e.id == i.id)
            item.descriptions = i.descriptions
        }
    }
}, { immediate: true })

</script>
<template>
    <el-checkbox-group v-model="selectedItems" v-if="items.length > 0">
        <div class="items-group" v-for="item of items">
            <el-checkbox :label="item.id">
                <div class="item-line">
                    <div class="name">{{ item.names['en-us'] }}</div>
                    <el-input v-model="item.descriptions['en-us']" :placeholder="$t('fields.desc')" />
                    <el-input v-model="item.descriptions['zh-cn']" :placeholder="$t('fields.descZh')" />
                    <el-input v-model="item.descriptions['zh-tw']" :placeholder="$t('fields.descTw')" />
                    <el-input v-model="item.descriptions['fr-fr']" :placeholder="$t('fields.descFr')" />
                    <el-input v-model="item.descriptions['es-es']" :placeholder="$t('fields.descEs')" />
                </div>
            </el-checkbox>
        </div>
    </el-checkbox-group>
</template>
<style lang="scss" scoped>
.items-group {
    display: flex;
    flex-flow: column nowrap;
    margin-bottom: 6px;

    .item-line {
        display: flex;
        flex-direction: row;
        place-items: center center;
        place-content: center center;
        place-self: center center;

        .name {
            width: 300px;
            max-width: 300px;
            min-width: 300px;
        }

        .el-input {
            width: 20%;
            margin-right: 12px;
        }

    }

}
</style>
