<script setup name="AdministratorsList">
import { deleteAdministrator, getAdministrators } from '@/api/modules/administrators'
import { usePagination } from '@/utils/composables'
import eventBus from '@/utils/eventBus'
import { Delete, UserFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    // 列表数据
    dataList: []
})

onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams()
    getAdministrators(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'administratorCreate'
    })
}

function onEdit(row) {
    router.push({
        name: 'administratorEdit',
        params: {
            id: row.id
        }
    })
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deleteAdministrator({ id: row.id }).then(() => {
            getDataList()
        })
    }).catch(() => { })
}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('administrator.title')" />
        <page-main>
            <div class="top-buttons">
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                :row-class-name="tableRowClassName" :row-style="{ cursor: 'pointer' }" highlight-current-row
                @sort-change="sortChange" @row-dblclick="onEdit">
                <el-table-column prop="is_self" align="center" width="50">
                    <template #default="scope">
                        <el-icon v-if="scope.row.is_self" :size="20">
                            <UserFilled />
                        </el-icon>
                    </template>
                </el-table-column>
                <el-table-column prop="avatar" :label="$t('administrator.fields.avatar')">
                    <template #default="scope">
                        <el-avatar>
                            <img :src="scope.row.avatar">
                        </el-avatar>
                    </template>
                </el-table-column>
                <el-table-column prop="name" :label="$t('administrator.fields.name')" />
                <el-table-column prop="username" :label="$t('administrator.fields.username')" />
                <el-table-column prop="role" :label="$t('administrator.fields.role')" align="center">
                    <template #default="scope">
                        <el-tag v-if="scope.row.is_super" type="success">
                            Super
                        </el-tag>
                        <el-tag v-else-if="scope.row.role" type="primary">
                            {{ scope.row.role }}
                        </el-tag>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" width="160" :label="$t('user.fields.createdAt')" sortable="custom" />
                <el-table-column prop="updated_at" width="160" :label="$t('user.fields.updatedAt')" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)"
                            v-if="scope.row.username != 'automator' && !scope.row.is_self" />
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class=" pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.link-row {
    cursor: pointer;
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
