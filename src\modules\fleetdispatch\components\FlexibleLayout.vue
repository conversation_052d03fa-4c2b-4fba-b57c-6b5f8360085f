<template>
    <div class="flexible-layout">
        <Splitpanes class="default-theme main-layout" ref="splitpanesRef">
            <!-- 左侧司机和路线列表 -->
            <Pane :size="20" min-size="15">
                <div class="panel-content">
                    <slot name="driver-route-panel" />
                </div>
            </Pane>

            <!-- 右侧地图和订单的上下布局 -->
            <Pane :size="80" min-size="50">
                <Splitpanes horizontal ref="innerSplitpanesRef">
                    <!-- 上部分：地图 -->
                    <Pane :size="60" min-size="40">
                        <div class="panel-content map-container">
                            <slot name="map-view" />
                        </div>
                    </Pane>

                    <!-- 下部分：订单列表 -->
                    <Pane :size="40" min-size="20">
                        <!-- <div class="panel-header">
              <h3>订单列表 <span class="count-badge">({{ orderCount }})</span></h3>
            </div> -->
                        <div class="panel-content">
                            <slot name="order-list" />
                        </div>
                    </Pane>
                </Splitpanes>
            </Pane>
        </Splitpanes>
    </div>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import { computed, ref, onMounted, nextTick } from 'vue'
import { useOrderStore } from '../stores/order'
import { useDriverStore } from '../stores/driver'
import { useRouteStore } from '../stores/route'

const splitpanesRef = ref(null)
const innerSplitpanesRef = ref(null)

const orderStore = useOrderStore()
const driverStore = useDriverStore()
const routeStore = useRouteStore()

const orderCount = computed(() => orderStore.orders?.length || 0)
const driverCount = computed(() => driverStore.drivers?.length || 0)
const routeCount = computed(() => routeStore.routes?.length || 0)

// 确保DOM完全渲染后再初始化splitpanes
onMounted(() => {
    // 使用Vue的nextTick而不是setTimeout
    nextTick(() => {
        initSplitpanes()
    })
})

// 初始化Splitpanes并添加重试机制
const initSplitpanes = () => {
    try {
        if (splitpanesRef.value) {
            splitpanesRef.value.$forceUpdate()

            // 不使用$on，因为在Vue3中可能不兼容
            // 改用监听splitpanes自定义事件机制
        }

        if (innerSplitpanesRef.value) {
            innerSplitpanesRef.value.$forceUpdate()

            // 不使用$on，因为在Vue3中可能不兼容
        }

        // 防止递归调用的重新实现
        const handleResize = () => {
            // 防止递归调用window.dispatchEvent
            if (splitpanesRef.value) splitpanesRef.value.$forceUpdate()
            if (innerSplitpanesRef.value) innerSplitpanesRef.value.$forceUpdate()
        }

        // 添加窗口大小变化事件监听，移除之前可能递归触发的代码
        window.addEventListener('resize', handleResize)
    } catch (error) {
        console.warn('Splitpanes初始化失败，正在重试', error)
        // 如果失败，再次尝试初始化
        setTimeout(initSplitpanes, 200)
    }
}
</script>

<style scoped>
.flexible-layout {
    width: 100%;
    height: calc(100vh - var(--g-header-height));
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    height: 40px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    background-color: #f5f7fa;
    z-index: 1;
    position: sticky;
    top: 0;
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.panel-content {
    height: calc(100% - 0px);
    overflow: auto;
    padding: 0;
}

/* 单一的地图容器定义 */

.map-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

/* 专门针对地图面板的样式 */

.panel-content.map-container {
    height: 100%;
    padding: 0;
    position: relative;
    overflow: hidden;
}

/* 确保地图元素本身正确填充 */

:deep(#map) {
    width: 100% !important;
    height: 100% !important;
}

/* 针对订单列表的样式调整 */

.panel-content:last-child {
    height: 100%;
    overflow: hidden;
}

:deep(.splitpanes) {
    height: 100%;
    background-color: transparent;
}

:deep(.splitpanes__pane) {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    background-color: white;
}

/* 水平分割线样式 */

:deep(.splitpanes--horizontal > .splitpanes__splitter) {
    height: 8px;
    background-color: #ddd;
}

:deep(.splitpanes--horizontal > .splitpanes__splitter::before) {
    width: 30px;
    height: 2px;
    transform: translate(-50%, -50%);
}

/* 自定义分割条样式 */

:deep(.splitpanes__splitter) {
    background-color: #ddd;
    position: relative;
    min-width: 8px;
    margin: 0 1px;
    transition: background-color 0.2s;
}

:deep(.splitpanes__splitter:hover) {
    background-color: #bbb;
}

:deep(.splitpanes__splitter::before) {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 30px;
    background-color: #999;
    transform: translate(-50%, -50%);
    border-radius: 3px;
}

:deep(.splitpanes__splitter:hover::before) {
    background-color: #666;
}

.count-badge {
    font-size: 14px;
    color: #666;
    font-weight: normal;
    margin-left: 5px;
}

/* 确保主布局占满整个容器 */

.main-layout {
    width: 100%;
    height: 100%;
}
</style>