<script setup name="ImageUploader">
import { nextTick } from "vue";
const props = defineProps({
    images: {
        type: Array,
        required: true
    },
    action: {
        type: Function,
        required: true
    },
    onRemove: {
        type: Function
    },
    noTip: {
        type: Boolean,
        default: false
    },
    ext: {
        type: Array,
        default: () => ['jpg', 'png']
    },
    width: {
        type: Number,
        default: 150
    },
    height: {
        type: Number,
        default: 150
    },
    size: {
        type: Number,
        default: 2
    },


})

const data = ref({
    refresh: true
})

watch(() => props.images,
    () => {
        data.value.refresh = false
        nextTick(() => {
            data.value.refresh = true
        })

    },
    { deep: true }
)

const emit = defineEmits(['update:url', 'on-success'])

const uploadData = ref({
    imageViewerVisible: false,
    progress: {
        preview: '',
        percent: 0
    },
    previewInitIndex: 0
})

// 预览
function preview(file) {
    uploadData.value.imageViewerVisible = true
    uploadData.value.previewInitIndex = props.images.indexOf(file)
}

// 关闭预览
function previewClose() {
    uploadData.value.imageViewerVisible = false
}
// 移除
function remove() {
    emit('update:url', '')
}
function beforeUpload(file) {
    const fileName = file.name.split('.')
    const fileExt = fileName.at(-1)
    const isTypeOk = props.ext.indexOf(fileExt.toLowerCase()) >= 0
    const isSizeOk = file.size / 1024 / 1024 < props.size
    if (!isTypeOk) {
        ElMessage.error(`上传图片只支持 ${props.ext.join(' / ')} 格式！`)
    }
    if (!isSizeOk) {
        ElMessage.error(`上传图片大小不能超过 ${props.size}MB！`)
    }
    if (isTypeOk && isSizeOk) {
        uploadData.value.progress.preview = URL.createObjectURL(file)
    }
    return isTypeOk && isSizeOk
}
function onProgress(file) {
    uploadData.value.progress.percent = ~~file.percent
}
function onSuccess(res, file, fileList) {
    fileList = props.images
    uploadData.value.progress.preview = ''
    uploadData.value.progress.percent = 0
    emit('on-success', res)
}

function handleRemove(res) {
    props.onRemove && props.onRemove(res)
}

</script>

<template>
    <div class="upload-container">
        <el-upload v-if="data.refresh" :file-list.sync="props.images" action="#" list-type="picture-card"
            :on-preview="preview" :on-remove="handleRemove" :http-request="action" :before-upload="beforeUpload"
            :on-progress="onProgress" :on-success="onSuccess">
            <el-icon>
                <svg-icon name="i-ep:plus" />
            </el-icon>
        </el-upload>
        <div v-if="!noTip" class="el-upload__tip">
            <div style="display: inline-block;">
                <el-alert :title="`上传图片支持 ${ext.join(' / ')} 格式，且图片大小不超过 ${size}MB，建议图片尺寸为 ${width}*${height}`" type="info"
                    show-icon :closable="false" />
            </div>
        </div>
        <el-image-viewer v-if="uploadData.imageViewerVisible" :url-list="props.images.map(m => m.url)"
            :initial-index="uploadData.previewInitIndex" @close="previewClose" />
    </div>
</template>

<style lang="scss" scoped>
.upload-container {
    line-height: initial;
}
</style>
