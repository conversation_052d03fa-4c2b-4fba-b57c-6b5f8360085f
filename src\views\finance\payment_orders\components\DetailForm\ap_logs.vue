<script setup>
import { usePagination } from '@/utils/composables'
import { getAlphapayOrders } from '@/api/modules/payment';
import { currencyFormatter } from '@/utils/formatter'

const { pagination, getParams, onCurrentChange } = usePagination()

const props = defineProps({
    orderId: {
        type: String,
        default: null
    }
})

const data = ref({
    loading: false,
    dataList: [],
})

const logStyles = {
    PAY_SUCCESS: {
        type: 'success'
    },
    PAYING: {
        type: 'success',
        hollow: true
    },
    CREATE_FAIL: {
        type: 'info',
        hollow: true
    },
    CLOSED: {
        type: 'info'
    },
    PAY_FAIL: {
        type: 'info',
        hollow: true
    },
    FULL_REFUND: {
        type: 'danger'
    },
    PARTIAL_REFUND: {
        type: 'danger',
        hollow: true
    },
}

defineExpose({
    reload() {
        getDataList()
    }
})


onMounted(() => {
    getDataList()
})

function getDataList() {
    data.value.loading = true
    let params = getParams({ paymentOrderId: props.orderId })
    getAlphapayOrders(params).then(res => {
        pagination.value.total = res.data.total
        data.value.dataList = res.data.order_list
        data.value.loading = false
    })
}
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

</script>
<template>
    <page-main>
        <template #title>
            Alphapay&ThickSpace;{{ $t('finance.paymentOrder.sections.log') }}
        </template>
        <el-timeline v-if="data.dataList?.length">
            <el-timeline-item v-for="(item, index) in data.dataList" :key="index" :timestamp="item.create_time"
                :type="logStyles[item.result_code]?.type" :hollow="logStyles[item.result_code]?.hollow" placement="top">
                <el-space direction="vertical" alignment="start" size="large" class="logs">
                    <el-space>
                        <el-tag :type="logStyles[item.result_code]?.type" size="small" round>
                            {{ item.result_code }}
                        </el-tag>
                        <span class="operator">{{ item.operator }}</span>
                    </el-space>
                    <el-descriptions :column="1">
                        <el-descriptions-item :label="$t('finance.apLog.fields.inputFee')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ currencyFormatter(_, _, item.input_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.totalFee')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ currencyFormatter(_, _, item.total_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.realFee')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ currencyFormatter(_, _, item.real_fee) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.realFee')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ currencyFormatter(_, _, item.available_amount) }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.currency')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.currency }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.rate')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.rate }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.desc')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.order_description }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.resultCode')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.result_code }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.orderId')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.order_id }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.customerId')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.customer_id }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.createTime')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.create_time }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.payTime')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.pay_time }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.returnCode')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.return_code }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.returnedMsg')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.return_message }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.channel')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.channel }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.channelErrCode')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.channel_error_code }}
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('finance.apLog.fields.channelErrMsg')" label-align="right"
                            label-class-name="log-label" class-name="log-content">
                            {{ item.channel_error_message }}
                        </el-descriptions-item>
                    </el-descriptions>
                </el-space>
            </el-timeline-item>
        </el-timeline>
        <el-empty v-else :description="$t('noData')" />
        <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
            :layout="pagination.layoutM" :hide-on-single-page="true" class="pagination" @current-change="currentChange" />
    </page-main>
</template>

