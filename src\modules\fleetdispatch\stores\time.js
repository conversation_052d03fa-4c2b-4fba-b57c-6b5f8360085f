import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import { shiftAPI } from '../api' // 导入 shiftAPI

export const useTimeStore = defineStore('time', {
    state: () => ({
        selectedDate: dayjs().year(new Date().getFullYear()).format('YYYY-MM-DD'),
        selectedShift: ref(null), // 初始化为 null
        _shifts: ref([]), // 用于存储从 API 获取的原始班次数据
        isLoadingShifts: ref(false) // 添加加载状态
    }),

    getters: {
    // 获取班次选项 - 现在从 state 中的 _shifts 派生
        shifts: (state) => {
            if (!state._shifts || state._shifts.length === 0) {
                return [];
            }
            return state._shifts
                .filter(shift => shift.is_available) // 只显示可用的班次
                .map(shift => ({
                    label: `${shift.name} (${shift.start_at}-${shift.end_at})`, // 格式化标签
                    value: shift.id, // 使用 id 作为 value
                    ...shift // 保留原始班次对象的所有属性
                }));
        },

        // 获取当前时间范围 - 使用新的 selectedShift 结构
        currentTimeRange: state => {
            // const shift = state.shifts.find(s => s.value === state.selectedShift)
            const shift = state.selectedShift // selectedShift 现在是完整的对象
            if (!shift || !shift.start_at || !shift.end_at) return null

            const currentDate = dayjs(state.selectedDate)
            // 直接使用 start_at 和 end_at (格式为 HH:mm)
            return {
                start: `${currentDate.format('YYYY-MM-DD')} ${shift.start_at}:00`,
                end: `${currentDate.format('YYYY-MM-DD')} ${shift.end_at}:00`
            }
        }

    },

    actions: {
        // 获取班次数据
        async fetchShifts() {
            this.isLoadingShifts = true;
            try {
                console.log('timeStore: 开始获取班次数据...');
                const response = await shiftAPI.getShifts();
                console.log('timeStore: 班次API响应:', response);

                // 检查响应是否为数组
                if (response && Array.isArray(response)) {
                    this._shifts = response;
                    console.log(`timeStore: 成功获取 ${this._shifts.length} 个班次:`, this._shifts);

                    // 检查班次数据是否包含必要的字段
                    const validShifts = this._shifts.filter(shift =>
                        shift && shift.name && shift.start_at && shift.end_at
                    );

                    if (validShifts.length === 0) {
                        console.warn('timeStore: 班次数据缺少必要字段，使用默认班次');
                        this._shifts = this.getDefaultShifts();
                    }
                } else {
                    console.error('timeStore: 获取班次数据格式不正确:', response);
                    this._shifts = this.getDefaultShifts();
                }
            } catch (error) {
                console.error('timeStore: 获取班次数据失败:', error);
                this._shifts = this.getDefaultShifts();
            } finally {
                this.isLoadingShifts = false;
            }

            // 确保 _shifts 不为空
            if (!this._shifts || this._shifts.length === 0) {
                console.warn('timeStore: 班次列表为空，使用默认班次');
                this._shifts = this.getDefaultShifts();
            }

            // 设置当前班次
            this.setCurrentShift();

            return this._shifts;
        },

        // 获取默认班次数据
        getDefaultShifts() {
            const defaultShifts = [
                {
                    id: 1,
                    name: 'AM',
                    label: 'AM (08:00-12:00)',
                    start_at: '08:00',
                    end_at: '12:00',
                    is_available: true
                },
                {
                    id: 2,
                    name: 'PM',
                    label: 'PM (12:00-18:00)',
                    start_at: '12:00',
                    end_at: '18:00',
                    is_available: true
                },
                {
                    id: 3,
                    name: 'NT',
                    label: 'NT (18:00-22:00)',
                    start_at: '18:00',
                    end_at: '22:00',
                    is_available: true
                }
            ];

            console.log('timeStore: 使用默认班次数据:', defaultShifts);
            return defaultShifts;
        },

    // 更新日期
        setDate(date) {
            this.selectedDate = date
        },

        // 更新班次 - 现在接收完整的班次对象
        setShift(shiftObject) {
            // 确保传入的是对象，而不是 null 或 undefined
            if (shiftObject && typeof shiftObject === 'object') {
                 this.selectedShift = shiftObject;
            } else {
                 console.warn('尝试设置无效的班次对象:', shiftObject);
                 this.selectedShift = null; // 或者设置为默认值
            }
        },

        // 根据当前时间自动设置班次 - 使用 _shifts 和 dayjs 比较
        setCurrentShift() {
            if (this._shifts.length === 0) {
                console.warn('无法设置当前班次，班次列表为空');
                this.selectedShift = null;
                return;
            }

            const now = dayjs();

            let foundShift = null;
            for (const shift of this._shifts) {
                if (!shift.is_available || !shift.start_at || !shift.end_at) continue; // 跳过不可用或数据不完整的班次

                const start = dayjs(shift.start_at, 'HH:mm');
                const end = dayjs(shift.end_at, 'HH:mm');

                // 处理跨天班次 (结束时间早于开始时间)
                if (end.isBefore(start)) {
                    // 检查当前时间是否在 [start, 24:00) 或 [00:00, end)
                    if (now.isAfter(start) || now.isSame(start) || now.isBefore(end)) {
                        foundShift = shift;
                        break;
                    }
                } else {
                    // 普通班次 (当天)
                    // 检查当前时间是否在 [start, end)
                    if ((now.isAfter(start) || now.isSame(start)) && now.isBefore(end)) {
                        foundShift = shift;
                        break;
                    }
                }
            }

            if (foundShift) {
                this.selectedShift = foundShift;
                console.log('根据当前时间自动设置班次为:', foundShift.name);
            } else {
                console.warn('当前时间不在任何可用班次范围内，将设置默认班次');
                // 如果找不到匹配的班次，默认选择第一个可用的班次
                this.selectedShift = this._shifts.find(s => s.is_available) || this._shifts[0] || null;
            }
        }
    }
})