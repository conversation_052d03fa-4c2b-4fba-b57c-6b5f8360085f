/*
* Author: <EMAIL>'
* Date: '2023-04-29 20:03:35'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/payment/todos.js'
* File: 'todos.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

import pinia from '@/store'
import useTodosStore from '@/store/modules/todos'
const todosStore = useTodosStore(pinia)


export default {
    path: '/payment/todos',
    component: Layout,
    redirect: { path: '/payment/todos/list', query: { module: 'payment' } },
    name: 'paymentTodos',
    meta: {
        title: '待办事务',
        icon: 'logos:todomvc',
        auth: ['super'],
        i18n: 'route.todo.title',
        badge: () => todosStore.payment.length
    },
    children: [
        {
            path: 'list',
            name: 'paymentTodoList',
            component: () => import('@/views/finance/todos/todos/list.vue'),
            props: { module: 'payment' },
            meta: {
                title: '事务列表',
                icon: 'logos:todomvc',
                i18n: 'route.todo.title',
                activeMenu: '/payment/todos',
                auth: ['super'],
                sidebar: false,
                breadcrumb: false,

            }
        },

    ]
}
