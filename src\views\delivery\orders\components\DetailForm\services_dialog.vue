<script setup>

import { updateDeliveryOrder, getServicesByOrder } from '@/api/modules/delivery';
import { ElMessageBox } from 'element-plus'
import { currencyFormatter } from '@/utils/formatter'
import { useI18n } from 'vue-i18n';
import { Minus, Plus } from '@element-plus/icons-vue'

const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: null
    },
    id: {
        type: String,
        default: null
    },
    order: {
        type: Object,
        default: undefined
    }
})

const emit = defineEmits(['update:modelValue', 'success'])
const selected = ref([])
const options = ref([])


const multiChoicesType = ['addedServices', 'promotions']

const dialogVisible = computed({
    get: () => { return props.modelValue },
    set: (val) => {
        emit('update:modelValue', val)
        return val
    }
})

// Api
function onUpdateServices() {
    var params = {
        id: props.order.id,
    }
    switch (props.type) {
        case 'packageType':
            params.typeId = selected.value.id
            break
        case 'sizeWeight':
            params.sizeId = selected.value.id
            break
        case 'addedServices':
            params.addedServiceIds = selected.value.map(s => s.id)
            break
        case 'promotions':
            params.promotionIds = selected.value.map(s => s.id)
            break
        case 'insurance':
            params.insuranceId = selected.value.id
            break
        default:
            return
    }
    updateDeliveryOrder(params).then(res => {
        if (res.data.errCode === 365) {
            emit('success')
            if (res.data.total_difference) {
                ElMessageBox.alert(
                    `${t('delivery.orders.messages.totalBeenUpdated')} <br /><b>${currencyFormatter(null, null, res.data.total_difference)}</b>`,
                    'Warning',
                    {
                        confirmButtonText: 'OK',
                        type: 'warning',
                        dangerouslyUseHTMLString: true,
                    }
                )
            }
        }
        dialogVisible.value = false
    })

}

const getOptions = () => {
    if (['packageType', 'sizeWeight', 'addedServices', 'packaging'].includes(props.type)) {
        let params = {
            orderId: props.order.id,
            type: props.type
        }
        getServicesByOrder(params).then((res) => {
            if (!res.data.errCode) {
                options.value = res.data
            }
        })
    }
}


// Methods
const init = () => {
    getOptions()
    if (props.order != undefined) {
        switch (props.type) {
            case 'packageType':
                selected.value = JSON.parse(JSON.stringify(props.order.extended_info.package_type))
                break
            case 'sizeWeight':
                selected.value = JSON.parse(JSON.stringify(props.order.extended_info.size_weight))
                break
            case 'addedServices':
                selected.value = JSON.parse(JSON.stringify(props.order.extended_info.added_services))
                break
            case 'packaging':
                props.order.extended_info.packaging.forEach(p => {
                    selected.value.push(
                        {
                            packaging: {
                                id: p.id,
                                name: p.name,
                                fee: p.fee
                            },
                            quantity: p.quantity
                        }
                    )
                }
                )
                break
            case 'promotions':
                selected.value = JSON.parse(JSON.stringify(props.order.extended_info.promotion_infos))
            case 'insurance':
                selected.value = JSON.parse(JSON.stringify(props.order.extended_info.insurance_policy))
            default:
                return
        }
    }
}

const clearData = () => {
    options.value = []
    selected.value = []
}

const addPackaging = () => {
    selected.value.push(JSON.parse(JSON.stringify(packagingObj)))
}

const removePackaging = (idx) => {
    selected.value.splice(idx, 1)
}

const packagingObj = {
    packaging: {
        id: null,
        name: null,
        fee: null
    },
    quantity: 1
}
const onCancelSelect = () => {
    dialogVisible.value = false
    clearData()
}

</script>
<template>
    <el-dialog v-model="dialogVisible" :title="$t('delivery.orders.fields.' + props.type)" width="30%" align-center
        destroy-on-close @open="init" @close="clearData">
        <template v-if="props.type == 'packaging'">
            <template v-if="!selected.length">
                <el-button type="danger" :icon="Minus" circle plain size="small" @click="removePackaging(idx)" />
                <el-button type="success" :icon="Plus" circle plain size="small" @click="addPackaging" />
            </template>
            <template v-for="(item, idx) in selected" :key="idx">
                <el-row :gutter="10" align="middle" class="margin-top">
                    <el-col :span="14">
                        <el-select v-model="item.packaging" class="m-2"
                            :placeholder="$t('selectPlaceHolder', { field: $t('delivery.orders.fields.' + props.type) })"
                            size="large" prop="service" value-key="id" no-data-text="No available service">
                            <el-option v-for="item in options" :key="item.id" :label="item.names['en-us']" :value="item"
                                :disabled="selected.includes(item.id)" />
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-input-number v-model="item.quantity" :min="1" :max="10" @change="handleChange"
                            class="packaging-quantity" controls-position="right" size="large" />
                    </el-col>
                    <el-col :span="4" align="right">
                        <el-button type="danger" :icon="Minus" circle plain size="small"
                            @click="removePackaging(idx)" />
                        <el-button type="success" :icon="Plus" circle plain size="small" @click="addPackaging" />
                    </el-col>
                </el-row>
            </template>
        </template>
        <template v-else>
            <el-select v-model="selected" class="m-2"
                :placeholder="$t('selectPlaceHolder', { field: $t('delivery.orders.fields.' + props.type) })"
                size="large" prop="service" value-key="id" :multiple="multiChoicesType.includes(props.type)"
                no-data-text="No available service">
                <el-option v-for="item in options" :key="item.id" :label="item.names['en-us']" :value="item" />
            </el-select>
            <template v-if="selected && selected.length">
                <el-descriptions v-for="item, idx in selected" :key="idx" class="margin-top" :column="1" size="small"
                    border>
                    <el-descriptions-item :label="$t('fields.name')">
                        {{ item.name }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('delivery.services.fields.fee')">
                        {{ currencyFormatter(null, null, item.fee) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('fields.desc')">
                        {{ item.description }}
                    </el-descriptions-item>
                </el-descriptions>
            </template>
        </template>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="onCancelSelect">
                    {{ $t('operations.cancel') }}
                </el-button>
                <el-button type="primary" @click="onUpdateServices">
                    {{ $t('operations.confirm') }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>
<style lang="scss" scoped>
.margin-top {
    margin-top: 20px;
}

:deep(.el-descriptions__label) {
    width: 90px;
}

.packaging-quantity {
    width: 100%;
    min-width: 120px;
}
</style>