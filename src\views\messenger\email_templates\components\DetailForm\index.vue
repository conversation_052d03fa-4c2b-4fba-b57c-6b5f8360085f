<script setup>
    import { getEmailTemplates, updateEmailTemplate, addEmailTemplate, getCategoryList, previewEmailTemplate } from '@/api/modules/messenger'
    import { languages } from '@/utils/constants'
    import { getUserSimpleList } from '@/api/modules/users'
    const props = defineProps({
        id: {
            type: String,
            default: ''
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
        },
        rules: {
            priority: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            name: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            language: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            subject: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            category: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            plain_text: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
        },
        previewRules: {
            user_id: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            order_id: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],

        }
    })

    const categories = ref([])

    const previewParams = ref({})
    const userList = ref()

    onMounted(() => {
        getCategories();
        if (data.value.form.id != null) {
            getInfo()
        }
    })

    function getCategories() {
        getCategoryList().then(res => {
            categories.value = res.data;
        })
    }


    async function getInfo() {
        data.value.loading = true
        getEmailTemplates({ id: data.value.form.id }).then(res => {
            data.value.loading = false
            data.value.form = res.data
        })
    }

    defineExpose({
        submit(callback) {
            let params = JSON.parse(JSON.stringify(data.value.form))
            params.category = data.value.form.category.id
            if (data.value.form.id == undefined) {
                formRef.value.validate(valid => {
                    if (valid) {
                        addEmailTemplate(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            } else {
                formRef.value.validate(valid => {
                    if (valid) {
                        updateEmailTemplate(params).then((res) => {
                            if (res.data.errCode == 365) { callback && callback() }
                        })
                    }
                })
            }
        },
        preview() {
            data.value.form.category.params.forEach(e => {
                console.log(e)
                if (e.name.startsWith('user')) {
                    previewParams.value['user_id'] = null
                } else if (e.name.startsWith('order')) {
                    previewParams.value['order_id'] = null
                } else {
                    previewParams.value[e.name] = null
                }

            });
            previewDialogVisible.value = true;

        }
    })

    const previewDialogVisible = ref(false)
    function getUsers(query) {
        if (query && query.length > 3) {
            getUserSimpleList({ terms: query }).then(res => {
                userList.value = res.data
            })
        } else {
            userList.value = []
        }
    }


    async function onConfirmPreview() {
        if (data.value.form.id == '') {
            await getInfo();
        }
        let params = { id: data.value.form.id, ...previewParams.value }
        previewEmailTemplate(params);
        onCancelPreview();
    }
    function onCancelPreview() {
        previewParams.value = ref({})
        previewDialogVisible.value = false;
    }
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.priority')" prop="priority">
                <el-input v-model="data.form.priority" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" :maxlength="50" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" />
            </el-form-item>
            <el-form-item :label="$t('fields.language')" prop="language">
                <el-select v-model="data.form.language" class="m-2" placeholder="Select">
                    <el-option v-for="item in languages" :key="item"
                        :label="$t(`messenger.selections.languageSelections.${item}`)" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('messenger.email.fields.subject')" prop="subject">
                <el-input v-model="data.form.subject" placeholder="请输入标题" :maxlength="100" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('fields.category')" prop="category">
                <el-select v-model="data.form.category" value-key="id" class="m-2" placeholder="Select" size="large">
                    <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('messenger.emailTemplate.fields.params')">
                <el-descriptions :column="1" border>
                    <el-descriptions-item v-for="item in data.form.category?.params" :label="`\{${item.name}\}`"
                        label-align="left">
                        {{ item.description }}
                    </el-descriptions-item>
                </el-descriptions>
            </el-form-item>
            <br />
            <el-form-item :label="$t('messenger.email.fields.plainText')" prop="plain_text">
                <el-input v-model="data.form.plain_text" placeholder="请输入标题" type="textarea" rows="5" />
            </el-form-item>
            <el-form-item :label="$t('messenger.email.fields.richText')" prop="rich_text">
                <editor v-model="data.form.rich_text" />
            </el-form-item>
        </el-form>
        <el-dialog v-model="previewDialogVisible" title="Input Params" width="30%">
            <el-form :model="previewParams" :rules="data.previewRules" label-width="80px">
                <el-form-item :label="$t('fields.user')" v-if="Object.keys(previewParams).includes('user_id')"
                    prop="user_id">
                    <el-select v-model="previewParams.user_id" value-key="id" filterable remote reserve-keyword
                        clearable :remote-method="getUsers" placeholder="Input user name or company name to search">
                        <el-option v-for="item of userList" :value="item.id" :label="item.name">
                            {{ item.name }} {{ item.code ? (`(${item.code})`) : '' }}
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('fields.order')" v-if="Object.keys(previewParams).includes('order_id')"
                    prop="order_id">
                    <!-- <el-select v-model="previewParams.order_id" value-key="id" filterable remote reserve-keyword clearable
                        :remote-method="getUsers" placeholder="Input user name or company name to search">
                        <el-option v-for="item of userList" :value="item.id" :label="item.name">
                            {{ item.name }} {{ item.code ? (`(${item.code})`) : '' }}
                        </el-option>
                    </el-select> -->
                </el-form-item>
                <template v-for="k in Object.keys(JSON.parse(JSON.stringify(previewParams)))">
                    <el-form-item :label="k" v-if="!['user_id', 'order_id'].includes(k) && !k.startsWith('_')">
                        <el-input v-model="previewParams[k]" />
                    </el-form-item>
                </template>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelPreview">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="onConfirmPreview">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
    // scss
</style>