<script setup name="PaymentSuppliersList">
import FormMode from './components/FormMode/index.vue'
import { Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

import { getPaymentSuppliers, alterPaymentSupplierAvailability, deletePaymentSupplier } from '@/api/modules/payment'

const { t } = useI18n()

const data = ref({
    loading: false,
    formModeProps: {
        visible: false,
        id: ''
    },
    dataList: []
})

onMounted(() => {
    getDataList()
})


function getDataList() {
    data.value.loading = true
    getPaymentSuppliers().then(res => {
        data.value.loading = false
        data.value.dataList = res.data
    })
}

function onCreate() {
    data.value.formModeProps.id = ''
    data.value.formModeProps.visible = true
}

function onEdit(row) {
    data.value.formModeProps.id = row.id
    data.value.formModeProps.visible = true
}

function onDel(row) {
    ElMessageBox.confirm(t('dialog.messages.deletion', { name: row.name }), t('dialog.titles.confirmation')).then(() => {
        deletePaymentSupplier({ id: row.id }).then((res) => {
            if (res.data.errCode === 365) {
                getDataList()
            }

        })
    }).catch(() => { })
}
function alterStatus(row) {
    let params = {
        id: row.id,
        isAvailable: !row.is_available
    }
    ElMessageBox.confirm(
        t(row.status === 1 ? 'dialog.messages.disable' : 'dialog.messages.enable', { name: row.name }),
        t('dialog.titles.confirmation')
    ).then(() => {
        data.value.loading = true
        alterPaymentSupplierAvailability(params).then(res => {
            if (res.data.errCode === 365) {
                getDataList()
            }
            data.value.loading = true
        })
    }).catch(() => { })

}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (!row.is_available) {
        return 'banned-row'
    } else {
        return ''
    }
}
</script>
<template>
    <div>
        <page-header :title="$t('finance.paymentSupplier.title')" />
        <page-main>
            <el-button type="primary" size="large" @click="onCreate">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:plus" />
                    </el-icon>
                </template>
                {{ $t('operations.add') }}
            </el-button>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" border stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit"
                :row-class-name="tableRowClassName" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column prop="name" :label="$t('fields.name')" />
                <el-table-column prop="id_1" :label="$t('finance.paymentSupplier.fields.id_1')" />
                <el-table-column prop="id_2" :label="$t('finance.paymentSupplier.fields.id_2')" />
                <el-table-column prop="created_at" width="160" :label="$t('fields.createdAt')" sortable="custom" />
                <el-table-column prop="updated_at" width="160" :label="$t('fields.updatedAt')" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item"
                            :content="scope.row.is_available ? $t('operations.disable') : $t('operations.enable')"
                            placement="top-start">
                            <el-button :type="scope.row.is_available ? 'warning' : 'success'" circle size="small"
                                @click="alterStatus(scope.row)">
                                <svg-icon :name="scope.row.is_available ? 'codicon:debug-stop' : 'codicon:debug-start'" />
                            </el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </page-main>
        <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" @success="getDataList" />
    </div>
</template>

<style lang="scss">
.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .banned-row {
        color: var(--g-unavailable-color);
    }
}
</style>
