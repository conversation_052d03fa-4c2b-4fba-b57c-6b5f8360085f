<script setup>
import { getVideos, addVideo, updateVideo } from '@/api/modules/topics'

const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        name: '',
    },
    rules: {
        name: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getVideos({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

const video = ref()
function onVideoChanged(file, fileList) {
    console.log(file)
    console.log(fileList)
    video.value = file
}


defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                const formData = new FormData();
                formData.append('file', video.value.raw)
                formData.append('name', data.value.form.name)
                if (valid) {
                    addVideo(formData).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    let params = { id: data.value.form.id, name: data.value.form.name }
                    updateVideo(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
            <el-form-item :label="$t('fields.name')" prop="video" v-if="props.id == ''">
                <el-upload :auto-upload="false" @on-change="onVideoChanged" accept=".mp4, .mov">
                    <template #trigger>
                        <el-button type="primary">{{ $t('operations.update') }}</el-button>
                    </template>
                </el-upload>

            </el-form-item>
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss
</style>
