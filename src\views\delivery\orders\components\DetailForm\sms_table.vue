<script setup name="OrderSMSList">
    import { usePagination } from '@/utils/composables'

    import { getSMS } from '@/api/modules/messenger'

    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const props = defineProps({
        part: {
            type: String,
            default: ''
        },
        orderId: {
            type: String,
            default: null
        }
    })
    const data = ref({
        loading: false,
        // 列表数据
        dataList: []
    })
    const searchBarCollapsed = ref(0)


    onMounted(() => {
        getDataList()
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify({ order_id: props.orderId }),
            }
        )
        getSMS(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.message_list
            pagination.value.total = res.data.total
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        onSortChange(prop, order).then(() => getDataList())
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (!row.is_available) {
            return 'not-available-row'
        } else {
            return ''
        }
    }

</script>

<template>
    <div>
        <page-main>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row @sort-change="sortChange">
                <el-table-column prop="category" label="Category" />
                <el-table-column prop="recipient" label="Recipient">
                    <template #default="scope">
                        <el-space direction="vertical">
                            {{ scope.row.recipient.name }}
                            {{ scope.row.recipient.phone }}
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="body" label="Body" />
                <el-table-column prop="status" label="Status" />
                <!-- <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" /> -->
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" />
                <!-- <el-table-column :label="$t('fields.operations')" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.delete')" placement="top-start">
                            <el-button type="danger" :icon="Delete" circle size="small" @click="onDel(scope.row)" />
                        </el-tooltip>
                    </template>
                </el-table-column> -->
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
    </div>
</template>

<style lang="scss">
    .el-pagination {
        margin-top: 20px;
    }

    .el-table {
        font-size: 0.8em;

        .sign-row {

            .cell {
                padding: 0 !important;
                text-overflow: initial;
            }
        }

        .not-available-row {
            color: #bbb;
        }
    }

    .top-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: top;
    }
</style>