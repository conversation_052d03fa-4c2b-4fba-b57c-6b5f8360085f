<script setup name="PaymentOrderList">
    import eventBus from '@/utils/eventBus'
    import { usePagination } from '@/utils/composables'
    import FormMode from './components/FormMode/index.vue'
    import { getPaymentOrders } from '@/api/modules/payment'
    import { currencyFormatter, orderNoFormatter, userNameFormatter } from '@/utils/formatter'
    import { useClipboard } from '@vueuse/core'
    import { DocumentCopy } from '@element-plus/icons-vue'
    import { orderStatusStyles } from '@/utils/constants'
    import { useI18n } from 'vue-i18n'

    const { text, copy, copied, isSupported } = useClipboard()
    const { t } = useI18n()
    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const router = useRouter()
    // const route = useRoute()

    const data = ref({
        loading: false,
        /**
         * 详情展示模式
         * router 路由跳转
         * dialog 对话框
         * drawer 抽屉
         */
        formMode: 'router',
        // 详情
        formModeProps: {
            visible: false,
            id: ''
        },
        // 搜索
        search: {
            no__icontains: null,
            user__name__icontains: null,
            total__gte: 0,
            amount__lte: null,
            amount__gte: null,
            refunded_amount__gte: 0,
            refunded_amount__lte: null,
            invoice__isnull: null,
            status: null,
            created_at__lte: null,
            created_at__gte: null,
            updated_at__lte: null,
            updated_at__gte: null,
        },
        // 批量操作
        batch: {
            enable: false,
            selectionDataList: []
        },
        // 列表数据
        dataList: []
    })

    const statusList = ['closed', 'created', 'paying', 'paid', 'refunding', 'partiallyRefunded', 'refunded', 'completed']
    const invoiceStatusList = [
        {
            label: 'all',
            value: null
        },
        {
            label: 'invoiced',
            value: true
        },
        {
            label: 'notInvoiced',
            value: false
        },
    ]

    watch(copied, (val) => {
        val && ElMessage.success(`${t('message.copied')}：${text.value}`)
    })


    onMounted(() => {
        getDataList()
        if (data.value.formMode === 'router') {
            eventBus.on('get-data-list', () => {
                getDataList()
            })
        }
    })

    onBeforeUnmount(() => {
        if (data.value.formMode === 'router') {
            eventBus.off('get-data-list')
        }
    })

    function getDataList() {
        data.value.loading = true
        let params = getParams(
            {
                filters: JSON.stringify(data.value.search)
            }
        )
        getPaymentOrders(params).then(res => {
            data.value.loading = false
            data.value.dataList = res.data.order_list
            pagination.value.total = res.data.total
            window.scrollTo(0, 0);
        })
    }

    // Filters
    function resetFilters() {
        data.value.search =
        {
            no__icontains: null,
            user__name__icontains: null,
            total__gte: 0,
            total__lte: null,
            refunded_amount__gte: 0,
            refunded_amount__lte: null,
            invoice_id__isnull: null,
            invoice__no: null,
            status: null,
            created_at__lte: null,
            created_at__gte: null,
            updated_at__lte: null,
            updated_at__gte: null,
        }
        currentChange()
    }



    // 每页数量切换
    function sizeChange(size) {
        data.value.loading = true
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        data.value.loading = true
        onCurrentChange(page).then(() => getDataList())
    }

    // 字段排序
    function sortChange(prop, order) {
        data.value.loading = true
        onSortChange(prop, order).then(() => getDataList())
    }

    function onEdit(row) {
        router.push({
            name: 'paymentOrderDetail',
            query: {
                id: row.id
            }
        })
    }

    const tableRowClassName = ({
        row,
        rowIndex,
    }) => {
        if (row.status == 'closed') {
            return 'banned-row'
        } else {
            return ''
        }
    }


</script>

<template>
    <div>
        <page-header :title="$t('finance.paymentOrder.title')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('finance.paymentOrder.fields.no')">
                                        <el-input v-model="data.search.no__icontains"
                                            :placeholder="$t('placeholder', { field: $t('finance.paymentOrder.fields.no') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('user.fields.createdAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.created_at__gte" type="date"
                                                :placeholder="$t('user.fields.createdAtMin')"
                                                :disabled-date="disabledDate" :shortcuts="shortcuts" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                                @keydown.enter="currentChange()" @clear="currentChange()"
                                                @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.created_at__lte" type="date"
                                                :placeholder="$t('user.fields.createdAtMax')"
                                                :disabled-date="disabledDate" :shortcuts="shortcuts" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                                @keydown.enter="currentChange()" @clear="currentChange()"
                                                @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('user.fields.updatedAt')">
                                        <el-space>
                                            <el-date-picker v-model="data.search.updated_at__gte" type="date"
                                                :placeholder="$t('user.fields.updatedAtMin')"
                                                :disabled-date="disabledDate" :shortcuts="shortcuts" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                                @keydown.enter="currentChange()" @clear="currentChange()"
                                                @change="currentChange()" />
                                            <span>~</span>
                                            <el-date-picker v-model="data.search.updated_at__lte" type="date"
                                                :placeholder="$t('user.fields.updatedAtMax')"
                                                :disabled-date="disabledDate" :shortcuts="shortcuts" clearable
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                                @keydown.enter="currentChange()" @clear="currentChange()"
                                                @change="currentChange()" />
                                        </el-space>
                                    </el-form-item>
                                </el-col>

                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <el-form-item :label="$t('finance.paymentOrder.fields.amount')">
                                        <el-input-number v-model="data.search.total__gte" :min="0"
                                            :placeholder="$t('finance.paymentOrder.fields.amountMin')"
                                            controls-position="right" clearable @keydown.enter="currentChange()"
                                            @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.total__lte" :min="0"
                                            :placeholder="$t('finance.paymentOrder.fields.amountMax')"
                                            controls-position="right" clearable @keydown.enter="currentChange()"
                                            @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item :label="$t('finance.paymentOrder.fields.refunded')">
                                        <el-input-number v-model="data.search.refunded_amount__gte" :min="0"
                                            :placeholder="$t('finance.paymentOrder.fields.refundedMin')"
                                            controls-position="right" clearable @keydown.enter="currentChange()"
                                            @clear="currentChange()" />
                                        <span>&ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;</span>
                                        <el-input-number v-model="data.search.refunded_amount__lte" :min="0"
                                            :placeholder="$t('finance.paymentOrder.fields.refundedMax')"
                                            controls-position="right" clearable @keydown.enter="currentChange()"
                                            @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item :label="$t('fields.status')">
                                        <el-select v-model="data.search.status"
                                            :placeholder="$t('statSelectHolder', { field: $t('fields.status') })"
                                            size="large" clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in statusList" :key="item"
                                                :label="$t(`finance.paymentOrder.selections.status.${item}`)"
                                                :value="item" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-form-item :label="$t('finance.paymentOrder.fields.invoiceStatus')">
                                        <el-select v-model="data.search.invoice_id__isnull"
                                            :placeholder="$t('statSelectHolder', { field: $t('finance.paymentOrder.fields.invoiceStatus') })"
                                            size="large" clearable @change="currentChange()" @clear="currentChange()">
                                            <el-option v-for="item in invoiceStatusList" :key="item"
                                                :label="$t(`finance.paymentOrder.selections.invoiceStatus.${item.label}`)"
                                                :value="item.value" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item
                                        :label="`${$t('finance.paymentOrder.fields.invoice')} ${$t('finance.paymentOrder.fields.no')}`">
                                        <el-input v-model="data.search.invoice__no"
                                            :placeholder="$t('placeholder', { field: `${$t('finance.paymentOrder.fields.invoice')} ${$t('finance.paymentOrder.fields.no')}` }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <batch-action-bar v-if="data.batch.enable" :data="data.dataList"
                :selection-data="data.batch.selectionDataList">
                <el-button size="default"> 单个批量操作按钮</el-button>
                <el-button-group>
                    <el-button size="default">批量操作按钮组1</el-button>
                    <el-button size="default">批量操作按钮组2</el-button>
                </el-button-group>
            </batch-action-bar>
            <el-table ref="table" v-loading="data.loading" :data="data.dataList" stripe highlight-current-row
                :row-class-name="tableRowClassName" :row-style="{ cursor: 'pointer' }" @row-dblclick="onEdit"
                @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
                <el-table-column type="index" width="50" fixed />
                <el-table-column prop="no" :label="$t('finance.paymentOrder.fields.no')" width="135" fixed>
                    <template #default="scope">
                        <el-space>
                            <el-tooltip placement="top">
                                <template #content>
                                    {{ $t('operations.copy') }}
                                </template>
                                <el-button @click="copy(scope.row.no)" size="small" type="primary" plain circle
                                    :icon="DocumentCopy" />
                            </el-tooltip>
                            <div class="order-number">{{ orderNoFormatter(_, _, scope.row.no) }}</div>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="user" :label="$t('fields.user')" sortable="custom" :show-overflow-tooltip="true"
                    min-width="150">
                    <template #default="scope">
                        <el-space v-if="scope.row.user">
                            <el-tag v-if="scope.row.user?.user_code" size="small" type="info" effect="plain">
                                {{ scope.row.user?.user_code }}</el-tag>
                            <span>{{ userNameFormatter(scope.row) }}</span>
                        </el-space>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" width="160" :label="$t('fields.createdAt')" sortable="custom"
                    align="right" />
                <el-table-column prop="total" :label="$t('finance.paymentOrder.fields.amount')" sortable="custom"
                    :formatter="currencyFormatter" class-name="order-number" align="right" width="100" />
                <el-table-column prop="refunded_amount" :label="$t('finance.paymentOrder.fields.refunded')"
                    :formatter="currencyFormatter" align="right" width="100">
                    <template #default="scope">
                        <span v-if="scope.row.refunded_amount" class="refunded-column">
                            ({{ currencyFormatter(_, _, scope.row.refunded_amount) }})
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="paid_at" width="160" :label="$t('finance.paymentOrder.fields.paidAt')"
                    sortable="custom" align="center" />
                <el-table-column prop="method_img" width="160" :label="$t('finance.paymentOrder.fields.paymentMethod')"
                    sortable="custom" align="center">
                    <template #default="scope">
                        <el-image :src="scope.row.method_img" style="height: 20px;" v-if="scope.row.method_img" />
                        <span v-else>-</span>
                    </template>
                </el-table-column>

                <el-table-column prop="status" :label="$t('fields.status')" align="center" width="130">
                    <template #default="scope">
                        <el-tag :type="orderStatusStyles[scope.row.status]" round size="small">
                            •&ThickSpace;{{ $t('finance.paymentOrder.selections.status.' + scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="updated_at" align="right" width="160" :label="$t('fields.updatedAt')"
                    sortable="custom" />
            </el-table>

            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false"
                class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>

        <FormMode v-if="['dialog', 'drawer'].includes(data.formMode)" :id="data.formModeProps.id"
            v-model="data.formModeProps.visible" :mode="data.formMode" @success="getDataList" />
    </div>
</template>

<style lang="scss">
    .el-table {
        font-size: 0.8em;

        .banned-row {
            color: var(--g-unavailable-color);
        }
    }

    .el-pagination {
        margin-top: 20px;
    }

    .order-number {
        font-weight: bolder;
        font-size: 1em;
    }

    .refunded_amount-column {
        color: orangered;
    }
</style>