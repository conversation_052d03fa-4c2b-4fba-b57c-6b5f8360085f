<script setup>
import api from '@/api'
import { getSMS } from '@/api/modules/messenger';
import { phoneNumberFormatter } from '@/utils/formatter'
import statusStyles from '../../../status'


const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        recipient: {}
    },
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getSMS({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

</script>

<template>
    <div v-loading="data.loading">
        <el-descriptions :column="1">
            <template #title>
                <el-space>
                    <el-tag :type="statusStyles[data.form.status]" round size="small">
                        {{ $t(`messenger.selections.status.${data.form.status}`) }}
                    </el-tag>
                    {{ data.form.recipient.name }}
                    {{ phoneNumberFormatter(data.form.recipient.phone) }}
                </el-space>
            </template>
            <el-descriptions-item :label="$t('fields.createdAt')">{{ data.form.created_at }}</el-descriptions-item>
            <el-descriptions-item label="Content">{{ data.form.body }}</el-descriptions-item>
            <el-descriptions-item :label="$t('fields.category')">{{ data.form.category.name }}</el-descriptions-item>
        </el-descriptions>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>