<script setup>
    import { usePagination } from '@/utils/composables'

    import { getUsers, getUserGroups, updateUser, updateUserStatus, getCreditLogs } from '@/api/modules/users';
    import { getOpenUser, addOpenUser, updateOpenUserSecret, alterOpenUserStatus, deleteOpenUserStatus, approveOpenUser } from '@/api/modules/open';
    import { phoneNumberFormatter } from '@/utils/formatter'

    import LogsView from './logs.vue'
    import CreditLogsView from './credit_logs.vue'
    import { useI18n } from 'vue-i18n'
    const { t } = useI18n()

    const { pagination, getParams, onCurrentChange } = usePagination()

    const props = defineProps({
        id: {
            type: String,
            default: null
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            extended_info: {
                user_group: { id: null }
            }
        },
        logs: [],
        creditLogs: [],

        openInfo: null,
    })

    const userGroups = ref([])
    const selectedGroup = ref()

    const isAvailable = computed(() => data.value.form.status != 'disabled')

    onMounted(() => {
        getInfo()
    })


    function getInfo() {
        data.value.loading = true
        getUsers({ id: data.value.form.id }).then(res => {
            data.value.loading = false
            data.value.form = res.data
            selectedGroup.value = res.data.user_group?.id
            getUserOpenInfo();
        })
        getUserGroups().then(res => {
            userGroups.value = res.data.group_list
        })
        getCLogs()
    }


    function getCLogs() {
        let params = getParams({ userId: data.value.form.id })
        getCreditLogs(params).then(res => {
            pagination.value.total = res.data.total
            data.value.logs = res.data.log_list
        })
    }

    function alterStatus() {
        let params = {
            id: data.value.form.id,
            status: data.value.form.status != 'disabled' ? 'disabled' : 'created'
        }
        ElMessageBox.confirm(
            t(data.value.form.status != 'disabled' ? 'dialog.messages.disable' : 'dialog.messages.enable', { name: data.value.form.name }),
            t('dialog.titles.confirmation')
        ).then(() => {
            data.value.loading = true
            updateUserStatus(params).then(res => {
                if (res.data.errCode === 365) {
                    getInfo()
                }
                data.value.loading = true
            })
        }).catch(() => { })
    }

    function getUserOpenInfo() {
        let params = { id: data.value.form.id }
        getOpenUser(params).then(res => {
            data.value.openInfo = res.data;
        })

    }


    const logRef = ref()

    function onSaveInfo() {
        let params = {
            id: data.value.form.id,
            userCode: data.value.form.user_code,
            groupId: selectedGroup.value,
            userType: data.value.form.user_type,
            status: data.value.form.status
        }
        updateUser(params).then(() => {
            logRef.value.getDataList()
        })
    }

    function onAddOpen() {
        let params = {
            id: data.value.form.id
        }
        addOpenUser(params).then(res => {
            if (res.data.errCode == 365) {
                getUserOpenInfo();
            }
        })
    }

    function onDeleteOpen() {
        let params = {
            id: data.value.openInfo.id
        }
        deleteOpenUserStatus(params).then(res => {
            if (res.data.errCode == 365) {
                getUserOpenInfo();
            }
        })
    }

    function onAlterOpenStatus(toStatus) {
        let params = {
            id: data.value.openInfo.id,
            status: toStatus
        }
        alterOpenUserStatus(params).then(res => {
            if (res.data.errCode == 365) {
                getUserOpenInfo();
            }
        })
    }

    function onApprove() {
        let params = {
            id: data.value.openInfo.id,
        }
        approveOpenUser(params).then(res => {
            if (res.data.errCode == 365) {
                getUserOpenInfo();
            }
        })
    }

    function onResetOpenSecret() {
        let params = { id: data.value.openInfo.id }
        updateOpenUserSecret(params).then(res => {
            if (res.data.errCode == 365) {
                getUserOpenInfo();
            }
        })
    }


</script>

<template>
    <el-row>
        <el-col :span="8">
            <page-main>
                <el-descriptions :column="1">
                    <template #title>
                        <el-space>
                            <span>{{ data.form.name }}
                            </span>
                            <el-tag :type="data.form.user_code ? 'danger' : 'info'" effect="dark">
                                {{ data.form.user_code ? data.form.user_code :
                                    $t('user.selection.noCode') }}
                            </el-tag>
                        </el-space>
                    </template>
                    <template #extra>
                        <el-space>
                            <el-tag :type="data.form.user_type == 'business' ? 'primary' : 'info'">
                                {{ $t(`user.selection.userType.${data.form.user_type}`) }}
                            </el-tag>
                            <el-tag :type="data.form.user_group?.id ? 'success' : 'info'">
                                {{ data.form.user_group?.name ?? $t('user.selection.noGroup') }}
                            </el-tag>
                            <ElTag size="small" :type="data.form.language ? 'warning' : 'info'">
                                {{ data.form.language ? $t(`user.selection.language.${data.form.language}`) :
                                    $t('user.selection.noLanguage')
                                }}
                            </ElTag>

                        </el-space>
                    </template>
                    <el-descriptions-item :label="$t('user.fields.name')">
                        {{ data.form.name }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.phoneNumber')">
                        {{ phoneNumberFormatter(data.form.phone_number) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.secondaryPhone')" v-if="data.form.sec_phone_number">
                        {{ phoneNumberFormatter(data.form.sec_phone_number) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.extensionPhone')" v-if="data.form.ext_phone_number">
                        {{ data.form.ext_phone_number }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.email')">
                        <el-link :href="`mailto:{{data.form.email }}`">{{ data.form.email }}</el-link>
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.address')">
                        {{ data.form.address }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.unitNo')" v-if="data.form.unit_no">
                        {{ data.form.unit_no }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.buzzerCode')" v-if="data.form.buzzer_code">
                        {{ data.form.buzzer_code }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.postalCode')">
                        {{ data.form.postal_code }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.city')">
                        {{ data.form.city }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.province')">
                        {{ data.form.province }}
                    </el-descriptions-item>
                </el-descriptions>
            </page-main>
            <page-main v-if="data.form.user_type == 'business'">
                <el-descriptions :column="1">
                    <el-descriptions-item :label="$t('user.fields.companyName')">
                        {{ data.form.company_name }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.operationHours')">
                        {{ data.form.open_at }} ~ {{ data.form.close_at }}
                    </el-descriptions-item>
                </el-descriptions>
            </page-main>
            <page-main>
                <el-descriptions :column="1">
                    <el-descriptions-item :label="$t('user.fields.username')">
                        {{ data.form.username }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.createdAt')">
                        {{ data.form.created_at }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.updatedAt')">
                        {{ data.form.updated_at }}
                    </el-descriptions-item>
                </el-descriptions>
            </page-main>
        </el-col>
        <el-col :span="8">
            <page-main :title="$t('operations.edit')">
                <template #extra>
                    <el-button :type="data.form.status == 'disabled' ? 'success' : 'warning'" @click="alterStatus">
                        {{ data.form.status == 'disabled' ? $t('operations.enable') : $t('operations.disable') }}
                    </el-button>
                </template>
                <div v-loading="data.loading">
                    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px"
                        label-position="left">
                        <el-form-item :label="$t('user.fields.code')" prop="user_code">
                            <el-input v-model="data.form.user_code"
                                :placeholder="$t('placeholder', { field: $t('user.fields.code') })" />
                        </el-form-item>
                        <el-form-item :label="$t('user.fields.type')" prop="user_type">
                            <el-radio-group v-model="data.form.user_type">
                                <el-radio label="business" size="large">{{ $t('user.selection.userType.business') }}
                                </el-radio>
                                <el-radio label="personal" size="large">{{ $t('user.selection.userType.personal') }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item :label="$t('user.fields.group')" prop="user_group">
                            <el-select v-model="selectedGroup" clearable
                                :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                                <el-option v-for="item in userGroups" :key="item.id" :label="item.name"
                                    :value="item.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('user.fields.status')" prop="status">
                            {{ $t(`user.selection.status.${data.form.status}`) }}
                        </el-form-item>
                    </el-form>
                    <el-divider />
                    <el-button type="warning" @click="getInfo" plain>{{ $t('operations.reset') }}</el-button>
                    <el-button type="primary" @click="onSaveInfo">{{ $t('operations.save') }}</el-button>
                </div>
            </page-main>
        </el-col>
        <el-col :span="8">
            <page-main :title="$t('user.fields.openInfo')">
                <template #extra>
                    <el-button type="primary" @click="onAddOpen" v-if="!data.openInfo">
                        {{ $t('operations.add') }}
                    </el-button>
                    <el-button type="success" @click="onApprove"
                        v-if="data.openInfo && data.openInfo.status == 'created'">
                        {{ $t('operations.approve') }}
                    </el-button>
                    <el-button type="warning" text @click="onAlterOpenStatus('disabled')"
                        v-if="data.openInfo && data.openInfo.status == 'valid'">
                        {{ $t('operations.disable') }}
                    </el-button>
                    <el-button type="success" plain @click="onAlterOpenStatus('valid')"
                        v-if="data.openInfo && data.openInfo.status == 'disabled'">
                        {{ $t('operations.enable') }}
                    </el-button>
                    <el-button type="danger" text @click="onDeleteOpen"
                        v-if="data.openInfo && ['disabled', 'created'].includes(data.openInfo.status)">
                        {{ $t('operations.delete') }}
                    </el-button>
                </template>
                <el-descriptions :column="1" v-if="data.openInfo">
                    <el-descriptions-item :label="$t('user.fields.appId')">
                        {{ data.openInfo?.app_id }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.appSecret')">
                        <el-space>
                            {{ data.openInfo?.app_secret }}
                            <el-button type="success" text @click="onResetOpenSecret"
                                v-if="data.openInfo && data.openInfo.status == 'disabled'">
                                {{ $t('operations.reset') }}
                            </el-button>
                        </el-space>
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.status')">
                        {{ $t(`user.selection.status.${data.openInfo.status}`) }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.createdAt')">
                        {{ data.openInfo?.created_at }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="$t('user.fields.updatedAt')">
                        {{ data.openInfo?.updated_at }}
                    </el-descriptions-item>
                </el-descriptions>
                <el-empty v-else />

            </page-main>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="12">
            <LogsView ref="logRef" :user-id="data.form.id" />
        </el-col>
        <el-col :span="12">
            <CreditLogsView :user-id="data.form.id" :credits="data.form.credits" />
        </el-col>
    </el-row>
</template>
<style>
    .log-operator {
        font-size: 0.8em;
        font-weight: bolder;
        color: #bcbcbc;
    }

    .log-label,
    .log-content {
        font-size: 0.9em;
    }

    .log-label {
        font-weight: bold;
    }
</style>