<script setup>
import Detail from '../eod/step6.vue'
const router = useRouter()
const route = useRoute()


function goBack() {
    router.push({ name: 'financeEODSalaryList' })
}

</script>
<template>
    <div>
        <page-header :title="$t('finance.eod.s6')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>

        <page-main>
            <Detail />
        </page-main>
    </div>
</template>