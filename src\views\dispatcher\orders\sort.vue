<script setup name="DispatcherSortOrders">
    import { sortOrders } from '@/api/modules/dispatcher';
    import { searchDeliveryOrderByPartialNum } from '@/api/modules/delivery'



    const router = useRouter()
    function goBack() {
        router.push({ name: 'dispatcherOrders' })
    }

    const scanMode = ref(true)

    const data = ref({
        no: null,
        index: 1,
        quantity: 1
    })

    const scannedNo = ref('')



    onMounted(() => {
        inputRef.value.focus();
    })

    const result = ref(null)

    const deliveryOrderList = ref([]);

    function getDeliveryOrders(query) {
        if (query?.length < 6) return;
        searchDeliveryOrderByPartialNum(query).then(res => {
            deliveryOrderList.value = res.data;
        })

    }

    function onClearDeliveryOrderList() {
        deliveryOrderList.value = []
    }

    function getResult() {
        console.log(data.value.no)
        if (data.value.no.length != 20) return;
        let params = data.value;
        sortOrders(params).then((res) => {
            if (!res.data.errCode) {
                result.value = res.data
                resultError.value = null
            } else if (res.data.errCode == 444) {
                result.value = res.data.data
                resultError.value = null
            } else if (res.data.errCode == 499) {
                resultError.value = 'Order not dispatched'
                console.log(resultError.value)
            } else {
                result.value = null
                resultError.value = null
            }
        })
    }

    const inputRef = ref()

    const scannedData = ref(null)

    const resultError = ref(null)

    function onScan() {
        if (!scanMode.value) return;
        let val = scannedData.value.replace(/[^\w:,]*/g, '')
        if (val && val != undefined && val.length > 0) {
            inputRef.value.clear();
            let params = Object.fromEntries(val.split(',').map((e) => e.split(':')))
            if (params?.no?.length != 20) return;
            scannedNo.value = params.no;
            data.value.index = params.index ?? 1
            data.value.quantity = params.quantity ?? 1
            sortOrders(params).then((res) => {
                if (!res.data.errCode) {
                    result.value = res.data
                    resultError.value = null
                } else if (res.data.errCode == 444) {
                    result.value = res.data.data
                    resultError.value = null
                } else if (res.data.errCode == 499) {
                    resultError.value = 'Order not dispatched'
                    console.log(resultError.value)
                } else {
                    result.value = null
                    resultError.value = null
                }
            })
        }
        inputRef.value.focus();
    }
</script>

<template>
    <div>
        <page-header :title="$t('dispatcher.sort.title')">
            <el-button size="default" round @click="goBack">
                <template #icon>
                    <el-icon>
                        <svg-icon name="ep:arrow-left" />
                    </el-icon>
                </template>
                {{ $t('operations.back') }}
            </el-button>
        </page-header>
        <page-main>
            <div class="container">
                <el-space>
                    <el-input ref="inputRef" v-model="scannedData" v-if="scanMode" placeholder="Please Scan Label"
                        style="width: 500px;" clearable autofocus @keyup.enter="onScan">
                        <template #prepend>QR</template>
                        <template #prefix>
                            <el-button text type="primary" @click="scanMode = false; resultError = 'Input Order No.'">
                                Scan
                            </el-button>
                        </template>
                        <template #suffix>
                            {{ scannedNo }}
                        </template>
                    </el-input>
                    <el-select v-else v-model="data.no" clearable filterable remote reserve-keyword size="small"
                        autofocus placeholder="Please enter at least 6 numbers" :remote-method="getDeliveryOrders"
                        value-key="no" :loading="loading" style="min-width: 440px;" @change="getResult"
                        @clear="onClearDeliveryOrderList">
                        <template #header>Please enter at least 6 numbers</template>
                        <template #prefix>
                            <el-button text type="success" @click="scanMode = true; resultError = null">
                                Keyboard
                            </el-button>
                        </template>
                        <el-option v-for="item in deliveryOrderList" :key="item" :label="item.no" :value="item.no" />
                    </el-select>
                    <el-input v-model="data.index" placeholder="Please input">
                        <template #prepend>序号</template>
                    </el-input>
                    /
                    <el-input v-model="data.quantity" placeholder="Please input" />
                    <el-button type="primary" @click="getResult">{{ $t('operations.query') }}</el-button>
                </el-space>

                <template v-if="result != null">
                    <el-divider />
                    <el-row>
                        <el-text tag="b" size="large">{{ result.no }}</el-text>
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-space direction="vertical" alignment="start">
                                <el-text size="small">{{ result.sender_name }} - {{ result.sender_company }}</el-text>
                                <el-text size="small">
                                    {{ result.sender_phone }},
                                    {{ result.sender_secondary_phone }}
                                </el-text>
                                <el-text size="small">
                                    {{ result.sender_address }},
                                    {{ result.sender_city }},
                                    {{ result.sender_province }},
                                    {{ result.sender_postal_code }}
                                </el-text>
                                <el-text size="small">
                                    {{ result.expected_pickup_time }}
                                </el-text>
                            </el-space>
                        </el-col>
                        <el-col :span="6">
                            <el-space direction="vertical" alignment="start" v-if="result">
                                <el-text size="small">{{ result.receiver_name }} - {{ result.receiver_company
                                    }}</el-text>
                                <el-text size="small">
                                    {{ result.receiver_phone }},
                                    {{ result.receiver_secondary_phone }}
                                </el-text>
                                <el-text size="small">
                                    {{ result.receiver_address }},
                                    {{ result.receiver_city }},
                                    {{ result.receiver_province }},
                                    {{ result.receiver_postal_code }}
                                </el-text>
                                <el-text size="small">
                                    {{ result.expected_delivery_time }}
                                </el-text>
                            </el-space>
                        </el-col>
                        <el-col :span="6">
                            <el-space>
                                <el-text>Picked up by:</el-text>
                                <el-text tag="b">
                                    {{ result.picked_up_by }}
                                </el-text>
                                <el-text>at</el-text>
                                <el-text>
                                    {{ result.picked_up_at }}
                                </el-text>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-divider />
                    <el-row :gutter="20">
                        <el-col :span="18">
                            <el-space direction="vertical">
                                <el-text style="font-size: 6em;">
                                    {{ result.delivery_bucket }}
                                </el-text>
                                <el-text style="font-size: 6em;" tag="b">
                                    {{ result.delivered_by }}
                                </el-text>
                            </el-space>
                        </el-col>
                        <el-col :span="6">
                            <el-space direction="vertical">
                                <el-space>
                                    <el-text style="font-size: 6em;" tag="b">
                                        {{ result.delivery_stop_no }}
                                    </el-text> /
                                    <el-text style="font-size: 4em;">
                                        {{ result.total_stops }}
                                    </el-text>
                                </el-space>
                                <el-text size="large" tag="b">
                                    {{ result.delivery_option }}
                                </el-text>
                                <el-space size="large" style="margin-top: 36px;">
                                    <template v-for="p of result.pickup_photos">
                                        <el-space direction="vertical">
                                            <el-image style="width: 100px; height: 100px;" :src="p.photo" fit="cover"
                                                :preview-src-list="[p.photo]" hide-on-click-modal />
                                            <el-text size="small" tag="b">
                                                <code>{{ $t(`staffs.worksheet.selections.photoCategories.pickup`) }}</code>
                                            </el-text>
                                        </el-space>
                                    </template>
                                </el-space>
                            </el-space>
                        </el-col>
                    </el-row>
                </template>
                <el-text v-else tag="b">
                    <el-empty :description="resultError != null ? resultError : 'Ready to Scan'" />
                </el-text>

            </div>
        </page-main>
    </div>
</template>

<style lang="scss" scoped>
    .container {
        min-height: calc(100vh - 360px);
    }


    .address {
        display: flex;
        flex-flow: column wrap;
    }
</style>