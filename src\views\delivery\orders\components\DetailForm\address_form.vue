<script setup>
    import { updateDeliveryOrder } from '@/api/modules/delivery';
    import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'

    const props = defineProps({
        id: {
            type: String,
            default: null
        },
        cardShadow: {
            type: String,
            default: 'always'
        },
        data: {
            type: Object,
            default: {},
        },
        part: {
            type: String,
            default: null
        }
    })


    defineExpose({
        submit() {
            updateAddresses()
        },
    })

    const formRef = ref()

    const emit = defineEmits(['success'])

    const data = ref({
        addrDb: props.data.addrDb,
        address: props.data.address,
        city: props.data.city,
        province: props.data.province,
        postalCode: props.data.postalCode,
        unitNo: props.data.unitNo
    })

    const enableAutoCompletion = ref(true)

    const addressesFormRules = {
        address: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        city: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        province: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        postalCode: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ],
    }


    function updateAddresses() {
        let params = {
            id: props.id,
            part: props.part,
        };
        if (enableAutoCompletion.value) {
            params.addrDb = data.value.addrDb;
            params.unitNo = data.value.unitNo;
        } else {
            params.address = data.value.address;
            params.city = data.value.city;
            params.province = data.value.province;
            params.postalCode = data.value.postalCode;
            params.unitNo = data.value.unitNo;
        }
        formRef.value.validate(valid => {
            if (valid) {
                updateDeliveryOrder(params).then(res => {
                    if (res.data.errCode === 365) {
                        emit('success')
                    }
                })
            }
        })
    }

    function onClearAddress() {
        data.value.address = null
        data.value.city = null
        data.value.province = null
        data.value.postalCode = null
        data.value.addrDb = null
    }

    function retrieveAddress(val) {
        data.value.address = val.Line1
        data.value.city = val.City
        data.value.province = val.ProvinceName
        data.value.postalCode = val.PostalCode
        data.value.addrDb = val.Id
    }


</script>
<template>
    <el-card
        :header="`${props.part == 'pickup' ? $t('delivery.orders.fields.sender') : $t('delivery.orders.fields.receiver')} ${$t('user.fields.address')}`"
        :class="props.part == 'pickup' ? 'sender-card' : 'receiver-card'" :shadow="cardShadow">
        <el-form :rules="addressesFormRules" :model="data" ref="formRef">
            <el-form-item>
                <el-checkbox v-model="enableAutoCompletion" label="Auto" />
            </el-form-item>
            <el-form-item :label="$t('user.fields.address')" prop="address">
                <AddressSelect v-if="enableAutoCompletion" ref="addressSelectRef" :address="data.address"
                    :id="data.addrDb" :city="data.city" :province="data.province" :postal-code="data.postalCode"
                    :on-clear="onClearAddress" @success="retrieveAddress" />
                <el-input v-model="data.address" :disabled="enableAutoCompletion" text
                    :type="enableAutoCompletion ? 'hidden' : 'text'" />
            </el-form-item>
            <el-form-item prop="city" :label="$t('user.fields.city')">
                <el-input v-model="data.city" :disabled="enableAutoCompletion" text />
            </el-form-item>
            <el-form-item prop="province" :label="$t('user.fields.province')">
                <el-input v-model="data.province" :disabled="enableAutoCompletion" text />
            </el-form-item>
            <el-form-item prop="postalCode" :label="$t('user.fields.postalCode')">
                <el-input v-model="data.postalCode" :disabled="enableAutoCompletion" text />
            </el-form-item>
            <el-form-item :label="$t('user.fields.unitNo')">
                <el-input v-model="data.unitNo" text />
            </el-form-item>
        </el-form>
    </el-card>
</template>
<style scoped lang="scss">
    :deep(.el-card__header) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-weight: bolder;
        color: white;
    }

    .el-alert {
        margin: 20px 0 0;

        :deep(.el-alert__description) {
            margin: 0;
            padding: 0;
            line-height: normal;
        }
    }

    .el-alert:first-child {
        margin: 0;
    }

    .sender-card {

        :deep(.el-card__header) {
            background-color: #eebe77 !important;
        }
    }

    .receiver-card {

        :deep(.el-card__header) {
            background-color: #b3e19d !important;
        }
    }

    .address-not-available {
        display: block;
        text-align: right;
        font-size: 0.8em;
        color: red;
        line-height: 0.8em;
    }

    .address-auto-span {
        font-weight: bolder;
        text-align: center;
        width: 100%;
        margin-top: -20px;
        margin-bottom: -20px;
        padding-right: 10px;
    }
</style>