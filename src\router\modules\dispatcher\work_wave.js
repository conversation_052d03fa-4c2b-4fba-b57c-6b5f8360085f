
/*
* Author: <EMAIL>'
* Date: '2024-05-16 20:54:26'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/dispatcher/work_wave.js'
* File: 'work_wave.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/work-wave',
    component: Layout,
    redirect: '/dispatcher/work-wave/drivers',
    name: 'dispatcherWorkWave',
    meta: {
        title: 'Work Wave',
        icon: 'carbon:image-service',
        auth: ['super'],
        // i18n: 'route.dispatcher.suppliers.title'
    },
    children: [
        {
            path: 'drivers',
            name: 'dispatcherSupplierWorkWaveDrivers',
            component: () => import('@/views/dispatcher/work_wave/drivers.vue'),
            meta: {
                title: 'Drivers',
                icon: 'tabler:message-2-up',
                activeMenu: '/dispatcher/work_wave/drivers',
                i18n: 'route.dispatcher.eliteExtra.drivers',
                auth: ['super']
            }
        },
    ]
}
