<!--
Author: <EMAIL>'
Date: '2023-01-24 16:47:44'
Project: 'Admin-UI'
Path: 'src/views/delivery/orders/components/DetailForm/order_item.vue'
File: 'order_item.vue'
Version: '1.0.0'
-->

<script setup name="DeliveryOrderItem">
import { Plus, Delete } from '@element-plus/icons-vue'
import { getServices } from '@/api/modules/delivery';
import { getPostalCodes } from '@/api/modules/dispatcher'
import { getPickupTimeByPc, getDeliveryTimeByPc, getHolidayWeekdayFees } from '@/api/modules/delivery';
import { currencyFormatter, phoneNumberFormatter } from '@/utils/formatter'
import AddressSelect from '@/views/components/OrderAddresses/address_select.vue'
import { emailRegex, phoneRegex } from '@/utils/validator'

import { useI18n } from 'vue-i18n';

const { t } = useI18n()

const props = defineProps({
    package: {
        type: Object,
        default: {}
    },
    deliveryFee: {
        type: Number,
        default: 0
    },
    userId: {
        type: String,
        default: null
    }
})

//  Validators
const formRef = ref()
const data = ref({
    pkg: props.package,
    rules: {
        sender_name: [
            { required: true, message: t('delivery.adminOrders.messages.noSenderName'), trigger: 'change' }
        ],
        sender_phone: [
            { required: true, trigger: 'blur', validator: phoneNumberValidator }
        ],
        sender_secondary_phone: [
            { trigger: 'blur', validator: phoneNumberValidator }
        ],
        sender_email: [
            { required: true, trigger: 'blur', validator: emailValidator }
        ],
        sender_address: [
            { required: true, message: t('delivery.adminOrders.messages.noSenderAddress'), trigger: 'change' }
        ],
        expectedPickupTime: [
            { type: 'object', required: true, message: t('delivery.adminOrders.messages.noPickupTime'), trigger: 'change' }
        ],

        receiver_name: [
            { required: true, message: t('delivery.adminOrders.messages.noReceiverName'), trigger: 'change' }
        ],
        receiver_phone: [
            { required: true, trigger: 'blur', validator: phoneNumberValidator }
        ],
        receiver_secondary_phone: [
            { trigger: 'blur', validator: phoneNumberValidator }
        ],
        receiver_email: [
            { type: 'email', required: true, trigger: 'blur', validator: emailValidator }
        ],
        receiver_address: [
            { required: true, message: t('delivery.adminOrders.messages.noReceiverAddress'), trigger: 'change' }
        ],
        expectedDeliveryTime: [
            { type: 'object', required: true, message: t('delivery.adminOrders.messages.noDeliveryTime'), trigger: 'change' }
        ],

        quantity: [
            { type: 'number', required: true, message: t('delivery.adminOrders.messages.noQuantity'), trigger: 'change' }
        ],
        packageType: [
            { type: 'object', required: true, message: t('delivery.adminOrders.messages.noPackageType'), trigger: 'change' }
        ],
        sizeWeight: [
            { type: 'object', required: true, message: t('delivery.adminOrders.messages.noSize'), trigger: 'change' }
        ],

    }
})

function emailValidator(rule, value, callback) {
    if (!value) {
        return callback(new Error(t('delivery.adminOrders.messages.' + (rule.field == 'sender_email' ? 'noSenderEmail' : 'noReceiverEmail'))))
    } else {
        let ret = emailRegex.test(value)
        console.log(value)
        if (ret) {
            callback()
        } else {
            callback(new Error(t('delivery.adminOrders.messages.wrongEmailFormat')))
        }
    }
}
function phoneNumberValidator(rule, value, callback) {
    if (!value) {
        if (['sender_phone', 'receiver_phone'].includes(rule.field)) {
            return callback(new Error(t('delivery.adminOrders.messages.' + (rule.field == 'sender_phone' ? 'noSenderPhone' : 'noReceiverPhone'))))
        } else {
            return callback()
        }
    } else {
        let ret = phoneRegex.test(value)
        if (ret) {
            callback()
        } else {
            if (['sender_phone', 'receiver_phone'].includes(rule.field)) {
                callback(new Error(t('delivery.adminOrders.messages.wrongPhoneFormat')))
            } else {
                callback(new Error(t('delivery.adminOrders.messages.optionalPhoneNumber')))
            }
        }
    }
}

onUpdated(() => {
    fees.value.deliveryFee = props.deliveryFee
    data.value.pkg.user = props.userId
})

watch(
    () => props.package,
    val => {
        data.value.pkg = val
    },
    {
        deep: true,
    }
)

watch(() => data.value.pkg.sender_postal_code, (val) => {
    if (val && val.length >= 6) {
        getPickupTimes()
    }
    else {
        pickupTimes.value = []
        data.value.pkg.expectedPickupTime = undefined
        clearServices()
    }
}
)

watch(() => data.value.pkg.receiver_postal_code, (val) => {
    if (val && val.length >= 6) {
        getDeliveryTimes()
    }
    else {
        pickupTimes.value = []
        data.value.pkg.expectedDeliveryTime = undefined
    }
}
)

watch(() => data.value.pkg.expectedPickupTime, (val) => {
    if (val != undefined) {
        getServiceList()
    } else {
        clearServices()
    }
},
    { deep: true }
)

// Addresses
const senderPcIsAvailable = ref(null)
const receiverPcIsAvailable = ref(null)
const pickupTimes = ref([])
const deliveryTimes = ref([])
const senderAddressSelectRef = ref()
const receiverAddressSelectRef = ref()

// Services
const packageTypes = ref([])
const sizeWeights = ref([])
const addedServices = ref([])


// Api
function getPickupTimes() {
    if (senderPcIsAvailable.value != false) {
        let params = {
            postalCode: data.value.pkg.sender_postal_code.replace(' ', '')
        }

        if (data.value.pkg.user?.id) {
            params.userId = data.value.pkg.user.id
        }

        getPickupTimeByPc(params).then(res => {
            if (!res.data.errCode) {
                pickupTimes.value = res.data
            }
        })
    } else {
        pickupTimes.value = []
        data.value.pkg.expectedPickupTime = undefined
        clearServices()
    }
}

function getDeliveryTimes() {
    if (data.value.pkg.receiver_postal_code && data.value.pkg.expectedPickupTime?.date) {
        let params = {
            postalCode: data.value.pkg.receiver_postal_code.replace(' ', ''),
            date: data.value.pkg.expectedPickupTime.date
        }

        if (data.value.pkg.user?.id) {
            params.userId = data.value.pkg.user.id
        }

        getDeliveryTimeByPc(params).then(res => {
            if (!res.data.errCode) {
                deliveryTimes.value = res.data
            }
        })
    }
}


const getServiceList = () => {
    getTypes()
    getSizes()
    getAddedServiceList()
    getPackagingList()
}

const getTypes = () => {
    let params = {
        type: 'packageType',
        pickupTime: `${data.value.pkg.expectedPickupTime.date} ${data.value.pkg.expectedPickupTime.start_at}`,
    }
    if (data.value.pkg.user?.id) {
        params.userId = data.value.pkg.user.id
    }

    getServices(params).then(res => {
        packageTypes.value = res.data
    })
}
const getSizes = () => {
    let params = {
        type: 'sizeWeight',
        pickupTime: `${data.value.pkg.expectedPickupTime.date} ${data.value.pkg.expectedPickupTime.start_at}`,
    }
    if (data.value.pkg.user?.id) {
        params.userId = data.value.pkg.user.id
    }

    getServices(params).then(res => {
        sizeWeights.value = res.data
    })
}
const getAddedServiceList = () => {
    let params = {
        type: 'addedServices',
        pickupTime: `${data.value.pkg.expectedPickupTime.date} ${data.value.pkg.expectedPickupTime.start_at}`,
    }
    if (data.value.pkg.user?.id) {
        params.userId = data.value.pkg.user.id
    }

    getServices(params).then(res => {
        addedServices.value = res.data
    })
}
const getPackagingList = () => {
    let params = {
        type: 'packaging',
        pickupTime: `${data.value.pkg.expectedPickupTime.date} ${data.value.pkg.expectedPickupTime.start_at}`,
    }
    if (data.value.pkg.user?.id) {
        params.userId = data.value.pkg.user.id
    }

    getServices(params).then(res => {
        const temp = res.data
        temp.forEach(p => p.quantity = 0)
        data.value.pkg.packaging = temp
    })
}

// Addresses
function retrieveSenderAddress(val) {
    data.value.pkg.sender_address = val.Line1
    data.value.pkg.sender_city = val.City
    data.value.pkg.sender_province = val.ProvinceName
    data.value.pkg.sender_postal_code = val.PostalCode
    data.value.pkg.senderAddressId = val.Id
    fees.value.addressFee = Math.max(fees.value.addressFee, val.fee)
    senderPcIsAvailable.value = val.is_available
    getPickupTimes()
}

function retrieveReceiverAddress(val) {
    data.value.pkg.receiver_address = val.Line1
    data.value.pkg.receiver_city = val.City
    data.value.pkg.receiver_province = val.ProvinceName
    data.value.pkg.receiver_postal_code = val.PostalCode
    data.value.pkg.receiverAddressId = val.Id
    fees.value.addressFee = Math.max(fees.value.addressFee, val.fee)
    receiverPcIsAvailable.value = val.is_available
    getDeliveryTimes()
}

function onClearSenderAddress() {
    data.value.pkg.sender_address = null
    data.value.pkg.sender_city = null
    data.value.pkg.sender_province = null
    data.value.pkg.sender_postal_code = null
    clearServices()
}

function onClearReceiverAddress() {
    data.value.pkg.receiver_address = null
    data.value.pkg.receiver_city = null
    data.value.pkg.receiver_province = null
    data.value.pkg.receiver_postal_code = null
    deliveryTimes.value = []
}


// Extended Infos
const clearServices = () => {
    packageTypes.value = []
    sizeWeights.value = []
    addedServices.value = []
    data.value.pkg.packaging = []
    data.value.pkg.packageType = undefined
    data.value.pkg.sizeWeight = undefined
    data.value.pkg.insurancePolicy = undefined
    data.value.pkg.addedServices = []
    data.value.pkg.packaging = []
    data.value.pkg.promotions = []
    data.value.pkg.expectedPickupTime = undefined
    data.value.pkg.expectedDeliveryTime = undefined
}

// Add note
const newNote = ref(null)
const newNoteDialogVisible = ref(false)

function onAddNote() {
    newNoteDialogVisible.value = true
}
function onCancelAddNote() {
    newNoteDialogVisible.value = false
    newNote.value = null
}
function addNote() {
    data.value.pkg.adminNotes.push(newNote.value)
    newNoteDialogVisible.value = false
    newNote.value = null
}

function removeNote(idx) {
    data.value.pkg.adminNotes.splice(idx, 1)
}


// Fees
const fees = ref({
    deliveryFee: props.deliveryFee,
    expressFee: 0,
    pcFee: 0,
    addressFee: 0,
    zoneFee: 0,
    holidayFee: 0,
    weekdayFee: 0,
    timeFee: 0,
    typeFee: 0,
    sizeFee: 0,
    addedServiceFee: 0,
    packagingFee: 0,
    insuranceFee: 0,
    promotionDeduction: 0,
})

const tempFees = ref({
    pickupPcFee: 0,
    pickupZoneFee: 0,
    pickupHolidayFee: 0,
    pickupWeekdayFee: 0,
    deliveryPcFee: 0,
    deliveryZoneFee: 0,
    deliveryHolidayFee: 0,
    deliveryWeekdayFee: 0,
})

const subtotal = computed(() => {
    let subtotal = (fees.value.deliveryFee + fees.value.typeFee + fees.value.sizeFee) * data.value.pkg.quantity + fees.value.expressFee + fees.value.pcFee + fees.value.addressFee + fees.value.zoneFee + fees.value.holidayFee + fees.value.weekdayFee + fees.value.timeFee + fees.value.addedServiceFee + fees.value.packagingFee + fees.value.insuranceFee - fees.value.promotionDeduction
    data.value.pkg.subtotal = subtotal
    return subtotal
})
// Delivery Fee
// PC Fee & Zone Fee
watch(() => data.value.pkg.sender_postal_code, (val) => getPcFees(val, 'p'))
watch(() => data.value.pkg.receiver_postal_code, (val) => getPcFees(val, 'd'))
const getPcFees = (pc, type) => {
    if (pc && pc.length >= 6) {
        getPostalCodes({ code: pc }).then(res => {
            if (!res.data.errCode) {
                switch (type) {
                    case 'p':
                        tempFees.value.pickupPcFee = res.data.fee
                        if (res.data.zone) {
                            tempFees.value.pickupZoneFee = res.data.zone.fee
                        }
                        break
                    case 'd':
                        tempFees.value.deliveryPcFee = res.data.fee
                        if (res.data.zone) {
                            tempFees.value.deliveryZoneFee = res.data.zone.fee
                        }
                        break
                }
                fees.value.pcFee = Math.max(tempFees.value.pickupZoneFee, tempFees.value.deliveryZoneFee)
            }
        })
    }
}
// Holiday Fee & Weekend Fee
watch(() => data.value.pkg.expectedPickupTime?.date, (val) => getTimeFees(val, 'p'))
watch(() => data.value.pkg.expectedDeliveryTime?.date, (val) => getTimeFees(val, 'd'))
const getTimeFees = (date, type) => {
    getHolidayWeekdayFees({ date: date }).then(res => {
        if (!res.data.errCode) {

            if (!res.data.errCode) {
                switch (type) {
                    case 'p':
                        if (res.data.holiday_fee) {
                            tempFees.value.pickupHolidayFee = res.data.holiday_fee.fee
                        }
                        if (res.data.weekday_fee) {
                            tempFees.value.pickupWeekdayFee = res.data.weekday_fee.fee
                        }
                        break
                    case 'd':
                        if (res.data.holiday_fee) {
                            tempFees.value.deliveryHolidayFee = res.data.holiday_fee.fee
                        }
                        if (res.data.weekday_fee) {
                            tempFees.value.deliveryWeekdayFee = res.data.weekday_fee.fee
                        }
                        break
                }
                fees.value.holidayFee = Math.max(tempFees.value.pickupHolidayFee, tempFees.value.deliveryHolidayFee)
                fees.value.holidayFee = Math.max(tempFees.value.pickupWeekdayFee, tempFees.value.deliveryWeekdayFee)
            }
        }
    })
}

// Time Fees
const onPickupTimeChange = val => {
    fees.value.timeFee = Math.max(val.fee, fees.value.timeFee)
    getDeliveryTimes()
}
const onDeliveryTimeChange = val => {
    fees.value.timeFee = Math.max(val.fee, fees.value.timeFee)
}
// Type Fee
const onPackageTypeChanged = (val) => {
    fees.value.typeFee = val.fee
}
// Size Fee
const onSizeWeightChanged = (val) => {
    fees.value.sizeFee = val.fee
}
// Packaging Fee
watch(
    () => data.value.pkg.packaging,
    val => {
        if (val && val.length) {
            fees.value.packagingFee = val.reduce((total, p) => {
                total += p.fee * p.quantity
                return total
            }, 0)
        }
    },
    { deep: true }
)
// Added Services Fee
watch(
    () => data.value.pkg.addedServices,
    val => {
        if (val && val.length) {
            fees.value.addedServiceFee = val.reduce((total, s) => {
                let service = addedServices.value.find(ss => ss.id == s)
                total += service.fee
                return total
            }, 0)

        }
    },
    { deep: true }
)
// Insurance Fee
// Promotion Deduction
// Express Fee

// Tip
const tipTemp = ref(0.0)
const onTipChanged = (val) => {
    data.value.pkg.tip = Number((val * 100).toFixed(0))
}

// Validation
const isComplete = ref(null)

defineExpose({
    validate,
    subtotal,
    isComplete
})


async function validate() {
    let ret = await formRef.value.validate(isValid => {
        isComplete.value = isValid;
        return isValid
    })
    return ret
}



</script>
<template>
    <div>
        <page-main>
            <el-form ref="formRef" :model="data.pkg" :rules="data.rules">
                <el-row :gutter="20">
                    <!-- Main -->
                    <el-col :span="18">
                        <!-- Addresses -->
                        <el-row :gutter="20">
                            <!-- Sender -->
                            <el-col :span="12">
                                <el-card :header="$t('delivery.orders.fields.sender')" class="sender-card" shadow="hover">
                                    <el-form-item :label="$t('fields.name')" prop="sender_name">
                                        <el-input v-model="data.pkg.sender_name" maxlength="50" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.companyName')" prop="sender_company">
                                        <el-input v-model="data.pkg.sender_company" maxlength="50" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.phoneNumber')" prop="sender_phone">
                                        <el-input v-model="data.pkg.sender_phone" :formatter="phoneNumberFormatter" />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.secondaryPhone')" prop="sender_secondary_phone">
                                        <el-input v-model="data.pkg.sender_secondary_phone"
                                            :formatter="phoneNumberFormatter" />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.extensionPhone')">
                                        <el-input v-model="data.pkg.sender_extension_no" maxlength="10" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.email')" prop="sender_email">
                                        <el-input v-model="data.pkg.sender_email" />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.unitNo')">
                                        <el-input v-model="data.pkg.sender_unit_no" maxlength="10" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.buzzerCode')">
                                        <el-input v-model="data.pkg.sender_buzzer_code" maxlength="10" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.address')" prop="sender_address">
                                        <AddressSelect ref="senderAddressSelectRef" :address="data.pkg.sender_address"
                                            :city="data.pkg.sender_city" :province="data.pkg.sender_province"
                                            :postal-code="data.pkg.sender_postal_code" :on-clear="onClearSenderAddress"
                                            @success="retrieveSenderAddress" />
                                    </el-form-item>
                                    <el-form-item prop="sender_postal_code">
                                        <div class="address-auto-span">
                                            <span v-if="data.pkg.sender_postal_code">
                                                {{ data.pkg.sender_postal_code }}&ThickSpace;{{
                                                    data.pkg.sender_city
                                                }},&ThickSpace;{{ data.pkg.sender_province }}
                                            </span>
                                            <span v-else>&ThickSpace;</span>
                                            <span class="address-not-available" v-if="senderPcIsAvailable === false">
                                                (Not Available)
                                            </span>
                                            <span v-else>&ThickSpace;</span>
                                            <el-input v-model="data.pkg.sender_postal_code" type="hidden"
                                                @change="senderPcIsAvailable ? getPickupTimes : null" />
                                        </div>
                                    </el-form-item>
                                    <el-form-item :label="$t('delivery.orders.fields.expectedPickupTime')"
                                        prop="expectedPickupTime">
                                        <el-select v-model="data.pkg.expectedPickupTime" placeholder="Select"
                                            :disabled="!pickupTimes.length" value-key="sn" @change="onPickupTimeChange">
                                            <el-option-group v-for="group in pickupTimes" :key="group.label"
                                                :label="$t('fields.' + group.label) + ' ' + group.date + ' ' + $t('selection.weekdayShort.' + group.weekday)">
                                                <el-option v-for="item in group.options" :key="item.id"
                                                    :label="`${item.start_at?.slice(0, -3)} ~${item.end_at?.slice(0, -3)} (${item.date})`"
                                                    :value="item" />
                                            </el-option-group>
                                        </el-select>
                                    </el-form-item>
                                </el-card>
                            </el-col>
                            <!-- Receiver -->
                            <el-col :span="12">
                                <el-card :header="$t('delivery.orders.fields.receiver')" class="receiver-card"
                                    shadow="hover">
                                    <el-form-item :label="$t('fields.name')" prop="receiver_name">
                                        <el-input v-model="data.pkg.receiver_name" maxlength="50" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.companyName')" prop="receiver_company">
                                        <el-input v-model="data.pkg.receiver_company_name" maxlength="50" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.phoneNumber')" prop="receiver_phone">
                                        <el-input v-model="data.pkg.receiver_phone" :formatter="phoneNumberFormatter" />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.secondaryPhone')" prop="receiver_secondary_phone">
                                        <el-input v-model="data.pkg.receiver_secondary_phone"
                                            :formatter="phoneNumberFormatter" />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.extensionPhone')">
                                        <el-input v-model="data.pkg.receiver_extension_no" maxlength="10" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.email')" prop="receiver_email">
                                        <el-input v-model="data.pkg.receiver_email" />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.unitNo')">
                                        <el-input v-model="data.pkg.receiver_unit_no" maxlength="10" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.buzzerCode')">
                                        <el-input v-model="data.pkg.receiver_buzzer_code" maxlength="10" show-word-limit />
                                    </el-form-item>
                                    <el-form-item :label="$t('user.fields.address')" prop="receiver_address">
                                        <AddressSelect ref="receiverAddressSelectRef" :address="data.pkg.receiver_address"
                                            :city="data.pkg.receiver_city" :province="data.pkg.receiver_province"
                                            :postal-code="data.pkg.receiver_postal_code" :on-clear="onClearReceiverAddress"
                                            @success="retrieveReceiverAddress" />
                                    </el-form-item>
                                    <el-form-item prop="receiver_postal_code">
                                        <div class="address-auto-span">
                                            <span v-if="data.pkg.receiver_postal_code">
                                                {{ data.pkg.receiver_postal_code }}&ThickSpace;{{
                                                    data.pkg.receiver_city
                                                }},&ThickSpace;{{ data.pkg.receiver_province }}
                                            </span>
                                            <span v-else>&ThickSpace;</span>
                                            <span class="address-not-available" v-if="senderPcIsAvailable === false">
                                                (Not Available)
                                            </span>
                                            <span v-else>&ThickSpace;</span>
                                            <el-input v-model="data.pkg.receiver_postal_code" type="hidden"
                                                @change="getDeliveryTimes" />
                                        </div>
                                    </el-form-item>
                                    <el-form-item :label="$t('delivery.orders.fields.expectedDeliveryTime')"
                                        prop="expectedDeliveryTime">
                                        <el-select v-model="data.pkg.expectedDeliveryTime" placeholder="Select"
                                            :disabled="!deliveryTimes.length" value-key="sn" @change="onDeliveryTimeChange">
                                            <el-option-group v-for="group in deliveryTimes" :key="group.label"
                                                :label="group.date + ' ' + $t(`selection.weekdayShort.` + group.weekday)">
                                                <el-option v-for="item in group.options" :key="item.id"
                                                    :label="`${item.start_at?.slice(0, -3)} ~${item.end_at?.slice(0, -3)} (${item.date})`"
                                                    :value="item" />
                                            </el-option-group>
                                        </el-select>
                                    </el-form-item>
                                </el-card>
                            </el-col>
                        </el-row>
                        <!-- Extended Infos -->
                        <el-card shadow="hover" :header="$t('delivery.orders.sections.extendedInfo')" class="box-card">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('fields.quantity')" prop="quantity">
                                        <el-input-number :min="1" :step="1" v-model="data.pkg.quantity" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="9">
                                    <el-form-item :label="$t('delivery.orders.fields.packageType')" prop="packageType">
                                        <el-select v-model="data.pkg.packageType" :disabled="!packageTypes.length"
                                            @change="onPackageTypeChanged" value-key="id">
                                            <el-option v-for="item in packageTypes" :value="item" :label="item.name">
                                                {{ item.name }}
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="9">
                                    <el-form-item :label="$t('delivery.orders.fields.sizeWeight')" prop="sizeWeight">
                                        <el-select v-model="data.pkg.sizeWeight" :disabled="!sizeWeights.length"
                                            @change="onSizeWeightChanged" value-key="id">
                                            <el-option v-for="item in sizeWeights" :value="item" :label="item.name">
                                                {{ item.name }}
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-card>
                        <!-- Notes -->
                        <el-card shadow="hover" class="notes-card">
                            <template #header>
                                <span>{{ $t('delivery.orders.fields.notes') }}</span>
                                <el-button circle type="success" :icon="Plus" size="small" plain @click="onAddNote" />
                            </template>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-input type="textarea" :rows="5" maxlength="200" show-word-limit
                                        v-model="data.pkg.notes" />
                                </el-col>
                                <el-col :span="12">
                                    <div class="admin-notes">
                                        <div v-for="item, idx in data.pkg.adminNotes">
                                            <p class="note-small">
                                                <span>{{ item }}</span>
                                                <el-button circle :icon="Delete" type="danger" plain size="small"
                                                    @click="removeNote(idx)" />
                                            </p>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </el-card>
                        <!-- Added Services -->
                        <el-card class="box-card" shadow="hover" :header="$t('delivery.orders.fields.addedServices')"
                            v-if="addedServices.length">
                            <el-form-item prop="addedServices">
                                <el-checkbox-group v-model="data.pkg.addedServices">
                                    <el-checkbox v-for="item of addedServices" :label="item.id">
                                        {{ item.name }} {{ item.fee }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        </el-card>
                        <!-- Packaging -->
                        <el-card class="box-card" shadow="hover" :header="$t('delivery.orders.fields.packaging')"
                            v-if="data.pkg.packaging?.length">
                            <el-form-item prop="packaging">
                                <el-space size="large" wrap>
                                    <el-space v-for="item of data.pkg.packaging">
                                        <span>{{ item.name }}</span>
                                        <span>{{ item.fee }}</span>
                                        <el-input-number v-model="item.quantity" :min="0" :step="1" />
                                    </el-space>
                                </el-space>
                            </el-form-item>
                        </el-card>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <!-- Express -->
                                <el-card class="box-card" :header="$t('delivery.orders.fields.isExpress')" shadow="hover">
                                    <el-form-item :label="$t('delivery.orders.fields.isExpress')" prop="isExpress">
                                        <el-checkbox v-model="data.pkg.isExpress" />
                                    </el-form-item>
                                </el-card>
                            </el-col>
                            <el-col :span="12">
                                <!-- Tip -->
                                <el-card class="box-card" :header="$t('delivery.orders.fields.tip')" shadow="hover">
                                    <el-form-item prop="tip">
                                        <el-input-number v-model="tipTemp" :min="0" :step="0.1" :precision="1"
                                            @change="onTipChanged" />
                                    </el-form-item>
                                </el-card>

                            </el-col>
                        </el-row>
                    </el-col>
                    <!-- Fees -->
                    <el-col :span="6">
                        <div style="position: relative; height: 100%;">
                            <el-card :header="$t('delivery.orders.sections.feeInfo')"
                                style="position: absolute; bottom: 0; width: 100%;">
                                <el-descriptions :column="1">
                                    <el-descriptions-item :label="$t('delivery.orders.fields.deliveryFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.deliveryFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.expressFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.expressFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.typeFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.typeFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.sizeFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.sizeFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.addedServicesFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.addedServiceFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.packagingFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.packagingFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.addressFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.addressFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.pcFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.pcFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.holidayFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.holidayFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.weekdayFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.weekdayFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.timeFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.timeFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.zoneFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.zoneFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.insuranceFee')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, fees.insuranceFee) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.promotionalDeduction')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        - {{ currencyFormatter(_, _, fees.promotionDeduction) }}
                                    </el-descriptions-item>
                                </el-descriptions>
                                <el-divider border-style="dashed" />
                                <el-descriptions :column="1">
                                    <el-descriptions-item :label="$t('delivery.orders.fields.subtotal')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, subtotal) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item :label="$t('delivery.orders.fields.tip')"
                                        class-name="bold-content" label-class-name="desc-label">
                                        {{ currencyFormatter(_, _, data.pkg.tip) }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </el-card>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
        </page-main>
        <!-- Note Dialog -->
        <el-dialog v-model="newNoteDialogVisible" title="Add Note" align-center>
            <el-input v-model="newNote" type="textarea" maxlength="200" show-word-limit :rows="5" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancelAddNote">{{ $t('operations.cancel') }}</el-button>
                    <el-button type="primary" @click="addNote">
                        {{ $t('operations.submit') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped>
.box-card {
    margin-top: 20px;
}

:deep(.el-card__header) {
    font-weight: bolder;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.sender-card {

    :deep(.el-card__header) {
        background-color: #eebe77 !important;
        color: white;
    }
}

.receiver-card {

    :deep(.el-card__header) {
        background-color: #b3e19d !important;
        color: white;
    }
}

.notes-card {
    margin-top: 20px;

    :deep(.el-card__header) {
        background-color: #606266 !important;
        color: white;
    }
}

.admin-notes {
    border-left: 1px solid #ccc !important;
    padding-left: 20px;

    .note-small {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: small;
        color: #676767;
        margin-bottom: 0;
    }
}

.address-not-available {
    display: block;
    text-align: right;
    font-size: 0.8em;
    color: red;
    line-height: 0.8em;
}

.address-auto-span {
    font-weight: bolder;
    text-align: right;
    width: 100%;
    margin-top: -20px;
    padding-right: 10px;
}

:deep(.el-descriptions__cell) {

    .bold-content {
        font-weight: bolder;
    }

    .desc-label {
        font-size: 0.9em;
        color: #676767 !important;
    }

    .red-desc {
        color: #f56c6c !important;
    }
}
</style>
