{"addresses": {"title": "Addresses", "fields": {}}, "zones": {"title": "Zones"}, "postalCodes": {"title": "Postal Codes", "fields": {"pc1": "PC 1"}}, "suppliers": {"title": "Suppliers", "fields": {"key": "Key", "pin": "<PERSON>n", "baseUrl": "Base Url"}}, "elite": {"template": "Elite Template", "templates": "Elite Templates", "availableVars": "Available Variables", "vars": {"AddedServiceCode": "Added Service Code", "AdminNotes": "Admin Notes", "DeliveryDay": "Delivery Date(Day)", "DeliveryHour": "Delivery Time(Hour)", "DeliveryMonth": "Delivery Date(Month)", "DeliveryOption": "Delivery Option", "DeliveryTime": "Delivery Time", "PickupDay": "Pickup Date(Day)", "PickupHour": "Pickup Time(Hour)", "PickupMonth": "Pickup Date(Month)", "PickupTime": "Pickup Time", "ReceiverAddress2": "Receiver Address 2 (Unit: xxx, Buzzer: yyy", "ReceiverCompany": "Receiver Company", "ReceiverName": "Receiver Name", "ReceiverPhone": "Receiver Phone ", "ReceiverSecondaryPhone": "Receiver Secondary Phone", "SenderAddress2": "Sender Address 2 (Unit: xxx, Buzzer: yyy", "SenderCompany": "Sender Company", "SenderName": "Sender Name", "SenderPhone": "Sender Phone", "SenderSecondaryPhone": "Sender Secondary Phone", "TipFee": "<PERSON><PERSON>e", "UserCode": "User Code", "UserCompanyName": "User Company Name", "UserLanguage": "User Language", "UserVip": "User Vip"}}, "logs": {"title": "Logs", "fields": {"doc": "Doc", "exceptions": "Exceptions", "orderNo": "Order #", "tracking": "Tracking", "userCompanyName": "User Company", "userName": "User Name"}, "selections": {"status": {"failed": "Failed", "success": "Success"}}}, "orders": {"title": "Orders", "fields": {"dispatchedAt": "Dispatched at", "pickedUpBy": "Picked up by", "pickupStopNo": "Pickup Stop #", "pickedUpAt": "Picked up at", "deliveredBy": "Delivered by", "deliveryStopNo": "Delivery Stop #", "deliveredAt": "Delivered at", "isSorted": "Sorted", "address": "Address", "contact": "Contact", "travelTime": "Travel Time", "distance": "Distance", "completedAt": "Completed at", "eta": "ETA", "stop": "Stop", "timeWindow": "Time Window", "phoneNumber": "Phone", "location": "Location", "serviceTime": "Service Time", "unitNo": "Unit No.", "buzzerCode": "Buzzer Code", "relatedOrders": "Related Orders", "uploadedAt": "Last Uploaded At", "uploadAt": "Upload At", "duration": "Duration"}}, "sort": {"title": "Sort Order"}, "exceptions": {"fields": {"notifyUser": "Notify Users", "notifyMethod": "Notify Methods", "messageCategory": "Message Category", "needAck": "Need Ack"}}, "routes": {"fields": {"startAt": "Start at", "completedAt": "Completed at", "driver": "Driver", "pickups": "Pickups", "deliveries": "Deliveries", "name": "Route"}}, "serviceOrders": {"selections": {"status": {"CANCELED": "Canceled", "CREATED": "Created", "PENDING": "Pending", "SCHEDULED": "Scheduled", "FAILED": "Failed", "COMPLETED": "Completed"}}}}