import routeJson from './route'
import common from './common'
import user from './user'
import system from './system'
import administrator from './administrator'
import finance from './finance'
import delivery from './delivery'
import dispatcher from './dispatcher'
import todo from './todos'
import messenger from './messenger'
import modules from './modules'
import statistics from './statistics'
import responses from './responses'
import topics from './topics'
import webContents from './web_contents'
import promotions from './promotions'
import cms from './cms'
import staffs from './staffs'



export default {
    ...common,
    user: user,
    settings: system,
    ...administrator,
    finance: finance,
    delivery: delivery,
    route: routeJson,
    dispatcher: dispatcher,
    todo: todo,
    messenger: messenger,
    modules: modules,
    statistics: statistics,
    response: responses,
    topics: topics,
    webContents: webContents,
    promotions: promotions,
    cms: cms,
    staffs: staffs,
}
