const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher',
    component: Layout,
    redirect: '/dispatcher/zones/list',
    name: 'dispatcherZones',
    meta: {
        title: '区域管理',
        icon: 'material-symbols:activity-zone-outline',
        auth: ['super'],
        i18n: 'route.dispatcher.zones.title'
    },
    children: [
        {
            path: 'zones/list',
            name: 'dispatcherZoneList',
            component: () => import('@/views/dispatcher/zones/list.vue'),
            meta: {
                title: '区域列表',
                icon: 'material-symbols:auto-activity-zone-outline',
                activeMenu: '/dispatcher/zones/list',
                i18n: 'route.dispatcher.zones.zones',
                auth: ['super']
            }
        },
        {
            path: 'zones/detail',
            name: 'dispatcherZoneDetail',
            component: () => import('@/views/dispatcher/zones/detail.vue'),
            meta: {
                title: '详情',
                icon: 'bx:key',
                activeMenu: '/dispatcher/zones/list',
                i18n: 'route.dispatcher.zones.zones',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },
        {
            path: 'postalcode/list',
            name: 'dispatcherPostalCodeList',
            component: () => import('@/views/dispatcher/postal_codes/list.vue'),
            meta: {
                title: '邮编列表',
                icon: 'ph:signpost',
                activeMenu: '/dispatcher/postalcode/list',
                i18n: 'route.dispatcher.zones.postalCodes',
                auth: ['super']
            }
        },
        {
            path: 'postalcode/detail',
            name: 'dispatcherPostalCodeDetail',
            component: () => import('@/views/dispatcher/postal_codes/detail.vue'),
            meta: {
                title: '详情',
                icon: 'bx:key',
                activeMenu: '/dispatcher/postalcode/list',
                i18n: 'route.dispatcher.zones.postalCodes',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },
    ]
}
