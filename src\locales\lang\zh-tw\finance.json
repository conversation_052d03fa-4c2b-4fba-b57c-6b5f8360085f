{
    // Finance
    "paymentSetting": {
        "title": "支付系统设置",
        "fields": {
            "taxRate": "税率",
            "resultUrl": "支付结果页面URL"
        }
    },
    "paymentSupplier": {
        "title": "支付渠道",
        "fields": {
            "id_1": "ID 1",
            "id_2": "ID 2",
            "code": "代码",
            "pin": "密钥",
            "baseUrl": "基本URL",
            "notifyPath": "通知路径"
        }
    },
    "paymentOrder": {
        "title": "支付订单",
        "detailPage": "支付订单详情",
        "fields": {
            "no": "#",
            "total": "总计",
            "subtotal": "小计",
            "tax": "税款",
            "tip": "小费",
            "refunded": "退款",
            "status": "状态",
            "paidAt": "支付时间",
            "paymentMethod": "支付方式",
            "amountMax": "最大总计",
            "amountMin": "最小总计",
            "invoice": "发票",
            "invoiceStatus": "开票状态",
            "refundedMax": "最大退款",
            "refundedMin": "最小退款",
            "resultRemark": "支付结果信息"
        },
        "selections": {
            "status": {
                "closed": "交易关闭",
                "created": "已创建",
                "paying": "正在支付",
                "paid": "已支付",
                "refunding": "正在退款",
                "partiallyRefunded": "部分退款",
                "refunded": "已退款",
                "completed": "交易完成"
            },
            "logStatus": {
                "closed": "关闭交易",
                "created": "创建订单",
                "updated": "更新订单",
                "paying": "正在支付",
                "paid": "支付完成",
                "refunding": "正在退款",
                "partiallyRefunded": "部分退款",
                "refunded": "完成退款",
                "completed": "完成交易"
            },
            "invoiceStatus": {
                "all": "全部",
                "invoiced": "已开票",
                "notInvoiced": "未开票"
            },
            "noInvoice": "未开具"
        },
        "sections": {
            "log": "日志"
        },
        "operations": {
            "close": "关闭交易",
            "refund": "退款",
            "complete": "完成交易",
            "query": "查询状态"
        },
        "statistics": {
            "title": "支付订单统计",
            "totalCounts": "订单总数",
            "todayCounts": "今日订单数 {num}",
            "total": "总计",
            "refundedSum": "退款总和",
            "monthOrders": "本月订单数",
            "monthDelta": "自上月增长 {num}%",
            "monthIncoming": "本月收入",
            "cash": "现金",
            "monthCash": "本月现金 {num}",
            "alphapay": "Alphapay",
            "elavon": "Elavon",
            "credit": "用户余额支付",
            "month": "本月总计",
            "cashMonth": "本月现金",
            "paymentStatistics": "支付统计"
        }
    },
    "ccLog": {
        "title": "Elavon 订单",
        "detailPage": "Elavon 订单详情",
        "fields": {
            "status": "交易状态",
            "amount": "金额",
            "currency": "卡片货币",
            "txnType": "交易类别",
            "result": "交易结果",
            "txnId": "交易号码",
            "txnTime": "交易时间",
            "approvalCode": "批准代码",
            "oarData": "OAR",
            "ps2000Data": "PS2000",
            "settlementBatch": "结算单号",
            "settleTime": "结算时间",
            "cardType": "卡片类型",
            "token": "卡片代码",
            "customerId": "用户 ID",
            "cardNumber": "卡号",
            "expDate": "有效期",
            "shortDesc": "卡片描述",
            "avsResponse": "AVS 结果",
            "firstName": "名",
            "lastName": "姓",
            "address": "地址",
            "zipCode": "邮编",
            "city": "城市",
            "state": "省份",
            "country": "国家",
            "amountMax": "最大金额",
            "amountMin": "最小金额"
        }
    },
    "apLog": {
        "title": "Alphapay 订单",
        "detailPage": "Alphapay 订单详情",
        "fields": {
            "inputFee": "输入金额",
            "realFee": "支付金额",
            "totalFee": "订单金额",
            "currency": "交易货币",
            "rate": "交易汇率",
            "desc": "订单描述",
            "resultCode": "交易结果",
            "orderId": "交易号码",
            "customerId": "用户 ID",
            "createTime": "交易时间",
            "payTime": "支付时间",
            "returnCode": "返回码",
            "returnedMsg": "返回信息",
            "channel": "支付渠道",
            "channelErrCode": "渠道错误码",
            "channelErrMsg": "渠道错误信息",
            "totalFeeMax": "最大订单金额",
            "totalFeeMin": "最小订单金额"
        }
    },
    "crLog": {
        "title": "用户余额日志",
        "fields": {
            "type": "类型",
            "amount": "数量"
        },
        "selections": {
            "type": {
                "0": "消费",
                "1": "退款",
                "2": "优惠返还"
            }
        }
    },
    "invoices": {
        "title": "发票管理",
        "list": "发票列表",
        "detail": "发票详情",
        "fields": {
            "periodStart": "开票周期自",
            "periodEnd": "开票周期至",
            "downloaded": "下载次数",
            "createdAt": "开票时间"
        }
    },
    "userInvoice": {
        "title": "用户发票",
        "fields": {}
    }
}
