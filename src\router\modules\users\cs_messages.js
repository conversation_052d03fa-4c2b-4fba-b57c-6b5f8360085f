import useMessagesStore from '@/store/modules/messages'
import pinia from '@/store'

const messageStore = useMessagesStore(pinia)

const Layout = () => import('@/layout/index.vue')

export default {
    path: '/users/messenger/cs-messages',
    component: Layout,
    redirect: '/users/messenger/cs-messages/list',
    name: 'csMessages',
    meta: {
        title: '客服消息',
        icon: 'jam:messages-f',
        i18n: 'route.users.csMessages.title',
        badge: () => messageStore.newMessagesCount > 0


    },
    children: [
        {
            path: 'list',
            name: 'csMessageList',
            component: () => import('@/views/messenger/cs_messages/list.vue'),
            meta: {
                title: '消息列表',
                icon: 'typcn:messages',
                i18n: 'route.users.csMessages.messages',
                activeMenu: '/users/messenger/cs-messages/list',
            }
        },
        {
            path: 'chat',
            name: 'csMessageChat',
            component: () => import('@/views/messenger/cs_messages/chat.vue'),
            meta: {
                title: '客服信息',
                icon: 'jam:messages-alt-f',
                i18n: 'route.users.csMessages.messenger',
                activeMenu: '/users/messenger/cs-messages/chat',
                badge: () => messageStore.newMessagesCount,
            },
        },
        {
            path: 'email-list',
            name: 'emailList',
            component: () => import('@/views/messenger/emails/list.vue'),
            meta: {
                title: 'Email列表',
                icon: 'mdi:email-arrow-right',
                i18n: 'route.users.csMessages.emails',
                activeMenu: '/users/messenger/cs-messages/email-list',
            }
        },
        {
            path: 'email-detail',
            name: 'emailDetail',
            component: () => import('@/views/messenger/emails/detail.vue'),
            meta: {
                title: 'Email',
                i18n: 'route.messenger.emails',
                activeMenu: '/users/messenger/cs-messages/email-detail',
                sidebar: false
            }
        },
        {
            path: 'sms-list',
            name: 'smsList',
            component: () => import('@/views/messenger/sms/list.vue'),
            meta: {
                title: 'SMS',
                icon: 'mdi:email-arrow-right',
                i18n: 'route.users.csMessages.sms',
                activeMenu: '/users/messenger/cs-messages/sms-list',
            }
        },
        {
            path: 'sms-detail',
            name: 'smsDetail',
            component: () => import('@/views/messenger/sms/detail.vue'),
            meta: {
                title: 'SMS',
                i18n: 'route.messenger.sms',
                activeMenu: '/users/messenger/cs-messages/sms-detail',
                sidebar: false
            }
        },
        {
            path: 'visitor-list',
            name: 'visitorList',
            component: () => import('@/views/cms/chat_now/visitors/list.vue'),
            meta: {
                title: '访客列表',
                icon: 'material-symbols:nest-doorbell-visitor-outline',
                i18n: 'route.users.csMessages.visitors',
                activeMenu: '/users/messenger/cs-messages/visitor-list',
            }
        },
        {
            path: 'visitor-detail',
            name: 'visitorDetail',
            component: () => import('@/views/cms/chat_now/visitors/detail.vue'),
            meta: {
                title: '访客列表',
                i18n: 'route.messenger.visitors',
                activeMenu: '/users/messenger/cs-messages/visitor-list',
                sidebar: false
            }
        },
    ]
}
