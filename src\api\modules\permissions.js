/*
 * Author: <EMAIL>'
 * Date: '2022-11-01 00:55:14'
 * Project: 'Admin-UI'
 * Path: 'src/api/apis/permissions.js'
 * File: 'permissions.js'
 * Version: '1.0.0'
 */

import { DEL, GET, PAT, POST, PUT } from '../methods'

const path = 'permissions/'
export const getPermissions = (params) => GET(path, params)
export const getAllPermissions = (params) => GET(path + 'all/', params)
export const updatePermission = params => PUT(path, params)
export const deletePermission = params => DEL(path, params)

const rolePath = path + 'roles/'
export const getRoles = params => GET(rolePath, params)
export const addRole = params => POST(rolePath, params)
export const updateRole = params => PUT(rolePath, params)
export const updateRoleAvailability = params => PAT(rolePath, params)
export const deleteRole = params => DEL(rolePath, params)

const modulePath = path + 'modules/'
export const getModules = params => GET(modulePath, params)
export const getAllModules = params => GET(modulePath + 'all/', params)
export const addModule = params => POST(modulePath, params)
export const updateModule = params => PUT(modulePath, params)
export const updateModuleAvailability = params => PAT(modulePath, params)
export const deleteModule = params => DEL(modulePath, params)

const operationPath = path + 'operations/'
export const getOperations = params => GET(operationPath, params)
export const getAllOperations = params => GET(operationPath + 'all/', params)
export const addOperation = params => POST(operationPath, params)
export const updateOperation = params => PUT(operationPath, params)
export const updateOperationAvailability = params =>
    PAT(operationPath, params)
export const deleteOperation = params => DEL(operationPath, params)
