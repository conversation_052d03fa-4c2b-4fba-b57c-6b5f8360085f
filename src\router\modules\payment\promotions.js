/*
* Author: <EMAIL>'
* Date: '2023-12-05 22:24:16'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/payment/promotions.js'
* File: 'promotions.js'
* Version: '1.0.0'
*/
const Layout = () => import('@/layout/index.vue')

export default {
    path: '/payment/promotions',
    component: Layout,
    redirect: '/payment/promotions/discounts',
    name: 'paymentPromotion',
    meta: {
        title: '优惠管理',
        icon: 'teenyicons:discount-outline',
        auth: ['super'],
        i18n: 'route.finance.promotion.title'
    },
    children: [
        {
            path: 'discounts',
            name: 'paymentPromotionDiscountList',
            component: () => import('@/views/finance/promotions/discounts/list.vue'),
            meta: {
                title: '折扣管理',
                icon: 'teenyicons:discount-solid',
                i18n: 'route.finance.promotion.discount',
                activeMenu: '/payment/promotions/discounts',
                auth: ['super'],
                cache: ['userInvoiceDetail']

            }
        },
        {
            path: 'discount-detail',
            name: 'paymentPromotionDiscountDetail',
            component: () => import('@/views/finance/promotions/discounts/detail.vue'),
            meta: {
                title: '支付订单',
                icon: 'teenyicons:discount-solid',
                i18n: 'route.finance.promotion.discount',
                activeMenu: '/payment/promotions/discounts',
                auth: ['super'],
                sidebar: false,
            }
        },
        {
            path: 'coupons',
            name: 'paymentPromotionCouponList',
            component: () => import('@/views/finance/promotions/coupons/list.vue'),
            meta: {
                title: '优惠券管理',
                icon: 'mdi:coupon',
                i18n: 'route.finance.promotion.coupon',
                activeMenu: '/payment/promotions/coupons',
                auth: ['super'],
                cache: ['userInvoiceDetail']

            }
        },
        {
            path: 'coupon-detail',
            name: 'paymentPromotionCouponDetail',
            component: () => import('@/views/finance/promotions/coupons/detail.vue'),
            meta: {
                title: '优惠券管理',
                icon: 'mdi:coupon',
                i18n: 'route.finance.promotion.coupon',
                activeMenu: '/payment/promotions/coupons',
                auth: ['super'],
                sidebar: false,
            }
        },
        {
            path: 'logs',
            name: 'paymentPromotionLogs',
            meta: {
                title: '使用日志',
                icon: 'pajamas:log',
                i18n: 'route.finance.promotion.logs',
                activeMenu: '/payment/promotions/logs',
                auth: ['super'],
            },
            children: [
                {
                    path: 'discount-usage-logs',
                    name: 'paymentPromotionDiscountUsageLogs',
                    component: () => import('@/views/finance/promotions/logs/discount_usage.vue'),
                    meta: {
                        title: '折扣',
                        icon: 'octicon:log-24',
                        i18n: 'route.finance.promotion.discountLogs',
                        activeMenu: '/payment/promotions/logs/discount-usage-logs',
                        auth: ['super'],
                    }
                },
                {
                    path: 'coupon-usage-logs',
                    name: 'paymentPromotionCouponUsageLogs',
                    component: () => import('@/views/finance/promotions/logs/coupon_usage.vue'),
                    meta: {
                        title: '优惠券',
                        icon: 'fluent-emoji-high-contrast:blue-book',
                        i18n: 'route.finance.promotion.couponLogs',
                        activeMenu: '/payment/promotions/logs/coupon-usage-logs',
                        auth: ['super'],
                    }
                },

            ]
        },
    ]
}
