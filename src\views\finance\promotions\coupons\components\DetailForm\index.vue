<script setup>
    import { getCoupons, create<PERSON><PERSON>pon, update<PERSON>oupon, deleteCoupons, getCouponExclusiveDiscounts } from '@/api/modules/promotions'
    import UserSelector from '@/views/components/UserSelector/index.vue'
    import { currencyFormatter, snakeToCamel } from '@/utils/formatter'

    import { codeStyles } from '../../constants.js'
    const router = useRouter()

    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })

    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            title: ''
        },
        codeLength: 8,
        codeStyle: 'A',
        rules: {
            discount: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ]
        }
    })

    onMounted(() => {
        getDiscounts();
        if (data.value.form.id != '') {
            getInfo()
        }
    })

    function getInfo() {
        data.value.loading = true
        getCoupons({ id: data.value.form.id }).then(res => {
            data.value.loading = false
            data.value.form = res.data
        })
    }

    defineExpose({
        submit(callback) {
            let params = JSON.parse(JSON.stringify(data.value.form))
            params.discount = data.value.form.discount.id;
            params.code_length = data.value.codeLength;
            params.code_style = data.value.codeStyle;
            if (data.value.form.id == '') {
                formRef.value.validate(valid => {
                    if (valid) {
                        createCoupon(params).then((res) => {
                            if (res.data.errCode == 365) {
                                console.log(res.data)
                                data.value.form.id = res.data.id;
                                getInfo();
                            }
                        })
                    }
                })
            } else {
                formRef.value.validate(valid => {
                    if (valid) {
                        if (data.value.form.can_update) {
                            updateCoupon(params).then((res) => {
                                if (res.data.errCode == 365) { callback && callback() }
                            })
                        }
                        else {
                            callback && callback()
                        }

                    }
                })
            }
        }
    })

    const discounts = ref([])
    function getDiscounts() {
        getCouponExclusiveDiscounts().then(res => {
            discounts.value = res.data;
        })
    }

    const discountECurrencyFields = ['fixed_amount', 'subtotal_max', 'subtotal_min', 'total_max', 'total_min', 'total_subtotal_max', 'total_subtotal_min']
    const discountExcludedFields = ['id', 'created_at', 'updated_at', ...discountECurrencyFields]

    function onDel() {
        ElMessageBox.confirm(`确认删除「${data.value.form.code}」吗？`, '确认信息').then(() => {
            deleteCoupons({ ids: JSON.stringify([data.value.form.id]) }).then((res) => {
                if (res.data.errCode == 365) {
                    router.push({ name: 'paymentPromotionCouponList' })
                }
            })
        }).catch(() => { })
    }

    const disableUpdate = computed(() => !(data.value.form.id === '' || data.value.form.can_update))

    const codeLength = computed({
        get: function () {
            data.value.codeLength = Math.max(data.value.codeLength, data.value.code?.length ?? 0)
            return data.value.codeLength;
        },
        set: function (val) {
            data.value.codeLength = val;
        }
    })

</script>

<template>
    <div>
        <el-row>
            <el-col :span="12">
                <page-main :title="$t('promotions.coupon.title')">
                    <template #extra>
                        <el-button type="danger" size="small" @click="onDel" v-if="data.form.can_delete">{{
                            $t('operations.delete') }}</el-button>
                    </template>
                    <div v-loading="data.loading">
                        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px">
                            <el-form-item :label="$t('promotions.coupon.fields.code')" v-if="data.form.id != ''">
                                <el-text tag="b" size="large">
                                    {{ data.form.code }}
                                </el-text>
                            </el-form-item>
                            <el-form-item :label="$t('promotions.coupon.fields.code')" v-else>
                                <el-text tag="b" size="large">
                                    <el-input v-model="data.form.code" placeholder="请输入标题" maxlength="18"
                                        show-word-limit clearable><template #prepend>FC</template></el-input>
                                </el-text>
                            </el-form-item>
                            <el-form-item :label="$t('promotions.coupon.fields.codeLength')"
                                v-if="data.form.id == '' && (!data.form.code || data.form.data == '')">
                                <el-input-number v-model="codeLength" placeholder="请输入标题" :min="8" :max="20" />
                            </el-form-item>
                            <el-form-item :label="$t('promotions.coupon.fields.codeStyle')"
                                v-if="data.form.id == '' && (!data.form.code || data.form.data == '')">
                                <el-radio-group v-model="data.codeStyle">
                                    <el-radio v-for="s of codeStyles"
                                        :label="$t(`promotions.coupon.selections.codeStyles.${s.label}`)"
                                        :value="s.value" />
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item :label="$t('promotions.coupon.fields.tag')">
                                <el-input v-model="data.form.tag" placeholder="请输入标题" maxlength="50" show-word-limit
                                    clearable />
                            </el-form-item>
                            <el-form-item :label="$t('promotions.coupon.fields.users')">
                                <UserSelector v-model="data.form.users"
                                    place-holder="Leave blank for selecting all users" multiple
                                    :disabled="disableUpdate" />
                            </el-form-item>
                            <el-form-item :label="$t('fields.isAvailable')">
                                <el-switch v-model="data.form.is_available" :disabled="disableUpdate" />
                            </el-form-item>
                            <el-form-item :label="$t('promotions.coupon.fields.discount')" prop="discount">
                                <el-select v-model="data.form.discount" placeholder="Select" style="width: 100%"
                                    :disabled="disableUpdate">
                                    <el-option v-for="item in discounts" :key="item.value" :label="item.name"
                                        :value="item" />
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </div>
                </page-main>
                <page-main :title="$t('promotions.coupon.fields.usedBy')" :extra="data.form.used_at"
                    v-if="data.form.used_by_users?.length ?? 0 > 0">
                    {{ data.form.used_by_users.map(e => e.name).join(', ') }}
                </page-main>
            </el-col>
            <el-col :span="12">
                <page-main v-if="data.form.discount" :title="$t('promotions.coupon.fields.discount')">
                    <el-descriptions :column="1" border>
                        <template v-for="(v, k) of data.form.discount">
                            <el-descriptions-item v-if="!discountExcludedFields.includes(k)"
                                :label="$t(`promotions.discounts.fields.${snakeToCamel(k)}`)">
                                {{ v }}
                            </el-descriptions-item>
                            <el-descriptions-item v-if="discountECurrencyFields.includes(k)"
                                :label="$t(`promotions.discounts.fields.${snakeToCamel(k)}`)">
                                {{ currencyFormatter(_, _, v) }}
                            </el-descriptions-item>
                        </template>
                    </el-descriptions>
                </page-main>
            </el-col>
        </el-row>

    </div>

</template>

<style lang="scss" scoped>
    // scss
</style>