<script setup name="MessengerCsMessagesIndex">

import OfflineMessagesTab from './components/TabContent/offline_messages.vue'


const data = ref({
    loading: false,
    dataList: []
})


onMounted(() => {
    getAdminStatus();
})

async function getAdminStatus() {
    data.value.loading = true;
    await messageStore.getAdminStatus();
    data.value.loading = false;
}


async function onPunchIn() {
    data.value.loading = true;
    await messageStore.punchIn()
    data.value.loading = false;
}

async function onPunchOut() {
    data.value.loading = true;
    await messageStore.punchOut();
    data.value.loading = false;
}

</script>

<template>
    <div>
        <page-header :title="$t('messenger.titles.csMessages')">
            <template #extra>
                <el-button type="primary" size="large" v-if="!messageStore.adminIsOnline" @click="onPunchIn">
                    {{ $t('messenger.operations.punchIn') }}
                </el-button>
                <el-button type="primary" plain size="large" v-else @click="onPunchOut">
                    {{ $t('messenger.operations.punchOut') }}
                </el-button>
            </template>
        </page-header>
        <page-main style="height: calc(100vh - 322px); width: calc(100% - 40px);" v-if="messageStore.adminIsOnline">
            <el-tabs tab-position="left" stretch>
                <el-tab-pane label="用户留言">
                    <OfflineMessagesTab />
                </el-tab-pane>
                <el-tab-pane label="客服消息">
                    客服消息
                </el-tab-pane>
                <el-tab-pane label="消息记录">
                    消息记录
                </el-tab-pane>
                <el-tab-pane label="订单记录">
                    订单记录
                </el-tab-pane>
                <el-tab-pane label="知识库">
                    知识库
                </el-tab-pane>
            </el-tabs>
        </page-main>
        <el-empty v-else />
    </div>
</template>
