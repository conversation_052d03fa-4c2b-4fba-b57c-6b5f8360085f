<!--
Author: <EMAIL>'
Date: '2023-01-22 23:58:19'
Project: 'Admin-UI'
Path: 'src/views/delivery/orders/components/DetailForm/logs_list.vue'
File: 'logs_list.vue'
Version: '1.0.0'
-->
<script setup>
    import { usePagination } from '@/utils/composables'
    import { currencyFormatter, snakeToCamel } from '@/utils/formatter'
    import statusStyles from '../../status'
    import { getOrderLogs } from '@/api/modules/delivery';
    import { deliveryOptions } from '@/utils/constants'

    import { useI18n } from 'vue-i18n';
    const { te } = useI18n()
    const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
    const props = defineProps({
        id: {
            type: String,
            default: null
        }
    })

    const data = ref({
        dataList: []
    })

    const logStyles = {
        created: {
            type: 'success'
        },
        updated: {
            type: 'primary',
        },
        noted: {
            type: 'success',
            hollow: true
        },
        noteRemoved: {
            type: 'danger',
            hollow: true
        }
    }

    const currencyFields = [
        'total', 'subtotal', 'tax', 'refunded', 'delivery_fee', 'express_fee', 'type_fee', 'size_fee', 'added_services_fee', 'packaging_fee', 'address_fee', 'pc_fee',
        'holiday_fee', 'weekday_fee', 'time_fee', 'zone_fee', 'insurance_fee', 'promotional_deduction', 'paid_amount'
    ]

    const serviceFields = ['package_type', 'size_weight', 'added_services']
    const logExcludedFields = ['status', 'expected_pickup_start_at', 'expected_pickup_end_at', 'expected_delivery_start_at', 'expected_delivery_end_at', 'admin_notes', 'delivery_option', 'marks', ...currencyFields, ...serviceFields,]

    onMounted(() => {
        if (props.id) {
            getDataList()
        }
    })

    defineExpose({
        reload() {
            getDataList();
        }
    })

    // Api
    const getDataList = () => {
        data.value.loading = true
        let params = getParams(
            {
                orderId: props.id
            }
        )
        getOrderLogs(params).then((res) => {
            if (!res.data.errCode) {
                data.value.dataList = res.data.log_list
                pagination.value.total = res.data.total
            }
        })
    }

    // 每页数量切换
    function sizeChange(size) {
        onSizeChange(size).then(() => getDataList())
    }

    // 当前页码切换（翻页）
    function currentChange(page = 1) {
        onCurrentChange(page).then(() => getDataList())
    }

    function deliveryOptionName(optionKey) {
        let option = deliveryOptions.find(e => e.value == optionKey);
        console.log(option)
        if (option) {
            return option.name
        }
    }


</script>
<template>
    <page-main :title="$t('delivery.orders.sections.logs')">
        <el-timeline v-if="data.dataList?.length">
            <el-timeline-item v-for="(item, index) in data.dataList" :key="index" :timestamp="item.created_at"
                :type="logStyles[item.operation]?.type" :hollow="logStyles[item.operation]?.hollow" placement="top">
                <el-space direction="vertical" alignment="start" size="large" class="logs">
                    <el-space>
                        <el-tag :type="logStyles[item.operation]?.type" size="small">
                            {{ $t('delivery.orders.logs.' + item.operation) }}
                        </el-tag>
                        <span class="operator">{{ item.operator }}</span>
                    </el-space>
                    <el-descriptions :column="1" v-if="item.record">
                        <el-descriptions-item :label="$t('fields.status')" label-align="right"
                            label-class-name="log-label" v-if="item.record.status">
                            <el-space>
                                <span>
                                    <el-tag :type="statusStyles[item.record.status.prev]" round>
                                        {{ $t(`delivery.orders.selections.status.${item.record.status.prev}`) }}
                                    </el-tag>
                                </span>
                                <el-icon>
                                    <svg-icon name="ep:right" />
                                </el-icon>
                                <el-tag :type="statusStyles[item.record.status.curr]" round>
                                    {{ $t(`delivery.orders.selections.status.${item.record.status.curr}`) }}
                                </el-tag>
                            </el-space>
                        </el-descriptions-item>
                        <template v-for="(v, k, idx) of item.record" :key="idx">
                            <el-descriptions-item :label="$t('delivery.orders.fields.' + snakeToCamel(k))"
                                label-align="right" label-class-name="log-label" v-if="currencyFields.includes(k)">
                                <el-space>
                                    <span>{{ currencyFormatter(_, _, item.record[k].prev) }}</span>
                                    <el-icon>
                                        <svg-icon name="ep:right" />
                                    </el-icon>
                                    <span>{{ currencyFormatter(_, _, item.record[k].curr) }}</span>
                                </el-space>
                            </el-descriptions-item>
                            <el-descriptions-item :label="$t('delivery.orders.fields.' + snakeToCamel(k))"
                                label-align="right" label-class-name="log-label" v-if="serviceFields.includes(k)">
                                <el-space>
                                    <template v-if="item.record[k].prev.name != 'None'">
                                        <el-text :tag="item.record[k].curr.name == 'None' ? 'del' : 'p'">{{
                                            item.record[k].prev.name }}</el-text>
                                        <el-icon v-if="item.record[k].curr.name != 'None'">
                                            <svg-icon name="ep:right" />
                                        </el-icon>
                                    </template>
                                    <span>{{ item.record[k].curr.name }}</span>
                                    <el-text v-if="item.record[k].curr.name != 'None'" tag="del">{{
                                        item.record[k].prev.name }}</el-text>
                                </el-space>
                            </el-descriptions-item>
                        </template>
                        <el-descriptions-item :label="$t('delivery.orders.fields.expectedPickupTime')"
                            label-align="right" label-class-name="log-label"
                            v-if="item.record.expected_pickup_start_at">
                            <el-space>
                                <template
                                    v-if="(item.record.expected_pickup_start_at?.prev ?? 'None') != 'None' && (item.record.expected_pickup_end_at?.prev ?? 'None') != 'None'">
                                    <span>
                                        {{ item.record.expected_pickup_start_at.prev }} ~ {{
                                            item.record.expected_pickup_end_at.prev
                                        }}
                                    </span>
                                    <el-icon>
                                        <svg-icon name="ep:right" />
                                    </el-icon>
                                </template>
                                <span>
                                    {{ item.record.expected_pickup_start_at.curr }} ~ {{
                                        item.record.expected_pickup_end_at.curr
                                    }}
                                </span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.expectedDeliveryTime')"
                            label-align="right" label-class-name="log-label"
                            v-if="item.record.expected_delivery_start_at">
                            <el-space>
                                <template
                                    v-if="(item.record.expected_delivery_start_at?.prev ?? 'None') != 'None' && (item.record.expected_delivery_end_at?.prev ?? 'None') != 'None'">
                                    <span>
                                        {{ item.record.expected_delivery_start_at.prev }} ~ {{
                                            item.record.expected_delivery_end_at.prev
                                        }}
                                    </span>
                                    <el-icon>
                                        <svg-icon name="ep:right" />
                                    </el-icon>
                                </template>
                                <span
                                    v-if="(item.record.expected_delivery_start_at?.curr ?? 'None') != 'None' && (item.record.expected_delivery_end_at?.curr ?? 'None') != 'None'">
                                    {{ item.record.expected_delivery_start_at.curr }} ~ {{
                                        item.record.expected_delivery_end_at.curr
                                    }}
                                </span>
                            </el-space>
                        </el-descriptions-item>
                        <template v-for="(v, k, idx) in item.record" :key="idx">
                            <el-descriptions-item :label="$t('delivery.orders.fields.' + snakeToCamel(k))"
                                label-align="right" label-class-name="log-label" v-if="!logExcludedFields.includes(k)">
                                <el-space>
                                    <template v-if="v.prev != 'None'">
                                        <el-text :tag="v.curr == 'None' ? 'del' : 'p'">{{ v.prev }}</el-text>
                                        <el-icon v-if="v.curr != 'None'">
                                            <svg-icon name="ep:right" />
                                        </el-icon>
                                    </template>
                                    <el-text v-if="v.curr != 'None'">{{ v.curr }}</el-text>
                                </el-space>
                            </el-descriptions-item>
                        </template>
                        <el-descriptions-item :label="$t('delivery.orders.fields.deliveryOption')" label-align="right"
                            label-class-name="log-label" v-if="item.record.delivery_option">
                            <el-space>
                                <span>
                                    {{ deliveryOptionName(item.record.delivery_option.prev) }}
                                </span>
                                <el-icon>
                                    <svg-icon name="ep:right" />
                                </el-icon>
                                <span>{{ deliveryOptionName(item.record.delivery_option.curr) }}</span>
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.adminNotes')" label-align="right"
                            label-class-name="log-label" v-if="item.record.admin_notes">
                            <el-space>
                                {{ item.record.admin_notes }}
                            </el-space>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('delivery.orders.fields.marks')" label-align="right"
                            label-class-name="log-label" v-if="item.record.marks">
                            <el-space>
                                <template v-if="item.record.marks.prev && item.record.marks.prev.length > 0">
                                    <template v-for="m of item.record.marks.prev">
                                        <el-text :style="{ color: m.color }"
                                            :tag="!item.record.marks.curr || item.record.marks.curr.length == 0 ? 'del' : 'p'">
                                            ●
                                        </el-text>
                                        <el-text
                                            :tag="!item.record.marks.curr || item.record.marks.curr.length == 0 ? 'del' : 'p'">
                                            {{ m.notes }}
                                        </el-text>
                                    </template>
                                    <el-icon v-if="item.record.marks.curr && item.record.marks.curr.length > 0">
                                        <svg-icon name="ep:right" />
                                    </el-icon>
                                </template>
                                <template v-for="m of item.record.marks.curr">
                                    <span :style="{ 'color': m.color }">●</span>{{ m.notes }}
                                </template>
                            </el-space>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-space>
            </el-timeline-item>
        </el-timeline>
        <el-empty v-else :description="$t('noData')" />
        <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
            :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="true" class="pagination"
            background @size-change="sizeChange" @current-change="currentChange" />
    </page-main>
</template>
<style lang="scss" scoped>
    .el-pagination {
        margin-top: 20px;
    }
</style>