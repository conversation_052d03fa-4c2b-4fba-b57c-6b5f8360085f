<script setup>
import api from '@/api'
import { addMessengerSupplier, getMessengerSuppliers, updateMessengerSupplier } from '@/api/modules/messenger';
const props = defineProps({
    id: {
        type: [Number, String],
        default: ''
    }
})

const formRef = ref()
const data = ref({
    loading: false,
    form: {
        id: props.id,
        title: ''
    },
    rules: {
        title: [
            { required: true, message: '请输入标题', trigger: 'blur' }
        ]
    }
})

onMounted(() => {
    if (data.value.form.id != '') {
        getInfo()
    }
})

function getInfo() {
    data.value.loading = true
    getMessengerSuppliers({ id: data.value.form.id }).then(res => {
        data.value.loading = false
        data.value.form = res.data
    })
}

defineExpose({
    submit(callback) {
        if (data.value.form.id == '') {
            formRef.value.validate(valid => {
                if (valid) {
                    addMessengerSupplier(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        } else {
            formRef.value.validate(valid => {
                if (valid) {
                    updateMessengerSupplier(data.value.form).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    }
})
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-divider />
            <el-form-item label="Key" prop="key">
                <el-input v-model="data.form.key" placeholder="请输入标题" />
            </el-form-item>
            <el-divider />
            <el-form-item label="Data1 desc." prop="data1_desc">
                <el-input v-model="data.form.data1_desc" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item label="Data 1" prop="data1">
                <el-input v-model="data.form.data1" placeholder="请输入标题" />
            </el-form-item>
            <el-divider />
            <el-form-item label="Data2 desc." prop="data2_desc">
                <el-input v-model="data.form.data2_desc" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item label="Data 2" prop="data2">
                <el-input v-model="data.form.data2" placeholder="请输入标题" />
            </el-form-item>
            <el-divider />
            <el-form-item label="Data3 desc." prop="data3_desc">
                <el-input v-model="data.form.data3_desc" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item label="Data 3" prop="data3">
                <el-input v-model="data.form.data3" placeholder="请输入标题" />
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
// scss</style>
