/*
* Author: <EMAIL>'
* Date: '2024-05-09 21:31:37'
* Project: 'FleetNowV3'
* Path: 'src/router/modules/dispatcher/console.js'
* File: 'console.js'
* Version: '1.0.0'
*/


const Layout = () => import('@/layout/index.vue')

export default {
    path: '/dispatcher/console',
    component: Layout,
    redirect: '/dispatcher/console/index',
    name: 'dispatcherConsole',
    meta: {
        title: '控制台',
        icon: 'mdi:address-marker-outline',
        auth: ['super'],
        i18n: 'route.dispatcher.console.title'
    },
    children: [
        {
            path: 'index',
            name: 'dispatcherConsoleIndex',
            component: () => import('@/views/dispatcher/console/index.vue'),
            meta: {
                title: '首页',
                icon: 'bx:key',
                activeMenu: '/dispatcher/console/index',
                i18n: 'route.dispatcher.console.index',
                sidebar: false,
                breadcrumb: false,
                auth: ['super']
            }
        },
    ]
}
