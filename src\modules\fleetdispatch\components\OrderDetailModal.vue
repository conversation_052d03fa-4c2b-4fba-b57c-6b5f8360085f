<template>
    <!-- 全屏订单详情模态弹窗 -->
    <div v-if="visible" class="order-detail-modal">
        <div class="modal-overlay" @click="close" />
        <div class="modal-container">
            <div class="modal-header">
                <h2>订单详情: {{ orderNo || '加载中...' }}</h2>
                <button class="close-button" @click="close">&times;</button>
            </div>
            <div class="modal-content">
                <iframe 
                    v-if="orderUrl" 
                    :src="orderUrl" 
                    class="order-iframe"
                    frameborder="0"
                />
                <div v-else class="loading-indicator">
                    <div class="spinner" />
                    <p>正在加载订单信息...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="close-button-large" @click="close">关闭</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'

// 模态框状态
const visible = ref(false)
const orderUrl = ref('')
const orderId = ref(null)
const orderNo = ref('')

// 定义props
const props = defineProps({
    initialVisible: {
        type: Boolean,
        default: false
    }
})

// 在组件挂载时添加ESC键监听
onMounted(() => {
    window.addEventListener('keydown', handleKeyDown)
    
    // 如果初始状态是可见的，设置模态框为可见
    if (props.initialVisible) {
        visible.value = true
    }
})

// 在组件销毁时移除事件监听
onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown)
    
    // 确保组件销毁时恢复背景滚动
    document.body.style.overflow = 'auto'
})

// 处理ESC键关闭模态框
const handleKeyDown = event => {
    if (event.key === 'Escape' && visible.value) {
        close()
    }
}

// 打开订单详情模态框
const open = (id, no, url) => {
    orderId.value = id
    orderNo.value = no || '未知订单号'
    orderUrl.value = url
    
    // 显示模态框
    visible.value = true
    
    // 阻止背景滚动
    document.body.style.overflow = 'hidden'
    
    console.log('打开全局订单详情模态框:', id)
}

// 关闭订单详情模态框
const close = () => {
    visible.value = false
    
    // 延迟清空URL，以避免iframe闪烁
    setTimeout(() => {
        orderUrl.value = ''
    }, 300)
    
    // 恢复背景滚动
    document.body.style.overflow = 'auto'
    
    console.log('关闭全局订单详情模态框')
}

// 暴露方法给父组件
defineExpose({
    open,
    close
})
</script>

<style scoped>
/* 模态弹窗样式 */
.order-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99999; /* 确保在最上层 */
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(2px);
}

.modal-container {
    position: relative;
    width: 90vw;
    height: 90vh;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dfe4e8;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.close-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #333;
}

.modal-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.order-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.modal-footer {
    padding: 12px 20px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #dfe4e8;
}

.close-button-large {
    padding: 8px 20px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.close-button-large:hover {
    background-color: #0d47a1;
}

.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style> 