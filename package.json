{"name": "fantastic-admin", "version": "0.1.0", "scripts": {"dev": "vite", "build:test": "vite build --mode test", "build": "vite build", "serve:test": "http-server ./dist-test -o", "serve": "http-server ./dist -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.js", "lint:eslint": "eslint src/**/*.{js,vue} --fix", "lint:stylelint": "stylelint src/**/*.{css,scss,vue} --fix", "prepare": "husky install", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@element-plus/icons-vue": "^2.3.1", "@googlemaps/routing": "^2.0.1", "@imengyu/vue3-context-menu": "^1.4.1", "@mapbox/mapbox-gl-draw": "^1.5.0", "@tinymce/tinymce-vue": "^5.1.1", "@turf/turf": "^7.2.0", "@vueuse/core": "^10.11.0", "@vueuse/integrations": "^10.11.0", "axios": "^1.7.2", "dayjs": "^1.11.11", "element-plus": "^2.7.6", "firebase": "^10.12.3", "hotkeys-js": "^3.13.7", "leaflet": "^1.9.4", "loadash": "^1.0.0", "lodash": "^4.17.21", "mapbox-gl": "^3.9.1", "maplibre-gl": "^5.0.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "node-waves": "^0.7.6", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.2", "pinia": "^2.1.7", "qs": "^6.12.3", "sortablejs": "^1.15.2", "spinkit": "^2.0.1", "splitpanes": "^4.0.3", "supercluster": "^8.0.1", "tinymce": "^6.8.4", "vue": "^3.4.31", "vue-grid-layout": "^2.4.0", "vue-i18n": "^9.13.1", "vue-router": "^4.4.0", "vue3-video-play": "^1.3.2", "vuedraggable": "^4.1.0", "vuefire": "^3.1.23"}, "devDependencies": {"@iconify/json": "^2.2.226", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^0.13.0", "@unocss/preset-icons": "^0.55.7", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.4.31", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.27.0", "esno": "^0.17.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "husky": "^8.0.3", "inquirer": "^9.3.5", "lint-staged": "^14.0.1", "plop": "^3.1.2", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "sass": "^1.77.7", "stylelint": "^15.11.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-scss": "^5.3.2", "svgo": "^3.3.2", "unocss": "^0.55.7", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.3", "vite-plugin-banner": "^0.7.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^3.0.2", "vite-plugin-pages": "^0.31.0", "vite-plugin-pwa": "^0.16.7", "vite-plugin-restart": "^0.3.1", "vite-plugin-spritesmith": "^0.1.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-layouts": "^0.8.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.4.3"}}