<script setup name="MessengerMessagesList">
import eventBus from '@/utils/eventBus'
import { usePagination } from '@/utils/composables'
import { getMessages, approveMessage, abandonMessage, sendMessage } from '@/api/modules/messenger'
import statusStyles from '../status'
import { languages, messageMethods, appPlatforms } from '@/utils/constants'
import { CloseBold, Select, Promotion } from '@element-plus/icons-vue'

import { useI18n } from 'vue-i18n'


const { t } = useI18n()

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
const router = useRouter()
// const route = useRoute()

const data = ref({
    loading: false,
    sending: false,
    // 搜索
    search: {
        name__icontains: null,
    },
    // 批量操作
    batch: {
        selectionDataList: []
    },
    // 列表数据
    dataList: []
})
const searchBarCollapsed = ref(0)


// Filters
function resetFilters() {
    data.value.search =
    {
        name__icontains: null,
    }
    currentChange()
}


onMounted(() => {
    getDataList()
    eventBus.on('get-data-list', () => {
        getDataList()
    })
})

onBeforeUnmount(() => {
    eventBus.off('get-data-list')
})

function getDataList() {
    data.value.loading = true
    let params = getParams(
        {
            filters: JSON.stringify(data.value.search)
        }
    )
    getMessages(params).then(res => {
        data.value.loading = false
        data.value.dataList = res.data.message_list
        pagination.value.total = res.data.total
    })
}

// 每页数量切换
function sizeChange(size) {
    onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
    onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange(prop, order) {
    onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
    router.push({
        name: 'messageDetail'
    })
}

function onEdit(row) {
    router.push({
        name: 'messageDetail',
        query: {
            id: row.id
        }
    })
}

function onApprove(row) {
    ElMessageBox.confirm(t('dialog.messages.approve', { name: row.content }), t('dialog.titles.confirmation')).then(() => {
        approveMessage({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

function onAbandon(row) {
    ElMessageBox.confirm(t('dialog.messages.abandon', { name: row.content }), t('dialog.titles.confirmation')).then(() => {
        abandonMessage({ id: row.id }).then((res) => {
            if (res.data.errCode == 365) {
                getDataList()
            }
        })
    }).catch(() => { })
}

const showSendConfirmationDialog = ref(false)
const messageToSend = ref({
    id: '',
    title: '',
    category: '',
    content: '',
    expired_at: '',
    platforms: appPlatforms,
    language: null,
    methods: ['fcm']
})

function showSendConfirmation(row) {
    showSendConfirmationDialog.value = true;
    messageToSend.value.id = row.id;
    messageToSend.value.title = row.title;
    messageToSend.value.category = row.category.name;
    messageToSend.value.content = row.content;
    messageToSend.value.expired_at = row.expired_at;
    messageToSend.value.language = row.language;
}

function hideSendConfirmation(row) {
    showSendConfirmationDialog.value = false;
    messageToSend.value.id = '';
    messageToSend.value.title = '';
    messageToSend.value.category = '';
    messageToSend.value.content = '';
    messageToSend.value.expired_at = '';
    messageToSend.value.platforms = appPlatforms;
    messageToSend.value.language = null;
    messageToSend.value.methods = ['fcm'];
}


function onSend() {
    let params = {
        id: messageToSend.value.id,
        language: messageToSend.value.language,
        platforms: messageToSend.value.platforms,
        methods: messageToSend.value.methods,
    }
    data.value.sending = true;
    sendMessage(params).then((res) => {
        if (res.data.errCode == 365) {
            getDataList()
        }
        data.value.sending = false;
    }).catch(e => {
        getDataList();
        data.value.sending = false;
    })
}


const tableRowClassName = ({
    row,
    rowIndex,
}) => {
    if (row.status == 'abandoned') {
        return 'not-available-row'
    } else {
        return ''
    }
}

</script>

<template>
    <div>
        <page-header :title="$t('messenger.titles.messages')" />
        <page-main>
            <el-collapse v-model="searchBarCollapsed">
                <el-collapse-item :title="$t('fields.filters')" name="1">
                    <search-bar>
                        <el-form :model="data.search" size="default" label-width="100px" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item :label="$t('delivery.addressBook.fields.user')">
                                        <el-input v-model="data.search.user__name__icontains"
                                            :placeholder="$t('placeholder', { field: $t('delivery.addressBook.fields.user') }) + ', ' + $t('fuzzySupported')"
                                            clearable @keydown.enter="currentChange()" @clear="currentChange()" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item>
                                <el-button type="warning" @click="resetFilters()" plain>
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:refresh-left" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.reset') }}
                                </el-button>
                                <el-button type="primary" @click="currentChange()">
                                    <template #icon>
                                        <el-icon>
                                            <svg-icon name="ep:search" />
                                        </el-icon>
                                    </template>
                                    {{ $t('operations.filter') }}
                                </el-button>
                            </el-form-item>

                        </el-form>
                    </search-bar>
                </el-collapse-item>
            </el-collapse>
        </page-main>
        <page-main>
            <div class="top-buttons">
                <batch-action-bar :data="data.dataList" :selection-data="data.batch.selectionDataList">
                    <el-button-group>
                        <el-button size="default" type="success" plain>
                            {{ $t('operations.batch', { op: $t('operations.approve') }) }}
                        </el-button>
                        <el-button size="default" type="danger">
                            {{ $t('operations.batch', { op: $t('operations.abandon') }) }}
                        </el-button>
                    </el-button-group>
                </batch-action-bar>
                <el-button type="primary" @click="onCreate">
                    <template #icon>
                        <el-icon>
                            <svg-icon name="ep:plus" />
                        </el-icon>
                    </template>
                    {{ $t('operations.add') }}
                </el-button>
            </div>
            <el-table ref="table" v-loading="data.loading" class="list-table" :data="data.dataList" stripe
                highlight-current-row :row-style="{ cursor: 'pointer' }" :row-class-name="tableRowClassName"
                @row-dblclick="onEdit" @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
                <el-table-column type="selection" align="center" fixed />
                <el-table-column prop="title" :label="$t('messenger.fields.title')" width="200" show-overflow-tooltip />
                <el-table-column prop="category.name" :label="$t('messenger.fields.category')" width="200" />
                <el-table-column prop="content" :label="$t('messenger.fields.content')" show-overflow-tooltip width="200" />
                <el-table-column prop="language" :label="$t('messenger.fields.language')" width="100" />
                <el-table-column prop="image" :label="$t('messenger.fields.image')" width="100" />
                <el-table-column prop="status" :label="$t('fields.status')" align="center">
                    <template #default="scope">
                        <el-tag :type="statusStyles[scope.row.status]" round size="small">
                            {{ $t(`messenger.selections.status.${scope.row.status}`) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="$t('fields.createdAt')" width="160" sortable="custom" />
                <el-table-column prop="updated_at" :label="$t('fields.updatedAt')" width="160" sortable="custom" />
                <el-table-column :label="$t('fields.operations')" align="center" fixed="right" width="120">
                    <template #default="scope">
                        <el-tooltip class="box-item" :content="$t('operations.approve')" placement="top-start"
                            v-if="scope.row.status == 'created'">
                            <el-button type="success" plain :icon="Select" circle size="small"
                                @click="onApprove(scope.row)" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.send')" placement="top-start"
                            v-if="scope.row.status == 'approved'">
                            <el-button type="success" :icon="Promotion" circle size="small"
                                @click="showSendConfirmation(scope.row)" />
                        </el-tooltip>
                        <el-tooltip class="box-item" :content="$t('operations.abandon')" placement="top-start">
                            <el-button type="danger" :icon="CloseBold" circle size="small" @click="onAbandon(scope.row)"
                                v-if="['approved', 'created'].includes(scope.row.status)" />
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
                :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
                background @size-change="sizeChange" @current-change="currentChange" />
        </page-main>
        <el-dialog v-model="showSendConfirmationDialog" :title="$t('dialog.messages.send', { name: messageToSend.title })">
            <el-descriptions :title="messageToSend.title" :column="1">
                <el-descriptions-item :label="$t('messenger.fields.content')">
                    {{ messageToSend.content }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('messenger.fields.category')">
                    {{ messageToSend.category }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('messenger.fields.expiredAt')">
                    {{ messageToSend.expired_at }}
                </el-descriptions-item>
            </el-descriptions>
            <el-form v-loading="data.sending">
                <el-form-item :label="$t('messenger.fields.language')">
                    <el-select v-model="messageToSend.language" class="m-2" placeholder="Select">
                        <el-option v-for="item in languages" :key="item" :label="$t(`selection.languages.${item}`)"
                            :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('messenger.fields.platforms')">
                    <el-checkbox-group v-model="messageToSend.platforms">
                        <el-checkbox v-for="item in appPlatforms" :key="item" :label="item">
                            {{ item }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item :label="$t('messenger.fields.method')">
                    <el-checkbox-group v-model="messageToSend.methods">
                        <el-checkbox v-for="item in messageMethods" :key="item" :label="item">
                            {{ $t(`messenger.selections.sendMethods.${item}`) }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="hideSendConfirmation" v-loading="data.sending">
                        {{ $t('operations.cancel') }}
                    </el-button>
                    <el-button type="primary" @click="onSend" v-loading="data.sending">
                        {{ $t('operations.confirm') }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
.el-pagination {
    margin-top: 20px;
}

.el-table {
    font-size: 0.8em;

    .sign-row {

        .cell {
            padding: 0 !important;
            text-overflow: initial;
        }
    }

    .not-available-row {
        color: var(--g-unavailable-color);
    }
}

.top-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: top;
}
</style>
