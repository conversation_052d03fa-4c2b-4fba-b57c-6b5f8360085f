<script setup>
    import { getDiscounts, createUpdateDiscount } from '@/api/modules/promotions'
    import { getUsers, getUserGroups } from '@/api/modules/users';
    import { getAllServices } from '@/api/modules/delivery';
    import { languages } from '@/utils/constants';
    import { round } from 'lodash';

    const props = defineProps({
        id: {
            type: [Number, String],
            default: ''
        }
    })


    const discountTypes = ['instant', 'completion']
    const orderTypes = ['PAYMENT', 'DELIVERY', 'RECHARGEMENT']
    const fixedAmount = ref(null)
    const totalSubtotalMax = ref(null)
    const totalSubtotalMin = ref(null)
    const userGroups = ref([])
    const packageTypes = ref([])
    const sizeWeights = ref([])
    const addedServices = ref([])
    const paymentMethods = ['credits', 'cc', 'wechat', 'alipay']


    const formRef = ref()
    const data = ref({
        loading: false,
        form: {
            id: props.id,
            name: '',
            user_groups: [],
            languages_included: [],
            languages_excluded: [],
            payment_methods_included: [],
            payment_methods_excluded: [],
        },
        rules: {
            name: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            description: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            discount_type: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
            order_type: [
                { required: true, message: '请输入标题', trigger: 'blur' }
            ],
        }
    })

    onMounted(() => {
        if (data.value.form.id != '') {
            getInfo()
        }
    })

    function getInfo() {
        data.value.loading = true
        getDiscounts({ id: data.value.form.id }).then(res => {
            data.value.loading = false
            data.value.form = res.data
            fixedAmount.value = res.data.fixed_amount != null ? Number((res.data.fixed_amount / 100).toFixed(2)) : null
            totalSubtotalMax.value = res.data.total_subtotal_max != null ? Number((res.data.total_subtotal_max / 100).toFixed(2)) : null
            totalSubtotalMin.value = res.data.total_subtotal_min != null ? Number((res.data.total_subtotal_min / 100).toFixed(2)) : null
        })
        getUserGroups().then(res => {
            userGroups.value = res.data.group_list
        })
        getAllServices().then(res => {
            packageTypes.value = res.data.package_types;
            sizeWeights.value = res.data.size_weights;
            addedServices.value = res.data.added_services;
        })

    }

    defineExpose({
        submit(callback) {
            formRef.value.validate(valid => {
                if (valid) {
                    let params = JSON.parse(JSON.stringify(data.value.form))
                    params.fixed_amount = round(fixedAmount.value * 100)
                    createUpdateDiscount(params).then((res) => {
                        if (res.data.errCode == 365) { callback && callback() }
                    })
                }
            })
        }
    })
</script>

<template>
    <div v-loading="data.loading">
        <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="160px">
            <el-form-item :label="$t('fields.name')" prop="name">
                <el-input v-model="data.form.name" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item :label="$t('fields.desc')" prop="description">
                <el-input v-model="data.form.description" placeholder="请输入标题" type="textarea" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.discountType')" prop="discount_type">
                <el-select v-model="data.form.discount_type">
                    <el-option v-for="t in discountTypes"
                        :label="$t(`promotions.discounts.selections.discountType.${t}`)" :value="t">{{
                            $t(`promotions.discounts.selections.discountType.${t}`) }}</el-option>
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.orderType')" prop="order_type">
                <el-select v-model="data.form.order_type">
                    <el-option v-for="t in orderTypes" :label="$t(`promotions.discounts.selections.orderTypes.${t}`)"
                        :value="t">
                        {{ $t(`promotions.discounts.selections.orderTypes.${t}`) }}
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.fixedAmount')">
                <el-input-number v-model="fixedAmount" placeholder="请输入标题" :precision="2" :step="0.1" :min="0.0" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.percentage')">
                <el-input-number v-model="data.form.percentage" placeholder="请输入标题" :min="0" />&ThinSpace; %
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.isReturnedToCredits')">
                <el-switch v-model="data.form.is_returned_to_credits" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.maxUsageCount')">
                <el-input-number v-model="data.form.max_usage_count" placeholder="请输入标题" :min="0" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.maxUsageCountPerUser')">
                <el-input-number v-model="data.form.max_usage_count_per_user" placeholder="请输入标题" :min="0" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.availableFrom')">
                <el-date-picker v-model="data.form.available_from" type="datetime" placeholder="Select date and time"
                    value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.availableUntil')">
                <el-date-picker v-model="data.form.available_until" type="datetime" placeholder="Select date and time"
                    value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.isAvailable')">
                <el-switch v-model="data.form.is_available" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.couponExclusive')">
                <el-switch v-model="data.form.is_exclusive_for_coupon" />
            </el-form-item>
            <el-form-item :label="$t('promotions.discounts.fields.canBeCombined')">
                <el-switch v-model="data.form.can_be_combined" />
            </el-form-item>
            <el-collapse v-model="activeNames" @change="handleChange">
                <el-collapse-item :title="$t('fields.descGroups')" name="1">
                    <el-form-item :label="$t('fields.descZh')">
                        <el-input v-model="data.form.description_zh_cn" placeholder="请输入标题" type="textarea" />
                    </el-form-item>
                    <el-form-item :label="$t('fields.descTw')">
                        <el-input v-model="data.form.description_zh_tw" placeholder="请输入标题" type="textarea" />
                    </el-form-item>
                    <el-form-item :label="$t('fields.descFr')">
                        <el-input v-model="data.form.description_fr" placeholder="请输入标题" type="textarea" />
                    </el-form-item>
                    <el-form-item :label="$t('fields.descEs')">
                        <el-input v-model="data.form.description_es" placeholder="请输入标题" type="textarea" />
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item :title="$t('promotions.discounts.fields.userConditions')" name="2">
                    <el-form-item :label="$t('user.fields.type')" prop="user_type">
                        <el-radio-group v-model="data.form.user_type">
                            <el-radio label="business">{{ $t('user.selection.userType.business') }}
                            </el-radio>
                            <el-radio label="personal">{{ $t('user.selection.userType.personal') }}
                            </el-radio>
                            <el-radio :label="null">{{ $t('notSpecific') }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.group')">
                        <el-select v-model="data.form.user_groups" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in userGroups" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('user.fields.language')">
                        {{ $t('fields.included') }}
                        <el-select v-model="data.form.languages_included" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in languages" :key="item" :label="$t(`selection.languages.${item}`)"
                                :value="item" :disabled="data.form.languages_excluded.includes(item)" />
                        </el-select>
                        {{ $t('fields.excluded') }}
                        <el-select v-model="data.form.languages_excluded" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in languages" :key="item" :label="$t(`selection.languages.${item}`)"
                                :value="item" :disabled="data.form.languages_included.includes(item)" />
                        </el-select>
                    </el-form-item>

                    <el-form-item :label="$t('user.fields.createdAt')">
                        <el-date-picker v-model="data.form.user_register_after" type="date" placeholder="Pick a day"
                            :size="size" />
                        &ThickSpace;~&ThickSpace;
                        <el-date-picker v-model="data.form.user_register_before" type="date" placeholder="Pick a day"
                            :size="size" />
                    </el-form-item>
                    <el-form-item :label="$t('promotions.discounts.fields.totalOrders')">
                        <el-input-number v-model="data.form.total_order_count_min" placeholder="请输入标题" :min="0" />
                        &ThickSpace;~&ThickSpace;
                        <el-input-number v-model="data.form.total_order_count_max" placeholder="请输入标题"
                            :min="data.form.total_order_count_min ?? 0" />
                    </el-form-item>
                    <el-form-item :label="$t('promotions.discounts.fields.totalSubtotal')">
                        <el-input-number v-model="totalSubtotalMax" placeholder="请输入标题" :min="0" />
                        &ThickSpace;~&ThickSpace;
                        <el-input-number v-model="totalSubtotalMin" placeholder="请输入标题" :min="totalSubtotalMin ?? 0" />
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item :title="$t('promotions.discounts.fields.packageConditions')" name="3">
                    <el-form-item :label="$t('delivery.services.packageType')">
                        {{ $t('fields.included') }}
                        <el-select v-model="data.form.package_types_included" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in packageTypes" :key="item.id" :label="item.names['en-us']"
                                :value="item.id" :disabled="data.form.package_types_excluded.includes(item.id)" />
                        </el-select>
                        {{ $t('fields.excluded') }}

                        <el-select v-model="data.form.package_types_excluded" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in packageTypes" :key="item.id" :label="item.names['en-us']"
                                :disabled="data.form.package_types_included.includes(item.id)" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('delivery.services.sizeWeight')">
                        {{ $t('fields.included') }}
                        <el-select v-model="data.form.size_weights_included" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in sizeWeights" :key="item.id" :label="item.names['en-us']"
                                :value="item.id" :disabled="data.form.size_weights_excluded.includes(item.id)" />
                        </el-select>
                        {{ $t('fields.excluded') }}
                        <el-select v-model="data.form.size_weights_excluded" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in sizeWeights" :key="item.id" :label="item.names['en-us']"
                                :disabled="data.form.size_weights_included.includes(item.id)" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('delivery.services.addedService')">
                        {{ $t('fields.included') }}
                        <el-select v-model="data.form.added_services_included" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in addedServices" :key="item.id" :label="item.names['en-us']"
                                :value="item.id" :disabled="data.form.added_services_excluded.includes(item.id)" />
                        </el-select>
                        {{ $t('fields.excluded') }}
                        <el-select v-model="data.form.added_services_excluded" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in addedServices" :key="item.id" :label="item.names['en-us']"
                                :disabled="data.form.added_services_included.includes(item.id)" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('finance.paymentOrder.fields.paymentMethod')">
                        {{ $t('fields.included') }}
                        <el-select v-model="data.form.payment_methods_included" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in paymentMethods" :key="item"
                                :label="$t(`selection.paymentMethod.${item}`)" :value="item"
                                :disabled="data.form.payment_methods_excluded.includes(item)" />
                        </el-select>
                        {{ $t('fields.excluded') }}
                        <el-select v-model="data.form.payment_methods_excluded" clearable multiple
                            :placeholder="$t('selectHolder', { field: $t('user.fields.group') })">
                            <el-option v-for="item in paymentMethods" :key="item"
                                :label="$t(`selection.paymentMethod.${item}`)" :value="item"
                                :disabled="data.form.payment_methods_included.includes(item)" />
                        </el-select>
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item :title="$t('promotions.discounts.fields.orderConditions')" name="4">
                    <el-form-item :label="$t('promotions.discounts.fields.subtotal')">
                        <el-space>
                            <el-input-number v-model="data.form.subtotal_min" placeholder="请输入标题" :precision="2"
                                :step="0.1" :min="0.0" />
                            ~
                            <el-input-number v-model="data.form.subtotal_max" placeholder="请输入标题" :precision="2"
                                :step="0.1" :min="0.0" />
                        </el-space>
                    </el-form-item>
                    <el-form-item :label="$t('promotions.discounts.fields.total')">
                        <el-space>
                            <el-input-number v-model="data.form.total_min" placeholder="请输入标题" :precision="2"
                                :step="0.1" :min="0.0" />
                            ~
                            <el-input-number v-model="data.form.total_max" placeholder="请输入标题" :precision="2"
                                :step="0.1" :min="0.0" />
                        </el-space>
                    </el-form-item>
                    <el-form-item :label="$t('promotions.discounts.fields.samePatchCount')">
                        <el-input-number v-model="data.form.same_patch_order_count_min" placeholder="请输入标题" :min="0" />
                        &ThickSpace;&ThickSpace;~&ThickSpace;&ThickSpace;
                        <el-input-number v-model="data.form.same_patch_order_count_max" placeholder="请输入标题"
                            :min="data.form.same_patch_order_count_min ?? 0" />
                    </el-form-item>

                </el-collapse-item>
            </el-collapse>

        </el-form>
    </div>
</template>

<style lang="scss" scoped>
    // scss</style>