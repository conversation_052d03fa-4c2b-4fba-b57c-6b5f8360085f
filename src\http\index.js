/*
@author: Tchiang<PERSON>, <EMAIL>
*/

import instance from "./request";

const axios = ({ method, url, data, config }) => {
    method = method.toLowerCase();

    switch (method) {
        case "post":
            return instance.post(url, data, { ...config });
        case "postform":
            return instance.postForm(url, data, { ...config });

        case "get":
            return instance.get(url, { params: data, ...config });

        case "delete":
            return instance.delete(url, { params: data, ...config });

        case "put":
            return instance.put(url, data, { ...config });

        case "putform":
            return instance.putForm(url, data);

        case "patch":
            return instance.patch(url, data);

        default:
            console.error("Unknown Method:" + method);
            return false;
    }
};
export default axios;
